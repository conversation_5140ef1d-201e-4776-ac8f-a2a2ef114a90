#!/usr/bin/env python3
"""
Test script to verify the consent form fix works correctly.

This script demonstrates the issue where customers were automatically marked as having
completed all forms, even when they hadn't signed forms for the specific business
they were booking with.

The fix ensures that the business_id parameter is used to check the correct business's
form requirements.
"""

import os
import sys
import django

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from django.contrib.auth import get_user_model
from business.models import Business, BusinessCustomer
from customers.models import CustomerProfile
from forms.models import FormTemplate, BusinessRequiredForm
from django.test import RequestFactory
from api.v1.business.views import CustomerFormCompletionView

User = get_user_model()

def create_test_data():
    """Create test data to demonstrate the issue and fix."""
    print("🔧 Creating test data...")
    
    # Create two businesses
    business1 = Business.objects.get_or_create(
        name="Business 1 (No Required Forms)",
        defaults={
            'owner_id': 1,  # Assuming admin user exists
            'slug': 'business-1'
        }
    )[0]
    
    business2 = Business.objects.get_or_create(
        name="Business 2 (Has Required Forms)",
        defaults={
            'owner_id': 1,  # Assuming admin user exists
            'slug': 'business-2'
        }
    )[0]
    
    # Create a test customer
    user = User.objects.get_or_create(
        email='<EMAIL>',
        defaults={
            'first_name': 'Test',
            'last_name': 'Customer',
            'phone_number': '+1234567890'
        }
    )[0]
    
    customer_profile = CustomerProfile.objects.get_or_create(
        user=user
    )[0]
    
    # Create business customer relationships
    bc1 = BusinessCustomer.objects.get_or_create(
        business=business1,
        customer=customer_profile
    )[0]
    
    bc2 = BusinessCustomer.objects.get_or_create(
        business=business2,
        customer=customer_profile
    )[0]
    
    # Create a required form for business 2 only
    form_template = FormTemplate.objects.get_or_create(
        name="Consent Form",
        business=business2,
        defaults={
            'document_type': 'consent',
            'content': {'fields': [{'type': 'signature', 'required': True}]}
        }
    )[0]
    
    BusinessRequiredForm.objects.get_or_create(
        business=business2,
        form_template=form_template,
        defaults={
            'is_required': True,
            'required_for_new_customers': True
        }
    )
    
    print(f"✅ Created test data:")
    print(f"   - Business 1 (ID: {business1.id}): No required forms")
    print(f"   - Business 2 (ID: {business2.id}): Has required consent form")
    print(f"   - Customer: {user.email}")
    
    return {
        'user': user,
        'business1': business1,
        'business2': business2,
        'customer_profile': customer_profile,
        'bc1': bc1,
        'bc2': bc2
    }

def test_old_behavior(user, business1, business2):
    """Test the old behavior (without business_id parameter)."""
    print("\n🔍 Testing OLD behavior (without business_id parameter):")
    
    factory = RequestFactory()
    request = factory.get('/business-customers/me/forms/')
    request.user = user
    
    view = CustomerFormCompletionView()
    view.request = request
    
    # Simulate old behavior by not passing business_id
    # This would use .first() and potentially get the wrong business
    response = view.get(request)
    
    print(f"   - Status Code: {response.status_code}")
    if response.status_code == 200:
        data = response.data
        print(f"   - Business: {data.get('business_name')}")
        print(f"   - All forms completed: {data.get('all_forms_completed')}")
        print(f"   - Missing forms: {len(data.get('missing_forms', []))}")
        
        if data.get('all_forms_completed'):
            print("   ⚠️  ISSUE: Customer marked as completed even though they need forms for business 2!")
    
    return response

def test_new_behavior(user, business1, business2):
    """Test the new behavior (with business_id parameter)."""
    print("\n✅ Testing NEW behavior (with business_id parameter):")
    
    factory = RequestFactory()
    
    # Test with business 1 (no required forms)
    request1 = factory.get(f'/business-customers/me/forms/?business_id={business1.id}')
    request1.user = user
    
    view1 = CustomerFormCompletionView()
    view1.request = request1
    response1 = view1.get(request1)
    
    print(f"   Business 1 (ID: {business1.id}):")
    print(f"   - Status Code: {response1.status_code}")
    if response1.status_code == 200:
        data1 = response1.data
        print(f"   - Business: {data1.get('business_name')}")
        print(f"   - All forms completed: {data1.get('all_forms_completed')}")
        print(f"   - Missing forms: {len(data1.get('missing_forms', []))}")
    
    # Test with business 2 (has required forms)
    request2 = factory.get(f'/business-customers/me/forms/?business_id={business2.id}')
    request2.user = user
    
    view2 = CustomerFormCompletionView()
    view2.request = request2
    response2 = view2.get(request2)
    
    print(f"\n   Business 2 (ID: {business2.id}):")
    print(f"   - Status Code: {response2.status_code}")
    if response2.status_code == 200:
        data2 = response2.data
        print(f"   - Business: {data2.get('business_name')}")
        print(f"   - All forms completed: {data2.get('all_forms_completed')}")
        print(f"   - Missing forms: {len(data2.get('missing_forms', []))}")
        
        if not data2.get('all_forms_completed'):
            print("   ✅ CORRECT: Customer correctly marked as needing forms for business 2!")
    
    return response1, response2

def main():
    """Main test function."""
    print("🧪 Testing Consent Form Fix")
    print("=" * 50)
    
    try:
        # Create test data
        test_data = create_test_data()
        
        # Test old behavior
        test_old_behavior(test_data['user'], test_data['business1'], test_data['business2'])
        
        # Test new behavior
        test_new_behavior(test_data['user'], test_data['business1'], test_data['business2'])
        
        print("\n🎉 Test completed successfully!")
        print("\n📋 Summary:")
        print("   - The fix ensures that consent status is checked for the specific business")
        print("   - Customers will see consent forms only for businesses that require them")
        print("   - No more false positives where customers skip required consent forms")
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()
