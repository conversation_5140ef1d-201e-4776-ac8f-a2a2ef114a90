import React, { useEffect, useState } from 'react'
import { useEmployees } from '../../../employees/hooks'
import { workingHoursService } from '../../services/workingHoursService'
import { appointmentService } from '../../services/appointmentService'

/**
 * Demo component showing how to use the new Django API integration
 * This replaces mock data with real API calls
 */
const EmployeeCalendarConnector = ({ children, onDataLoaded }) => {
  const { employees, currentEmployee, loading, error } = useEmployees()
  const [workingHours, setWorkingHours] = useState({})
  const [appointments, setAppointments] = useState([])
  const [apiStatus, setApiStatus] = useState({
    employees: 'idle',
    workingHours: 'idle',
    appointments: 'idle'
  })

  // Load working hours when employees are loaded
  useEffect(() => {
    if (employees.length > 0) {
      loadWorkingHours()
    }
  }, [employees])

  // Load appointments when employees are loaded
  useEffect(() => {
    if (employees.length > 0) {
      loadAppointments()
    }
  }, [employees])

  const loadWorkingHours = async () => {
    setApiStatus(prev => ({ ...prev, workingHours: 'loading' }))
    try {
      const hours = await workingHoursService.fetchAllWorkingHours()
      setWorkingHours(hours)
      setApiStatus(prev => ({ ...prev, workingHours: 'success' }))
      console.log('✅ Working hours loaded from Django API:', hours)
    } catch (error) {
      console.warn('❌ Working hours API failed, using fallback:', error)
      setApiStatus(prev => ({ ...prev, workingHours: 'error' }))
    }
  }

  const loadAppointments = async () => {
    setApiStatus(prev => ({ ...prev, appointments: 'loading' }))
    try {
      const appointmentData = await appointmentService.fetchAppointments({
        start: new Date().toISOString().split('T')[0],
        end: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0]
      })
      setAppointments(appointmentData)
      setApiStatus(prev => ({ ...prev, appointments: 'success' }))
      console.log('✅ Appointments loaded from Django API:', appointmentData.length, 'items')
    } catch (error) {
      console.warn('❌ Appointments API failed, using fallback:', error)
      setApiStatus(prev => ({ ...prev, appointments: 'error' }))
    }
  }

  // Notify parent when data is loaded
  useEffect(() => {
    if (onDataLoaded && employees.length > 0) {
      onDataLoaded({
        employees,
        currentEmployee,
        workingHours,
        appointments,
        apiStatus
      })
    }
  }, [employees, currentEmployee, workingHours, appointments, apiStatus, onDataLoaded])

  // Render loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="text-center">
          <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
          <p className="text-sm text-gray-600">Connecting to Django API...</p>
        </div>
      </div>
    )
  }

  // Render error state
  if (error) {
    return (
      <div className="bg-yellow-50 border border-yellow-200 rounded-md p-4 m-4">
        <div className="flex">
          <div className="flex-shrink-0">
            <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
            </svg>
          </div>
          <div className="ml-3">
            <h3 className="text-sm font-medium text-yellow-800">API Connection Issue</h3>
            <p className="mt-1 text-sm text-yellow-700">
              {error} - Using fallback data for now.
            </p>
          </div>
        </div>
      </div>
    )
  }

  // Render success state with API status
  return (
    <div>
      {/* API Status Indicator */}
      <div className="bg-green-50 border border-green-200 rounded-md p-3 m-4">
        <div className="flex items-center">
          <svg className="h-5 w-5 text-green-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
          </svg>
          <div className="text-sm">
            <span className="font-medium text-green-800">Django API Connected</span>
            <div className="text-green-700 mt-1">
              Employees: {employees.length} loaded | 
              Working Hours: {apiStatus.workingHours} | 
              Appointments: {apiStatus.appointments}
              {currentEmployee && ` | Current: ${currentEmployee.name || currentEmployee.email}`}
            </div>
          </div>
        </div>
      </div>

      {/* Render children with enhanced data */}
      {children}
    </div>
  )
}

export default EmployeeCalendarConnector 