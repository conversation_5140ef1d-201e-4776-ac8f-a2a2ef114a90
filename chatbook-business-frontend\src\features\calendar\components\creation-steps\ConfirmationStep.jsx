import React, { useState } from 'react'
import { 
  UserIcon,
  CalendarDaysIcon,
  ClockIcon,
  CurrencyDollarIcon,
  PencilIcon,
  BellIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline'

/**
 * ConfirmationStep - Final review and confirmation step
 * Features: Complete appointment summary, notes, deposit settings, customer notifications
 */
const ConfirmationStep = ({
  formData,
  onUpdateNotes,
  onUpdateDeposit,
  onUpdateNotification,
  validationErrors = {},
  isCreating = false
}) => {
  const [showNotesEditor, setShowNotesEditor] = useState(false)
  const [tempNotes, setTempNotes] = useState(formData.notes || '')

  // Handle notes save
  const handleNotesSave = () => {
    onUpdateNotes(tempNotes)
    setShowNotesEditor(false)
  }

  // Handle notes cancel
  const handleNotesCancel = () => {
    setTempNotes(formData.notes || '')
    setShowNotesEditor(false)
  }

  // Format date for display
  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Format time for display
  const formatTime = (date) => {
    return date.toLocaleTimeString('en-US', {
      hour: 'numeric',
      minute: '2-digit',
      hour12: true
    })
  }

  // Format duration
  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins > 0 ? `${mins}m` : ''}`
    }
    return `${mins}m`
  }

  // Calculate end time and appointment details
  const calculateAppointmentDetails = () => {
    if (!formData.selectedDate || !formData.totalDuration) return null
    
    const endTime = new Date(formData.selectedDate)
    endTime.setMinutes(endTime.getMinutes() + formData.totalDuration)
    
    const depositAmount = (formData.totalPrice * formData.depositPercentage) / 100
    const remainingBalance = formData.totalPrice - depositAmount
    
    return {
      endTime,
      depositAmount,
      remainingBalance,
      isValidAppointment: formData.selectedCustomer && formData.selectedService && 
                         formData.selectedDate && formData.selectedEmployee
    }
  }

  const appointmentDetails = calculateAppointmentDetails()
  
  // Enhanced validation checking
  const getValidationStatus = () => {
    const errors = []
    const warnings = []
    
    if (!formData.selectedCustomer) errors.push('Customer is required')
    if (!formData.selectedService) errors.push('Service selection is required')
    if (!formData.selectedDate) errors.push('Date and time is required')
    if (!formData.selectedEmployee) errors.push('Employee selection is required')
    
    // Check for potential issues
    if (formData.selectedDate && new Date(formData.selectedDate) < new Date()) {
      warnings.push('Appointment is scheduled in the past')
    }
    
    if (formData.totalDuration > 480) { // 8 hours
      warnings.push('Appointment duration is unusually long')
    }
    
    if (formData.depositPercentage === 0 && formData.totalPrice > 100) {
      warnings.push('Consider requiring a deposit for high-value services')
    }
    
    return { errors, warnings, isValid: errors.length === 0 }
  }
  
  const validationStatus = getValidationStatus()

  return (
    <div className="p-6 space-y-6">
      {/* Step Header */}
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Review & Confirm
        </h3>
        <p className="text-gray-600">
          Please review all details before creating the appointment
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Details */}
        <div className="lg:col-span-2 space-y-6">
          {/* Customer Information */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center space-x-2">
              <UserIcon className="h-5 w-5" />
              <span>Customer</span>
            </h4>
            {formData.selectedCustomer ? (
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white font-medium">
                    {formData.selectedCustomer.name
                      .split(' ')
                      .map(n => n[0])
                      .join('')
                      .toUpperCase()
                      .slice(0, 2)}
                  </span>
                </div>
                <div>
                  <h5 className="font-medium text-gray-900">{formData.selectedCustomer.name}</h5>
                  <p className="text-sm text-gray-600">{formData.selectedCustomer.email}</p>
                  <p className="text-sm text-gray-600">{formData.selectedCustomer.phone}</p>
                </div>
              </div>
            ) : (
              <p className="text-red-600">No customer selected</p>
            )}
          </div>

          {/* Service Information */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3">Service Details</h4>
            {formData.selectedService ? (
              <div className="space-y-3">
                <div className="flex justify-between items-start">
                  <div>
                    <h5 className="font-medium text-gray-900">{formData.selectedService.name}</h5>
                    <p className="text-sm text-gray-600">{formData.selectedService.description}</p>
                  </div>
                  <div className="text-right">
                    <div className="flex items-center space-x-1 text-green-600 font-medium">
                      <CurrencyDollarIcon className="h-4 w-4" />
                      <span>${formData.selectedService.price}</span>
                    </div>
                    <div className="flex items-center space-x-1 text-gray-500 text-sm">
                      <ClockIcon className="h-3 w-3" />
                      <span>{formatDuration(formData.selectedService.duration + (formData.selectedService.bufferTime || 0))}</span>
                    </div>
                  </div>
                </div>

                {/* Add-ons */}
                {formData.selectedAddOns && formData.selectedAddOns.length > 0 && (
                  <div className="border-t border-gray-100 pt-3">
                    <h6 className="text-sm font-medium text-gray-700 mb-2">Add-ons</h6>
                    {formData.selectedAddOns.map(addOn => (
                      <div key={addOn.id} className="flex justify-between items-center text-sm">
                        <span className="text-gray-600">+ {addOn.name}</span>
                        <div className="flex items-center space-x-3">
                          <span className="flex items-center space-x-1 text-green-600">
                            <CurrencyDollarIcon className="h-3 w-3" />
                            <span>${addOn.price}</span>
                          </span>
                          <span className="flex items-center space-x-1 text-gray-500">
                            <ClockIcon className="h-3 w-3" />
                            <span>{formatDuration(addOn.duration)}</span>
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                )}

                {/* Total */}
                <div className="border-t border-gray-200 pt-3">
                  <div className="flex justify-between items-center font-medium">
                    <span className="text-gray-900">Total</span>
                    <div className="flex items-center space-x-4">
                      <span className="flex items-center space-x-1 text-green-600 font-semibold">
                        <CurrencyDollarIcon className="h-4 w-4" />
                        <span>${formData.totalPrice}</span>
                      </span>
                      <span className="flex items-center space-x-1 text-gray-700">
                        <ClockIcon className="h-4 w-4" />
                        <span>{formatDuration(formData.totalDuration)}</span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            ) : (
              <p className="text-red-600">No service selected</p>
            )}
          </div>

          {/* Schedule Information */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3 flex items-center space-x-2">
              <CalendarDaysIcon className="h-5 w-5" />
              <span>Schedule</span>
            </h4>
            {formData.selectedDate && formData.selectedEmployee ? (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-gray-700">Date</label>
                  <p className="text-gray-900">{formatDate(formData.selectedDate)}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Time</label>
                  <p className="text-gray-900">
                    {formatTime(formData.selectedDate)}
                    {appointmentDetails?.endTime && (
                      <span className="text-gray-600 ml-2">
                        - {formatTime(appointmentDetails.endTime)}
                      </span>
                    )}
                  </p>
                  <p className="text-xs text-gray-500">
                    Duration: {formatDuration(formData.totalDuration)}
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Employee</label>
                  <p className="text-gray-900 flex items-center space-x-2">
                    <UserIcon className="h-4 w-4" />
                    <span>{formData.selectedEmployee.name}</span>
                  </p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Duration</label>
                  <p className="text-gray-900">{formatDuration(formData.totalDuration)}</p>
                </div>
              </div>
            ) : (
              <p className="text-red-600">Schedule information incomplete</p>
            )}
          </div>

          {/* Notes Section */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <div className="flex items-center justify-between mb-3">
              <h4 className="font-medium text-gray-900">Appointment Notes</h4>
              <button
                onClick={() => setShowNotesEditor(true)}
                className="text-blue-600 hover:text-blue-700 text-sm flex items-center space-x-1"
              >
                <PencilIcon className="h-4 w-4" />
                <span>{formData.notes ? 'Edit' : 'Add'} Notes</span>
              </button>
            </div>

            {showNotesEditor ? (
              <div className="space-y-3">
                <textarea
                  value={tempNotes}
                  onChange={(e) => setTempNotes(e.target.value.slice(0, 500))}
                  placeholder="Add any special instructions or notes for this appointment..."
                  className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                  rows={4}
                />
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-500">
                    {tempNotes.length}/500 characters
                  </span>
                  <div className="flex space-x-2">
                    <button
                      onClick={handleNotesCancel}
                      className="px-3 py-1.5 text-gray-600 bg-gray-100 rounded hover:bg-gray-200 transition-colors"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleNotesSave}
                      className="px-3 py-1.5 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors"
                    >
                      Save
                    </button>
                  </div>
                </div>
              </div>
            ) : (
              <div className="text-gray-600">
                {formData.notes ? (
                  <p className="whitespace-pre-wrap">{formData.notes}</p>
                ) : (
                  <p className="italic">No notes added</p>
                )}
              </div>
            )}
          </div>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Payment Settings */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-4">Payment Settings</h4>
            
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Deposit Required
                </label>
                <div className="space-y-2">
                  <input
                    type="range"
                    min="0"
                    max="100"
                    step="5"
                    value={formData.depositPercentage}
                    onChange={(e) => onUpdateDeposit(parseInt(e.target.value))}
                    className="w-full"
                  />
                  <div className="space-y-2">
                    <div className="flex justify-between items-center text-sm">
                      <span className="text-gray-600">Deposit ({formData.depositPercentage}%)</span>
                      <span className="font-medium text-green-600">
                        ${appointmentDetails?.depositAmount.toFixed(2) || '0.00'}
                      </span>
                    </div>
                    {appointmentDetails?.remainingBalance > 0 && (
                      <div className="flex justify-between items-center text-sm text-gray-500">
                        <span>Remaining balance</span>
                        <span>${appointmentDetails.remainingBalance.toFixed(2)}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Notification Settings */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-4">Notifications</h4>
            
            <div className="space-y-3">
              <label className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  checked={formData.notifyCustomer}
                  onChange={(e) => onUpdateNotification(e.target.checked)}
                  className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                />
                <div className="flex items-center space-x-2">
                  <BellIcon className="h-4 w-4 text-gray-400" />
                  <span className="text-sm text-gray-700">Send confirmation to customer</span>
                </div>
              </label>
              
              {formData.notifyCustomer && (
                <div className="ml-7 text-xs text-gray-500">
                  Customer will receive appointment confirmation via email and SMS
                </div>
              )}
            </div>
          </div>

          {/* Enhanced Validation Summary */}
          {validationStatus.errors.length > 0 && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <ExclamationTriangleIcon className="h-5 w-5 text-red-600" />
                <h4 className="font-medium text-red-800">Required fields missing:</h4>
              </div>
              <ul className="text-sm text-red-700 space-y-1">
                {validationStatus.errors.map((error, index) => (
                  <li key={index}>• {error}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Warnings */}
          {validationStatus.warnings.length > 0 && validationStatus.isValid && (
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
              <div className="flex items-center space-x-2 mb-2">
                <ExclamationTriangleIcon className="h-5 w-5 text-yellow-600" />
                <h4 className="font-medium text-yellow-800">Please review:</h4>
              </div>
              <ul className="text-sm text-yellow-700 space-y-1">
                {validationStatus.warnings.map((warning, index) => (
                  <li key={index}>• {warning}</li>
                ))}
              </ul>
            </div>
          )}

          {/* Success Indicator */}
          {validationStatus.isValid && validationStatus.warnings.length === 0 && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4">
              <div className="flex items-center space-x-2">
                <CheckCircleIcon className="h-5 w-5 text-green-600" />
                <span className="font-medium text-green-800">Ready to create appointment</span>
              </div>
              <p className="text-sm text-green-700 mt-1">
                All information looks good. You can proceed to create the appointment.
              </p>
            </div>
          )}

          {/* Quick Stats */}
          {appointmentDetails?.isValidAppointment && (
            <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Appointment Summary</h4>
              <div className="grid grid-cols-2 gap-3 text-sm">
                <div className="flex justify-between">
                  <span className="text-blue-700">Total Revenue:</span>
                  <span className="font-medium text-blue-900">${formData.totalPrice}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-blue-700">Duration:</span>
                  <span className="font-medium text-blue-900">{formatDuration(formData.totalDuration)}</span>
                </div>
                {appointmentDetails.depositAmount > 0 && (
                  <div className="flex justify-between">
                    <span className="text-blue-700">Deposit Due:</span>
                    <span className="font-medium text-blue-900">${appointmentDetails.depositAmount.toFixed(2)}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-blue-700">Status:</span>
                  <span className="font-medium text-blue-900">
                    {isCreating ? 'Creating...' : 'Pending'}
                  </span>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default ConfirmationStep 