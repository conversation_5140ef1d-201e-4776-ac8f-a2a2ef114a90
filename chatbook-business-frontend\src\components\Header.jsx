import { Link, NavLink, useNavigate } from 'react-router-dom'
import { useState, useRef, useEffect } from 'react'

function Header() {
  const [activeDropdown, setActiveDropdown] = useState(null)
  const timeoutRef = useRef(null)
  const dropdownRefs = useRef({})
  const navigate = useNavigate()

  const mainNavItems = [
    { label: 'Calendar', to: '/calendar' },
    { label: 'Checkout', to: '/checkout', hasSubmenu: true },
    { label: 'Customers', to: '/customers/management', hasSubmenu: true },
    { label: 'Marketing', to: '/marketing' },
    { label: 'Forms', to: '/forms' },
    { label: 'Reports', to: '/reports', hasSubmenu: true },
    { label: 'Settings', to: '/settings', hasSubmenu: true },
  ]

  const subMenus = {
    'Checkout': [
      { label: 'Checkout', to: '/checkout' },
      { label: 'Checkout Settings', to: '/checkout/settings' },
      { label: 'Refund Customer', to: '/checkout/refunds' },
      { label: 'Invoices', to: '/checkout/invoices' },
    ],
    'Customers': [
      { label: 'Customer Management', to: '/customers/management' },
      { label: 'Customer List', to: '/customers/list' },
      { label: 'Import Management', to: '/customers/import' },
    ],
    'Reports': [
      { label: 'All reports', to: '/reports/all' },
      { label: 'Transaction List', to: '/reports/transactions' },
      { label: 'Dashboard', to: '/dashboard' },
    ],
    'Settings': [
      { label: 'All settings', to: '/settings' },
      { label: 'Employee Profile', to: '/settings/employee-profile' },
      { label: 'Service Menu', to: '/settings/service-menu' },
      { label: 'Calendar Configuration', to: '/settings/calendar-config' },
      { label: 'Online Appointment Rules', to: '/settings/appointment-rules' },
    ],
  }

  const handleMouseEnter = (label) => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    
    if (mainNavItems.find(item => item.label === label)?.hasSubmenu) {
      setActiveDropdown(label)
    }
  }

  const handleMouseLeave = () => {
    timeoutRef.current = setTimeout(() => {
      setActiveDropdown(null)
    }, 150) // Small delay to allow moving to submenu
  }

  // If mouse enters the dropdown menu, clear the timeout
  const handleDropdownMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
  }

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (activeDropdown) {
        const isClickInside = Object.values(dropdownRefs.current).some(
          ref => ref && ref.contains(event.target)
        )
        
        if (!isClickInside) {
          setActiveDropdown(null)
        }
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [activeDropdown])

  const handleLogout = () => {
    localStorage.removeItem('auth_token')
    localStorage.removeItem('refresh_token')
    localStorage.removeItem('user_data')  // Clear user data for security
    navigate('/auth/login')
  }

  return (
    <header className="sticky top-0 z-30 bg-white border-b border-gray-200 shadow-sm">
      {/* Main Header Bar */}
      <div className="container flex items-center justify-between h-16 px-4 md:px-6 lg:px-8">
        {/* Brand */}
        <Link to="/" className="text-xl font-bold text-primary-700">Chatbook</Link>

        {/* Navigation */}
        <nav className="hidden md:flex space-x-6">
          {mainNavItems.map(item => (
            <div 
              key={item.to} 
              className="relative"
              ref={ref => dropdownRefs.current[item.label] = ref}
              onMouseEnter={() => handleMouseEnter(item.label)}
              onMouseLeave={handleMouseLeave}
            >
              <NavLink
                to={item.to}
                className={({ isActive }) =>
                  `text-sm font-medium transition-colors pb-4 ${isActive ? 'text-primary-700' : 'text-gray-600 hover:text-primary-600'}`
                }
              >
                {item.label}
              </NavLink>
              
              {/* Dropdown Menu */}
              {item.hasSubmenu && activeDropdown === item.label && (
                <div 
                  className="absolute left-0 top-6 w-56 bg-white rounded-md shadow-lg border border-gray-200 z-40"
                  onMouseEnter={handleDropdownMouseEnter}
                  onMouseLeave={handleMouseLeave}
                >
                  <div className="py-1">
                    {subMenus[item.label].map((subItem) => (
                      <Link
                        key={subItem.to}
                        to={subItem.to}
                        className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                        onClick={() => setActiveDropdown(null)}
                      >
                        {subItem.label}
                      </Link>
                    ))}
                  </div>
                </div>
              )}
            </div>
          ))}
        </nav>

        {/* Right-side actions */}
        <div className="flex items-center space-x-4">
          {/* Theme toggle placeholder */}
          <button className="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center" title="Toggle theme">
            <span className="sr-only">Toggle theme</span>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-5 h-5 text-gray-600">
              <path d="M12 3.75A8.25 8.25 0 1 0 20.25 12 8.26 8.26 0 0 0 12 3.75Zm0 14.25a6 6 0 1 1 6-6 6 6 0 0 1-6 6Z" />
            </svg>
          </button>

          {/* Logout button */}
          <button
            onClick={handleLogout}
            className="flex items-center text-sm text-gray-600 hover:text-red-600"
            title="Log out"
          >
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5 mr-1">
              <path strokeLinecap="round" strokeLinejoin="round" d="M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6A2.25 2.25 0 0 0 5.25 5.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15M18 12H8.25m0 0 3-3m-3 3 3 3" />
            </svg>
            Logout
          </button>

          {/* User avatar placeholder */}
          <div className="w-8 h-8 rounded-full bg-gray-300 overflow-hidden">
            {/* could use <img src={user.avatar} alt="" /> */}
          </div>
        </div>
      </div>

      {/* Additional header sections can go here in the future */}
    </header>
  )
}

export default Header 