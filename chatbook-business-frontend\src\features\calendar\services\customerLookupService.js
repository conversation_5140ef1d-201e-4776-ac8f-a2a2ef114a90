import { customerApi } from '../../customers/services/customerApi'

/**
 * Customer Lookup Service
 * Provides efficient customer name lookups for appointment blocks
 */
class CustomerLookupService {
  constructor() {
    this.customerCache = new Map()
    this.employeeCache = new Map()
    this.customerLoadingPromise = null
    this.employeeLoadingPromise = null
  }

  /**
   * Get customer name by ID
   * @param {number|string} customerId - Customer ID
   * @returns {Promise<string>} Customer name
   */
  async getCustomerName(customerId) {
    if (!customerId) return 'Customer'
    
    // Normalize customer ID to string for consistent lookup
    const normalizedId = customerId.toString()
    
    console.log('🔍 CustomerLookupService: Attempting to get customer name for ID:', normalizedId)
    
    // Check cache first
    if (this.customerCache.has(normalizedId)) {
      const cachedName = this.customerCache.get(normalizedId)
      console.log('✅ CustomerLookupService: Found cached name:', cachedName)
      return cachedName
    }
    
    // Also check if we have it stored as a number
    if (this.customerCache.has(customerId)) {
      const cachedName = this.customerCache.get(customerId)
      console.log('✅ CustomerLookupService: Found cached name (original type):', cachedName)
      return cachedName
    }
    
    try {
      // Load all customers and wait for completion
      await this.loadAllCustomers()
      
      // Try both string and original ID
      const customerName = this.customerCache.get(normalizedId) || 
                          this.customerCache.get(customerId) || 
                          `Customer ${customerId}`
      console.log('✅ CustomerLookupService: Resolved customer name:', customerName)
      return customerName
    } catch (error) {
      console.warn('⚠️ CustomerLookupService: Failed to load customers, using fallback name:', error)
      // Return a fallback name if lookup fails
      return `Customer ${customerId}`
    }
  }

  /**
   * Load all customers into cache
   */
  async loadAllCustomers() {
    // If already loading, return the existing promise
    if (this.customerLoadingPromise) {
      return this.customerLoadingPromise
    }
    
    // Create and store the loading promise
    this.customerLoadingPromise = this._doLoadCustomers()
    
    try {
      await this.customerLoadingPromise
    } finally {
      // Clear the promise when done (success or failure)
      this.customerLoadingPromise = null
    }
  }

  /**
   * Internal method to actually load customers
   */
  async _doLoadCustomers() {
    try {
      console.log('📋 CustomerLookupService: Loading customers for appointment display...')
      const customers = await customerApi.getCustomers()
      console.log('📋 CustomerLookupService: Raw customer API response:', customers)
      
      // Handle different response formats
      let customerList = []
      if (customers.results) {
        customerList = customers.results
        console.log('📋 CustomerLookupService: Using paginated results format')
      } else if (Array.isArray(customers)) {
        customerList = customers
        console.log('📋 CustomerLookupService: Using direct array format')
      } else {
        console.warn('⚠️ CustomerLookupService: Unexpected customer API response format:', typeof customers)
        return // Exit early if format is unexpected
      }
      
      console.log(`📋 CustomerLookupService: Processing ${customerList.length} customers`)
      
      // Cache customer names by ID
      customerList.forEach(customer => {
        let name = customer.name
        if (!name) {
          const firstName = customer.first_name || customer.customer?.user?.first_name || ''
          const lastName = customer.last_name || customer.customer?.user?.last_name || ''
          name = `${firstName} ${lastName}`.trim()
        }
        
        // Ensure we have a reasonable fallback
        const finalName = name || `Customer ${customer.id}`
        
        // Cache both the original ID and string version for consistent lookup
        this.customerCache.set(customer.id, finalName)
        this.customerCache.set(customer.id.toString(), finalName)
        
        console.log(`📋 CustomerLookupService: Cached customer ${customer.id} (both types): "${finalName}"`)
      })
      
      console.log(`✅ CustomerLookupService: Successfully cached ${customerList.length} customer names`)
      console.log('🗂️ CustomerLookupService: Cache contents:', Array.from(this.customerCache.entries()))
      
    } catch (error) {
      console.error('❌ CustomerLookupService: Failed to load customers for appointment display:', error)
      console.error('📍 CustomerLookupService: Error details:', {
        message: error.message,
        name: error.name,
        stack: error.stack
      })
      throw error // Re-throw to let caller handle
    }
  }

  /**
   * Get employee name by ID
   * @param {number} employeeId - Employee ID
   * @returns {Promise<string>} Employee name
   */
  async getEmployeeName(employeeId) {
    if (!employeeId) return 'Employee'
    
    // Check cache first
    if (this.employeeCache.has(employeeId)) {
      return this.employeeCache.get(employeeId)
    }
    
    // Load all employees and wait for completion
    await this.loadAllEmployees()
    
    // Return actual employee name or fallback
    return this.employeeCache.get(employeeId) || `Employee ${employeeId}`
  }

  /**
   * Load all employees into cache
   */
  async loadAllEmployees() {
    // If already loading, return the existing promise
    if (this.employeeLoadingPromise) {
      return this.employeeLoadingPromise
    }
    
    // Create and store the loading promise
    this.employeeLoadingPromise = this._doLoadEmployees()
    
    try {
      await this.employeeLoadingPromise
    } finally {
      // Clear the promise when done (success or failure)
      this.employeeLoadingPromise = null
    }
  }

  /**
   * Internal method to actually load employees
   */
  async _doLoadEmployees() {
    try {
      console.log('👥 Loading employees for appointment display...')
      
      // Import employeeApiService dynamically to avoid circular imports
      const { employeeApiService } = await import('../../employees/services')
      const employees = await employeeApiService.getAllEmployees()
      
      console.log('👥 CustomerLookupService: Raw employee API response:', employees)
      
      // Cache employee names by ID
      employees.forEach(employee => {
        const name = employee.full_name || employee.name || `Employee ${employee.id}`
        
        // Cache both the original ID and string version for consistent lookup
        this.employeeCache.set(employee.id, name)
        this.employeeCache.set(employee.id.toString(), name)
        
        console.log(`👥 CustomerLookupService: Cached employee ${employee.id} (both types): "${name}"`)
      })
      
      console.log(`✅ CustomerLookupService: Successfully cached ${employees.length} employee names`)
      
    } catch (error) {
      console.error('❌ Failed to load employees for appointment display:', error)
      throw error // Re-throw to let caller handle
    }
  }

  /**
   * Update customer name in cache
   * @param {number} customerId - Customer ID
   * @param {string} name - Customer name
   */
  updateCustomerName(customerId, name) {
    this.customerCache.set(customerId, name)
  }

  /**
   * Update employee name in cache
   * @param {number} employeeId - Employee ID
   * @param {string} name - Employee name
   */
  updateEmployeeName(employeeId, name) {
    this.employeeCache.set(employeeId, name)
  }

  /**
   * Clear all caches
   */
  clearCache() {
    this.customerCache.clear()
    this.employeeCache.clear()
  }
}

// Export singleton instance
export const customerLookupService = new CustomerLookupService() 