import './Services.css';

const ServiceItem = ({ service, onBook }) => {
    const formatPrice = (price) => {
        return typeof price === 'number' 
            ? price.toFixed(2) 
            : parseFloat(price || 0).toFixed(2);
    };

    return (
        <div className="service-item">
            <div className="service-item-header">
                <h4 className="service-name">{service.name}</h4>
                <div className="service-price">
                    ${formatPrice(service.price)}
                </div>
            </div>
            <p className="service-description">{service.description}</p>
            
            {service.note && (
                <p className="service-note">Note: {service.note}</p>
            )}
            
            <div className="service-meta">
                {service.duration && (
                    <p className="duration">Duration: {service.duration} mins</p>
                )}
                <button 
                    className="book-service-btn" 
                    onClick={() => onBook(service)}
                >
                    Book Now
                </button>
            </div>
        </div>
    );
};

export default ServiceItem;
