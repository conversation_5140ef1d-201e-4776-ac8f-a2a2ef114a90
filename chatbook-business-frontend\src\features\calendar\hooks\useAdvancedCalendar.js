import { useState, useEffect, useCallback, useMemo, useRef } from 'react'
import { appointmentService } from '../services/appointmentService'
import { workingHoursManager } from '../services/workingHoursService'
import { workingHoursService } from '../services/workingHoursService'
import { useCalendarConfig } from './useCalendarConfig'
import { servicesApiService } from '../services/servicesApiService'
import { useEmployees } from '../../employees/hooks'
import { generateWeekDays } from '../utils/weekUtils'

// Display modes matching iOS implementation
export const DISPLAY_MODES = {
  DAY: 'day',
  WEEK: 'week'
}

// Navigation directions
export const NAVIGATION_DIRECTION = {
  FORWARD: 'forward',
  BACKWARD: 'backward'
}

export const useAdvancedCalendar = () => {
  // Calendar configuration
  const { 
    config, 
    updateConfig,
    setTimeResolution, 
    setDisplayHours, 
    setWeekStartDay,
    getTimeSlots,
    getDisplayHours 
  } = useCalendarConfig()

  // Employee data from Django API
  const { 
    employees: apiEmployees, 
    currentEmployee, 
    loading: employeesLoading, 
    error: employeesError 
  } = useEmployees()

  // Core state
  const [selectedDate, setSelectedDate] = useState(new Date())
  const [displayMode, setDisplayMode] = useState(config.defaultView || DISPLAY_MODES.WEEK)
  
  // Update displayMode when config.defaultView changes (only from settings, not manual switching)
  useEffect(() => {
    if (config.defaultView && config.defaultView !== displayMode) {
      setDisplayMode(config.defaultView)
    }
  }, [config.defaultView])
  const [appointments, setAppointments] = useState([])
  const [workingHours, setWorkingHours] = useState({})
  const [workingHoursVersion, setWorkingHoursVersion] = useState(0) // Track working hours updates
  const [isLoading, setIsLoading] = useState(false)
  const [isRefreshing, setIsRefreshing] = useState(false)
  const [error, setError] = useState(null)
  
  // Transform API employees to match calendar format
  const employees = useMemo(() => {
    if (!apiEmployees || apiEmployees.length === 0) {
      // Fallback to mock employees if API fails
      return [
        { id: 'emp1', name: 'Sarah Johnson', color: '#3b82f6', avatar: 'SJ' },
        { id: 'emp2', name: 'Mike Chen', color: '#10b981', avatar: 'MC' },
        { id: 'emp3', name: 'Lisa Rodriguez', color: '#f59e0b', avatar: 'LR' },
        { id: 'emp4', name: 'David Kim', color: '#ef4444', avatar: 'DK' },
      ]
    }

    // Debug: Log the raw API data to see the actual structure
    console.log('Raw Django API employees data:', apiEmployees)

    // Transform Django API employees to calendar format
    const colors = ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6', '#ec4899']
    return apiEmployees.map((emp, index) => {
      // Use the correct field name from your Django API
      const displayName = emp.full_name || emp.name || `Employee ${index + 1}`
      
      // Generate avatar from the display name (first letter of each word)
      const avatar = displayName.split(' ').map(word => word.charAt(0)).join('').toUpperCase().substring(0, 2)

      const transformedEmployee = {
        id: emp.id?.toString() || `emp-${index}`,
        name: displayName,
        color: colors[index % colors.length],
        avatar: avatar || 'U',
        stylist_level: emp.stylist_level,
        stylist_level_display: emp.stylist_level_display,
        is_active: emp.is_active,
        // Keep original data for debugging
        _original: emp
      }

      console.log(`✅ Transformed employee ${index}:`, transformedEmployee)
      return transformedEmployee
    })
  }, [apiEmployees])
  
  // Employee and service selection
  const [selectedEmployeeIDs, setSelectedEmployeeIDs] = useState(new Set())
  const [selectedServices, setSelectedServices] = useState(new Set())
  const [availableServices, setAvailableServices] = useState([])
  const [serviceCategories, setServiceCategories] = useState([])

  // Initialize employee selection when employees are loaded
  useEffect(() => {
    console.log('🔍 Employee selection useEffect running:', {
      employeesLength: employees.length,
      selectedEmployeeIDsSize: selectedEmployeeIDs.size,
      currentEmployee: currentEmployee,
      userDataInStorage: localStorage.getItem('user_data')
    })
    
    // Run auto-selection when employees are loaded (regardless of existing selection)
    if (employees.length > 0) {
      let selectedEmployeeId = null
      let shouldUpdateSelection = false
      
      console.log('🔍 Starting auto-selection process with employees:', employees.map(emp => ({
        id: emp.id,
        name: emp.name,
        full_name: emp.full_name,
        original: emp._original
      })))
      
      // Priority 1: Use the current employee from the useEmployees hook
      if (currentEmployee && currentEmployee.id) {
        selectedEmployeeId = currentEmployee.id.toString()
        shouldUpdateSelection = true
        console.log('🎯 Auto-selecting current employee from hook:', currentEmployee.full_name || currentEmployee.name, 'ID:', selectedEmployeeId)
      } else {
        console.log('❌ No currentEmployee from hook:', currentEmployee)
      }
      
      // Priority 2: Try to match the logged-in user with employee records
      if (!selectedEmployeeId) {
        const userDataString = localStorage.getItem('user_data')
        console.log('🔍 Checking localStorage user_data:', userDataString)
        
        if (userDataString) {
          try {
            const userData = JSON.parse(userDataString)
            const userFullName = `${userData.first_name} ${userData.last_name}`.trim()
            
            console.log('🔍 Looking for employee matching user:', {
              userId: userData.id,
              userEmail: userData.email,
              userFullName: userFullName
            })
            
            // Find employee that matches the logged-in user
            const matchingEmployee = employees.find(emp => {
              const matches = {
                userIdMatch: emp._original?.user?.id === userData.id,
                emailMatch: emp._original?.user_details?.email === userData.email,
                nameMatch: emp.name === userFullName,
                originalEmailMatch: emp._original?.email === userData.email
              }
              
              console.log(`🔍 Checking employee ${emp.name}:`, matches)
              
              return (
                matches.userIdMatch ||
                matches.emailMatch ||
                matches.nameMatch ||
                matches.originalEmailMatch
              )
            })
            
            if (matchingEmployee) {
              selectedEmployeeId = matchingEmployee.id?.toString()
              shouldUpdateSelection = true
              console.log('🎯 Auto-selecting employee matching logged-in user:', matchingEmployee.name || matchingEmployee.full_name, 'ID:', selectedEmployeeId)
            } else {
              console.log('❌ No employee found matching user data')
            }
          } catch (error) {
            console.warn('Failed to parse user data for employee matching:', error)
          }
        } else {
          console.log('❌ No user_data in localStorage')
        }
      }
      
      // Priority 3: Only use fallback if no selection exists and no user match found
      if (!selectedEmployeeId && selectedEmployeeIDs.size === 0) {
        selectedEmployeeId = employees[0]?.id?.toString()
        shouldUpdateSelection = true
        console.log('⚠️ Auto-selecting first employee as fallback:', employees[0]?.name || employees[0]?.full_name, 'ID:', selectedEmployeeId)
      }
      
      // Update selection if we found a user-specific employee or need fallback
      if (selectedEmployeeId && shouldUpdateSelection) {
        const currentSelection = Array.from(selectedEmployeeIDs)[0]
        if (currentSelection !== selectedEmployeeId) {
          console.log('✅ Updating selectedEmployeeIDs from', currentSelection, 'to:', selectedEmployeeId)
          setSelectedEmployeeIDs(new Set([selectedEmployeeId]))
        } else {
          console.log('✅ Already selected correct employee:', selectedEmployeeId)
        }
      } else if (!selectedEmployeeId) {
        console.log('❌ No employee ID to select')
      }
    } else {
      console.log('❌ No employees loaded yet')
    }
  }, [employees, currentEmployee]) // Removed selectedEmployeeIDs.size from deps so it runs when currentEmployee changes
  
  // UI state
  const [scrollPosition, setScrollPosition] = useState(() => {
    return config.rememberScrollPosition 
      ? parseInt(localStorage.getItem('calendar-scroll-position') || '0')
      : 0
  })
  const [viewDidAppear, setViewDidAppear] = useState(false)
  const [isViewReady, setIsViewReady] = useState(false)
  
  // Real-time updates
  const refreshIntervalRef = useRef(null)
  const [lastRefreshTime, setLastRefreshTime] = useState(new Date())
  
  // Performance tracking
  const [performanceMetrics, setPerformanceMetrics] = useState({
    appointmentLoadTime: 0,
    workingHoursLoadTime: 0,
    renderTime: 0
  })

  // Generate display days based on current mode and selected date
  const displayDays = useMemo(() => {
    if (displayMode === DISPLAY_MODES.DAY) {
      return [new Date(selectedDate)]
    }
    
    // Week view - use utility function for consistent week generation
    return generateWeekDays(selectedDate, config.weekStartDay)
  }, [selectedDate, displayMode, config.weekStartDay])

  // Selected employees as array
  const selectedEmployees = useMemo(() => {
    return employees.filter(emp => selectedEmployeeIDs.has(emp.id))
  }, [employees, selectedEmployeeIDs])

  // Filtered appointments based on selected employees and date range
  const filteredAppointments = useMemo(() => {
    const employeeIds = Array.from(selectedEmployeeIDs)
    
    // Set proper date range - start of first day to end of last day
    const startDate = new Date(displayDays[0])
    startDate.setHours(0, 0, 0, 0) // Start of day
    
    const endDate = new Date(displayDays[displayDays.length - 1])
    endDate.setHours(23, 59, 59, 999) // End of day
    
    console.log('🔍 Filtering appointments:', {
      totalAppointments: appointments.length,
      selectedEmployeeIds: employeeIds,
      selectedServicesArray: Array.from(selectedServices),
      dateRange: `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`
    })
    
    // Filter appointments based on current selection criteria
    const filtered = appointments.filter(apt => {
      const aptDate = new Date(apt.start)
      
      // 🔧 IMMEDIATE CANCELLATION FILTER: Hide cancelled appointments immediately
      const normalizedStatus = apt.status?.toLowerCase()
      const isNotCancelled = normalizedStatus !== 'cancelled' && 
                            normalizedStatus !== 'canceled'
      
      // 🔧 DEBUG: Log cancelled appointments to understand the filtering
      if (!isNotCancelled) {
        console.log('🚫 Filtering out cancelled appointment:', {
          id: apt.id,
          title: apt.title,
          originalStatus: apt.status,
          normalizedStatus: normalizedStatus,
          isNotCancelled: isNotCancelled
        })
      }
      
      // 🔧 FIX: Make service filtering optional to prevent appointments from disappearing
      // If selectedServices is empty (service loading failed), show all appointments
      // If selectedServices has items, only show appointments with matching services
      const serviceMatch = selectedServices.size === 0 || selectedServices.has(apt.serviceId)
      
      const employeeMatch = employeeIds.includes(apt.employeeId) || employeeIds.includes(apt.employeeId?.toString())
      const dateMatch = aptDate >= startDate && aptDate <= endDate
      
      // 🔧 DEBUG: Extra logging for appointment moves
      if (apt.id === 3) { // Log specifically for appointment ID 3 (the one being moved)
        console.log(`🎯 APPOINTMENT MOVE DEBUG - Appointment ${apt.id}:`, {
          title: apt.title,
          employeeId: apt.employeeId,
          employeeIdType: typeof apt.employeeId,
          selectedEmployeeIds: employeeIds,
          employeeMatch,
          dateMatch,
          status: apt.status,
          passes: employeeMatch && serviceMatch && dateMatch && isNotCancelled
        })
      }
      
      console.log(`🔍 Appointment ${apt.id} filter check:`, {
        appointmentData: {
          id: apt.id,
          title: apt.title,
          start: apt.start,
          employeeId: apt.employeeId,
          serviceId: apt.serviceId,
          status: apt.status
        },
        checks: {
          isNotCancelled: `Status ${apt.status} is not cancelled = ${isNotCancelled}`,
          serviceMatch: selectedServices.size === 0 ? 
            `No service filter (showing all services) = ${serviceMatch}` :
            `${apt.serviceId} in [${Array.from(selectedServices).join(', ')}] = ${serviceMatch}`,
          employeeMatch: `${apt.employeeId} in [${employeeIds.join(', ')}] = ${employeeMatch}`,
          dateMatch: `${aptDate.toLocaleDateString()} in range = ${dateMatch}`
        },
        passes: employeeMatch && serviceMatch && dateMatch && isNotCancelled
      })
      
      return employeeMatch && serviceMatch && dateMatch && isNotCancelled
    })
    
    // Log summary only when there are changes or in development
    console.log('📅 Calendar filter summary:', {
      total: appointments.length,
      displayed: filtered.length,
      dateRange: `${startDate.toLocaleDateString()} - ${endDate.toLocaleDateString()}`,
      employees: employeeIds.length,
      services: selectedServices.size,
      filteredAppointments: filtered.map(apt => ({
        id: apt.id,
        title: apt.title,
        employeeId: apt.employeeId,
        serviceId: apt.serviceId,
        start: apt.start
      }))
    })
    
    return filtered
  }, [appointments, selectedEmployeeIDs, displayDays, selectedServices, displayMode])

  // Initialize calendar
  useEffect(() => {
    const initializeCalendar = async () => {
      // Wait for employees to load first
      if (employeesLoading) return
      
      setIsLoading(true)
      setError(null)
      
      try {
        // Load appointments, working hours, services, and service categories in parallel
        const [appointmentsData, workingHoursData, servicesData, categoriesData] = await Promise.all([
          appointmentService.fetchAppointments(),
          workingHoursManager.getAllWorkingHours(),
          servicesApiService.fetchServices().catch(error => {
            console.warn('Failed to fetch services from Django API, using empty array:', error)
            return []
          }),
          servicesApiService.fetchServiceCategories().catch(error => {
            console.warn('Failed to fetch service categories from Django API, using empty array:', error)
            return []
          })
        ])
        
        setAppointments(appointmentsData)
        setWorkingHours(workingHoursData)
        setAvailableServices(servicesData)
        setServiceCategories(categoriesData)
        
        // Initialize selectedServices with all available service IDs
        const serviceIds = servicesData && servicesData.length > 0 ? 
          servicesData.map(service => service.id) : []
        
        // 🔧 FIX: Also include service IDs from appointment data to prevent missing services
        const appointmentServiceIds = appointmentsData
          .map(apt => apt.serviceId)
          .filter(serviceId => serviceId != null && serviceId !== undefined)
        
        // Combine service IDs from Django API and appointment data
        const allServiceIds = [...new Set([...serviceIds, ...appointmentServiceIds])]
        
        setSelectedServices(new Set(allServiceIds))
        console.log('✅ Initialized selectedServices with:', {
          fromDjangoAPI: serviceIds.length,
          fromAppointments: appointmentServiceIds.length,
          total: allServiceIds.length,
          serviceIds: allServiceIds
        })
        
        setIsViewReady(true)
        
        // Scroll to current time if configured
        if (config.scrollToCurrentTime) {
          scrollToCurrentTime()
        }
      } catch (error) {
        console.error('Failed to initialize calendar:', error)
        setError(error.message)
      } finally {
        setIsLoading(false)
      }
    }

    initializeCalendar()
  }, [employeesLoading, config.scrollToCurrentTime]) // Run when employees finish loading

  // Real-time updates setup
  useEffect(() => {
    if (config.enableRealTimeUpdates) {
      refreshIntervalRef.current = setInterval(() => {
        refreshAppointments()
      }, 30000) // Refresh every 30 seconds
      
      return () => {
        if (refreshIntervalRef.current) {
          clearInterval(refreshIntervalRef.current)
        }
      }
    }
  }, [config.enableRealTimeUpdates])

  // Working hours subscription
  useEffect(() => {
    console.log('🔔 Setting up working hours subscription...')
    const unsubscribe = workingHoursManager.subscribe((updatedWorkingHours) => {
      console.log('🔄 Working hours updated via subscription:', Object.keys(updatedWorkingHours))
      setWorkingHours(updatedWorkingHours)
      setWorkingHoursVersion(prev => prev + 1) // Force re-render
      console.log('✅ React state updated with new working hours, version:', workingHoursVersion + 1)
    })
    
    return unsubscribe
  }, [])

  // Save scroll position
  useEffect(() => {
    if (config.rememberScrollPosition) {
      localStorage.setItem('calendar-scroll-position', scrollPosition.toString())
    }
  }, [scrollPosition, config.rememberScrollPosition])

  // Navigation methods
  const navigateDate = useCallback((direction) => {
    const newDate = new Date(selectedDate)
    const daysToMove = displayMode === DISPLAY_MODES.DAY ? 1 : 7
    
    if (direction === NAVIGATION_DIRECTION.FORWARD) {
      newDate.setDate(newDate.getDate() + daysToMove)
    } else {
      newDate.setDate(newDate.getDate() - daysToMove)
    }
    
    setSelectedDate(newDate)
    
    if (config.autoScrollOnDateChange) {
      scrollToCurrentTime()
    }
  }, [selectedDate, displayMode, config.autoScrollOnDateChange])

  const goToToday = useCallback(() => {
    setSelectedDate(new Date())
    if (config.scrollToCurrentTime) {
      scrollToCurrentTime()
    }
  }, [config.scrollToCurrentTime])

  const selectDate = useCallback((date) => {
    setSelectedDate(new Date(date))
    if (config.autoScrollOnDateChange) {
      scrollToCurrentTime()
    }
  }, [config.autoScrollOnDateChange])

  // View mode management
  const setViewMode = useCallback((mode) => {
    // Enforce employee selection rules like iOS
    if (mode === DISPLAY_MODES.WEEK && selectedEmployeeIDs.size > 1) {
      // Force single employee selection for week view
      const firstEmployee = Array.from(selectedEmployeeIDs)[0]
      setSelectedEmployeeIDs(new Set([firstEmployee]))
    }
    
    setDisplayMode(mode)
    resetScrollPosition()
  }, [selectedEmployeeIDs])

  // Employee management
  const updateSelectedEmployeeIDs = useCallback((employeeIds) => {
    const newSelection = new Set(Array.isArray(employeeIds) ? employeeIds : [employeeIds])
    
    // Enforce view-specific rules
    if (displayMode === DISPLAY_MODES.WEEK && newSelection.size > 1) {
      // Week view only allows single employee
      const firstEmployee = Array.from(newSelection)[0]
      setSelectedEmployeeIDs(new Set([firstEmployee]))
    } else {
      setSelectedEmployeeIDs(newSelection)
    }
  }, [displayMode])

  // Appointment management
  const refreshAppointments = useCallback(async () => {
    console.log('🔄 Refreshing appointments...')
    setIsRefreshing(true)
    setError(null)
    
    try {
      const startTime = performance.now()
      const appointmentsData = await appointmentService.fetchAppointments()
      const endTime = performance.now()
      
      console.log(`✅ Appointments refreshed: ${appointmentsData.length} appointments loaded`, appointmentsData)
      
      // 🔧 FIX: Update selectedServices to include any new service IDs from appointments
      const appointmentServiceIds = appointmentsData
        .map(apt => apt.serviceId)
        .filter(serviceId => serviceId != null && serviceId !== undefined)
      
      setSelectedServices(prev => {
        const newServiceIds = appointmentServiceIds.filter(id => !prev.has(id))
        if (newServiceIds.length > 0) {
          console.log('✅ Added new service IDs to selectedServices:', newServiceIds)
          return new Set([...prev, ...newServiceIds])
        }
        return prev
      })
      
      // Clear existing appointments first, then set new ones to trigger re-render
      setAppointments([])
      // Use setTimeout to ensure state update is processed
      setTimeout(() => {
        setAppointments(appointmentsData)
        setLastRefreshTime(new Date())
        console.log('🎯 UI state updated with fresh appointment data')
      }, 10)
      
      // Update performance metrics
      setPerformanceMetrics(prev => ({
        ...prev,
        appointmentLoadTime: endTime - startTime
      }))
      
    } catch (error) {
      console.error('❌ Failed to refresh appointments:', error)
      setError(error.message)
    } finally {
      setIsRefreshing(false)
    }
  }, [])

  const createAppointment = useCallback(async (appointmentData) => {
    try {
      const newAppointment = await appointmentService.createAppointment(appointmentData)
      
      // Immediately refresh appointments to get the latest data from API
      // This ensures consistency with backend state
      console.log('🔄 Refreshing appointments after creation...')
      await refreshAppointments()
      
      // 🔥 FIX: Add the appointment's service to selectedServices to prevent filtering
      if (newAppointment.serviceId) {
        setSelectedServices(prev => new Set([...prev, newAppointment.serviceId]))
        console.log('✅ Added service to selectedServices for visibility:', newAppointment.serviceId)
      }
      
      console.log('✅ New appointment created and calendar refreshed:', newAppointment)
      return newAppointment
    } catch (error) {
      console.error('Failed to create appointment:', error)
      setError(error.message)
      throw error
    }
  }, [refreshAppointments])

  const updateAppointment = useCallback(async (appointmentId, updates) => {
    // 🔧 DETECT CANCELLATION: Check if this is a cancellation to trigger hard delete
    const isCancellation = updates.status === 'cancelled' || 
                           updates.status === 'canceled' || 
                           updates.status === 'CANCELLED' || 
                           updates.status === 'CANCELED'
    
    try {
      if (isCancellation) {
        // 🔧 HARD DELETE: When status becomes cancelled, use DELETE method
        console.log('🔧 Appointment cancelled - triggering hard DELETE')
        
        // Remove from UI immediately for better UX
        setAppointments(prev => prev.filter(apt => apt.id !== appointmentId))
        console.log('✅ Immediately removed cancelled appointment from UI')
        
        // Call hard delete API (DELETE method)
        await appointmentService.deleteAppointment(appointmentId)
        console.log('✅ Appointment hard deleted from database')
        
        // Refresh to ensure consistency
        await refreshAppointments()
        return { id: appointmentId, deleted: true }
      } else {
        // 🔧 REGULAR UPDATE: For non-cancellation status changes
        const updatedAppointment = await appointmentService.updateAppointment(appointmentId, updates)
        
        // Refresh appointments to ensure consistency with backend
        console.log('🔄 Refreshing appointments after update...')
        await refreshAppointments()
        
        console.log('✅ Appointment updated and calendar refreshed:', updatedAppointment)
        return updatedAppointment
      }
    } catch (error) {
      console.error('Failed to update appointment:', error)
      
      // 🔧 REVERT ON ERROR: If the operation failed, revert the UI state
      if (isCancellation) {
        // Restore the appointment if hard delete failed
        await refreshAppointments()
        console.log('❌ Reverted appointment removal due to delete error')
      }
      
      setError(error.message)
      throw error
    }
  }, [refreshAppointments])

  const deleteAppointment = useCallback(async (appointmentId) => {
    // 🔧 DIRECT DELETE: Use DELETE method as requested
    console.log('🔧 Directly deleting appointment using DELETE method')
    
    try {
      // Remove from UI immediately for better UX
      const appointmentToDelete = appointments.find(apt => apt.id === appointmentId)
      setAppointments(prev => prev.filter(apt => apt.id !== appointmentId))
      console.log('✅ Immediately removed appointment from UI')
      
      // Call DELETE API directly
      await appointmentService.deleteAppointment(appointmentId)
      console.log('✅ Appointment hard deleted from database')
      
      // Refresh to ensure consistency
      await refreshAppointments()
      return { id: appointmentId, deleted: true }
    } catch (error) {
      console.error('Failed to delete appointment:', error)
      
      // Restore the appointment if delete failed
      await refreshAppointments()
      console.log('❌ Reverted appointment removal due to delete error')
      
      setError(error.message)
      throw error
    }
  }, [appointments, refreshAppointments])

  // Working hours management - FIXED to actually load working hours
  const getWorkingHoursForEmployee = useCallback(async (employeeId, date) => {
    try {
      // First check if we have cached working hours
      let workingHours = workingHoursManager.getVisualWorkingHours(employeeId, date)
      
      if (!workingHours) {
        // Load working hours if not cached
        console.log(`🔄 Loading working hours for employee ${employeeId}...`)
        await workingHoursManager.fetchWorkingHours(employeeId)
        workingHours = workingHoursManager.getVisualWorkingHours(employeeId, date)
      }
      
      // If still no working hours, use defaults
      if (!workingHours) {
        console.log(`⚠️ Using default working hours for employee ${employeeId}`)
        const defaultHours = workingHoursManager.generateDefaultWorkingHours()
        workingHoursManager.setEmployeeWorkingHours(employeeId, defaultHours)
        workingHours = workingHoursManager.getVisualWorkingHours(employeeId, date)
      }
      
      return workingHours
    } catch (error) {
      console.error(`Failed to get working hours for employee ${employeeId}:`, error)
      // Return fallback working hours
      return {
        start: '09:00',
        end: '17:00',
        isWorking: true
      }
    }
  }, [])

  // Synchronous version for immediate use (uses cached data)
  const getWorkingHoursForEmployeeSync = useCallback((employeeId, date) => {
    // CRITICAL: Use the React state instead of manager cache for re-renders
    const employeeWorkingHours = workingHours[employeeId]
    
    if (employeeWorkingHours) {
      const dayName = workingHoursManager.getDayNameFromDate(date)
      const dayHours = employeeWorkingHours[dayName]
      
      if (dayHours) {
        if (dayHours.isWorking) {
          return {
            start: dayHours.startTime,
            end: dayHours.endTime,
            isWorking: true
          }
        } else {
          return null // CRITICAL: Return null for non-working days
        }
      }
    }
    
    // Fallback to manager cache if not in React state
    const workingHoursFromManager = workingHoursManager.getVisualWorkingHours(employeeId, date)
    
    if (workingHoursFromManager) {
      return workingHoursFromManager
    }
    
    // Final fallback to defaults ONLY if we have no data at all
    return {
      start: '09:00',
      end: '17:00',
      isWorking: true
    }
  }, [workingHours]) // FIXED: Remove workingHoursVersion to prevent infinite loop

  const isEmployeeWorking = useCallback((employeeId, date) => {
    return workingHoursManager.isEmployeeWorking(employeeId, date)
  }, [])

  // Scroll management
  const scrollToCurrentTime = useCallback(() => {
    const now = new Date()
    const currentHour = now.getHours()
    const currentMinute = now.getMinutes()
    
    // Calculate scroll position based on current time
    const hoursSinceStart = currentHour - config.displayHourStart
    const minuteOffset = currentMinute / 60
    const totalHours = hoursSinceStart + minuteOffset
    const scrollPos = Math.max(0, totalHours * config.gridHeight - 200) // Offset for visibility
    
    setScrollPosition(scrollPos)
  }, [config.displayHourStart, config.gridHeight])

  const resetScrollPosition = useCallback(() => {
    setScrollPosition(0)
  }, [])

  // Utility functions
  const getAppointmentsForDay = useCallback((date, employeeIds = null) => {
    const targetEmployeeIds = employeeIds || Array.from(selectedEmployeeIDs)
    const dayStart = new Date(date)
    dayStart.setHours(0, 0, 0, 0)
    const dayEnd = new Date(date)
    dayEnd.setHours(23, 59, 59, 999)
    
    return appointments.filter(apt => {
      const aptDate = new Date(apt.start)
      return targetEmployeeIds.includes(apt.employeeId) &&
             aptDate >= dayStart && 
             aptDate <= dayEnd
    })
  }, [appointments, selectedEmployeeIDs])

  const getAppointmentsForTimeSlot = useCallback((date, hour, minute, employeeId = null) => {
    const targetEmployeeIds = employeeId ? [employeeId] : Array.from(selectedEmployeeIDs)
    
    return appointments.filter(apt => {
      const aptDate = new Date(apt.start)
      const aptHour = aptDate.getHours()
      const aptMinute = aptDate.getMinutes()
      
      // Check employee and date match
      const employeeMatch = targetEmployeeIds.includes(apt.employeeId)
      const dateMatch = aptDate.toDateString() === date.toDateString()
      
      if (!employeeMatch || !dateMatch || aptHour !== hour) {
        return false
      }
      
      // ✅ FIXED: Use slot-based matching instead of exact minute matching
      const appointmentSlotMinute = Math.floor(aptMinute / config.timeResolution) * config.timeResolution
      return appointmentSlotMinute === minute
    })
  }, [appointments, selectedEmployeeIDs, config.timeResolution])

  // Utility function to parse time string to minutes
  const parseTime = useCallback((timeString) => {
    const [hours, minutes] = timeString.split(':').map(Number)
    return hours * 60 + minutes
  }, [])

  // Helper function to check if a time slot is within working hours
  const isWorkingHourSlot = useCallback((date, hour, minute, employeeId) => {
    const workingHours = getWorkingHoursForEmployeeSync(employeeId, date)
    
    // If no working hours data or day is not working, return false
    if (!workingHours || !workingHours.isWorking) {
      return false
    }
    
    const slotTime = hour * 60 + minute
    const startTime = parseTime(workingHours.start)
    const endTime = parseTime(workingHours.end)
    
    return slotTime >= startTime && slotTime < endTime
  }, [getWorkingHoursForEmployeeSync, parseTime])

  // Load working hours for all employees when they change
  useEffect(() => {
    const loadAllWorkingHours = async () => {
      if (employees.length > 0) {
        try {
          console.log('🕒 Preloading working hours for all employees...')
          await workingHoursService.fetchAllWorkingHours()
          console.log('✅ Successfully preloaded working hours for all employees')
        } catch (error) {
          console.warn('⚠️ Failed to preload working hours, will load on demand:', error)
        }
      }
    }

    loadAllWorkingHours()
  }, [employees])

  // Combine loading states and errors
  const combinedLoading = isLoading || employeesLoading
  const combinedError = error || employeesError

  // Return the complete advanced calendar interface
  return {
    // Core state
    selectedDate,
    displayMode,
    displayDays,
    appointments,
    filteredAppointments,
    workingHours,
    workingHoursVersion,
    employees,
    selectedEmployeeIDs,
    selectedEmployees,
    selectedServices,
    availableServices,
    serviceCategories,
    currentEmployee,
    isLoading: combinedLoading,
    isRefreshing,
    error: combinedError,
    isViewReady,
    viewDidAppear,
    scrollPosition,
    lastRefreshTime,
    performanceMetrics,
    
    // Configuration
    config,
    updateConfig,
    setTimeResolution,
    setDisplayHours,
    setWeekStartDay,
    getTimeSlots,
    getDisplayHours,
    
    // Navigation
    navigateDate,
    goToToday,
    selectDate,
    
    // View management
    setViewMode,
    setViewDidAppear,
    
    // Employee management
    updateSelectedEmployeeIDs,
    setSelectedServices,
    
    // Appointment management
    refreshAppointments,
    createAppointment,
    updateAppointment,
    deleteAppointment,
    getAppointmentsForDay,
    getAppointmentsForTimeSlot,
    
    // Working hours
    getWorkingHoursForEmployee,
    getWorkingHoursForEmployeeSync,
    isEmployeeWorking,
    isWorkingHourSlot,
    
    // Scroll management
    scrollToCurrentTime,
    resetScrollPosition,
    setScrollPosition,
    
    // Utility
    setError,
    setIsLoading
  }
} 