"""
SQS Worker Management Command

Replaces Celery worker - continuously polls SQS queue for file processing jobs.
Designed to run in ECS containers with auto-scaling based on queue depth.
"""
import json
import logging
import signal
import sys
import time
from datetime import datetime
from typing import Dict, Any

from django.core.management.base import BaseCommand
from django.conf import settings
from botocore.exceptions import ClientError

from config.aws import aws_config
from customers.models import UploadedFile
from api.services.file_processing_service import FileProcessingService
from aws_services.sqs import sqs_service
from aws_services.sns import sns_service

logger = logging.getLogger(__name__)


class Command(BaseCommand):
    help = 'SQS Worker - polls SQS queue for file processing jobs'

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.shutdown = False
        self.processing_service = FileProcessingService()
        
        # Set up graceful shutdown
        signal.signal(signal.SIGTERM, self.signal_handler)
        signal.signal(signal.SIGINT, self.signal_handler)

    def add_arguments(self, parser):
        parser.add_argument(
            '--max-messages',
            type=int,
            default=10,
            help='Maximum messages to process per poll (default: 10)'
        )
        parser.add_argument(
            '--wait-time',
            type=int,
            default=20,
            help='Long polling wait time in seconds (default: 20)'
        )
        parser.add_argument(
            '--visibility-timeout',
            type=int,
            default=300,
            help='Message visibility timeout in seconds (default: 300)'
        )

    def signal_handler(self, signum, frame):
        """Handle graceful shutdown signals"""
        logger.info(f"Received signal {signum}, shutting down gracefully...")
        self.shutdown = True

    def handle(self, *args, **options):
        """Main worker loop"""
        if not aws_config.sqs_file_processing_queue_url:
            self.stderr.write(
                self.style.ERROR('SQS queue URL not configured. Check AWS_SQS_FILE_PROCESSING_QUEUE_URL')
            )
            return

        self.stdout.write(
            self.style.SUCCESS(
                f'Starting SQS worker for queue: {aws_config.sqs_file_processing_queue_url}'
            )
        )
        self.stdout.write('Press Ctrl+C to stop gracefully')

        max_messages = options['max_messages']
        wait_time = options['wait_time']
        
        # Main processing loop
        while not self.shutdown:
            try:
                # Poll SQS for messages
                messages = sqs_service.receive_messages(
                    queue_url=aws_config.sqs_file_processing_queue_url,
                    max_messages=max_messages,
                    wait_time_seconds=wait_time
                )
                
                if messages:
                    logger.info(f"Received {len(messages)} messages from SQS")
                    
                    # Process each message
                    for message in messages:
                        if self.shutdown:
                            break
                        self._process_message(message)
                else:
                    logger.debug("No messages received, continuing to poll...")
                    
            except KeyboardInterrupt:
                self.shutdown = True
                break
            except ClientError as e:
                error_code = e.response['Error']['Code']
                if error_code in ['QueueDoesNotExist', 'AccessDenied']:
                    logger.error(f"SQS error: {e}")
                    break
                else:
                    logger.warning(f"Transient SQS error: {e}, retrying in 30s...")
                    time.sleep(30)
            except Exception as e:
                logger.exception(f"Unexpected error in worker loop: {e}")
                time.sleep(10)  # Brief pause before retrying

        self.stdout.write(self.style.SUCCESS('SQS worker stopped'))

    def _process_message(self, message: Dict[str, Any]):
        """Process a single SQS message"""
        message_body = message.get('Body', '{}')
        receipt_handle = message.get('ReceiptHandle')
        
        try:
            # Parse message body
            body_data = json.loads(message_body)
            file_id = body_data.get('file_id')
            
            if not file_id:
                logger.error(f"Invalid message - missing file_id: {body_data}")
                self._delete_message(receipt_handle)
                return
                
            logger.info(f"Processing file: {file_id}")
            
            # Update file status to processing
            try:
                uploaded_file = UploadedFile.objects.get(file_id=file_id)
                uploaded_file.status = 'processing'
                uploaded_file.started_at = datetime.now()
                uploaded_file.save()
            except UploadedFile.DoesNotExist:
                logger.error(f"File {file_id} not found in database")
                self._delete_message(receipt_handle)
                return
            
            # Extract processing options
            processing_options = body_data.get('processing_options', {})
            process_options = {
                'skip_duplicates': processing_options.get('duplicate_handling') == 'skip',
                'update_existing': processing_options.get('duplicate_handling') == 'update',
                'validate_emails': processing_options.get('validation_mode') == 'strict',
                'custom_field_mapping': {}
            }
            
            # Process the file
            result = self.processing_service.process_file(
                file_id=file_id,
                process_options=process_options,
                user=uploaded_file.uploaded_by
            )
            
            # Update file status based on result
            if result['status'] == 'completed':
                uploaded_file.status = 'completed'
                uploaded_file.processed_rows = result.get('file_info', {}).get('total_rows', 0)
                uploaded_file.total_rows = result.get('file_info', {}).get('total_rows', 0)
                uploaded_file.completed_at = datetime.now()
                logger.info(f"File processing completed: {file_id}")
            else:
                uploaded_file.status = 'failed'
                uploaded_file.error_message = result.get('message', 'Processing failed')
                uploaded_file.completed_at = datetime.now()
                logger.error(f"File processing failed: {file_id} - {result.get('message')}")
            
            uploaded_file.save()
            
            # Delete message from queue after successful processing
            self._delete_message(receipt_handle)
            
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in message body: {e}")
            self._delete_message(receipt_handle)
        except Exception as e:
            logger.exception(f"Error processing message: {e}")
            
            # Update file status to failed
            try:
                if 'file_id' in locals():
                    uploaded_file = UploadedFile.objects.get(file_id=file_id)
                    uploaded_file.status = 'failed'
                    uploaded_file.error_message = f"Processing error: {str(e)}"
                    uploaded_file.completed_at = datetime.now()
                    uploaded_file.save()
            except:
                pass
            
            # Send failure notification
            if aws_config.sns_topic_arn and 'file_id' in locals():
                self._send_failure_notification(file_id, str(e))
            
            # Delete message to prevent infinite retries
            # (SQS DLQ will handle messages that fail too many times)
            self._delete_message(receipt_handle)

    def _delete_message(self, receipt_handle: str):
        """Delete processed message from SQS queue"""
        try:
            sqs_service.delete_message(
                queue_url=aws_config.sqs_file_processing_queue_url,
                receipt_handle=receipt_handle
            )
            logger.debug("Message deleted from SQS")
        except Exception as e:
            logger.error(f"Failed to delete SQS message: {e}")

    def _send_failure_notification(self, file_id: str, error_message: str):
        """Send failure notification via SNS"""
        try:
            sns_service.send_failure_notification(file_id, error_message)
            logger.info(f"Failure notification sent for file: {file_id}")
        except Exception as e:
            logger.error(f"Failed to send failure notification: {e}") 