import { useState, useEffect } from 'react';
import { getBookingRules } from '../../api/bookingApi';

const CancellationPolicyDisplay = ({ businessId, selectedService }) => {
    const [policyData, setPolicyData] = useState(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState(null);
    
    useEffect(() => {
        // Only load the policy if we have a business ID
        if (!businessId) return;
        
        const fetchPolicy = async () => {
            try {
                setIsLoading(true);
                const data = await getBookingRules(businessId);
                setPolicyData(data);
                setError(null);
            } catch (err) {
                console.error('Error fetching cancellation policy:', err);
                setError('Unable to load cancellation policy. Please try again later.');
            } finally {
                setIsLoading(false);
            }
        };
        
        fetchPolicy();
    }, [businessId]);
    
    if (isLoading) return <div className="policy-loading">Loading cancellation policy...</div>;
    if (error) return <div className="policy-error">{error}</div>;
    if (!policyData) return null;
    
    // Only display if cancellation is allowed and there's a policy
    if (!policyData.allow_cancellation || !policyData.cancellation_policy) {
        return null;
    }
    
    return (
        <div className="cancellation-policy">
            <h3>Cancellation Policy</h3>
            <div className="policy-details">
                <p className="policy-summary">
                    You may cancel your appointment up to {policyData.cancellation_hours_before} hours before your scheduled time.
                </p>
                <div className="policy-content">
                    {policyData.cancellation_policy}
                </div>
            </div>
            <div className="policy-checkbox">
                <label>
                    <input type="checkbox" required />
                    I have read and agree to the cancellation policy
                </label>
            </div>
        </div>
    );
};

export default CancellationPolicyDisplay;
