# Permission System Implementation Guide

## Overview

This guide covers how to define a central *registry* of permission keys, load and enforce them in Django (via middleware, decorators, and service-layer guards), map your raw JSON flags into nested Python objects, and share the registry with client apps (iOS).

---

## 1. Registry File

### 1.1 File: `config/permissions.yml`

```yaml
# Maps each endpoint + HTTP method to a permission key
"/api/calendar/config":
  GET:   calendar.config.view
  PATCH: calendar.config.modify

"/api/appointments":
  POST:  appointments.accept.own
  PUT:   appointments.modify.own
```

* **Location**: `project_root/config/permissions.yml`
* **Ownership**: Version-controlled by engineering—source of truth for all available permissions.

---

## 2. Loading the Registry

Create `your_app/permissions_registry.py`:

```python
import yaml
from pathlib import Path

REGISTRY_PATH = (
    Path(__file__).resolve().parent.parent
    / "config" / "permissions.yml"
)

def load_registry():
    with open(REGISTRY_PATH) as f:
        data = yaml.safe_load(f)
    # Flatten to {(path, method): permission_key}
    return {
        (path, method): perm
        for path, methods in data.items()
        for method, perm in methods.items()
    }
```

* **Dependency**: Add `pyyaml` to `requirements.txt`.
* **Usage**: Import `load_registry()` in middleware or decorators.

---

## 3. Middleware

File: `your_project/middleware/permission_middleware.py`:

```python
from django.http import HttpResponseForbidden
from your_app.permissions_registry import load_registry

REGISTRY = load_registry()

class PermissionMiddleware:
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        key = (request.path, request.method)
        perm_key = REGISTRY.get(key)
        if perm_key and not request.user.access_level.has_permission(perm_key):
            return HttpResponseForbidden()
        return self.get_response(request)
```

Add to `settings.py`:

```python
MIDDLEWARE = [
    # ...
    'your_project.middleware.PermissionMiddleware',
    # ...
]
```

* **Behavior**: Runs *before* view resolution; blocks unauthorized requests globally.

---

## 4. Decorator

File: `your_app/decorators.py`:

```python
from functools import wraps
from django.http import HttpResponseForbidden

def permission_required(perm_key):
    def decorator(view_func):
        @wraps(view_func)
        def _wrapped(request, *args, **kwargs):
            if not request.user.access_level.has_permission(perm_key):
                return HttpResponseForbidden()
            return view_func(request, *args, **kwargs)
        return _wrapped
    return decorator
```

**Usage**:

```python
from your_app.decorators import permission_required

@permission_required('calendar.config.modify')
def patch_calendar_config(request):
    # ...
    pass
```

* **Use case**: Endpoints requiring custom logic or multiple flags.

---

## 5. Service-Layer Guard

File: `your_app/services/guards.py`:

```python
from django.core.exceptions import PermissionDenied

def require_permission(user, perm_key):
    if not user.access_level.has_permission(perm_key):
        raise PermissionDenied(f"Missing permission: {perm_key}")
```

**Usage** in service code:

```python
from .guards import require_permission

def update_calendar_config(user, data):
    require_permission(user, 'calendar.config.modify')
    # perform update...
```

* **Scope**: Protects non-HTTP entrypoints (Celery tasks, management commands).

---

## 6. Mapping Raw JSON Flags

File: `your_app/permissions_loader.py`:

```python
from collections import namedtuple

# Define structured types
ConfigPerms   = namedtuple('ConfigPerms', ['view', 'modify'])
ScopePerms    = namedtuple('ScopePerms', ['view', 'modify'])
CalendarPerms = namedtuple('CalendarPerms', ['config', 'own', 'others'])


def load_user_permissions(raw):
    return CalendarPerms(
        config = ConfigPerms(
            view = raw['calendar_config_view'],
            modify = raw['calendar_config_modify'],
        ),
        own = ScopePerms(
            view = raw['calendar_own_view'],
            modify = raw['calendar_own_modify'],
        ),
        others = ScopePerms(
            view = raw['calendar_others_view'],
            modify = raw['calendar_others_modify'],
        ),
    )
```

Attach to user/session:

```python
user.perms = load_user_permissions(user.access_level.permissions)
```

* **Benefit**: Write `user.perms.calendar.own.modify` instead of raw keys.

---

## 7. Sharing with iOS

1. **Embed registry**:

   * Add `config/permissions.yml` as a resource in your iOS project.
   * Parse it at startup into Swift enums/structs.
2. **Generate enums via CI**:

   ```bash
   python scripts/gen_swift_permissions.py \
     config/permissions.yml > iOS/App/Permissions.swift
   ```
3. **Fetch flags at runtime**:

   * `GET /api/me/permissions` → merge raw JSON into your Swift types.

* **Trust model**: UI-only. Always enforce on server.

---

## 8. Seeding Default Roles

File: `config/fixtures/default_accesslevels.yml`:

```yaml
- model: your_app.accesslevel
  pk: 1
  fields:
    name: "Admin"
    permissions:
      calendar_config_view: true
      calendar_config_modify: true
      # …
```

Load with:

```bash
python manage.py loaddata default_accesslevels.yml
```

* **Use**: Bootstrap your DB with initial roles.

---

**End of guide.** Feel free to copy this into your repo and adapt paths/names as needed.
