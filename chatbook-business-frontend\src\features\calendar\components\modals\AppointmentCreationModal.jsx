import React from 'react'
import { 
  XMarkIcon, 
  ChevronLeftIcon, 
  ChevronRightIcon,
  CheckIcon
} from '@heroicons/react/24/outline'

// Import step components
import CustomerSelectionStep from '../creation-steps/CustomerSelectionStep'
import ServiceSelectionStep from '../creation-steps/ServiceSelectionStep'
import DateTimeEditorStep from '../creation-steps/DateTimeEditorStep'
import ConfirmationStep from '../creation-steps/ConfirmationStep'

/**
 * AppointmentCreationModal - Desktop-optimized appointment creation wizard
 * Multi-step modal with progress indicator and desktop-friendly navigation
 */
const AppointmentCreationModal = ({
  appointmentCreationHook,
  onCreateAppointment,
  onCancel
}) => {
  const {
    isOpen,
    currentStep,
    formData,
    validationErrors,
    isCreating,
    nextStep,
    previousStep,
    goToStep,
    validateCurrentStep,
    updateFormData,
    closeModal,
    canProceed,
    getCurrentStepIndex,
    isLastStep,
    isFirstStep,
    totalSteps,
    steps,
    setIsCreating
  } = appointmentCreationHook

  if (!isOpen) return null

  // Handle modal close
  const handleClose = () => {
    if (isCreating) return // Prevent closing during creation
    closeModal()
    onCancel?.()
  }

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose()
    }
  }

  // Handle step navigation
  const handleNext = () => {
    if (validateCurrentStep()) {
      if (isLastStep) {
        // Double-check that all required fields are present before creation
        if (canProceed && formData.selectedCustomer && formData.selectedService && 
            formData.selectedEmployee && formData.selectedDate) {
          handleCreateAppointment()
        } else {
          console.error('❌ Cannot create appointment: missing required fields', {
            customer: !!formData.selectedCustomer,
            service: !!formData.selectedService,
            employee: !!formData.selectedEmployee,
            date: !!formData.selectedDate
          })
        }
      } else {
        nextStep()
      }
    }
  }

  const handlePrevious = () => {
    if (!isFirstStep) {
      previousStep()
    }
  }

  // Handle appointment creation
  const handleCreateAppointment = async () => {
    if (!canProceed) return

    setIsCreating(true)
    try {
      await onCreateAppointment?.(formData)
      closeModal()
    } catch (error) {
      console.error('Failed to create appointment:', error)
      // Error handling is done in the parent component
    } finally {
      setIsCreating(false)
    }
  }

  // Render current step component
  const renderStep = () => {
    switch (currentStep) {
      case 'customer':
        return (
          <CustomerSelectionStep
            selectedCustomer={formData.selectedCustomer}
            onCustomerSelect={(customer) => updateFormData({ type: 'SELECT_CUSTOMER', payload: customer })}
            validationErrors={validationErrors}
          />
        )
      case 'service':
        return (
          <ServiceSelectionStep
            selectedService={formData.selectedService}
            selectedAddOns={formData.selectedAddOns}
            onServiceSelect={(service) => updateFormData({ type: 'SELECT_SERVICE', payload: service })}
            onAddOnsSelect={(addOns) => updateFormData({ type: 'SELECT_ADD_ONS', payload: addOns })}
            validationErrors={validationErrors}
          />
        )
      case 'datetime':
        return (
          <DateTimeEditorStep
            selectedDate={formData.selectedDate}
            selectedEmployee={formData.selectedEmployee}
            selectedService={formData.selectedService}
            totalDuration={formData.totalDuration}
            onDateChange={(date) => updateFormData({ type: 'UPDATE_DATE', payload: date })}
            onEmployeeChange={(employee) => updateFormData({ type: 'UPDATE_EMPLOYEE', payload: employee })}
            validationErrors={validationErrors}
          />
        )
      case 'confirmation':
        return (
          <ConfirmationStep
            formData={formData}
            onUpdateNotes={(notes) => updateFormData({ type: 'UPDATE_NOTES', payload: notes })}
            onUpdateDeposit={(percentage) => updateFormData({ type: 'UPDATE_DEPOSIT', payload: percentage })}
            onUpdateNotification={(notify) => updateFormData({ type: 'UPDATE_NOTIFICATION', payload: notify })}
            validationErrors={validationErrors}
            isCreating={isCreating}
          />
        )
      default:
        return null
    }
  }

  // Step titles for UI
  const stepTitles = {
    customer: 'Select Customer',
    service: 'Choose Service',
    datetime: 'Set Date & Time',
    confirmation: 'Review & Confirm'
  }

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-semibold text-gray-900">Create New Appointment</h2>
            {formData.selectedEmployee && (
              <div className="text-sm text-gray-600">
                for <span className="font-medium text-blue-600">{formData.selectedEmployee.name}</span>
              </div>
            )}
          </div>
          <button
            onClick={handleClose}
            disabled={Boolean(isCreating)}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors disabled:opacity-50"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Progress Indicator */}
        <div className="px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center justify-between">
            {steps.map((step, index) => {
              const isActive = step === currentStep
              const isCompleted = getCurrentStepIndex > index
              const isClickable = getCurrentStepIndex >= index

              return (
                <React.Fragment key={step}>
                  <button
                    onClick={() => isClickable && !isCreating && goToStep(step)}
                    disabled={Boolean(!isClickable || isCreating)}
                    className={`
                      flex items-center space-x-2 px-3 py-2 rounded-lg transition-colors
                      ${isActive ? 'bg-blue-100 text-blue-700' : ''}
                      ${isCompleted ? 'text-green-600' : ''}
                      ${!isActive && !isCompleted ? 'text-gray-500' : ''}
                      ${isClickable && !isCreating ? 'hover:bg-gray-100 cursor-pointer' : 'cursor-default'}
                      disabled:opacity-50
                    `}
                  >
                    <div className={`
                      flex items-center justify-center w-6 h-6 rounded-full text-xs font-medium
                      ${isActive ? 'bg-blue-600 text-white' : ''}
                      ${isCompleted ? 'bg-green-600 text-white' : ''}
                      ${!isActive && !isCompleted ? 'bg-gray-300 text-gray-600' : ''}
                    `}>
                      {isCompleted ? <CheckIcon className="h-4 w-4" /> : index + 1}
                    </div>
                    <span className="text-sm font-medium">{stepTitles[step]}</span>
                  </button>
                  {index < steps.length - 1 && (
                    <div className="flex-1 h-0.5 bg-gray-300 mx-2">
                      <div 
                        className={`h-full transition-all duration-300 ${
                          getCurrentStepIndex > index ? 'bg-green-600' : 'bg-gray-300'
                        }`}
                      />
                    </div>
                  )}
                </React.Fragment>
              )
            })}
          </div>
        </div>

        {/* Step Content */}
        <div className="flex-1 overflow-y-auto">
          {renderStep()}
        </div>

        {/* Modal Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200 bg-gray-50">
          <div className="text-sm text-gray-600">
            Step {getCurrentStepIndex + 1} of {totalSteps}
          </div>
          
          <div className="flex items-center space-x-3">
            <button
              onClick={handlePrevious}
              disabled={Boolean(isFirstStep || isCreating)}
              className="px-4 py-2 text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              <ChevronLeftIcon className="h-4 w-4" />
              <span>Previous</span>
            </button>
            
            <button
              onClick={handleNext}
              disabled={Boolean(!canProceed || isCreating)}
              className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isCreating ? (
                <>
                  <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  <span>Creating...</span>
                </>
              ) : (
                <>
                  <span>{isLastStep ? 'Create Appointment' : 'Next'}</span>
                  {!isLastStep && <ChevronRightIcon className="h-4 w-4" />}
                </>
              )}
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default AppointmentCreationModal 