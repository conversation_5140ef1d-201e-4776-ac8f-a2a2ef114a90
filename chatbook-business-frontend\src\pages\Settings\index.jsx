import React, { useState, useEffect } from 'react'
import { Navigate, Routes, Route, useLocation, Link } from 'react-router-dom'
import PageWrapper from '../../components/PageWrapper'
import { EmployeeManagement } from '../../features/employees/settings'
import { OnlineAppointmentRules } from '../../features/booking/settings'
import ServiceMenu from '../../features/services/settings'
import { CalendarConfiguration } from '../../features/calendar/settings'

// Settings navigation items
const settingsNavigation = [
  {
    id: 'employee-profile',
    name: 'Employee Profile',
    href: '/settings/employee-profile',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
      </svg>
    ),
    description: 'Manage employee profiles and information'
  },
  {
    id: 'appointment-rules',
    name: 'Online Appointment Rules',
    href: '/settings/appointment-rules',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" />
      </svg>
    ),
    description: 'Configure online booking rules and restrictions'
  },
  {
    id: 'service-menu',
    name: 'Service Menu',
    href: '/settings/service-menu',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
      </svg>
    ),
    description: 'Manage services, pricing, and categories'
  },
  {
    id: 'calendar-config',
    name: 'Calendar Configuration',
    href: '/settings/calendar-config',
    icon: (
      <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
      </svg>
    ),
    description: 'Configure calendar views and preferences'
  }
]

const SettingsSidebar = ({ currentPath, isOpen, onClose }) => {
  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}
      
      {/* Sidebar */}
      <div className={`
        fixed lg:static inset-y-0 left-0 z-50 lg:z-auto
        w-80 lg:w-72 xl:w-80 bg-white border-r border-gray-200 flex flex-col h-full
        transform transition-transform duration-300 ease-in-out lg:transform-none
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        {/* Header */}
        <div className="p-4 lg:p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Settings</h2>
              <p className="text-sm text-gray-600 mt-1 hidden lg:block">Configure your application settings</p>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 lg:hidden"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Navigation */}
        <nav className="flex-1 overflow-y-auto">
          <div className="px-3 py-4">
            {settingsNavigation.map((item) => {
              const isActive = currentPath.startsWith(item.href)
              return (
                <Link
                  key={item.id}
                  to={item.href}
                  onClick={onClose}
                  className={`group flex items-start gap-3 px-3 py-3 rounded-lg text-sm font-medium transition-colors mb-2 ${
                    isActive
                      ? 'bg-blue-50 text-blue-700 border border-blue-200'
                      : 'text-gray-700 hover:text-gray-900 hover:bg-gray-50'
                  }`}
                >
                  <div className={`flex-shrink-0 ${isActive ? 'text-blue-600' : 'text-gray-400 group-hover:text-gray-600'}`}>
                    {item.icon}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className={`font-medium ${isActive ? 'text-blue-700' : 'text-gray-900'}`}>
                      {item.name}
                    </div>
                    <div className={`text-xs mt-1 hidden lg:block ${isActive ? 'text-blue-600' : 'text-gray-500'}`}>
                      {item.description}
                    </div>
                  </div>
                </Link>
              )
            })}
          </div>
        </nav>
      </div>
    </>
  )
}

function Settings() {
  const location = useLocation()
  const [sidebarOpen, setSidebarOpen] = useState(false)

  // Disable global scrollbar for Settings page (like Calendar page)
  useEffect(() => {
    // Store original overflow style
    const originalOverflow = document.body.style.overflow
    
    // Disable scrolling
    document.body.style.overflow = 'hidden'
    
    // Cleanup function to restore scrolling when component unmounts
    return () => {
      document.body.style.overflow = originalOverflow
    }
  }, [])

  return (
    <div className="flex h-screen bg-gray-50">
      <SettingsSidebar 
        currentPath={location.pathname} 
        isOpen={sidebarOpen}
        onClose={() => setSidebarOpen(false)}
      />
      
      <div className="flex-1 flex flex-col min-h-0">
        {/* Mobile header */}
        <div className="lg:hidden bg-white border-b border-gray-200 px-4 py-3 flex items-center justify-between">
          <button
            onClick={() => setSidebarOpen(true)}
            className="p-2 text-gray-400 hover:text-gray-600"
          >
            <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
            </svg>
          </button>
          <h1 className="text-lg font-semibold text-gray-900">Settings</h1>
          <div className="w-9"></div> {/* Spacer for center alignment */}
        </div>

        {/* Settings content with full height */}
        <div className="flex-1 min-h-0">
          <Routes>
            <Route path="/" element={<Navigate to="/settings/employee-profile" replace />} />
            <Route path="/employee-profile" element={<EmployeeManagement />} />
            <Route path="/appointment-rules" element={<OnlineAppointmentRules />} />
            <Route path="/service-menu" element={<ServiceMenu />} />
            <Route path="/calendar-config" element={<CalendarConfiguration />} />
          </Routes>
        </div>
      </div>
    </div>
  )
}

export default Settings 