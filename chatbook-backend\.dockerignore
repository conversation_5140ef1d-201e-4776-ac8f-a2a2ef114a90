# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
env/
ENV/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Git
.git/
.gitignore

# Logs
logs/
*.log
debug.log
server.log

# Media files (these should be in S3)
media/
uploads/
backups/

# Static files (will be collected during build)
staticfiles/

# Database
db.sqlite3
*.db

# Documentation
*.md
!README.md

# Test files
tests/
.coverage
.pytest_cache/

# CDK and Infrastructure
cdk.out/
*.js.map
node_modules/

# Booking website (if separate)
booking-website/

# Development files
.cursor/
*.pyc

# Environment files (contain local development settings)
.env
.env.*
!.env.example 