import { useState, useRef, useEffect } from "react";
import {
  ChevronDownIcon,
  MagnifyingGlassIcon,
} from "@heroicons/react/24/outline";

function CustomerSelector({
  value,
  onChange,
  customers = [],
  placeholder = "Select Customer",
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCustomers, setSelectedCustomers] = useState(value || []);
  const dropdownRef = useRef(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Filter customers based on search term
  const filteredCustomers = customers.filter(
    (customer) =>
      customer.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      customer.phone?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle individual customer selection
  const handleCustomerToggle = (customerId) => {
    const newSelection = selectedCustomers.includes(customerId)
      ? selectedCustomers.filter((id) => id !== customerId)
      : [...selectedCustomers, customerId];

    setSelectedCustomers(newSelection);
    onChange(newSelection);
  };

  // Handle select all
  const handleSelectAll = () => {
    const allCustomerIds = filteredCustomers.map((customer) => customer.id);
    setSelectedCustomers(allCustomerIds);
    onChange(allCustomerIds);
  };

  // Handle select none
  const handleSelectNone = () => {
    setSelectedCustomers([]);
    onChange([]);
  };

  // Get display text for selected customers
  const getDisplayText = () => {
    if (selectedCustomers.length === 0) {
      return placeholder;
    } else if (selectedCustomers.length === 1) {
      const customer = customers.find((c) => c.id === selectedCustomers[0]);
      return customer?.name || placeholder;
    } else if (selectedCustomers.length === customers.length) {
      return "All Customers";
    } else {
      return `${selectedCustomers.length} customers selected`;
    }
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* Selector Button */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white text-left flex items-center justify-between"
      >
        <span
          className={
            selectedCustomers.length === 0 ? "text-gray-500" : "text-gray-900"
          }
        >
          {getDisplayText()}
        </span>
        <ChevronDownIcon
          className={`h-5 w-5 text-gray-400 transition-transform ${
            isOpen ? "rotate-180" : ""
          }`}
        />
      </button>

      {/* Dropdown Menu */}
      {isOpen && (
        <div className="absolute z-50 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-80 overflow-hidden">
          {/* Search Box */}
          <div className="p-3 border-b border-gray-200">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 text-sm"
              />
            </div>
          </div>

          {/* Select All / Select None */}
          <div className="flex justify-between items-center p-3 border-b border-gray-200 bg-gray-50">
            <button
              type="button"
              onClick={handleSelectAll}
              className="text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Select All
            </button>
            <button
              type="button"
              onClick={handleSelectNone}
              className="text-gray-600 hover:text-gray-800 text-sm font-medium"
            >
              Select None
            </button>
          </div>

          {/* Customer List */}
          <div className="max-h-60 overflow-y-auto">
            {filteredCustomers.length === 0 ? (
              <div className="p-4 text-center text-gray-500 text-sm">
                No customers found
              </div>
            ) : (
              filteredCustomers.map((customer) => (
                <div
                  key={customer.id}
                  className="flex items-center p-3 hover:bg-gray-50 cursor-pointer"
                  onClick={() => handleCustomerToggle(customer.id)}
                >
                  <input
                    type="checkbox"
                    checked={selectedCustomers.includes(customer.id)}
                    onChange={() => handleCustomerToggle(customer.id)}
                    className="mr-3 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 text-sm">
                      {customer.name} {customer.phone && `(${customer.phone})`}
                    </div>
                    {customer.email && (
                      <div className="text-blue-500 text-xs">
                        {customer.email}
                      </div>
                    )}
                  </div>
                </div>
              ))
            )}
          </div>
        </div>
      )}
    </div>
  );
}

export default CustomerSelector;
