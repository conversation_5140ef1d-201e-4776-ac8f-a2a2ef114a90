from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.contrib.auth import get_user_model

User = get_user_model()

class ProfileView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        user = request.user
        
        # Get user permissions
        permissions = []
        for perm in user.get_all_permissions():
            app_label, codename = perm.split('.')
            permissions.append(codename)
        
        return Response({
            'id': user.id,
            'email': user.email,
            'first_name': user.first_name,
            'last_name': user.last_name,
            'role': user.user_type,  # This uses the property we defined earlier
            'permissions': permissions
        })

class SettingsView(APIView):
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        user = request.user
        settings = user.settings  # This uses the related_name from UserSettings model
        
        return Response({
            'theme': settings.theme,
            'notifications': settings.email_notifications,  # Using email_notifications as the main toggle
            'default_view': 'calendar',  # This could be stored in UserSettings if needed
            'time_zone': settings.timezone
        })
        
    def patch(self, request, *args, **kwargs):
        user = request.user
        settings = user.settings
        
        # Update only provided fields
        if 'theme' in request.data:
            settings.theme = request.data['theme']
        if 'notifications' in request.data:
            settings.email_notifications = request.data['notifications']
        if 'time_zone' in request.data:  # Accept snake_case in request
            settings.timezone = request.data['time_zone']
        
        settings.save()
        
        return Response({
            'theme': settings.theme,
            'notifications': settings.email_notifications,
            'default_view': 'calendar',
            'time_zone': settings.timezone
        }) 