import { useState, useEffect, useCallback } from 'react'
import { employeeApiService } from '../services'

/**
 * Custom hook for managing employee data
 * Connects to Django backend employee APIs
 */
export const useEmployees = () => {
  const [employees, setEmployees] = useState([])
  const [currentEmployee, setCurrentEmployee] = useState(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)

  // Load all employees
  const loadEmployees = useCallback(async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await employeeApiService.getAllEmployees()
      
      // Handle Django paginated response structure
      const employeeList = data.results || data || []
      console.log('✅ Loaded employees from Django:', employeeList)
      
      setEmployees(Array.isArray(employeeList) ? employeeList : [])
    } catch (err) {
      console.error('Failed to load employees:', err)
      setError(err.message)
      // Keep existing employees if load fails
    } finally {
      setLoading(false)
    }
  }, [])

  // Load current employee - skip for now since it requires authentication
  const loadCurrentEmployee = useCallback(async () => {
    try {
      // First try to get the actual current employee from the API
      const currentEmployeeData = await employeeApiService.getCurrentEmployee()
      
      if (currentEmployeeData) {
        console.log('✅ Loaded current employee from API:', currentEmployeeData)
        setCurrentEmployee(currentEmployeeData)
        return currentEmployeeData
      }
      
      // Fallback: Try to match user from localStorage with employee records
      const authToken = localStorage.getItem('auth_token')
      const userDataString = localStorage.getItem('user_data')
      
      if (authToken && userDataString) {
        try {
          const userData = JSON.parse(userDataString)
          console.log('🔍 Looking for employee matching user:', userData)
          
          // Find employee that matches the logged-in user
          const matchingEmployee = employees.find(emp => {
            // Try to match by user ID, email, or name
            const userFullName = `${userData.first_name} ${userData.last_name}`.trim()
            return (
              emp.user?.id === userData.id ||
              emp.user_details?.email === userData.email ||
              emp.full_name === userFullName ||
              (emp.user_details?.email === userData.email)
            )
          })
          
          if (matchingEmployee) {
            console.log('✅ Found matching employee for current user:', matchingEmployee)
            setCurrentEmployee(matchingEmployee)
            return matchingEmployee
          }
        } catch (error) {
          console.warn('Failed to parse user data from localStorage:', error)
        }
      }
      
      // Final fallback: use the first employee as "current" for development
      if (employees.length > 0) {
        console.log('⚠️ Using first employee as fallback current employee')
        const firstEmployee = employees[0]
        setCurrentEmployee(firstEmployee)
        return firstEmployee
      }
      
      return null
    } catch (err) {
      console.error('Failed to load current employee:', err)
      
      // Try the localStorage fallback even if API fails
      const userDataString = localStorage.getItem('user_data')
      if (userDataString && employees.length > 0) {
        try {
          const userData = JSON.parse(userDataString)
          const matchingEmployee = employees.find(emp => {
            const userFullName = `${userData.first_name} ${userData.last_name}`.trim()
            return (
              emp.user?.id === userData.id ||
              emp.user_details?.email === userData.email ||
              emp.full_name === userFullName
            )
          })
          
          if (matchingEmployee) {
            console.log('✅ Found matching employee via fallback:', matchingEmployee)
            setCurrentEmployee(matchingEmployee)
            return matchingEmployee
          }
        } catch (error) {
          console.warn('Fallback employee matching failed:', error)
        }
      }
      
      setError(err.message)
      return null
    }
  }, [employees])

  // Create new employee
  const createEmployee = useCallback(async (employeeData) => {
    try {
      const newEmployee = await employeeApiService.createEmployee(employeeData)
      setEmployees(prev => [...prev, newEmployee])
      return newEmployee
    } catch (err) {
      console.error('Failed to create employee:', err)
      throw err
    }
  }, [])

  // Update employee
  const updateEmployee = useCallback(async (employeeId, updates) => {
    try {
      const updated = await employeeApiService.updateEmployee(employeeId, updates)
      setEmployees(prev => 
        prev.map(emp => emp.id === employeeId ? { ...emp, ...updated } : emp)
      )
      
      // Update current employee if it's the one being updated
      if (currentEmployee && currentEmployee.id === employeeId) {
        setCurrentEmployee(prev => ({ ...prev, ...updated }))
      }
      
      return updated
    } catch (err) {
      console.error('Failed to update employee:', err)
      throw err
    }
  }, [currentEmployee])

  // Delete employee
  const deleteEmployee = useCallback(async (employeeId) => {
    try {
      await employeeApiService.deleteEmployee(employeeId)
      setEmployees(prev => prev.filter(emp => emp.id !== employeeId))
    } catch (err) {
      console.error('Failed to delete employee:', err)
      throw err
    }
  }, [])

  // Refresh data
  const refresh = useCallback(async () => {
    await loadEmployees()
    // loadCurrentEmployee will be called automatically when employees change
  }, [loadEmployees])

  // Load data on mount
  useEffect(() => {
    loadEmployees()
  }, [loadEmployees])

  // Set current employee when employees are loaded
  useEffect(() => {
    if (employees.length > 0 && !currentEmployee) {
      loadCurrentEmployee()
    }
  }, [employees, currentEmployee, loadCurrentEmployee])

  return {
    // Data
    employees,
    currentEmployee,
    loading,
    error,
    
    // Actions
    loadEmployees,
    loadCurrentEmployee,
    createEmployee,
    updateEmployee,
    deleteEmployee,
    refresh,
    
    // Utils
    getEmployeeById: useCallback((id) => 
      employees.find(emp => emp.id === id), [employees]
    ),
    isCurrentEmployee: useCallback((id) => 
      currentEmployee?.id === id, [currentEmployee])
  }
}

export default useEmployees 