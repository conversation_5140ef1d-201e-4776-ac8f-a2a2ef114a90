# User Model Production Readiness Improvements

## Removed recursive `save()` calls
- Moved UserSettings creation and default role assignment to Django signals (`accounts/signals.py`)
- Eliminated the risk of infinite recursion and improved performance

## Fixed unique constraints
- Removed redundant unique constraints (`uniq_user_email` and `uniq_user_phone`) since fields are already marked with `unique=True`
- Added proper indexes instead for performance improvement 

## Field improvements
- Made `phone_number` nullable instead of using a dummy default value of '+***********'
- Made `first_name` and `last_name` fields blank/optional rather than using placeholder defaults "--"
- Removed `phone_number` from `REQUIRED_FIELDS` since it's now nullable

## Manager method signature improvements
- Renamed `_create_user` parameter from `username` to `identifier` to clarify it can be either email or phone
- Updated docstring to better explain the parameter purpose

## JSONField default
- Added a comment to clarify the proper use of `default=dict` for JSO<PERSON>ield
- While `default=dict` is technically mutable, Django handles it correctly:
  - Django calls the callable on each new instance
  - The docstring was updated to reflect this behavior

## Module-level constants
- Extracted MFA method choices to a module-level constant `MFA_METHOD_CHOICES`
- Makes code more maintainable and centralizes these constants

## Database indexes
- Added strategic indexes for frequently queried fields:
  - `email` - Primary lookup field  
  - `phone_number` - Alternate lookup field
  - `created_at` - Common filter/sort field

## Role lookup performance
- Used `@lru_cache` to cache `user_type` property results
- Improved `user_type` and `has_role` methods to use `values_list()` with a single query instead of multiple `.exists()` calls
- Used `list()` to materialize the queryset once and reuse the results

## Additional Benefits
- Aligned with Django best practices for signal usage
- Reduced database queries
- Better organization and separation of concerns
- Improved code maintainability 