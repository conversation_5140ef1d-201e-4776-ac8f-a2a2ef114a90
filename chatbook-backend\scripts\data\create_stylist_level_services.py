#!/usr/bin/env python
import os
import sys
import django
from decimal import Decimal
from datetime import timed<PERSON><PERSON>
from django.db import transaction
import sqlite3

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from django.db import connection
from services.models import Service, ServiceCategory, StylistLevelService
from business.models import Business, StylistLevel
import traceback

def table_exists(table_name):
    """Check if a table exists in the database"""
    con = sqlite3.connect('db.sqlite3')
    cursor = con.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
    result = cursor.fetchone() is not None
    con.close()
    return result

def create_table_if_needed():
    """Create the StylistLevelService table if it doesn't exist"""
    if not table_exists('services_stylistlevelservice'):
        print("Creating services_stylistlevelservice table...")
        with connection.cursor() as cursor:
            cursor.execute("""
            CREATE TABLE services_stylistlevelservice (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                is_active BOOLEAN NOT NULL,
                price DECIMAL(10, 2) NOT NULL,
                duration INTEGER NOT NULL,
                is_offered BOOLEAN NOT NULL,
                created_at DATETIME NOT NULL,
                updated_at DATETIME NOT NULL,
                business_id INTEGER NOT NULL REFERENCES business_business(id),
                service_id INTEGER NOT NULL REFERENCES services_service(id),
                stylist_level_id INTEGER NOT NULL REFERENCES business_stylistlevel(id),
                UNIQUE(business_id, service_id, stylist_level_id)
            )
            """)
        return True
    return False

def reset_sequence(table_name):
    """Reset the SQLite sequence counter for a table"""
    with connection.cursor() as cursor:
        cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{table_name}'")
        print(f"Reset ID sequence for {table_name}")

@transaction.atomic
def create_stylist_level_services():
    """Create stylist level services for Clement Lash."""
    print("Starting creation of stylist level services...")
    
    # Make sure the table exists
    create_table_if_needed()
    
    # First, clean up any existing stylist level services
    print("Cleaning up existing stylist level services...")
    try:
        # Try using Django ORM
        StylistLevelService.objects.all().delete()
    except django.db.utils.OperationalError:
        # If table doesn't exist or there's another issue, try direct SQL
        if table_exists('services_stylistlevelservice'):
            with connection.cursor() as cursor:
                cursor.execute("DELETE FROM services_stylistlevelservice")
    
    # Reset the ID sequence in SQLite if table exists
    if table_exists('services_stylistlevelservice'):
        reset_sequence('services_stylistlevelservice')
    
    try:
        # Get Clement Lash business
        business = Business.objects.get(name='Clement Lash')
        print(f"Found Clement Lash business with ID: {business.id}")
        
        # Get stylist levels
        stylist_levels = StylistLevel.objects.filter(business=business)
        if not stylist_levels.exists():
            print("No stylist levels found for Clement Lash. Creating default ones...")
            master_level = StylistLevel.objects.create(
                business=business,
                name='Master Stylist',
                description='Expert lash artist with specialized training and years of experience',
                level_order=3,
                is_default=False,
                is_active=True
            )
            senior_level = StylistLevel.objects.create(
                business=business,
                name='Senior Stylist',
                description='Experienced lash artist with advanced techniques',
                level_order=2,
                is_default=False,
                is_active=True
            )
            junior_level = StylistLevel.objects.create(
                business=business,
                name='Junior Stylist',
                description='Newly trained lash artist',
                level_order=1,
                is_default=True,
                is_active=True
            )
            stylist_levels = [master_level, senior_level, junior_level]
        else:
            print(f"Found {stylist_levels.count()} stylist levels for Clement Lash")
        
        # Get services
        services = Service.objects.filter(business=business)
        if not services.exists():
            print("No services found for Clement Lash. Please create services first.")
            return []
        
        print(f"Found {services.count()} services for Clement Lash")
        
        # Create stylist level services
        created_items = []
        
        # Define price adjustments for different stylist levels
        level_price_adjustments = {
            'Master Stylist': {'fullset': Decimal('40.00'), 'refill': Decimal('20.00')},
            'Senior Stylist': {'fullset': Decimal('20.00'), 'refill': Decimal('10.00')},
            'Junior Stylist': {'fullset': Decimal('0.00'), 'refill': Decimal('0.00')}
        }
        
        # Define duration adjustments for different stylist levels (in minutes)
        level_duration_adjustments = {
            'Master Stylist': -20,  # 20 minutes faster
            'Senior Stylist': -10,  # 10 minutes faster
            'Junior Stylist': 0     # Same as base duration
        }
        
        # Get categories that should not have price/duration adjustments
        no_adjustment_categories = ServiceCategory.objects.filter(
            business=business,
            name__in=['Other Services', 'Add-on Services']
        ).values_list('id', flat=True)
        
        print(f"Found {len(no_adjustment_categories)} categories that will not receive price/duration adjustments")
        
        for service in services:
            # Determine if service is fullset or refill
            category_name = service.category.name if service.category else ""
            
            # Better determination of service type based on category name
            if "Fullset" in category_name or "Fullset" in service.name:
                service_type = 'fullset'
            elif "Refill" in category_name or "refill" in service.name:
                service_type = 'refill'
            else:
                service_type = 'other'  # Default to other for services that don't match
            
            # Check if this service is in a category that should not receive adjustments
            is_fixed_price_service = service.category_id in no_adjustment_categories
            
            # For debug output
            if is_fixed_price_service:
                print(f"Service '{service.name}' has fixed pricing (no adjustments)")
            
            for level in stylist_levels:
                # Skip if the service is not supposed to be offered by this level
                # For example, advanced services might not be offered by junior stylists
                if level.name == 'Junior Stylist' and 'Premium' in service.name:
                    continue
                
                # Apply price adjustment based on service type and stylist level
                # Only if this service is not in a fixed-price category
                if is_fixed_price_service or service_type == 'other':
                    price = service.base_price
                    duration = service.base_duration
                    if is_fixed_price_service:
                        print(f"Not applying adjustments to {service.name} (category: {service.category.name})")
                    else:
                        print(f"Not applying adjustments to {service.name} (service type: {service_type})")
                else:
                    price_adjustment = level_price_adjustments.get(level.name, {}).get(service_type, Decimal('0.00'))
                    price = service.base_price + price_adjustment
                    
                    # Apply duration adjustment (in minutes)
                    duration_adjustment_mins = level_duration_adjustments.get(level.name, 0)
                    base_minutes = service.base_duration.total_seconds() / 60
                    adjusted_minutes = max(1, int(base_minutes) + duration_adjustment_mins)  # Ensure minimum 1 minute
                    duration = timedelta(minutes=adjusted_minutes)
                
                # Try to create the service level directly in the database
                try:
                    # Create stylist level service
                    stylist_level_service = StylistLevelService(
                        business=business,
                        service=service,
                        stylist_level=level,
                        price=price,
                        duration=duration,
                        is_offered=True,
                        is_active=True
                    )
                    stylist_level_service.save()
                    created_items.append(stylist_level_service)
                except Exception as e:
                    # If ORM fails, try direct SQL insertion
                    print(f"ORM failed, trying direct SQL: {str(e)}")
                    now = django.utils.timezone.now().strftime('%Y-%m-%d %H:%M:%S')
                    
                    with connection.cursor() as cursor:
                        cursor.execute("""
                        INSERT INTO services_stylistlevelservice
                        (is_active, price, duration, is_offered, created_at, updated_at,
                        business_id, service_id, stylist_level_id)
                        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                        """, [
                            True,
                            float(price),
                            int(duration.total_seconds() * 1000000),  # Store duration as microseconds
                            True,
                            now,
                            now,
                            business.id,
                            service.id,
                            level.id
                        ])
                        
                        # Get the last inserted ID
                        cursor.execute("SELECT last_insert_rowid()")
                        last_id = cursor.fetchone()[0]
                        
                        # Create a dummy object for reporting
                        stylist_level_service = type('StylistLevelService', (), {
                            'id': last_id,
                            'business': business,
                            'service': service,
                            'stylist_level': level,
                            'price': price,
                            'duration': duration
                        })
                        created_items.append(stylist_level_service)
                
                # Update the output message based on whether we applied adjustments or not
                if is_fixed_price_service or service_type == 'other':
                    print(f"Created stylist level service (fixed price): {level.name} - {service.name} (${price}, {int(duration.total_seconds() / 60)} min)")
                else:
                    print(f"Created stylist level service (adjusted): {level.name} - {service.name} (${price}, {int(duration.total_seconds() / 60)} min)")
        
        print(f"\nCreated {len(created_items)} stylist level services successfully!")
        return created_items
        
    except Business.DoesNotExist:
        print("Error: Clement Lash business does not exist. Please create it first.")
    except Exception as e:
        print(f"Error: {str(e)}")
        print(traceback.format_exc())
        return []

def summarize_by_level(stylist_level_services):
    """Summarize services by stylist level"""
    if not stylist_level_services:
        return
    
    # Group by stylist level
    level_groups = {}
    for sls in stylist_level_services:
        level_name = sls.stylist_level.name
        if level_name not in level_groups:
            level_groups[level_name] = []
        level_groups[level_name].append(sls)
    
    # Print summary by level
    for level_name, services in level_groups.items():
        print(f"\n{level_name} ({len(services)} services):")
        for sls in services:
            if hasattr(sls, 'duration') and hasattr(sls.duration, 'total_seconds'):
                duration_mins = int(sls.duration.total_seconds() / 60)
            else:
                # If duration is stored as string, extract minutes
                duration_mins = "Unknown"
            print(f"  - {sls.service.name}: ${sls.price}, {duration_mins} min")

if __name__ == '__main__':
    stylist_level_services = create_stylist_level_services()
    
    # Print summary by level
    if stylist_level_services:
        print("\n--- Summary by Stylist Level ---")
        summarize_by_level(stylist_level_services) 