{"name": "customer-booking-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@heroicons/react": "^2.0.18", "@tanstack/react-query": "^5.84.0", "@tanstack/react-query-devtools": "^5.84.0", "axios": "^1.6.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "react": "^18.2.0", "react-datepicker": "^8.4.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.23.0", "zustand": "^5.0.7"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.16", "eslint": "^8.55.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.32", "tailwindcss": "^3.4.0", "vite": "^7.0.5"}}