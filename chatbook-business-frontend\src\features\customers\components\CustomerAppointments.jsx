import { useAppointmentData } from '../hooks/useAppointmentData'
import { useModals } from '../hooks/useModals'
import { getStatusBadge } from '../utils/appointmentUtils.jsx'
import AppointmentFilters from './appointment/AppointmentFilters'
import AppointmentActions from './appointment/AppointmentActions'
import TransactionDetails from './appointment/TransactionDetails'
import AppointmentPagination from './appointment/AppointmentPagination'
import ModificationHistoryModal from './appointment/ModificationHistoryModal'

function CustomerAppointments({ customer }) {
  const appointmentData = useAppointmentData(customer)
  const modals = useModals()

  const getStatusBadgeComponent = (status) => {
    const className = getStatusBadge(status)
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${className}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    )
  }

  const handlePageChange = (page) => {
    appointmentData.handlePageChange(page)
    modals.closeTransactionOnPageChange()
  }

  return (
    <div className="bg-white rounded-xl shadow-sm p-8">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h2 className="text-lg font-semibold text-gray-900">Appointment History</h2>
          {appointmentData.lastVisitDate && (
            <p className="text-sm text-gray-600 mt-1">
              Last Visit: {new Date(appointmentData.lastVisitDate).toLocaleDateString('en-US', { 
                year: 'numeric', 
                month: 'long', 
                day: 'numeric' 
              })}
            </p>
          )}
          <p className="text-sm text-gray-500 mt-1">
            Showing {appointmentData.currentAppointments.length} of {appointmentData.sortedAppointments.length} appointments
          </p>
        </div>
      </div>

      {/* Filters and Controls */}
      <AppointmentFilters 
        dateRangeFilter={appointmentData.dateRangeFilter}
        setDateRangeFilter={appointmentData.setDateRangeFilter}
        handleFilterChange={appointmentData.handleFilterChange}
        clearFilters={appointmentData.clearFilters}
        sortOrder={appointmentData.sortOrder}
        toggleSort={appointmentData.toggleSort}
        onCollapseAll={modals.collapseAllTransactions}
        hasExpandedTransactions={modals.hasExpandedTransactions()}
      />

      <div className="w-full">
        <table className="w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                Actions
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">
                Appointment Date
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28">
                Checkout Date
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24">
                Status
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Service Provider
              </th>
              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Service
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {appointmentData.currentAppointments.map((appointment) => (
              <>
                <tr key={appointment.id} className="hover:bg-gray-50">
                  <td className="px-3 py-4 text-sm font-medium w-24">
                    <AppointmentActions 
                      appointment={appointment}
                      onHistoryClick={modals.openHistoryModal}
                      onTransactionToggle={modals.toggleTransaction}
                      isTransactionExpanded={modals.isTransactionExpanded(appointment.id)}
                    />
                  </td>
                  <td className="px-3 py-4 text-sm text-gray-900 w-28">
                    <div className="truncate">
                      {new Date(appointment.appointmentDate).toLocaleDateString()}
                    </div>
                  </td>
                  <td className="px-3 py-4 text-sm text-gray-900 w-28">
                    <div className="truncate">
                      {appointment.checkoutDate ? new Date(appointment.checkoutDate).toLocaleDateString() : '-'}
                    </div>
                  </td>
                  <td className="px-3 py-4 w-24">
                    {getStatusBadgeComponent(appointment.status)}
                  </td>
                  <td className="px-3 py-4 text-sm text-gray-900">
                    <div className="truncate max-w-32" title={appointment.serviceProvider}>
                      {appointment.serviceProvider}
                    </div>
                  </td>
                  <td className="px-3 py-4 text-sm text-gray-900">
                    <div className="truncate max-w-40" title={appointment.service}>
                      {appointment.service}
                    </div>
                  </td>
                </tr>
                
                {/* Transaction Details Expandable Row */}
                <TransactionDetails 
                  appointment={appointment}
                  isExpanded={modals.isTransactionExpanded(appointment.id)}
                />
              </>
            ))}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      <AppointmentPagination 
        currentPage={appointmentData.currentPage}
        totalPages={appointmentData.totalPages}
        startIndex={appointmentData.startIndex}
        endIndex={appointmentData.endIndex}
        totalItems={appointmentData.sortedAppointments.length}
        onPageChange={handlePageChange}
      />

      {/* Empty State */}
      {appointmentData.sortedAppointments.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </div>
          <p className="text-gray-500 text-sm">
            No appointments match your filters
          </p>
          {appointmentData.dateRangeFilter !== 'all' && (
            <button
              onClick={appointmentData.clearFilters}
              className="mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium"
            >
              Clear filters to see all appointments
            </button>
          )}
        </div>
      )}

      {/* Modification History Modal */}
      <ModificationHistoryModal 
        isOpen={modals.showHistoryModal}
        appointment={modals.selectedAppointmentHistory}
        customer={customer}
        onClose={modals.closeHistoryModal}
      />
    </div>
  )
}

export default CustomerAppointments 