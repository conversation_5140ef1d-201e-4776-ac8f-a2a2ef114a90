import { useState, useEffect } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'

// Import Components
import ImportMethodSelector from '../../features/import/components/ImportMethodSelector'
import SpreadsheetImport from '../../features/import/components/SpreadsheetImport'
import VagaroImportModal from '../../features/import/components/VagaroImportModal'

function ImportManagement() {
  const [importMethod, setImportMethod] = useState(null) // null, 'excel', 'vagaro'
  const [showVagaroModal, setShowVagaroModal] = useState(false)
  const location = useLocation()
  const navigate = useNavigate()

  // Check if coming from Vagaro and set import method accordingly
  useEffect(() => {
    if (location.state?.fromVagaro === true) {
      setImportMethod('excel')
      setShowVagaroModal(false)
    } else if (location.state?.fromVagaro === false) {
      // User clicked regular spreadsheet import after coming from Vagaro
      setImportMethod('excel')
      setShowVagaroModal(false)
    } else {
      setImportMethod(null)
      setShowVagaroModal(false)
    }
  }, [location.key, location.state?.fromVagaro])

  // Reset to main selection page
  const resetToSelection = () => {
    setImportMethod(null)
    setShowVagaroModal(false)
  }

  // Handle method selection - clear Vagaro state for regular spreadsheet import
  const handleSelectMethod = (method) => {
    if (method === 'excel' && location.state?.fromVagaro) {
      // Clear the fromVagaro state when selecting regular spreadsheet import
      navigate('/customers/import', { state: { fromVagaro: false }, replace: true })
    } else {
      setImportMethod(method)
    }
  }

  // Handle Vagaro modal actions
  const handleShowVagaroModal = () => {
    setShowVagaroModal(true)
  }

  const handleCloseVagaroModal = () => {
    setShowVagaroModal(false)
  }

  const handleProceedToVagaro = () => {
    setShowVagaroModal(false)
    setImportMethod('excel') // Redirect to spreadsheet import instead
  }

  // Render appropriate view based on current state
  const renderCurrentView = () => {
    switch (importMethod) {
      case 'excel':
        return <SpreadsheetImport onBack={resetToSelection} />
      
      default:
        return (
          <ImportMethodSelector 
            onSelectMethod={handleSelectMethod}
            onShowVagaroModal={handleShowVagaroModal}
          />
        )
    }
  }

  return (
    <>
      {renderCurrentView()}
      
      <VagaroImportModal 
        isOpen={showVagaroModal}
        onClose={handleCloseVagaroModal}
        onProceed={handleProceedToVagaro}
      />
    </>
  )
}

export default ImportManagement 