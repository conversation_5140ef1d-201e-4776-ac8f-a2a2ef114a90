// checkoutApi.js - API service for checkout/POS system functionality

const API_BASE_URL =
  import.meta.env.VITE_API_URL || "http://localhost:8000/api";

class CheckoutApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Helper method to get auth token
  getAuthToken() {
    return (
      localStorage.getItem("auth_token") || sessionStorage.getItem("auth_token")
    );
  }

  // Helper method to make API requests
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;

    const authToken = this.getAuthToken();
    const defaultOptions = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
      ...options,
    };

    try {
      const response = await fetch(url, defaultOptions);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message ||
            errorData.error ||
            `HTTP error! status: ${response.status}`
        );
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // ========== 服务管理 API ==========

  // 获取服务列表
  async getServices(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.category) queryParams.append("category", params.category);
    if (params.active !== undefined)
      queryParams.append("active", params.active);
    if (params.search) queryParams.append("search", params.search);

    const queryString = queryParams.toString();
    const endpoint = `/services/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // 获取服务详情
  async getService(serviceId) {
    return await this.makeRequest(`/services/${serviceId}/`);
  }

  // ========== 产品管理 API ==========

  // 获取产品列表
  async getProducts(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.category) queryParams.append("category", params.category);
    if (params.in_stock !== undefined)
      queryParams.append("in_stock", params.in_stock);
    if (params.search) queryParams.append("search", params.search);

    const queryString = queryParams.toString();
    const endpoint = `/products/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // 获取产品详情
  async getProduct(productId) {
    return await this.makeRequest(`/products/${productId}/`);
  }

  // 扫码获取产品
  async scanProduct(barcode) {
    return await this.makeRequest(`/products/scan/${barcode}/`, {
      method: "POST",
    });
  }

  // ========== 订单处理 API ==========

  // 创建订单
  async createOrder(orderData) {
    return await this.makeRequest("/orders/", {
      method: "POST",
      body: JSON.stringify(orderData),
    });
  }

  // 获取订单详情
  async getOrder(orderId) {
    return await this.makeRequest(`/orders/${orderId}/`);
  }

  // 更新订单
  async updateOrder(orderId, orderData) {
    return await this.makeRequest(`/orders/${orderId}/`, {
      method: "PUT",
      body: JSON.stringify(orderData),
    });
  }

  // 取消订单
  async cancelOrder(orderId) {
    return await this.makeRequest(`/orders/${orderId}/`, {
      method: "DELETE",
    });
  }

  // ========== 支付处理 API ==========

  // 处理支付
  async processPayment(paymentData) {
    return await this.makeRequest("/payments/", {
      method: "POST",
      body: JSON.stringify(paymentData),
    });
  }

  // 获取支付详情
  async getPayment(paymentId) {
    return await this.makeRequest(`/payments/${paymentId}/`);
  }

  // 退款处理
  async processRefund(paymentId, refundData) {
    return await this.makeRequest(`/payments/${paymentId}/refund/`, {
      method: "POST",
      body: JSON.stringify(refundData),
    });
  }

  // ========== 发票管理 API ==========

  // 获取发票列表
  async getInvoices(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.start_date) queryParams.append("start_date", params.start_date);
    if (params.end_date) queryParams.append("end_date", params.end_date);
    if (params.customer_id)
      queryParams.append("customer_id", params.customer_id);
    if (params.status) queryParams.append("status", params.status);
    if (params.search) queryParams.append("search", params.search);

    const queryString = queryParams.toString();
    const endpoint = `/invoices/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // 获取发票详情
  async getInvoice(invoiceId) {
    return await this.makeRequest(`/invoices/${invoiceId}/`);
  }

  // 生成发票
  async createInvoice(invoiceData) {
    return await this.makeRequest("/invoices/", {
      method: "POST",
      body: JSON.stringify(invoiceData),
    });
  }

  // 下载发票PDF
  async downloadInvoicePdf(invoiceId) {
    const authToken = this.getAuthToken();
    const url = `${this.baseURL}/invoices/${invoiceId}/pdf/`;

    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to download invoice PDF: ${response.status}`);
    }

    return response.blob();
  }

  // ========== 员工管理 API ==========

  // 获取员工列表
  async getStaff(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.active !== undefined)
      queryParams.append("active", params.active);
    if (params.services) queryParams.append("services", params.services);

    const queryString = queryParams.toString();
    const endpoint = `/staff/${queryString ? `?${queryString}` : ""}`;

    return await this.makeRequest(endpoint);
  }

  // 获取员工可用时间
  async getStaffAvailability(staffId, date, serviceId) {
    const queryParams = new URLSearchParams();
    queryParams.append("date", date);
    if (serviceId) queryParams.append("service_id", serviceId);

    const queryString = queryParams.toString();
    const endpoint = `/staff/${staffId}/availability/?${queryString}`;

    return await this.makeRequest(endpoint);
  }

  // ========== 系统设置 API ==========

  // 获取收银设置
  async getCheckoutSettings() {
    return await this.makeRequest("/checkout/settings/");
  }

  // 更新收银设置
  async updateCheckoutSettings(settingsData) {
    return await this.makeRequest("/checkout/settings/", {
      method: "PUT",
      body: JSON.stringify(settingsData),
    });
  }

  // ========== 税费计算 API ==========

  // 计算税费
  async calculateTax(orderData) {
    return await this.makeRequest("/checkout/calculate-tax/", {
      method: "POST",
      body: JSON.stringify(orderData),
    });
  }

  // ========== 礼品卡和会员卡 API ==========

  // 验证礼品卡
  async validateGiftCard(cardNumber) {
    return await this.makeRequest("/gift-cards/validate/", {
      method: "POST",
      body: JSON.stringify({ card_number: cardNumber }),
    });
  }

  // 使用礼品卡
  async useGiftCard(cardNumber, amount) {
    return await this.makeRequest("/gift-cards/use/", {
      method: "POST",
      body: JSON.stringify({ card_number: cardNumber, amount }),
    });
  }

  // 获取客户积分
  async getCustomerPoints(customerId) {
    return await this.makeRequest(`/customers/${customerId}/points/`);
  }

  // 使用积分
  async usePoints(customerId, points) {
    return await this.makeRequest(`/customers/${customerId}/points/use/`, {
      method: "POST",
      body: JSON.stringify({ points }),
    });
  }
}

// Export singleton instance
export const checkoutApi = new CheckoutApiService();

// Export individual methods for easier importing
export const {
  // 服务管理
  getServices,
  getService,
  // 产品管理
  getProducts,
  getProduct,
  scanProduct,
  // 订单处理
  createOrder,
  getOrder,
  updateOrder,
  cancelOrder,
  // 支付处理
  processPayment,
  getPayment,
  processRefund,
  // 发票管理
  getInvoices,
  getInvoice,
  createInvoice,
  downloadInvoicePdf,
  // 员工管理
  getStaff,
  getStaffAvailability,
  // 系统设置
  getCheckoutSettings,
  updateCheckoutSettings,
  // 税费计算
  calculateTax,
  // 礼品卡和积分
  validateGiftCard,
  useGiftCard,
  getCustomerPoints,
  usePoints,
} = checkoutApi;
