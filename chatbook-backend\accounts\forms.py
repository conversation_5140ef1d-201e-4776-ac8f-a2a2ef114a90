from django import forms
from django.contrib.auth.forms import UserCreationForm
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from phonenumber_field.formfields import Phone<PERSON>umberField
from django.db import transaction
from .models.role import Role

User = get_user_model()

class CustomUserCreationForm(UserCreationForm):
    """
    A form that creates a user, with no privileges, from the given email, phone number and password.
    """
    email = forms.EmailField(
        max_length=254,
        help_text='Required. Enter a valid email address.',
        widget=forms.EmailInput(attrs={
            'class': 'appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': '<EMAIL>'
        })
    )
    phone_number = PhoneNumberField(
        help_text='Required. Enter a valid phone number (e.g., +1234567890).',
        widget=forms.TextInput(attrs={
            'class': 'appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': '+1234567890'
        })
    )
    first_name = forms.CharField(
        max_length=30,
        help_text='Required. Enter your first name.',
        widget=forms.TextInput(attrs={
            'class': 'appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'John'
        })
    )
    last_name = forms.CharField(
        max_length=30,
        help_text='Required. Enter your last name.',
        widget=forms.TextInput(attrs={
            'class': 'appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': 'Doe'
        })
    )
    password1 = forms.CharField(
        label='Password',
        help_text='Your password must contain at least 8 characters.',
        widget=forms.PasswordInput(attrs={
            'class': 'appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': '••••••••'
        })
    )
    password2 = forms.CharField(
        label='Confirm Password',
        help_text='Enter the same password as before, for verification.',
        widget=forms.PasswordInput(attrs={
            'class': 'appearance-none block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-sm',
            'placeholder': '••••••••'
        })
    )

    class Meta:
        model = User
        fields = ('email', 'phone_number', 'first_name', 'last_name', 'password1', 'password2')

    def clean_email(self):
        email = self.cleaned_data.get('email')
        if User.objects.filter(email=email).exists():
            raise ValidationError("A user with that email already exists.")
        return email

    def clean_phone_number(self):
        phone_number = self.cleaned_data.get('phone_number')
        if User.objects.filter(phone_number=phone_number).exists():
            raise ValidationError("A user with that phone number already exists.")
        return phone_number

    def save(self, commit=True):
        user = super().save(commit=False)
        user.email = self.cleaned_data['email']
        user.phone_number = self.cleaned_data['phone_number']
        user.first_name = self.cleaned_data['first_name']
        user.last_name = self.cleaned_data['last_name']
        if commit:
            user.save()
        return user

class BusinessSignupForm(CustomUserCreationForm):
    """Form for business user signup."""
    def save(self, commit=True):
        with transaction.atomic():
            user = super().save(commit=False)
            # Set signup_type before saving
            user.signup_type = 'business'
            if commit:
                user.save()
                # Assign business admin role
                admin_role, _ = Role.objects.get_or_create(name=Role.Choices.ADMIN)
                user.roles.add(admin_role)
            return user

class CustomerSignupForm(CustomUserCreationForm):
    """Form for customer user signup."""
    def save(self, commit=True):
        with transaction.atomic():
            user = super().save(commit=False)
            # Set signup_type before saving
            user.signup_type = 'customer'
            if commit:
                user.save()
                # Assign customer role
                customer_role, _ = Role.objects.get_or_create(name=Role.Choices.CUSTOMER)
                user.roles.add(customer_role)
            return user 