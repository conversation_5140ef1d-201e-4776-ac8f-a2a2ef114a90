"""
Timezone validation utilities for Django models and forms.

This module provides validators and validation functions to ensure
timezone data integrity across the application.
"""

import logging
from django.core.exceptions import ValidationError
from django.core.validators import BaseValidator
from utils.timezone_utils import validate_timezone

logger = logging.getLogger(__name__)


class TimezoneValidator(BaseValidator):
    """
    Django validator for timezone fields.
    
    Validates that a timezone string is a valid IANA timezone identifier.
    """
    message = 'Enter a valid timezone identifier (e.g., "America/Los_Angeles", "UTC").'
    code = 'invalid_timezone'
    
    def __init__(self, message=None):
        if message is not None:
            self.message = message
    
    def __call__(self, value):
        """
        Validate the timezone string.
        
        Args:
            value: Timezone string to validate
            
        Raises:
            ValidationError: If timezone is invalid
        """
        if value and not validate_timezone(value):
            raise ValidationError(self.message, code=self.code, params={'value': value})


def validate_business_timezone(timezone_str):
    """
    Validate timezone for business settings.
    
    Args:
        timezone_str: Timezone string to validate
        
    Raises:
        ValidationError: If timezone is invalid
    """
    if not timezone_str:
        raise ValidationError("Timezone is required for business settings.")
    
    if not validate_timezone(timezone_str):
        raise ValidationError(
            f"'{timezone_str}' is not a valid timezone identifier. "
            "Please use IANA timezone names like 'America/Los_Angeles' or 'UTC'."
        )


def validate_working_hours_timezone(timezone_str):
    """
    Validate timezone for employee working hours.
    
    Args:
        timezone_str: Timezone string to validate (can be None/empty)
        
    Raises:
        ValidationError: If timezone is provided but invalid
    """
    # Working hours timezone is optional - if not provided, business timezone is used
    if timezone_str and not validate_timezone(timezone_str):
        raise ValidationError(
            f"'{timezone_str}' is not a valid timezone identifier. "
            "Leave blank to use business timezone, or provide a valid IANA timezone name."
        )


def get_common_timezones():
    """
    Get a list of commonly used timezone identifiers for forms/admin.
    
    Returns:
        List[tuple]: List of (timezone_id, display_name) tuples
    """
    common_timezones = [
        ('UTC', 'UTC (Coordinated Universal Time)'),
        ('America/New_York', 'Eastern Time (US & Canada)'),
        ('America/Chicago', 'Central Time (US & Canada)'),
        ('America/Denver', 'Mountain Time (US & Canada)'),
        ('America/Los_Angeles', 'Pacific Time (US & Canada)'),
        ('America/Anchorage', 'Alaska Time'),
        ('Pacific/Honolulu', 'Hawaii Time'),
        ('Europe/London', 'London (GMT/BST)'),
        ('Europe/Paris', 'Central European Time'),
        ('Europe/Berlin', 'Central European Time'),
        ('Asia/Tokyo', 'Japan Standard Time'),
        ('Asia/Shanghai', 'China Standard Time'),
        ('Australia/Sydney', 'Australian Eastern Time'),
        ('Australia/Melbourne', 'Australian Eastern Time'),
    ]
    
    return common_timezones


def validate_appointment_timezone_data(appointment_data, business):
    """
    Validate timezone-related data for appointment creation/updates.
    
    Args:
        appointment_data: Dictionary containing appointment data
        business: Business model instance
        
    Returns:
        dict: Validated and normalized appointment data
        
    Raises:
        ValidationError: If timezone data is invalid
    """
    from utils.timezone_utils import validate_appointment_time, get_business_timezone
    from datetime import datetime
    
    errors = []
    
    # Validate start_time if provided
    if 'start_time' in appointment_data:
        start_time = appointment_data['start_time']
        
        # Ensure it's a datetime object
        if isinstance(start_time, str):
            try:
                start_time = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            except ValueError as e:
                errors.append(f"Invalid start_time format: {e}")
        
        if isinstance(start_time, datetime):
            # Validate the appointment time
            business_tz = get_business_timezone(business)
            min_hours = getattr(business.booking_rules, 'min_hours_before', 24) if hasattr(business, 'booking_rules') else 24
            
            is_valid, error_msg = validate_appointment_time(start_time, business_tz, min_hours)
            if not is_valid:
                errors.append(f"Invalid appointment time: {error_msg}")
    
    if errors:
        raise ValidationError(errors)
    
    return appointment_data


class TimezoneAwareModelMixin:
    """
    Mixin for Django models that handle timezone-aware data.
    
    Provides common timezone validation and utility methods.
    """
    
    def clean_timezone_fields(self):
        """
        Clean and validate timezone fields in the model.
        Override this method in your model to add custom timezone validation.
        """
        pass
    
    def clean(self):
        """
        Override Django's clean method to include timezone validation.
        """
        super().clean()
        self.clean_timezone_fields()
    
    def get_effective_timezone(self):
        """
        Get the effective timezone for this model instance.
        Override this method to provide model-specific timezone logic.
        """
        return 'UTC'


def log_timezone_warning(message, extra_data=None):
    """
    Log timezone-related warnings with consistent formatting.
    
    Args:
        message: Warning message
        extra_data: Additional data to include in log
    """
    log_data = {'timezone_warning': True}
    if extra_data:
        log_data.update(extra_data)
    
    logger.warning(f"Timezone Warning: {message}", extra=log_data)


def log_timezone_error(message, exception=None, extra_data=None):
    """
    Log timezone-related errors with consistent formatting.
    
    Args:
        message: Error message
        exception: Exception object if available
        extra_data: Additional data to include in log
    """
    log_data = {'timezone_error': True}
    if extra_data:
        log_data.update(extra_data)
    
    if exception:
        logger.error(f"Timezone Error: {message} - {str(exception)}", extra=log_data, exc_info=True)
    else:
        logger.error(f"Timezone Error: {message}", extra=log_data)
