#!/usr/bin/env python
import os
import sys
import django
from decimal import Decimal
from datetime import <PERSON><PERSON><PERSON>
from django.db import transaction
import sqlite3

# Add project root to Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from django.db import connection
from services.models import AddOn, ServiceCategory, StylistLevelAddOn
from business.models import Business, StylistLevel
import traceback

def table_exists(table_name):
    """Check if a table exists in the database"""
    con = sqlite3.connect('db.sqlite3')
    cursor = con.cursor()
    cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name=?", (table_name,))
    result = cursor.fetchone() is not None
    con.close()
    return result

def reset_sequence(table_name):
    """Reset the SQLite sequence counter for a table"""
    with connection.cursor() as cursor:
        cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{table_name}'")
        print(f"Reset ID sequence for {table_name}")

@transaction.atomic
def create_stylist_level_addons():
    """Create stylist level add-ons for Clement Lash."""
    print("Starting creation of stylist level add-ons...")
    
    # First, clean up any existing stylist level add-ons
    print("Cleaning up existing stylist level add-ons...")
    StylistLevelAddOn.objects.all().delete()
    
    # Reset the ID sequence in SQLite if table exists
    if table_exists('services_stylistleveladdon'):
        reset_sequence('services_stylistleveladdon')
    
    try:
        # Get Clement Lash business
        business = Business.objects.get(name='Clement Lash')
        print(f"Found Clement Lash business with ID: {business.id}")
        
        # Get stylist levels
        stylist_levels = StylistLevel.objects.filter(business=business)
        if not stylist_levels.exists():
            print("No stylist levels found for Clement Lash. Please create stylist levels first.")
            return []
        
        print(f"Found {stylist_levels.count()} stylist levels for Clement Lash")
        
        # Define hardcoded add-on data with specific durations for each level
        addon_data = [
            {
                'name': 'Bottom Lash(as an add-on service)',
                'durations': {
                    'Master Stylist': 15,
                    'Senior Stylist': 20,
                    'Junior Stylist': 25
                },
                'price': Decimal('29.00')
            },
            {
                'name': 'Lash Removal',
                'durations': {
                    'Master Stylist': 25,
                    'Senior Stylist': 25,
                    'Junior Stylist': 25
                },
                'price': Decimal('0.00')
            },
            {
                'name': 'Consulting Session',
                'durations': {
                    'Master Stylist': 15,
                    'Senior Stylist': 15,
                    'Junior Stylist': 15
                },
                'price': Decimal('0.00')
            }
        ]
        
        # Get add-ons
        addons = AddOn.objects.filter(business=business)
        if not addons.exists():
            print("No add-ons found for Clement Lash. Please create add-ons first.")
            return []
        
        print(f"Found {addons.count()} add-ons for Clement Lash")
        
        # Get categories that should not have price/duration adjustments
        fixed_price_categories = ServiceCategory.objects.filter(
            business=business,
            name__in=['Other Services', 'Add-on Services']
        ).values_list('id', flat=True)
        
        print(f"Found {len(fixed_price_categories)} categories that will use fixed pricing (no adjustments)")
        
        # Create stylist level add-ons
        created_items = []
        
        for addon in addons:
            # Find the matching addon data
            matching_addon_data = next((data for data in addon_data if data['name'] == addon.name), None)
            
            for level in stylist_levels:
                # Set price (always fixed for add-ons)
                price = addon.base_price
                
                # Set duration based on addon and stylist level
                if matching_addon_data and level.name in matching_addon_data['durations']:
                    duration_mins = matching_addon_data['durations'][level.name]
                    duration = timedelta(minutes=duration_mins)
                    print(f"Using hardcoded duration for {addon.name}: {level.name} - {duration_mins} mins")
                else:
                    # Fallback to base duration if not in our hardcoded list
                    duration = addon.base_duration
                    print(f"Using base duration for {addon.name}: {level.name} - {int(duration.total_seconds() / 60)} mins")
                
                # Create stylist level add-on
                stylist_level_addon = StylistLevelAddOn(
                    business=business,
                    addon=addon,
                    stylist_level=level,
                    price=price,
                    duration=duration,
                    is_offered=True,
                    is_active=True
                )
                stylist_level_addon.save()
                created_items.append(stylist_level_addon)
                
                # Log output
                print(f"Created stylist level add-on: {level.name} - {addon.name} (${price}, {int(duration.total_seconds() / 60)} min)")
        
        print(f"\nCreated {len(created_items)} stylist level add-ons successfully!")
        return created_items
        
    except Business.DoesNotExist:
        print("Error: Clement Lash business does not exist. Please create it first.")
    except Exception as e:
        print(f"Error: {str(e)}")
        print(traceback.format_exc())
        return []

def summarize_by_level(stylist_level_addons):
    """Summarize add-ons by stylist level"""
    if not stylist_level_addons:
        return
    
    # Group by stylist level
    level_groups = {}
    for sla in stylist_level_addons:
        level_name = sla.stylist_level.name
        if level_name not in level_groups:
            level_groups[level_name] = []
        level_groups[level_name].append(sla)
    
    # Print summary by level
    for level_name, addons in level_groups.items():
        print(f"\n{level_name} ({len(addons)} add-ons):")
        for sla in addons:
            if hasattr(sla, 'duration') and hasattr(sla.duration, 'total_seconds'):
                duration_mins = int(sla.duration.total_seconds() / 60)
            else:
                # If duration is stored as string, extract minutes
                duration_mins = "Unknown"
            print(f"  - {sla.addon.name}: ${sla.price}, {duration_mins} min")

if __name__ == '__main__':
    stylist_level_addons = create_stylist_level_addons()
    
    # Print summary by level
    if stylist_level_addons:
        print("\n--- Summary by Stylist Level ---")
        summarize_by_level(stylist_level_addons) 