from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from datetime import datetime
import json
import logging
import os

from django.conf import settings
from django.urls import reverse
from django.http import HttpRequest

# Import existing views to reuse their logic
from ..employees.views import (
    EmployeeCurrentUserView,
    EmployeeWorkingHoursView,
    EmployeePermissionsView,
    EmployeeCalendarConfigView,
)

# Import models for viewable employees query
from employees.models import Employee
from business.models import Business, OnlineBookingRules

logger = logging.getLogger(__name__)

class AppBootstrapView(APIView):
    """
    Backend-For-Frontend API that combines multiple endpoints
    into a single response to minimize client API calls
    """
    permission_classes = [IsAuthenticated]
    
    def get(self, request, *args, **kwargs):
        """
        Get all essential data for the app in a single call
        """
        try:
            # Get current timestamp for server_time in proper ISO format
            from django.utils import timezone
            server_time = timezone.now().strftime("%Y-%m-%dT%H:%M:%S%z")
            
            # App version from settings (default to 1.0.0 if not set)
            app_version = getattr(settings, "APP_VERSION", "1.0.0")
            
            # Feature flags - could be dynamic from a DB later
            feature_flags = {
                "newCheckoutFlow": True,
                "enablePushNotifications": True
            }
            
            # Execute all data fetching operations
            profile_data = self._get_profile_data(request)
            working_hours_data = self._get_working_hours_data(request)
            permissions_data = self._get_permissions_data(request)
            calendar_config_data = self._get_calendar_config_data(request)
            
            # Get viewable employees the current user can view
            viewable_employees = self._get_viewable_employees(request)
            
            # Get business timezone
            business_timezone = "UTC"  # Default timezone
            try:
                # Get the employee profile for the current user
                employee = Employee.objects.filter(user=request.user).first()
                if employee and employee.business:
                    # Try to get booking rules which contains timezone
                    booking_rules = OnlineBookingRules.objects.filter(business=employee.business).first()
                    if booking_rules:
                        business_timezone = booking_rules.timezone
            except Exception as e:
                logger.warning(f"Error fetching business timezone: {str(e)}")
            
            # Combine all data into a single response
            response_data = {
                "profile": profile_data,
                "working_hours": working_hours_data,
                "permissions": permissions_data,
                "calendar_config": calendar_config_data,
                "viewable_employees": viewable_employees,
                "server_time": server_time,
                "business_timezone": business_timezone,
                "app_version": app_version,
                "feature_flags": feature_flags
            }
            
            return Response(response_data)
            
        except Exception as e:
            logger.exception(f"Error in bootstrap API: {str(e)}")
            return Response({
                "error": "An error occurred while fetching bootstrap data",
                "details": str(e),
                "server_time": timezone.now().strftime("%Y-%m-%dT%H:%M:%S%z")
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _get_profile_data(self, request):
        """Get employee profile data"""
        view = EmployeeCurrentUserView()
        view.request = request
        response = view.get(request)
        return response.data if response.status_code == 200 else {}
    
    def _get_working_hours_data(self, request):
        """Get employee working hours data"""
        view = EmployeeWorkingHoursView()
        view.request = request
        response = view.get(request)
        return response.data.get('working_hours', []) if response.status_code == 200 else []
    
    def _get_permissions_data(self, request):
        """Get employee permissions data"""
        view = EmployeePermissionsView()
        view.request = request
        response = view.get(request)
        return response.data if response.status_code == 200 else {}
    
    def _get_calendar_config_data(self, request):
        """Get employee calendar config data"""
        view = EmployeeCalendarConfigView()
        view.request = request
        response = view.get(request)
        return response.data if response.status_code == 200 else {}
    
    def _get_viewable_employees(self, request):
        """
        Get employees that the current user has permission to view, including themselves
        """
        try:
            # Get the current employee
            current_employee = Employee.objects.filter(user=request.user).first()
            
            if not current_employee:
                logger.warning(f"No employee record found for user {request.user.id}")
                return []
            
            # Start with an empty result list
            result = []
            
            # Check permissions
            permissions_view = EmployeePermissionsView()
            permissions_view.request = request
            permissions_response = permissions_view.get(request)
            
            permissions_data = {}
            if permissions_response.status_code == 200:
                permissions_data = permissions_response.data
            
            permissions = permissions_data.get('permissions', {})
            
            # First, add the current employee if they have permission to view their own calendar
            has_own_view_permission = permissions.get('calendar_own_view', True)  # Default to True for viewing own
            
            if has_own_view_permission:
                # Add current employee to the list
                avatar_url = None
                if hasattr(current_employee, 'profile_image') and current_employee.profile_image:
                    avatar_url = request.build_absolute_uri(current_employee.profile_image.url)
                
                current_employee_data = {
                    "id": current_employee.id,
                    "full_name": f"{current_employee.user.first_name} {current_employee.user.last_name}".strip(),
                    "avatar_url": avatar_url,
                    "is_current_user": True  # Flag to identify this is the current user
                }
                
                # Add stylist level if available
                if current_employee.stylist_level:
                    current_employee_data["stylist_level"] = current_employee.stylist_level.name
                
                result.append(current_employee_data)
            
            # Now check for permission to view other calendars
            has_others_view_permission = permissions.get('calendar_others_view', False)
            
            if has_others_view_permission:
                # Get all other active employees from the same business
                other_employees = Employee.objects.filter(
                    business=current_employee.business,
                    is_active=True
                ).exclude(id=current_employee.id)
                
                # Format the employee data for others
                for employee in other_employees:
                    avatar_url = None
                    if hasattr(employee, 'profile_image') and employee.profile_image:
                        avatar_url = request.build_absolute_uri(employee.profile_image.url)
                    
                    employee_data = {
                        "id": employee.id,
                        "full_name": f"{employee.user.first_name} {employee.user.last_name}".strip(),
                        "avatar_url": avatar_url,
                        "is_current_user": False
                    }
                    
                    # Add stylist level if available
                    if employee.stylist_level:
                        employee_data["stylist_level"] = employee.stylist_level.name
                    
                    result.append(employee_data)
            
            return result
            
        except Exception as e:
            logger.exception(f"Error fetching viewable employees: {str(e)}")
            return [] 