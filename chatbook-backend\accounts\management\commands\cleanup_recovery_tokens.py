from django.core.management.base import BaseCommand
from accounts.models import RecoveryToken
from datetime import timedelta
from django.utils import timezone
from django.db.models import Q
import logging

# Configure logging
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    """
    Cleanup expired and used recovery tokens.
    
    This command should be scheduled to run regularly (e.g., daily) using:
    1. A cron job: 0 0 * * * python manage.py cleanup_recovery_tokens
    2. A task scheduler like Celery:
       @app.task
       def cleanup_tokens_task():
           call_command('cleanup_recovery_tokens')
       
       # In celery beat schedule:
       'cleanup-recovery-tokens': {
           'task': 'yourapp.tasks.cleanup_tokens_task',
           'schedule': crontab(hour=0, minute=0),  # Run daily at midnight
       }
    """
    help = 'Cleans up expired and used recovery tokens'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Perform a dry run without deleting any tokens',
        )
        parser.add_argument(
            '--older-than',
            type=int,
            help='Also clean tokens older than this many days regardless of their status',
        )

    def handle(self, *args, **options):
        dry_run = options.get('dry_run', False)
        older_than_days = options.get('older_than')
        
        if dry_run:
            # Count tokens that would be deleted without actually deleting them
            count = RecoveryToken.objects.cleanup_candidates().count()
            if older_than_days:
                # Also count tokens older than specified days
                cutoff = timezone.now() - timedelta(days=older_than_days)
                additional = RecoveryToken.objects.filter(created_at__lt=cutoff).exclude(
                    Q(used=True) | Q(expires_at__lt=timezone.now())
                ).count()
                count += additional
                
            self.stdout.write(
                self.style.WARNING(
                    f'[DRY RUN] Would delete {count} recovery tokens'
                )
            )
            logger.info(f'[DRY RUN] Would delete {count} recovery tokens')
        else:
            # Delete the tokens using the manager's cleanup method
            count = RecoveryToken.objects.cleanup_tokens(older_than_days=older_than_days)
            self.stdout.write(
                self.style.SUCCESS(
                    f'Successfully deleted {count} recovery tokens'
                )
            )
            logger.info(f'Successfully deleted {count} recovery tokens')
            
            # Log additional info if we specified older_than_days
            if older_than_days:
                logger.info(f'Included tokens older than {older_than_days} days')
                
            # Log total remaining tokens
            remaining = RecoveryToken.objects.count()
            logger.info(f'Remaining recovery tokens: {remaining}') 