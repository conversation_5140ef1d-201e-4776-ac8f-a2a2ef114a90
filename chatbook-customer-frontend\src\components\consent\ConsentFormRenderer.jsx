import { useState, useEffect } from 'react';
import { getConsentForm } from '../../api/consentApi';

// Style templates for consistent presentation
const CONSENT_TEMPLATES = {
    professional_legal: {
        container: "max-w-4xl mx-auto p-8 bg-white",
        title: "text-2xl font-bold text-gray-900 mb-6",
        section_title: "text-lg font-semibold text-gray-800 mb-3",
        content: "text-gray-700 leading-relaxed mb-4",
        warning: "text-red-600 font-medium",
        emphasis: "font-medium text-gray-900",
        introduction: "text-gray-700 leading-relaxed mb-6 p-4 bg-gray-50 rounded-lg"
    },

    modern_clean: {
        container: "max-w-3xl mx-auto p-6 bg-gray-50 rounded-lg",
        title: "text-3xl font-light text-gray-900 mb-8",
        section_title: "text-xl font-medium text-blue-900 mb-4",
        content: "text-gray-600 leading-loose mb-6",
        warning: "text-orange-600 font-semibold",
        emphasis: "font-semibold text-gray-800",
        introduction: "text-gray-600 leading-loose mb-8 p-6 bg-white rounded-lg shadow-sm"
    }
};

/**
 * Reusable Consent Form Renderer
 * Renders structured consent form content from API
 */
const ConsentFormRenderer = ({ 
    serviceType = 'eyelash_extensions',
    onSignatureChange,
    onElectronicConsentChange,
    signature = '',
    electronicConsent = false,
    className = ''
}) => {
    const [consentData, setConsentData] = useState(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchConsentForm = async () => {
            try {
                setLoading(true);
                const data = await getConsentForm(serviceType);
                setConsentData(data.consent_form);
            } catch (err) {
                setError('Failed to load consent form');
                console.error('Error loading consent form:', err);
            } finally {
                setLoading(false);
            }
        };

        fetchConsentForm();
    }, [serviceType]);

    if (loading) {
        return (
            <div className="consent-form-loading">
                <div className="animate-pulse">
                    <div className="h-8 bg-gray-200 rounded mb-4"></div>
                    <div className="space-y-3">
                        <div className="h-4 bg-gray-200 rounded"></div>
                        <div className="h-4 bg-gray-200 rounded"></div>
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    </div>
                </div>
            </div>
        );
    }

    if (error || !consentData) {
        return (
            <div className="consent-form-error text-red-600">
                <p>Unable to load consent form. Please try again.</p>
            </div>
        );
    }

    // Get the style template
    const template = CONSENT_TEMPLATES[consentData?.template_style] || CONSENT_TEMPLATES.professional_legal;

    const renderSection = (section, index) => {
        // Get style variant or default to content style
        const styleVariant = section.style_variant || 'content';
        const sectionClass = template[styleVariant] || template.content;

        switch (section.type) {
            case 'introduction':
                return (
                    <div key={index} className="consent-introduction mb-6">
                        <p className={template.introduction}>{section.content}</p>
                    </div>
                );

            case 'section':
                return (
                    <div key={index} className="consent-section mb-6">
                        {section.title && (
                            <h3 className={template.section_title}>{section.title}</h3>
                        )}
                        <p className={sectionClass}>{section.content}</p>
                    </div>
                );

            case 'list':
                return (
                    <div key={index} className="consent-list mb-6">
                        {section.title && (
                            <h3 className={template.section_title}>{section.title}</h3>
                        )}
                        <ul className={`list-disc list-inside space-y-2 ${template.content}`}>
                            {section.items.map((item, itemIndex) => (
                                <li key={itemIndex}>{item}</li>
                            ))}
                        </ul>
                    </div>
                );

            default:
                return null;
        }
    };

    return (
        <div className={`consent-form-renderer ${template.container} ${className}`}>
            {/* Form Title */}
            <h2 className={template.title}>
                {consentData.title}
            </h2>
            
            {/* Version Info (for legal compliance) */}
            {consentData.version && (
                <div className="text-xs text-gray-500 mb-4">
                    Version {consentData.version}
                    {consentData.legal_metadata?.updated_at && (
                        <span className="ml-2">
                            Updated: {new Date(consentData.legal_metadata.updated_at).toLocaleDateString()}
                        </span>
                    )}
                </div>
            )}

            <div className="border-b border-gray-300 mb-6"></div>

            {/* Render Content Sections */}
            <div className="consent-content space-y-4">
                {consentData.sections.map((section, index) => renderSection(section, index))}
            </div>

            {/* Signature Section */}
            {consentData.signature_requirements && (
                <div className="signature-section mt-8">
                    {/* Electronic Records Consent */}
                    {consentData.signature_requirements.electronic_records_consent && (
                        <div className="electronic-consent mb-6">
                            <label className="flex items-start space-x-3">
                                <input
                                    type="checkbox"
                                    checked={electronicConsent}
                                    onChange={(e) => onElectronicConsentChange?.(e.target.checked)}
                                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                />
                                <span className="text-sm text-gray-700">
                                    {consentData.signature_requirements.electronic_records_label}
                                </span>
                            </label>
                        </div>
                    )}

                    {/* Signature Field */}
                    {consentData.signature_requirements.signature_required && (
                        <div className="signature-field">
                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                <span className="text-red-500">
                                    {consentData.signature_requirements.signature_label}
                                </span>
                            </label>
                            <input
                                type="text"
                                value={signature}
                                onChange={(e) => onSignatureChange?.(e.target.value)}
                                placeholder="Type your full name as your signature"
                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                            />
                        </div>
                    )}
                </div>
            )}

            {/* Legal Metadata (hidden but available for compliance) */}
            <div className="hidden consent-metadata" data-consent-id={consentData.id} data-version={consentData.version}>
                {JSON.stringify(consentData.legal_metadata)}
            </div>
        </div>
    );
};

export default ConsentFormRenderer;
