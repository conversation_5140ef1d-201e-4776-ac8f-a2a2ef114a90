import { useState, useRef, useEffect } from 'react'
import { APPOINTMENT_STATUSES, BREAK_TYPES } from '../../constants/calendarConfig'

const StatusLegend = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [isDragging, setIsDragging] = useState(false)
  const [position, setPosition] = useState({ x: 20, y: 80 }) // Initial position (right: 20px, top: 80px)
  const [dragOffset, setDragOffset] = useState({ x: 0, y: 0 })
  const legendRef = useRef(null)

  const toggleLegend = () => setIsOpen(!isOpen)

  // Handle mouse down to start dragging
  const handleMouseDown = (e) => {
    if (e.target.closest('.legend-content')) return // Don't drag when clicking inside the dropdown
    
    setIsDragging(true)
    const rect = legendRef.current.getBoundingClientRect()
    setDragOffset({
      x: e.clientX - rect.left,
      y: e.clientY - rect.top
    })
    e.preventDefault()
  }

  // Handle mouse move during drag
  const handleMouseMove = (e) => {
    if (!isDragging) return
    
    const newX = e.clientX - dragOffset.x
    const newY = e.clientY - dragOffset.y
    
    // Keep legend within viewport bounds
    const maxX = window.innerWidth - 320 // Legend width
    const maxY = window.innerHeight - 100 // Some padding from bottom
    
    setPosition({
      x: Math.max(0, Math.min(newX, maxX)),
      y: Math.max(0, Math.min(newY, maxY))
    })
  }

  // Handle mouse up to stop dragging
  const handleMouseUp = () => {
    setIsDragging(false)
  }

  // Add global mouse event listeners
  useEffect(() => {
    if (isDragging) {
      document.addEventListener('mousemove', handleMouseMove)
      document.addEventListener('mouseup', handleMouseUp)
      document.body.style.cursor = 'grabbing'
      document.body.style.userSelect = 'none'
      
      return () => {
        document.removeEventListener('mousemove', handleMouseMove)
        document.removeEventListener('mouseup', handleMouseUp)
        document.body.style.cursor = ''
        document.body.style.userSelect = ''
      }
    }
  }, [isDragging, dragOffset])

  return (
    <div 
      ref={legendRef}
      className={`fixed z-50 ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}`}
      style={{
        left: `${position.x}px`,
        top: `${position.y}px`,
      }}
    >
      {/* Legend Toggle Button */}
      <button
        onClick={toggleLegend}
        onMouseDown={handleMouseDown}
        className={`bg-white border border-gray-300 rounded-lg px-3 py-2 shadow-lg hover:bg-gray-50 transition-colors flex items-center gap-2 text-sm font-medium text-gray-700 ${
          isDragging ? 'shadow-2xl scale-105' : ''
        }`}
        title="Status Legend (Drag to move)"
      >
        <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
        </svg>
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        Legend
        <svg 
          className={`w-4 h-4 transition-transform ${isOpen ? 'rotate-180' : ''}`} 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
        </svg>
      </button>

      {/* Legend Dropdown */}
      {isOpen && (
        <div className="legend-content absolute top-full left-0 mt-2 bg-white border border-gray-300 rounded-lg shadow-xl w-80 max-h-96 overflow-y-auto">
          <div className="p-4">
            <div className="flex items-center gap-2 mb-3">
              <svg className="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 10h16M4 14h16M4 18h16" />
              </svg>
              <h3 className="text-sm font-semibold text-gray-900">Appointment Status Legend</h3>
              <div className="text-xs text-gray-500 ml-auto">Draggable</div>
            </div>
            
            {/* Appointment Statuses */}
            <div className="space-y-2 mb-4">
              <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider">Appointment Status</h4>
              {Object.values(APPOINTMENT_STATUSES).map((status) => (
                <div key={status.id} className="flex items-center gap-3">
                  <div className="flex flex-col">
                    <div 
                      className="w-8 h-4 bg-gray-100 border border-gray-200 rounded-sm relative"
                    >
                      <div 
                        className="absolute top-0 left-0 right-0 h-1 rounded-t-sm"
                        style={{ backgroundColor: status.color }}
                      />
                    </div>
                    <div className="text-[10px] text-gray-400 text-center mt-0.5">Top Border</div>
                  </div>
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900">{status.name}</div>
                    <div className="text-xs text-gray-500">{status.description}</div>
                  </div>
                </div>
              ))}
            </div>

            {/* Break Types */}
            <div className="space-y-2 border-t border-gray-200 pt-4">
              <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider">Break Types</h4>
              {Object.values(BREAK_TYPES).map((breakType) => (
                <div key={breakType.id} className="flex items-center gap-3">
                  <div 
                    className="w-4 h-4 rounded-full border border-gray-200"
                    style={{ backgroundColor: breakType.color }}
                  />
                  <div className="flex-1">
                    <div className="text-sm font-medium text-gray-900 flex items-center gap-1">
                      <span>{breakType.icon}</span>
                      {breakType.name}
                    </div>
                    <div className="text-xs text-gray-500">Non-bookable time slot</div>
                  </div>
                </div>
              ))}
            </div>

            {/* Additional Info */}
            <div className="border-t border-gray-200 mt-4 pt-4">
              <div className="text-xs text-gray-600 space-y-1">
                <div className="flex items-center gap-2">
                  <svg className="w-3 h-3 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                  </svg>
                  <span>Drag appointments to reschedule</span>
                </div>
                <div className="flex items-center gap-2">
                  <svg className="w-3 h-3 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 8V4m0 0h4M4 4l5 5m11-1V4m0 0h-4m4 0l-5 5M4 16v4m0 0h4m-4 0l5-5m11 5l-5-5m5 5v-4m0 4h-4" />
                  </svg>
                  <span>Resize appointments by dragging edges</span>
                </div>
                <div className="flex items-center gap-2">
                  <svg className="w-3 h-3 text-purple-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                  <span>Completed appointments cannot be moved</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}

export default StatusLegend 