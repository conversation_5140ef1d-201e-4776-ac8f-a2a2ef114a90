# Employee Profile Fix

This document outlines steps to fix the employee profile issues in the chatbook-backend project. These instructions will help you squash all the migrations and recreate employee profiles cleanly.

## 💾 Backup First!

**IMPORTANT:** Always backup your database before making schema changes.

```bash
python manage.py dbbackup
```

## 🔄 Option 1: Complete Migration Reset and Recreation (Recommended)

This approach completely removes the existing employee migrations and creates a fresh migration with the current model structure.

1. Run the management command to squash all employee migrations:

```bash
python manage.py squash_employee_migrations
```

2. Recreate employee profiles with the correct structure:

```bash
python manage.py shell < scripts/data/recreate_employee_profiles.py
```

This approach is ideal if you're able to recreate your employee data from scratch or if you have very few employee profiles.

## 🛠️ Option 2: Manual SQL Fix (Alternate Approach)

If the management command doesn't work or you prefer a more direct approach:

1. Apply the SQL fix directly:

```bash
python manage.py dbshell < fix_employee_schema.sql
```

2. Recreate employee profiles:

```bash
python manage.py shell < scripts/data/recreate_employee_profiles.py
```

## 🔍 Verification

After applying either fix, you can verify that everything is working correctly:

```bash
# Check employee data
python manage.py shell -c "from employees.models import Employee; print(Employee.objects.all())"

# Verify working hours
python manage.py shell -c "from employees.models import EmployeeWorkingHours; print(EmployeeWorkingHours.objects.all())"
```

## ⚠️ Potential Gotchas

- **Foreign Key References**: If other tables reference employees, make sure they're updated to point to the correct employee IDs.
- **Admin Users**: Ensure that admin user accounts exist before recreating employee profiles.
- **Business References**: Make sure the business records exist and have the correct IDs.

If you encounter any issues, you can always restore from your backup. 