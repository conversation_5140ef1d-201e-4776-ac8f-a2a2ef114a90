/**
 * Appointment Management Types
 * Based on iOS CalendarViewModel and AppointmentDetailsViewModel
 */

// Appointment Status Enum (aligned with Django backend)
export const AppointmentStatus = {
  REQUESTED: 'requested',
  CONFIRMED: 'confirmed', 
  ACCEPTED: 'accepted',
  CHECKED_IN: 'checked_in',
  SERVICE_STARTED: 'service_started',
  COMPLETED: 'completed',
  CANCELLED: 'cancelled',
  NO_SHOW: 'no_show'
}

// Status transition rules (Django backend flow)
export const StatusTransitions = {
  [AppointmentStatus.REQUESTED]: [AppointmentStatus.CONFIRMED, AppointmentStatus.CANCELLED],
  [AppointmentStatus.CONFIRMED]: [AppointmentStatus.ACCEPTED, AppointmentStatus.CHECKED_IN, AppointmentStatus.CANCELLED, AppointmentStatus.NO_SHOW],
  [AppointmentStatus.ACCEPTED]: [AppointmentStatus.CHECKED_IN, AppointmentStatus.CANCELLED, AppointmentStatus.NO_SHOW],
  [AppointmentStatus.CHECKED_IN]: [AppointmentStatus.SERVICE_STARTED, AppointmentStatus.CANCELLED],
  [AppointmentStatus.SERVICE_STARTED]: [AppointmentStatus.COMPLETED, AppointmentStatus.CANCELLED],
  [AppointmentStatus.COMPLETED]: [],
  [AppointmentStatus.CANCELLED]: [],
  [AppointmentStatus.NO_SHOW]: []
}

// Customer data structure
export const createCustomer = ({
  id,
  firstName = '',
  lastName = '',
  email = '',
  phone = '',
  notes = '',
  preferredEmployee = null,
  appointmentHistory = []
}) => ({
  id,
  firstName,
  lastName,
  fullName: `${firstName} ${lastName}`.trim(),
  email,
  phone,
  notes,
  preferredEmployee,
  appointmentHistory,
  initials: getCustomerInitials(firstName, lastName)
})

// Service data structure
export const createService = ({
  id,
  name,
  shortName = '',
  description = '',
  duration = 60,
  bufferTime = 0,
  price = 0,
  category = '',
  categoryName = '',
  addOns = [],
  employeeServices = [],
  isActive = true
}) => ({
  id,
  name,
  shortName,
  description,
  duration,
  bufferTime,
  totalDuration: duration + bufferTime,
  price,
  category,
  categoryName,
  addOns,
  employeeServices,
  isActive
})

// Appointment data structure (matches iOS Appointment model)
export const createAppointment = ({
  id,
  customerId,
  customerName = '',
  employeeId,
  employeeName = '',
  startTime,
  endTime = null,
  duration = 60,
  services = [],
  addOns = [],
  status = AppointmentStatus.REQUESTED,
  notes = '',
  cancellationReason = '',
  price = 0,
  source = 'staff_booking',
  notifyCustomer = true
}) => {
  const start = new Date(startTime)
  const end = endTime ? new Date(endTime) : new Date(start.getTime() + duration * 60000)
  
  return {
    id,
    customerId,
    customerName,
    employeeId,
    employeeName,
    startTime: start,
    endTime: end,
    duration,
    totalDuration: services.reduce((total, service) => total + service.duration, 0) + 
                   addOns.reduce((total, addOn) => total + addOn.duration, 0),
    services,
    addOns,
    status,
    notes,
    cancellationReason,
    price,
    totalPrice: calculateTotalPrice(services, addOns, price),
    source,
    notifyCustomer,
    
    // Display properties
    title: customerName || `Customer ${customerId}`,
    start: start.toISOString().slice(0, 16).replace('T', ' '),
    end: end.toISOString().slice(0, 16).replace('T', ' '),
    
    // Computed properties
    get formattedPrice() {
      return formatCurrency(this.totalPrice)
    },
    
    get formattedDuration() {
      return formatDuration(this.totalDuration)
    },
    
    get statusLabel() {
      return getStatusLabel(this.status)
    },
    
    get canTransitionTo() {
      return StatusTransitions[this.status] || []
    }
  }
}

// Service with add-ons structure
export const createServiceWithAddOns = (service, selectedAddOns = []) => ({
  ...service,
  addOns: selectedAddOns,
  totalDuration: service.duration + service.bufferTime + 
                 selectedAddOns.reduce((total, addOn) => total + addOn.duration, 0),
  totalPrice: service.price + selectedAddOns.reduce((total, addOn) => total + addOn.price, 0)
})

// Appointment creation data
export const createAppointmentCreationData = ({
  customerId,
  employeeId,
  startDateTime,
  service,
  addOns = [],
  notes = '',
  notifyCustomer = true,
  priceOverride = null
}) => ({
  customerId,
  employeeId,
  startDateTime,
  service,
  addOns,
  notes,
  notifyCustomer,
  priceOverride,
  totalDuration: service.totalDuration + addOns.reduce((total, addOn) => total + addOn.duration, 0),
  totalPrice: priceOverride || (service.price + addOns.reduce((total, addOn) => total + addOn.price, 0))
})

// Utility functions
export const getCustomerInitials = (firstName, lastName) => {
  const first = firstName?.charAt(0)?.toUpperCase() || ''
  const last = lastName?.charAt(0)?.toUpperCase() || ''
  return `${first}${last}` || '?'
}

export const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount)
}

export const formatDuration = (minutes) => {
  const hours = Math.floor(minutes / 60)
  const mins = minutes % 60
  
  if (hours > 0) {
    return mins > 0 ? `${hours}h ${mins}m` : `${hours}h`
  }
  return `${mins}m`
}

export const getStatusLabel = (status) => {
  const labels = {
    [AppointmentStatus.PENDING]: 'Pending',
    [AppointmentStatus.CONFIRMED]: 'Confirmed',
    [AppointmentStatus.ACCEPTED]: 'Accepted',
    [AppointmentStatus.IN_PROGRESS]: 'In Progress',
    [AppointmentStatus.COMPLETED]: 'Completed',
    [AppointmentStatus.CANCELLED]: 'Cancelled',
    [AppointmentStatus.NO_SHOW]: 'No Show'
  }
  return labels[status] || status
}

export const canTransitionToStatus = (currentStatus, newStatus) => {
  const allowedTransitions = StatusTransitions[currentStatus] || []
  return allowedTransitions.includes(newStatus)
}

export const calculateTotalPrice = (services = [], addOns = [], priceOverride = null) => {
  if (priceOverride !== null) return priceOverride
  
  const serviceTotal = services.reduce((total, service) => total + (service.price || 0), 0)
  const addOnTotal = addOns.reduce((total, addOn) => total + (addOn.price || 0), 0)
  return serviceTotal + addOnTotal
}

// Validation functions
export const validateAppointment = (appointmentData) => {
  const errors = []
  
  if (!appointmentData.customerId) {
    errors.push('Customer is required')
  }
  
  if (!appointmentData.employeeId) {
    errors.push('Employee is required')
  }
  
  if (!appointmentData.service) {
    errors.push('Service is required')
  }
  
  if (!appointmentData.startDateTime) {
    errors.push('Start time is required')
  }
  
  return {
    isValid: errors.length === 0,
    errors
  }
}

export const validateStatusTransition = (currentStatus, newStatus) => {
  if (!canTransitionToStatus(currentStatus, newStatus)) {
    return {
      isValid: false,
      error: `Cannot transition from ${getStatusLabel(currentStatus)} to ${getStatusLabel(newStatus)}`
    }
  }
  
  return { isValid: true }
} 