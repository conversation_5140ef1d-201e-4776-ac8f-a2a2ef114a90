import React, { useState } from 'react'
import ToggleSwitch from './ToggleSwitch'

/**
 * Appointment Configuration Tab Content
 */
const AppointmentTab = ({ bookingRules, updateRule, convertHoursToDisplayUnit, convertDisplayUnitToHours }) => {
  const [settings, setSettings] = useState({
    showPhoneNumber: true
  })

  // Convert min_hours_before to display format
  const getLeadTimeDisplay = (hours) => {
    if (hours < 1) {
      return { value: Math.round(hours * 60), unit: 'mins' }
    } else {
      return convertHoursToDisplayUnit(hours)
    }
  }
  
  const leadTimeDisplay = bookingRules ? getLeadTimeDisplay(bookingRules.minHoursBefore) : { value: 1, unit: 'hours' }
  
  // Handle lead time change
  const handleLeadTimeChange = (value, unit) => {
    let hours
    if (unit === 'mins') {
      hours = parseInt(value) / 60 // Convert minutes to hours
    } else {
      hours = convertDisplayUnitToHours(parseInt(value), unit)
    }
    updateRule('minHoursBefore', hours)
  }

  // Handle appointment interval change  
  const handleIntervalChange = (value) => {
    updateRule('appointmentInterval', parseInt(value))
  }

  return (
  <div className="space-y-6">
    <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
      <h3 className="text-lg font-medium text-blue-900 mb-2">Appointment Settings</h3>
      <p className="text-blue-700">Configure general appointment booking rules and restrictions.</p>
    </div>
    
    <div className="space-y-4">
      {/* Appointment Lead Time */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Appointment Lead Time</h4>
        <p className="text-sm text-gray-600 mb-3">
          Limit how soon your customer can make an appointment with you online, giving you time to react to the request properly.
        </p>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Minimum lead time</label>
          <div className="flex items-center space-x-2">
            <input 
              type="number" 
              value={leadTimeDisplay.value} 
              min="0" 
              onChange={(e) => handleLeadTimeChange(e.target.value, leadTimeDisplay.unit)}
              className="w-20 border border-gray-300 rounded-lg px-3 py-2 text-center"
            />
            <select 
              value={leadTimeDisplay.unit === 'hours' ? 'hrs' : leadTimeDisplay.unit}
              onChange={(e) => {
                const unit = e.target.value === 'hrs' ? 'hours' : e.target.value
                handleLeadTimeChange(leadTimeDisplay.value, unit)
              }}
              className="border border-gray-300 rounded-lg px-3 py-2"
            >
              <option value="mins">mins</option>
              <option value="hrs">hrs</option>
              <option value="days">days</option>
              <option value="weeks">weeks</option>
            </select>
          </div>
        </div>
      </div>

      {/* Appointment Search Limit */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Appointment Search Limit</h4>
        <p className="text-sm text-gray-600 mb-3">
          Specify the number of available appointments your customers will see when they are booking an appointment online with your business.
        </p>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Maximum appointments to display</label>
          <div className="flex items-center space-x-2">
            <input 
              type="number" 
              defaultValue="30" 
              min="1" 
              className="w-20 border border-gray-300 rounded-lg px-3 py-2 text-center"
            />
            <button 
              type="button" 
              className="w-8 h-8 border border-gray-300 rounded-lg flex items-center justify-center hover:bg-gray-50"
              onClick={() => {
                const input = document.querySelector('input[defaultValue="30"]');
                if (input) input.value = Math.max(1, parseInt(input.value) - 1);
              }}
            >
              −
            </button>
            <button 
              type="button" 
              className="w-8 h-8 border border-gray-300 rounded-lg flex items-center justify-center hover:bg-gray-50"
              onClick={() => {
                const input = document.querySelector('input[defaultValue="30"]');
                if (input) input.value = parseInt(input.value) + 1;
              }}
            >
              +
            </button>
          </div>
        </div>
      </div>

      {/* Search Appointment Interval */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Search Appointment Interval</h4>
        <p className="text-sm text-gray-600 mb-3">
          Set the interval between available online appointments shown to customers.
        </p>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Time interval</label>
          <select 
            value={bookingRules?.appointmentInterval || 15}
            onChange={(e) => handleIntervalChange(e.target.value)}
            className="w-32 border border-gray-300 rounded-lg px-3 py-2"
          >
            <option value="15">15 min</option>
            <option value="30">30 min</option>
            <option value="45">45 min</option>
            <option value="60">1 hr</option>
            <option value="90">1.5 hrs</option>
            <option value="120">2 hrs</option>
          </select>
        </div>
      </div>

      {/* Display Business Phone Number */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Phone Number Display</h4>
        <p className="text-sm text-gray-600 mb-3">
          Show your business phone number when appointments are unavailable to help customers contact you directly.
        </p>
        <ToggleSwitch
          label="Display business phone number when appointments are unavailable"
          checked={settings.showPhoneNumber}
          onChange={(checked) => setSettings(prev => ({ ...prev, showPhoneNumber: checked }))}
        />
      </div>

      {/* Buffer Time Preference */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Buffer Time Preference</h4>
        <p className="text-sm text-gray-600 mb-3">
          Choose how cleanup time is applied when multiple services are booked.
        </p>
        <div className="space-y-3">
          <div className="flex items-center">
            <input
              type="radio"
              id="buffer-longest"
              name="buffer-preference"
              value="longest"
              className="mr-2"
              defaultChecked
            />
            <label htmlFor="buffer-longest" className="text-sm font-medium text-gray-700">
              Add longest cleanup time at the end of multiple booked services
            </label>
          </div>
          <div className="flex items-center">
            <input
              type="radio"
              id="buffer-individual"
              name="buffer-preference"
              value="individual"
              className="mr-2"
            />
            <label htmlFor="buffer-individual" className="text-sm font-medium text-gray-700">
              Apply cleanup time immediately after each individual service
            </label>
          </div>
        </div>
      </div>
    </div>
  </div>
  )
}

export default AppointmentTab 