# Customer Booking Frontend - Repository Structure Documentation

## Tech Stack
- **React 22**: Main UI library
- **Vite 6.x**: Build tool and development server
- **Tailwind CSS 3.x**: Utility-first CSS framework
- **React Router 6.x**: Client-side routing
- **Zustand**: State management
- **React Query**: Server state management
- **Axios**: HTTP client for API requests

## 📋 **Quick Start Guide**

For the complete booking flow implementation and best practices, see:
**[BOOKING_FLOW_GUIDE.md](./BOOKING_FLOW_GUIDE.md)**

This guide covers:
- Clean booking flow architecture
- Centralized navigation logic
- Consent form management
- Testing procedures
- Debugging tips

## Project Setup

### Initial Setup
```bash
npm create vite@latest customer-booking-frontend -- --template react
cd customer-booking-frontend
npm install
```

### Dependencies
Install these core dependencies:
```bash
npm install react-router-dom axios @heroicons/react react-hot-toast
```

### Dev Dependencies
```bash
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p
```

## File Structure

### Root Configuration Files
- `package.json`: Define dependencies and scripts
- `vite.config.js`: Configure Vite with proxy settings for API
- `tailwind.config.js`: Configure Tailwind theme and extensions
- `postcss.config.js`: Configure PostCSS plugins
- `.eslintrc.js`: ESLint configuration
- `.env`: Environment variables (create this file)
- `index.html`: Entry HTML file

### Source Directory Structure
```
src/
├── assets/            # Static assets like images, icons
├── components/        # Reusable UI components
│   ├── layout/        # Layout components
│   ├── ui/            # UI components like buttons, inputs
│   └── auth/          # Authentication related components
├── features/          # Feature-specific code
│   ├── auth/          # Authentication feature
│   │   ├── components/
│   │   ├── hooks/
│   │   └── services/
│   ├── booking/       # Booking feature
│   │   ├── components/
│   │   ├── hooks/
│   │   └── services/
│   ├── customers/     # Customer management feature
│   │   ├── components/
│   │   ├── hooks/
│   │   └── services/
│   └── settings/      # Settings feature
│       ├── components/
│       └── services/
├── hooks/             # Custom React hooks
├── pages/             # Page components
│   ├── Auth/          # Authentication pages
│   ├── Booking/       # Booking pages
│   ├── Customers/     # Customer pages
│   └── Settings/      # Settings pages
├── styles/            # Global styles
├── utils/             # Utility functions
├── App.jsx            # Main App component with routes
└── main.jsx          # Entry point
```

## Key Files Implementation

### vite.config.js
```javascript
import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  plugins: [react()],
  server: {
    port: 3000,
    open: true,
    proxy: {
      '/api': 'http://localhost:8000'
    }
  },
  build: {
    outDir: 'dist',
    sourcemap: true,
  },
})
```

### tailwind.config.js
```javascript
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
        },
      },
    },
  },
  plugins: [],
}
```

### src/styles/index.css
```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gray-50 text-gray-900;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md transition-colors;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700;
  }
  
  .container {
    @apply mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }
}
```

### src/main.jsx
```javascript
import React from 'react'
import ReactDOM from 'react-dom/client'
import { BrowserRouter } from 'react-router-dom'
import App from './App'
import './styles/index.css'

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <BrowserRouter>
      <App />
    </BrowserRouter>
  </React.StrictMode>,
)
```

### src/hooks/useApi.js
```javascript
import { useState } from 'react'
import axios from 'axios'

// Base API configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api',
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add request interceptor for authentication
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

/**
 * Custom hook for API interactions
 * @param {string} endpoint - API endpoint
 * @returns {Object} - API interaction methods and state
 */
export function useApi(endpoint) {
  const [data, setData] = useState(null)
  const [error, setError] = useState(null)
  const [loading, setLoading] = useState(false)

  // Fetch data from the API
  const fetchData = async (params = {}) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await api.get(endpoint, { params })
      setData(response.data)
      return response.data
    } catch (err) {
      setError(err.response?.data || { message: err.message })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Create a new resource
  const createData = async (payload) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await api.post(endpoint, payload)
      return response.data
    } catch (err) {
      setError(err.response?.data || { message: err.message })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Update an existing resource
  const updateData = async (id, payload) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await api.put(`${endpoint}/${id}`, payload)
      return response.data
    } catch (err) {
      setError(err.response?.data || { message: err.message })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Delete a resource
  const deleteData = async (id) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await api.delete(`${endpoint}/${id}`)
      return response.data
    } catch (err) {
      setError(err.response?.data || { message: err.message })
      return null
    } finally {
      setLoading(false)
    }
  }

  return {
    data,
    error,
    loading,
    fetchData,
    createData,
    updateData,
    deleteData,
  }
}

export default useApi
```

### src/components/RequireAuth.jsx
```javascript
import { Navigate, useLocation } from 'react-router-dom'

function RequireAuth({ children }) {
  const location = useLocation()
  const token = localStorage.getItem('auth_token')

  if (!token) {
    // Redirect unauthenticated users to login, preserve intended path
    return <Navigate to="/auth/login" state={{ from: location }} replace />
  }

  return children
}

export default RequireAuth
```

### src/components/Layout.jsx
```javascript
import { Outlet } from 'react-router-dom'
import Header from './Header'

function Layout() {
  return (
    <div className="flex flex-col h-screen">
      <Header />
      <main className="flex-1 min-h-0 overflow-hidden">
        <Outlet />
      </main>
    </div>
  )
}

export default Layout
```

### src/App.jsx
```javascript
import { Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/Layout';
import RequireAuth from './components/RequireAuth';

// Auth
import Login from './pages/Auth/Login';
import Signup from './pages/Auth/Signup';

// 404
import NotFound from './pages/NotFound';

function App() {
  return (
    <Routes>
      {/* Main app routes (require auth + common layout) */}
      <Route path="/" element={<RequireAuth><Layout /></RequireAuth>}>
        {/* Add your routes here as you implement pages */}
        
        {/* In-app 404 */}
        <Route path="*" element={<NotFound />} />
      </Route>

      {/* Auth (outside RequireAuth / Layout) */}
      <Route path="auth/login" element={<Login />} />
      <Route path="auth/signup" element={<Signup />} />
    </Routes>
  );
}

export default App;
```

## Context Providers

### Authentication Context
Create a context for managing authentication state:

```javascript
// src/features/auth/AuthContext.jsx
import { createContext, useContext, useState, useEffect } from 'react'

const AuthContext = createContext()

export function AuthProvider({ children }) {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Check if user is logged in on initial load
    const token = localStorage.getItem('auth_token')
    if (token) {
      // You might want to validate the token or fetch user data here
      setUser({ token })
    }
    setLoading(false)
  }, [])

  const login = (userData) => {
    localStorage.setItem('auth_token', userData.token)
    setUser(userData)
  }

  const logout = () => {
    localStorage.removeItem('auth_token')
    setUser(null)
  }

  return (
    <AuthContext.Provider value={{ 
      user, 
      loading, 
      login, 
      logout,
      isAuthenticated: !!user 
    }}>
      {children}
    </AuthContext.Provider>
  )
}

export function useAuth() {
  return useContext(AuthContext)
}
```

### Booking Context
```javascript
// src/features/booking/BookingContext.jsx
import { createContext, useContext, useState } from 'react'

const BookingContext = createContext()

export function BookingProvider({ children }) {
  const [bookings, setBookings] = useState([])
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState(null)

  // Add your booking-related functions here

  return (
    <BookingContext.Provider value={{ 
      bookings, 
      loading, 
      error 
    }}>
      {children}
    </BookingContext.Provider>
  )
}

export function useBooking() {
  return useContext(BookingContext)
}
```

## Recommended Page Structure

As you start implementing pages, consider this structure:

```
src/pages/
├── Auth/
│   ├── Login.jsx
│   └── Signup.jsx
├── Booking/
│   ├── BookingCalendar.jsx
│   ├── BookingConfirmation.jsx
│   └── BookingForm.jsx
├── Customers/
│   ├── CustomerProfile.jsx
│   └── CustomerList.jsx
├── Services/
│   ├── ServiceList.jsx
│   └── ServiceDetail.jsx
└── Settings/
    ├── BusinessSettings.jsx
    └── UserSettings.jsx
```

## Component Organization

For reusable components:

```
src/components/
├── layout/
│   ├── Header.jsx
│   ├── Sidebar.jsx
│   └── Footer.jsx
├── ui/
│   ├── Button.jsx
│   ├── Input.jsx
│   ├── Modal.jsx
│   └── Card.jsx
└── booking/
    ├── BookingCard.jsx
    ├── TimeSlotPicker.jsx
    └── ServiceSelector.jsx
```

## Environment Variables

Create a `.env` file in the root directory:

```
VITE_API_URL=http://localhost:8000/api
```

## Getting Started

1. Clone this repository structure
2. Install dependencies: `npm install`
3. Start development server: `npm run dev`

## Development Workflow

1. Set up the base structure as outlined above
2. Implement authentication flow first
3. Create basic layout components
4. Implement core booking features
5. Add customer management features
6. Implement settings and additional features

## Best Practices

- Use consistent naming conventions (PascalCase for components, camelCase for functions)
- Organize code by feature rather than by type
- Use context for global state management
- Keep components small and focused
- Use custom hooks for reusable logic
- Follow the Tailwind CSS utility-first approach 