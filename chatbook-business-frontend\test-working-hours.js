// Test script to verify working hours API format
const sampleWorkingHours = {
  "working_hours": [
    {
      "id": 12,
      "day": "friday",
      "start_time": "10:30",
      "end_time": "17:00",
      "is_active": true
    },
    {
      "id": 8,
      "day": "monday",
      "start_time": "10:00",
      "end_time": "18:00",
      "is_active": true
    },
    {
      "id": 13,
      "day": "saturday",
      "start_time": "10:00",
      "end_time": "18:00",
      "is_active": true
    },
    {
      "id": 14,
      "day": "sunday",
      "start_time": "12:00",
      "end_time": "15:00",
      "is_active": true
    },
    {
      "id": 11,
      "day": "thursday",
      "start_time": "09:00",
      "end_time": "18:00",
      "is_active": true
    },
    {
      "id": 9,
      "day": "tuesday",
      "start_time": "10:00",
      "end_time": "18:00",
      "is_active": true
    },
    {
      "id": 10,
      "day": "wednesday",
      "start_time": "10:00",
      "end_time": "18:00",
      "is_active": true
    }
  ]
}

// Test transformation function
function transformWorkingHoursResponse(backendData) {
  console.log('🔄 Transforming backend working hours to frontend format:', backendData)
  
  const workingHours = {
    monday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
    tuesday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
    wednesday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
    thursday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
    friday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
    saturday: { isWorking: false, startTime: '09:00', endTime: '17:00' },
    sunday: { isWorking: false, startTime: '09:00', endTime: '17:00' }
  }
  
  if (backendData.working_hours) {
    backendData.working_hours.forEach(dayHours => {
      if (workingHours[dayHours.day]) {
        workingHours[dayHours.day] = {
          isWorking: dayHours.is_active,
          startTime: dayHours.start_time,
          endTime: dayHours.end_time
        }
        console.log(`📅 Transformed ${dayHours.day}:`, {
          isWorking: dayHours.is_active,
          startTime: dayHours.start_time,
          endTime: dayHours.end_time
        })
      }
    })
  }
  
  return workingHours
}

// Test the transformation
const transformedHours = transformWorkingHoursResponse(sampleWorkingHours)
console.log('✅ Final transformed working hours:', transformedHours)

// Test working hours validation
function testWorkingHoursValidation() {
  console.log('\n🧪 Testing working hours validation...')
  
  // Test cases
  const testCases = [
    { day: 'monday', hour: 9, expected: false }, // Before 10:00 start
    { day: 'monday', hour: 10, expected: true }, // At 10:00 start
    { day: 'monday', hour: 15, expected: true }, // During working hours
    { day: 'monday', hour: 18, expected: false }, // At 18:00 end
    { day: 'friday', hour: 10, expected: false }, // Before 10:30 start
    { day: 'friday', hour: 11, expected: true }, // After 10:30 start
    { day: 'friday', hour: 17, expected: false }, // At 17:00 end
    { day: 'saturday', hour: 12, expected: true }, // Working Saturday
    { day: 'sunday', hour: 13, expected: true }, // Working Sunday
    { day: 'sunday', hour: 16, expected: false }, // After 15:00 end
  ]
  
  testCases.forEach(({ day, hour, expected }) => {
    const dayHours = transformedHours[day]
    let isWorking = false
    
    if (dayHours && dayHours.isWorking) {
      const startHour = parseInt(dayHours.startTime.split(':')[0])
      const endHour = parseInt(dayHours.endTime.split(':')[0])
      isWorking = hour >= startHour && hour < endHour
    }
    
    const result = isWorking === expected ? '✅' : '❌'
    console.log(`${result} ${day} ${hour}:00 - Expected: ${expected}, Got: ${isWorking}`)
  })
}

testWorkingHoursValidation()
