# Chatbook API Documentation

## Overview

This document provides comprehensive documentation for the Chatbook API. The API follows RESTful principles and uses JSON for data exchange.

## Base URL

All API endpoints are prefixed with:
```
/api/v1/
```

## Authentication

### Login
- **Endpoint**: `POST /auth/login/`
- **Description**: Authenticate and obtain access token
- **Request Body**:
  ```json
  {
    "email": "<EMAIL>",
    "password": "password"
  }
  ```
- **Response**:
  ```json
  {
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
  ```
  or with MFA:
  ```json
  {
    "mfa_required": true,
    "temp_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
  ```

### MFA Verification
- **Endpoint**: `POST /auth/verify-mfa/`
- **Description**: Verify MFA code and obtain access token
- **Request Body**:
  ```json
  {
    "token": "temporary_token",
    "code": "123456"
  }
  ```
- **Response**: Same as login response

### Refresh Token
- **Endpoint**: `POST /auth/refresh/`
- **Description**: Obtain new access token using refresh token
- **Request Body**:
  ```json
  {
    "refresh": "refresh_token"
  }
  ```
- **Response**:
  ```json
  {
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9..."
  }
  ```

## User Management

### User Profile
- **Endpoint**: `GET /users/profile/`
- **Description**: Get current user's profile information
- **Authentication**: Required
- **Response**: User profile details

### User Settings
- **Endpoint**: `GET /users/settings/`
- **Description**: Get user settings
- **Authentication**: Required
- **Response**: User settings

### Update User Settings
- **Endpoint**: `PATCH /users/settings/`
- **Description**: Update user settings
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "email_notifications": true,
    "sms_notifications": false
  }
  ```
- **Response**: Updated settings

## Employee Endpoints

### Current Employee
- **Endpoint**: `GET /employees/me/`
- **Description**: Get current employee's details
- **Authentication**: Required
- **Response**: Employee details including ID, name, role, etc.

### Employee Working Hours
- **Endpoint**: `GET /employees/me/working-hours/`
- **Description**: Get employee's working hours
- **Authentication**: Required
- **Response**: Working hours by day

### Update Working Hours
- **Endpoint**: `POST /employees/me/working-hours/`
- **Description**: Update employee's working hours
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "working_hours": {
      "monday": {
        "start": "09:00",
        "end": "17:00",
        "is_active": true
      },
      "tuesday": {
        "start": "09:00",
        "end": "17:00",
        "is_active": true
      }
      // ...other days
    }
  }
  ```
- **Response**: Updated working hours

### Employee Permissions
- **Endpoint**: `GET /employees/me/permissions/`
- **Description**: Get employee's permissions based on access level
- **Authentication**: Required
- **Response**:
  ```json
  {
    "permissions": {
      "view_appointments": true,
      "edit_appointments": true,
      "manage_services": false,
      "manage_employees": false
    }
  }
  ```

### Employee Calendar Configuration
- **Endpoint**: `GET /employees/me/calendar-configs/`
- **Description**: Get employee's calendar configuration
- **Authentication**: Required
- **Response**:
  ```json
  {
    "id": 1,
    "week_start_day": "Monday",
    "default_view": "Week",
    "calendar_resolution": 15,
    "created_at": "2023-05-17T12:30:45Z",
    "updated_at": "2023-05-17T12:30:45Z"
  }
  ```

### Update Calendar Configuration
- **Endpoint**: `POST /employees/me/calendar-configs/`
- **Description**: Update employee's calendar configuration
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "week_start_day": "Monday",
    "default_view": "Week",
    "calendar_resolution": 15
  }
  ```
- **Response**: Updated configuration

### Employee Appointments
- **Endpoint**: `GET /employees/me/appointments/`
- **Description**: Get appointments for the current employee
- **Authentication**: Required
- **Query Parameters**:
  - `from`: Start date (YYYY-MM-DD)
  - `to`: End date (YYYY-MM-DD)
- **Response**: List of appointments

## Appointments

### List Appointments
- **Endpoint**: `GET /appointments/`
- **Description**: Get list of appointments
- **Authentication**: Required
- **Query Parameters**:
  - `business_id`: Filter appointments by business ID (optional)
  - `customer_id`: Filter appointments by customer ID (optional)
- **Response**: List of appointments

### Get Appointment
- **Endpoint**: `GET /appointments/{id}/`
- **Description**: Get appointment details
- **Authentication**: Required
- **Response**: Detailed appointment information

### Create Appointment
- **Endpoint**: `POST /appointments/`
- **Description**: Create a new appointment with nested services and add-ons
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "customer": 1,
    "employee": 1,
    "start_time": "2023-09-15T10:30:00Z",
    "status": "requested",
    "payment_status": "unpaid",
    "source": "online",
    "notes_from_customer": "Please use the organic products",
    "appointment_services": [
      {
        "service": 1,
        "quantity": 1,
        "buffer_time": 15,
        "notes": "Use the special shampoo"
      }
    ],
    "appointment_add_ons": [
      {
        "add_on": 2,
        "add_on_price": 25.00,
        "duration": 15
      }
    ]
  }
  ```
- **Response**: Created appointment
- **Business Association**: Appointments are automatically associated with a business through both the employee and customer. The API validates that the employee and customer belong to the same business.

### Update Appointment
- **Endpoint**: `PUT /appointments/{id}/`
- **Description**: Update an appointment with nested services and add-ons
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "customer": 1,
    "employee": 1,
    "start_time": "2023-09-15T10:30:00Z",
    "status": "confirmed",
    "payment_status": "unpaid",
    "notes_from_customer": "Updated notes",
    "appointment_services": [
      {
        "service": 1,
        "quantity": 1,
        "buffer_time": 20,
        "notes": "Updated service notes"
      },
      {
        "service": 2,
        "quantity": 1,
        "buffer_time": 10,
        "notes": "Added a second service"
      }
    ],
    "appointment_add_ons": [
      {
        "add_on": 3,
        "add_on_price": 35.00,
        "duration": 20
      }
    ]
  }
  ```
- **Response**: Updated appointment
- **Automatic Status Changes**:
  - If a `confirmed` appointment is rescheduled to a future date/time, the status automatically changes to `accepted` to allow the client to confirm the new time

### Partial Update Appointment
- **Endpoint**: `PATCH /appointments/{id}/`
- **Description**: Partially update an appointment
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "status": "confirmed",
    "appointment_services": [
      {
        "service": 1,
        "quantity": 1,
        "buffer_time": 25,
        "notes": "Only updating services"
      }
    ]
  }
  ```
- **Response**: Updated appointment

### Cancel Appointment
- **Endpoint**: `DELETE /appointments/{id}/`
- **Description**: Cancel an appointment
- **Authentication**: Required
- **Response**: 204 No Content

### Available Time Slots
- **Endpoint**: `GET /businesses/{business_id}/appointments/available-times/`
- **Description**: Get available time slots for booking with automatic smart booking rules
- **Path Parameters**:
  - `business_id`: ID of the business (required)
- **Query Parameters**:
  - `date`: Date in YYYY-MM-DD format (required)
  - `service_id`: ID of the requested service (required)
  - `employee_id`: ID of a specific employee (optional)
- **Response**:
  ```json
  {
    "Employee Name": [
      "2025-06-11T17:00:00+00:00",
      "2025-06-11T17:30:00+00:00",
      "2025-06-11T18:00:00+00:00"
    ]
  }
  ```
  **Note**: Only available time slots are returned as ISO 8601 datetime strings. Unavailable slots are omitted.

### Service Suggestions for Business Customers
- **Endpoint**: `GET /business-customers/me/{customer_id}/service-suggestions/?requested_date=YYYY-MM-DD`
- **Description**: Get intelligent service suggestions for a specific customer based on their appointment history and requested appointment date. The backend automatically calculates all necessary data from the customer's appointment history and includes employee availability for suggested services.
- **Authentication**: Required (uses current user's business)
- **Path Parameters**:
  - `customer_id` (required): ID of the customer
- **Query Parameters**:
  - `requested_date` (optional): Date for the appointment in YYYY-MM-DD format (today or future). Defaults to today if not provided.
- **Response**:
  ```json
  {
    "customer_id": 1,
    "requested_date": "2025-06-27T00:00:00-07:00",
    "last_appointment": {
      "id": 1,
      "date": "2025-06-20T17:36:00-07:00",
      "employee_id": 12,
      "employee_name": "Caitlin",
      "appointment_services": [
        {
          "id": 1,
          "short_name": "Fullset C01-C02",
          "style_group": "Classic"
        }
      ],
      "appointment_add_ons": [
        {
          "id": 42,
          "short_name": "Bottom Lash"
        }
      ],
      "days_since": 7
    },
    "suggestions": [
      {
        "suggestion_id": 5,
        "suggested_appointment_services": {
          "id": 5,
          "short_name": "Classic 2-week refill",
          "style_group": "Classic",
          "color": "#C3BFDF"
        },
        "suggested_appointment_add_ons": [
          {
            "id": 1,
            "short_name": "Bottom Lash",
            "color": "#FFFFFF"
          }
        ],
        "availability": [
          {
            "employee_id": 12,
            "employee_name": "Caitlin",
            "slots": [
              "2025-07-01T10:00:00-07:00",
              "2025-07-02T14:00:00-07:00"
            ]
          },
          {
            "employee_id": 15,
            "employee_name": "Carol",
            "slots": [
              "2025-07-01T11:00:00-07:00",
              "2025-07-02T09:00:00-07:00"
            ]
          }
        ]
      }
    ]
  }
  ```
- **Validation**:
  - Date cannot be in the past (today or future allowed)
  - Customer must belong to the current user's business
  - Date format must be YYYY-MM-DD
- **Features**:
  - **Service Suggestions**: Based on lash service suggestion rules that consider the customer's last service style group and time elapsed
  - **Addon Suggestions**: Based on addon suggestion rules that consider new client status and timing
  - **Intelligent Timing**: Suggests 2-week refills (0-16 days), 3-week refills (17-23 days), 4-week refills (24-30 days), or fullsets (>30 days)
  - **Style Group Continuity**: Maintains the same style group (Classic, Styling, Volume, Real Mink) for service progression
  - **No History Handling**: Returns appropriate message when customer has no completed appointments

## Services

### Service Categories
- **Endpoint**: `GET /service-categories/`
- **Description**: Get list of service categories
- **Response**: List of service categories

### Services List
- **Endpoint**: `GET /services/`
- **Description**: Get list of services
- **Response**: List of services

### Service Detail
- **Endpoint**: `GET /services/{id}/`
- **Description**: Get service details
- **Response**: Service details

### Service Employees
- **Endpoint**: `GET /services/{id}/employees/`
- **Description**: Get employees offering this service
- **Response**: List of employees

### Create Service
- **Endpoint**: `POST /services/`
- **Description**: Create a new service
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "business": 1,
    "category": 1,
    "name": "New Service",
    "description": "Service description",
    "price": "99.99",
    "duration": 60,
    "is_active": true
  }
  ```
- **Response**: Created service

### Update Service
- **Endpoint**: `PUT /services/{id}/`
- **Description**: Update a service
- **Authentication**: Required
- **Response**: Updated service

### Delete Service
- **Endpoint**: `DELETE /services/{id}/`
- **Description**: Delete a service
- **Authentication**: Required
- **Response**: 204 No Content

## Employee Services

### List Employee Services
- **Endpoint**: `GET /employee-services/`
- **Description**: Get all employee services
- **Response**: List of employee services

### Filter by Employee
- **Endpoint**: `GET /employee-services/?employee_id={id}`
- **Description**: Get services assigned to an employee
- **Response**: List of services

### Filter by Service
- **Endpoint**: `GET /employee-services/?service_id={id}`
- **Description**: Get employees offering a specific service
- **Response**: List of employees

### Employee Service Detail
- **Endpoint**: `GET /employee-services/{id}/`
- **Description**: Get details of a specific employee service
- **Response**: Employee service details

### Create Employee Service
- **Endpoint**: `POST /employee-services/`
- **Description**: Create a new employee service relationship
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "business": 1,
    "employee": 1,
    "service": 1,
    "custom_price": 85.00,
    "custom_duration": 45,
    "is_active": true
  }
  ```
- **Response**: Created employee service

### Get Employee's Offered Services
- **Endpoint**: `GET /employees/{id}/services/`
- **Description**: Get all services offered by a specific employee
- **Response**: List of services with pricing and duration

### Get Employee's Service Add-ons
- **Endpoint**: `GET /employees/{id}/service-addons/`
- **Description**: Get all add-ons that an employee can offer
- **Response**: List of add-ons

## Stylist Level Services

### List Stylist Level Services
- **Endpoint**: `GET /stylist-level-services/`
- **Description**: Get all stylist level services configurations
- **Response**: List of stylist level services

### Filter by Service
- **Endpoint**: `GET /stylist-level-services/?service_id={id}`
- **Description**: Get all stylist level services for a specific service
- **Response**: List of stylist level services

### Stylist Level Service Detail
- **Endpoint**: `GET /stylist-level-services/{id}/`
- **Description**: Get details of a specific stylist level service
- **Response**: Stylist level service details

### Create Stylist Level Service
- **Endpoint**: `POST /stylist-level-services/`
- **Description**: Create a new stylist level service configuration
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "business": 1,
    "service": 1,
    "stylist_level": "Junior",
    "price": 70.00,
    "duration": 45,
    "is_active": true
  }
  ```
- **Response**: Created stylist level service

## Business Customers

### List Customers
- **Endpoint**: `GET /business-customers/?business_id={id}`
- **Description**: List all customers for a business
- **Authentication**: Required
- **Response**: List of customers

### List My Business Customers
- **Endpoint**: `GET /business-customers/me/`
- **Description**: List all customers for the current user's business
- **Authentication**: Required
- **Response**: List of customers

### Search Customers
- **Endpoint**: `GET /business-customers/?business_id={id}&q={search}`
- **Description**: Search customers by name, email, or phone
- **Authentication**: Required
- **Response**: List of matching customers

### Create Business Customer
- **Endpoint**: `POST /business-customers/?business_id={id}`
- **Description**: Create a business-customer relationship
- **Authentication**: Required
- **Request Body**:
  ```json
  {
    "customer_id": 1,
    "notes": "Customer notes here",
    "loyalty_points": 0,
    "opt_in_marketing": true,
    "email_reminders": true,
    "sms_reminders": true,
    "tag_ids": [1, 2, 3]
  }
  ```
- **Response**: Created business customer

### Get Business Customer
- **Endpoint**: `GET /business-customers/{id}/?business_id={id}`
- **Description**: Get a specific business customer
- **Authentication**: Required
- **Response**: Business customer details

## Backend-For-Frontend

### App Bootstrap
- **Endpoint**: `GET /app/bootstrap/`
- **Description**: Get all essential data for the app in a single call
- **Authentication**: Required
- **Response**: Combined data including profile, working hours, permissions, calendar config, and more

## Timezone Handling

The Chatbook API handles all date and time values in the following way:

### Storage
- All datetime values are stored in UTC in the database
- Django automatically converts local times to UTC when saving to the database

### API Responses
- All datetime values in API responses are returned in ISO 8601 format with timezone offset (e.g., "2025-06-11T17:00:00+00:00")
- The "+00:00" indicates UTC timezone (equivalent to 'Z' but more explicit)
- Example: "2025-06-11T17:00:00+00:00" means June 11, 2025 at 5:00 PM UTC

### Business Timezone
- Each business has a configured timezone setting in the OnlineBookingRules model
- The `bootstrap` endpoint returns the business's timezone as `business_timezone`
- When displaying times to users, clients should convert UTC times to the business's timezone
- Example: If a business is in Los Angeles (America/Los_Angeles), a "2025-06-03T18:00:00Z" appointment would be at 11:00 AM PDT (during daylight saving time) in the business's local time

### Creating and Updating Appointments
- When creating or updating appointments, times should be sent in UTC with timezone offset format
- Example: To create an appointment for 2:00 PM in Los Angeles, convert to UTC (9:00 PM during standard time, 10:00 PM during daylight saving time) and send as "2025-06-03T21:00:00+00:00" or "2025-06-03T22:00:00+00:00"

### Available Times
- The `available-times` endpoint returns available time slots as ISO 8601 datetime strings in UTC
- Only available time slots are returned (no unavailable slots)
- Format: "2025-06-11T17:00:00+00:00" (full datetime, not just time)
- **Note**: The old endpoint `/appointments/available-times/` is deprecated and returns HTTP 410 GONE. Use `/businesses/{business_id}/appointments/available-times/` instead.

## Data Models

### Appointment
- `id`: BigAutoField (primary key)
- `customer`: ForeignKey to BusinessCustomer
- `employee`: ForeignKey to Employee
- `services`: ManyToMany to Service through AppointmentService
- `start_time`: DateTimeField (stored in UTC)
- `status`: CharField (requested, confirmed, accepted, checked_in, service_started, completed, cancelled, no_show)
- `payment_status`: CharField (unpaid, paid, refunded)
- `source`: CharField (admin, online)
- `notes_from_customer`: TextField
- `cancellation_reason`: TextField
- `created_at`: DateTimeField
- `updated_at`: DateTimeField

### AppointmentService
- `appointment`: ForeignKey to Appointment
- `service`: ForeignKey to Service
- `employee_service`: ForeignKey to EmployeeService
- `quantity`: PositiveIntegerField
- `base_price`: DecimalField
- `price_override`: DecimalField
- `duration`: PositiveIntegerField
- `buffer_time`: PositiveIntegerField (can be customized per appointment service)
- `notes`: TextField

### Employee
- `user`: OneToOneField to User
- `business`: ForeignKey to Business
- `stylist_level`: ForeignKey to StylistLevel
- `is_active`: BooleanField

## Notes

- All endpoints that modify data require authentication
- Date parameters should be in YYYY-MM-DD format
- Time parameters should be in 24-hour format (HH:MM)
- Datetime parameters should be in ISO 8601 format with timezone offset (YYYY-MM-DDThh:mm:ss+00:00)
- Buffer times and durations are in minutes
- Prices are stored with 2 decimal places 