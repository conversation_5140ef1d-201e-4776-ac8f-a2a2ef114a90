import * as XLSX from 'xlsx'

// File validation utility
export const validateFileFormat = (file, acceptedFormats, maxSize) => {
  if (!file) {
    return { isValid: false, error: 'No file selected' }
  }

  // Check file size
  if (file.size > maxSize) {
    const maxSizeMB = Math.round(maxSize / (1024 * 1024))
    return { isValid: false, error: `File size exceeds ${maxSizeMB}MB limit` }
  }

  // Check file format
  const fileExtension = '.' + file.name.split('.').pop().toLowerCase()
  if (!acceptedFormats.includes(fileExtension)) {
    return { isValid: false, error: `Unsupported file format. Accepted formats: ${acceptedFormats.join(', ')}` }
  }

  return { isValid: true }
}

// CSV parsing utility
export const parseCSVFile = (file, headerRowIndex = 0) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const text = e.target.result
        const lines = text.split('\n').filter(line => line.trim())
        
        if (lines.length === 0) {
          reject(new Error('File is empty'))
          return
        }

        // If headerRowIndex is not specified, return raw data for header detection
        if (headerRowIndex === undefined || headerRowIndex === null) {
          const rawData = lines.map(line => parseCSVRow(line))
          resolve({
            rawData,
            fileName: file.name,
            fileSize: file.size,
            isRawData: true
          })
          return
        }

        // Validate header row index
        if (headerRowIndex >= lines.length) {
          reject(new Error(`Header row index ${headerRowIndex + 1} is beyond the file length`))
          return
        }

        // Parse header row from specified index
        const headers = parseCSVRow(lines[headerRowIndex])
        if (headers.length === 0) {
          reject(new Error(`No headers found in row ${headerRowIndex + 1}`))
          return
        }

        // Parse data rows starting from the row after headers
        const data = []
        for (let i = headerRowIndex + 1; i < lines.length; i++) {
          const row = parseCSVRow(lines[i])
          if (row.length > 0) {
            const rowData = {}
            headers.forEach((header, index) => {
              rowData[header] = row[index] || ''
            })
            data.push(rowData)
          }
        }

        resolve({
          headers,
          data,
          totalRows: data.length,
          fileName: file.name,
          fileSize: file.size,
          headerRowIndex
        })
      } catch (error) {
        reject(new Error(`Failed to parse CSV file: ${error.message}`))
      }
    }

    reader.onerror = () => {
      reject(new Error('Failed to read file'))
    }

    reader.readAsText(file)
  })
}

// Simple CSV row parser (handles basic cases, doesn't handle complex CSV edge cases)
const parseCSVRow = (row) => {
  const result = []
  let current = ''
  let inQuotes = false
  
  for (let i = 0; i < row.length; i++) {
    const char = row[i]
    
    if (char === '"') {
      inQuotes = !inQuotes
    } else if (char === ',' && !inQuotes) {
      result.push(current.trim())
      current = ''
    } else {
      current += char
    }
  }
  
  result.push(current.trim())
  return result.filter(cell => cell !== '')
}

// Excel parsing utility (using SheetJS library)
export const parseExcelFile = (file, headerRowIndex = 0) => {
  return new Promise((resolve, reject) => {
    // Check if SheetJS is available
    if (!XLSX) {
      reject(new Error('Excel parsing library not available. Please install SheetJS or use CSV format.'))
      return
    }

    const reader = new FileReader()
    
    reader.onload = (e) => {
      try {
        const arrayBuffer = new Uint8Array(e.target.result)
        const workbook = XLSX.read(arrayBuffer, { type: 'array' })
        
        // Get the first worksheet
        const firstSheetName = workbook.SheetNames[0]
        if (!firstSheetName) {
          reject(new Error('No worksheets found in the Excel file'))
          return
        }

        const worksheet = workbook.Sheets[firstSheetName]
        
        // Convert to JSON
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { 
          header: 1,
          defval: '',
          raw: false
        })

        if (jsonData.length === 0) {
          reject(new Error('Worksheet is empty'))
          return
        }

        // If headerRowIndex is not specified, return raw data for header detection
        if (headerRowIndex === undefined || headerRowIndex === null) {
          resolve({
            rawData: jsonData,
            fileName: file.name,
            fileSize: file.size,
            sheetName: firstSheetName,
            isRawData: true
          })
          return
        }

        // Validate header row index
        if (headerRowIndex >= jsonData.length) {
          reject(new Error(`Header row index ${headerRowIndex + 1} is beyond the file length`))
          return
        }

        // Extract headers from specified row
        const headers = jsonData[headerRowIndex].filter(header => header && header.trim())
        if (headers.length === 0) {
          reject(new Error(`No headers found in row ${headerRowIndex + 1}`))
          return
        }

        // Extract data starting from the row after headers
        const parsedData = []
        for (let i = headerRowIndex + 1; i < jsonData.length; i++) {
          const row = jsonData[i]
          if (row.some(cell => cell !== '')) { // Skip empty rows
            const rowData = {}
            headers.forEach((header, index) => {
              rowData[header] = (row[index] || '').toString().trim()
            })
            parsedData.push(rowData)
          }
        }

        resolve({
          headers,
          data: parsedData,
          totalRows: parsedData.length,
          fileName: file.name,
          fileSize: file.size,
          sheetName: firstSheetName,
          headerRowIndex
        })
      } catch (error) {
        reject(new Error(`Failed to parse Excel file: ${error.message}`))
      }
    }

    reader.onerror = () => {
      reject(new Error('Failed to read file'))
    }

    reader.readAsArrayBuffer(file)
  })
}

// Data validation utilities
export const validateRowData = (rowData, fieldMappings, validationRules = {}) => {
  const errors = []
  const warnings = []
  
  // Check required fields
  Object.entries(fieldMappings).forEach(([targetField, sourceField]) => {
    if (validationRules[targetField]?.required && !rowData[sourceField]) {
      errors.push(`${targetField} is required but missing`)
    }
  })

  // Validate email format
  if (fieldMappings.email && rowData[fieldMappings.email]) {
    const email = rowData[fieldMappings.email]
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      errors.push(`Invalid email format: ${email}`)
    }
  }

  // Validate phone format (basic validation)
  if (fieldMappings.phone && rowData[fieldMappings.phone]) {
    const phone = rowData[fieldMappings.phone]
    const phoneRegex = /^[\+]?[\d\s\-\(\)]{10,}$/
    if (!phoneRegex.test(phone)) {
      warnings.push(`Phone number may be invalid: ${phone}`)
    }
  }

  // Validate date format
  if (fieldMappings.birthday && rowData[fieldMappings.birthday]) {
    const dateStr = rowData[fieldMappings.birthday]
    const date = new Date(dateStr)
    if (isNaN(date.getTime())) {
      warnings.push(`Invalid date format: ${dateStr}`)
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings
  }
}

// Clean and normalize data
export const normalizeRowData = (rowData, fieldMappings) => {
  const normalized = {}
  
  Object.entries(fieldMappings).forEach(([targetField, sourceField]) => {
    let value = rowData[sourceField] || ''
    
    // Clean up the value
    value = value.toString().trim()
    
    // Apply field-specific normalization
    switch (targetField) {
      case 'email':
        value = value.toLowerCase()
        break
      case 'phone':
        // Remove common phone formatting
        value = value.replace(/[\s\-\(\)]/g, '')
        break
      case 'name':
        // Capitalize first letter of each word
        value = value.replace(/\w\S*/g, (txt) => 
          txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase()
        )
        break
      case 'gender':
        // Normalize gender values
        const genderMap = {
          'm': 'male',
          'male': 'male',
          'man': 'male',
          'f': 'female',
          'female': 'female',
          'woman': 'female'
        }
        value = genderMap[value.toLowerCase()] || value
        break
      default:
        break
    }
    
    normalized[targetField] = value
  })
  
  return normalized
}

// Generate sample data for preview
export const generateSampleRows = (data, maxRows = 5) => {
  return data.slice(0, Math.min(data.length, maxRows))
}

// Detect potential header rows in raw data
export const detectHeaderRows = (rawData, maxRowsToCheck = 10) => {
  const potentialHeaders = []
  const checkRows = Math.min(rawData.length, maxRowsToCheck)
  
  for (let i = 0; i < checkRows; i++) {
    const row = rawData[i]
    if (!row || row.length === 0) continue
    
    // Calculate score for this row being a header
    let score = 0
    let hasText = false
    let hasNumbers = false
    let validColumnCount = 0
    
    row.forEach(cell => {
      const cellStr = (cell || '').toString().trim()
      if (!cellStr) return
      
      validColumnCount++
      
      // Check if cell looks like a header
      if (isNaN(cellStr)) {
        hasText = true
        score += 2
        
        // Bonus for common header patterns
        const lowerCell = cellStr.toLowerCase()
        if (lowerCell.includes('name') || lowerCell.includes('email') || 
            lowerCell.includes('phone') || lowerCell.includes('address') ||
            lowerCell.includes('date') || lowerCell.includes('id')) {
          score += 3
        }
        
        // Bonus for title case or all caps (common in headers)
        if (cellStr === cellStr.toUpperCase() || 
            cellStr.charAt(0) === cellStr.charAt(0).toUpperCase()) {
          score += 1
        }
      } else {
        hasNumbers = true
        score -= 1 // Numbers are less likely in headers
      }
    })
    
    // Higher score for rows with mostly text and good column count
    if (hasText && validColumnCount >= 3) {
      score += validColumnCount
    }
    
    // Penalize rows with only numbers
    if (hasNumbers && !hasText) {
      score -= 5
    }
    
    potentialHeaders.push({
      rowIndex: i,
      score,
      preview: row.slice(0, 5), // First 5 columns for preview
      columnCount: validColumnCount,
      hasText,
      hasNumbers
    })
  }
  
  // Sort by score (highest first)
  return potentialHeaders.sort((a, b) => b.score - a.score)
}

// Calculate file statistics
export const calculateFileStats = (data) => {
  const stats = {
    totalRows: data.length,
    emptyRows: 0,
    duplicateEmails: 0,
    incompleteRows: 0
  }

  const emailSet = new Set()
  
  data.forEach(row => {
    // Check for empty rows
    const values = Object.values(row).filter(v => v && v.trim())
    if (values.length === 0) {
      stats.emptyRows++
    } else if (values.length < Object.keys(row).length / 2) {
      stats.incompleteRows++
    }

    // Check for duplicate emails
    const email = row.email || row.Email || ''
    if (email && email.trim()) {
      const normalizedEmail = email.toLowerCase().trim()
      if (emailSet.has(normalizedEmail)) {
        stats.duplicateEmails++
      } else {
        emailSet.add(normalizedEmail)
      }
    }
  })

  return stats
} 