import { useState, useEffect } from "react";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import Button from "../../components/Button";
import CustomerSelector from "../../components/CustomerSelector";
import DateRangePicker from "../../components/DateRangePicker";
import { transactionApi } from "../../features/reports/services/transactionApi";

function SalesSummary() {
  // ========== State Management ==========

  // Filter conditions
  const [filters, setFilters] = useState({
    startDate: new Date(2025, 5, 1), // Jun 1, 2025
    endDate: new Date(2025, 5, 30), // Jun 30, 2025
    employee: "all",
    customer: [], // Change to array to support multi-select
  });

  // Advanced filter conditions
  const [advancedFilters, setAdvancedFilters] = useState({
    tip: true,
    includePastEmployees: true,
    appointmentDate: "",
  });

  // Sales data
  const [salesData, setSalesData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasRunReport, setHasRunReport] = useState(false);

  // Show/hide advanced filters
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Dropdown options data
  const [employees, setEmployees] = useState([
    { id: "all", name: "All Employees" },
  ]);

  const [customers, setCustomers] = useState([]);

  // ========== Initial Data Loading ==========

  useEffect(() => {
    loadInitialData();
  }, []);

  // Load initial data (employees and customers)
  const loadInitialData = async () => {
    try {
      // Temporarily use mock employee data to avoid API calls
      const mockEmployees = [
        { id: "all", name: "All Employees" },
        { id: "emp1", name: "John Smith" },
        { id: "emp2", name: "Sarah Johnson" },
        { id: "emp3", name: "Mike Wilson" },
      ];

      setEmployees(mockEmployees);

      // Use the same customer data as checkout page
      const checkoutCustomers = [
        {
          id: "customer1",
          name: "Yuechen Guo",
          email: "<EMAIL>",
          phone: null,
          avatar: "YG",
          customerSince: "Jun 30, 2025",
          lastVisit: "---",
          birthday: "Not available",
          membership: "---",
          points: "0 pt",
        },
        {
          id: "customer2",
          name: "Jane Smith",
          email: "<EMAIL>",
          phone: null,
          avatar: "JS",
          customerSince: "Jan 15, 2024",
          lastVisit: "Dec 20, 2024",
          birthday: "Mar 15, 1990",
          membership: "Gold",
          points: "150 pt",
        },
        {
          id: "customer3",
          name: "Bob Johnson",
          email: "<EMAIL>",
          phone: null,
          avatar: "BJ",
          customerSince: "Sep 08, 2023",
          lastVisit: "Nov 30, 2024",
          birthday: "Jul 22, 1985",
          membership: "Silver",
          points: "85 pt",
        },
      ];

      setCustomers(checkoutCustomers);
    } catch (error) {
      console.error("Failed to load initial data:", error);
      // Set default data
      setEmployees([
        { id: "all", name: "All Employees" },
        { id: "emp1", name: "John Smith" },
        { id: "emp2", name: "Sarah Johnson" },
        { id: "emp3", name: "Mike Wilson" },
      ]);

      setCustomers([
        {
          id: "customer1",
          name: "Yuechen Guo",
          email: "<EMAIL>",
          phone: null,
          avatar: "YG",
          customerSince: "Jun 30, 2025",
          lastVisit: "---",
          birthday: "Not available",
          membership: "---",
          points: "0 pt",
        },
        {
          id: "customer2",
          name: "Jane Smith",
          email: "<EMAIL>",
          phone: null,
          avatar: "JS",
          customerSince: "Jan 15, 2024",
          lastVisit: "Dec 20, 2024",
          birthday: "Mar 15, 1990",
          membership: "Gold",
          points: "150 pt",
        },
        {
          id: "customer3",
          name: "Bob Johnson",
          email: "<EMAIL>",
          phone: null,
          avatar: "BJ",
          customerSince: "Sep 08, 2023",
          lastVisit: "Nov 30, 2024",
          birthday: "Jul 22, 1985",
          membership: "Silver",
          points: "85 pt",
        },
      ]);
    }
  };

  // ========== Event Handlers ==========

  // Handle filter condition changes
  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Handle advanced filter condition changes
  const handleAdvancedFilterChange = (key, value) => {
    setAdvancedFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Run report
  const handleRunReport = async () => {
    setLoading(true);
    setError(null);
    setHasRunReport(true);

    try {
      // Simulate loading delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Temporarily skip API call and show report directly
      // Build API query parameters
      const queryParams = {
        startDate: filters.startDate.toISOString().split("T")[0],
        endDate: filters.endDate.toISOString().split("T")[0],
        employee: filters.employee !== "all" ? filters.employee : undefined,
      };

      // Handle multi-select customers
      if (filters.customer && filters.customer.length > 0) {
        queryParams.customers = filters.customer;
      }

      // Add advanced filter parameters
      queryParams.tip = advancedFilters.tip;
      queryParams.includePastEmployees = advancedFilters.includePastEmployees;
      if (advancedFilters.appointmentDate) {
        queryParams.appointmentDate = advancedFilters.appointmentDate;
      }

      // Temporarily comment out API call and complete directly
      // const response = await transactionApi.getSalesSummary(queryParams);
      // setSalesData(response);

      // Simulate successful response
      console.log("Sales summary query params:", queryParams);
    } catch (err) {
      setError(err.message || "Failed to load sales summary");
    } finally {
      setLoading(false);
    }
  };

  // Export report
  const handleExport = async () => {
    try {
      // Temporarily use mock export
      alert("Export functionality will be implemented when API is ready");

      // The following is the real export logic, uncomment when API is ready
      /*
      const queryParams = {
        startDate: filters.startDate.toISOString().split("T")[0],
        endDate: filters.endDate.toISOString().split("T")[0],
        employee: filters.employee !== "all" ? filters.employee : undefined,
      };

              // Handle multi-select customers
        if (filters.customer && filters.customer.length > 0) {
          queryParams.customers = filters.customer;
        }

      const blob = await transactionApi.exportSalesSummary(queryParams, "csv");
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `sales-summary-${
        filters.startDate.toISOString().split("T")[0]
      }-${filters.endDate.toISOString().split("T")[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
      */
    } catch (err) {
      setError(err.message || "Failed to export sales summary");
    }
  };

  // Print report
  const handlePrint = () => {
    window.print();
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Filter panel */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
          {/* Transaction date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Transaction Date:
            </label>
            <DateRangePicker
              startDate={filters.startDate}
              endDate={filters.endDate}
              onChange={({ startDate, endDate }) => {
                setFilters((prev) => ({
                  ...prev,
                  startDate,
                  endDate,
                }));
              }}
              placeholder="Select date range"
            />
          </div>

          {/* Employees */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Employees:
            </label>
            <div className="relative">
              <select
                value={filters.employee}
                onChange={(e) => handleFilterChange("employee", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none"
              >
                {employees.map((employee) => (
                  <option key={employee.id} value={employee.id}>
                    {employee.name}
                  </option>
                ))}
              </select>
              <ChevronDownIcon className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none" />
            </div>
          </div>

          {/* Customer */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Customer:
            </label>
            <CustomerSelector
              value={filters.customer}
              onChange={(selectedCustomers) =>
                handleFilterChange("customer", selectedCustomers)
              }
              customers={customers}
              placeholder="All Customers"
            />
          </div>

          {/* Run report button */}
          <div>
            <Button
              onClick={handleRunReport}
              disabled={loading}
              className="w-full bg-red-600 text-white hover:bg-red-700 focus:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 py-2 px-4 rounded-md disabled:opacity-50 disabled:cursor-not-allowed font-medium shadow-sm transition-colors duration-200"
            >
              {loading ? "Loading..." : "Run Report"}
            </Button>
          </div>
        </div>
      </div>

      {/* Sales summary title and advanced filters */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-gray-800">Sales Summary</h2>
        <button
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
        >
          Advanced Filters {showAdvancedFilters ? "▲" : "▼"}
        </button>
      </div>

      {/* Advanced filter panel */}
      {showAdvancedFilters && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
          {/* Checkbox options */}
          <div className="flex gap-8 mb-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={advancedFilters.tip}
                onChange={(e) =>
                  handleAdvancedFilterChange("tip", e.target.checked)
                }
                className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">Tip</span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={advancedFilters.includePastEmployees}
                onChange={(e) =>
                  handleAdvancedFilterChange(
                    "includePastEmployees",
                    e.target.checked
                  )
                }
                className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">
                Include Past Employees
              </span>
            </label>
          </div>

          {/* Appointment Date */}
          <div className="max-w-md">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Appointment Date:
            </label>
            <div className="relative">
              <input
                type="date"
                value={advancedFilters.appointmentDate}
                onChange={(e) =>
                  handleAdvancedFilterChange("appointmentDate", e.target.value)
                }
                className="w-full px-3 py-2 pr-10 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <svg
                className="absolute right-3 top-2.5 h-5 w-5 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </div>
          </div>
        </div>
      )}

      {/* Main content area */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {!hasRunReport ? (
          // Placeholder content
          <div className="flex flex-col items-center justify-center py-20">
            <div className="text-6xl text-gray-400 mb-4">↑</div>
            <p className="text-lg text-gray-600 font-medium">
              Select filters above, then press Run Report.
            </p>
          </div>
        ) : (
          // Report results area
          <div className="p-6">
            {loading ? (
              <div className="flex items-center justify-center py-20">
                <div className="text-lg text-gray-600">Loading...</div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center py-20">
                <div className="text-lg text-red-600">Error: {error}</div>
              </div>
            ) : (
              // Sales summary report content
              <div>
                {/* Description text */}
                <p className="text-sm text-gray-600 mb-6">
                  *Total sales numbers are based on transaction date and do not
                  include items paid for by points.
                </p>

                {/* Sales summary table */}
                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden mb-8">
                  {/* Table header */}
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                          Type
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                          Business Cost
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                          Total Sale*
                        </th>
                      </tr>
                    </thead>
                  </table>

                  {/* Scrollable content area */}
                  <div className="max-h-64 overflow-y-auto">
                    <table className="w-full">
                      <tbody className="bg-white divide-y divide-gray-200">
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div className="flex items-center">
                              Service Sales
                              <span className="ml-2 inline-flex items-center justify-center w-4 h-4 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                                i
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div className="flex items-center">
                              Service Add-On Sales
                              <span className="ml-2 inline-flex items-center justify-center w-4 h-4 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                                i
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div className="flex items-center">
                              Class Sales
                              <span className="ml-2 inline-flex items-center justify-center w-4 h-4 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                                i
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div className="flex items-center">
                              Class Add-On Sales
                              <span className="ml-2 inline-flex items-center justify-center w-4 h-4 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                                i
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div className="flex items-center">
                              In-House Product Sales
                              <span className="ml-2 inline-flex items-center justify-center w-4 h-4 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                                i
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            Gift Card Sales
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            Membership Sales
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            Package Sales
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            Rent Collection
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            Commission (Deducted from Rent Collection)
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div className="flex items-center">
                              Service Discounts
                              <span className="ml-2 inline-flex items-center justify-center w-4 h-4 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                                i
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div className="flex items-center">
                              Service Add-On Discounts
                              <span className="ml-2 inline-flex items-center justify-center w-4 h-4 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                                i
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div className="flex items-center">
                              Class Discounts
                              <span className="ml-2 inline-flex items-center justify-center w-4 h-4 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                                i
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            <div className="flex items-center">
                              Class Add-On Discounts
                              <span className="ml-2 inline-flex items-center justify-center w-4 h-4 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                                i
                              </span>
                            </div>
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            Product Discounts
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            Gift Card Discounts
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            Membership Discounts
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            Package Discounts
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                        <tr>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            Tips
                          </td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                          <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                            $0.00
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>

                  {/* Bottom fixed rows */}
                  <table className="w-full border-t border-gray-200">
                    <tbody className="bg-white divide-y divide-gray-200">
                      <tr className="bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          Revenue Total
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          $0.00
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          $0.00
                        </td>
                      </tr>
                      <tr>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          Tax Total
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          $0.00
                        </td>
                      </tr>
                      <tr className="bg-gray-50">
                        <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                          Profit (Revenue - Business Cost - Tax)
                        </td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"></td>
                        <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                          $0.00
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>

                {/* Bottom statistics circles */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                  {/* Points Summary */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">
                      Points Summary
                    </h3>
                    <div className="flex gap-6">
                      <div className="text-center">
                        <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center text-white text-xl font-bold mb-2">
                          0
                        </div>
                        <div className="text-sm text-gray-600 flex items-center justify-center">
                          Points Given
                          <span className="ml-1 inline-flex items-center justify-center w-4 h-4 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                            i
                          </span>
                        </div>
                      </div>
                      <div className="text-center">
                        <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center text-white text-xl font-bold mb-2">
                          0
                        </div>
                        <div className="text-sm text-gray-600 flex items-center justify-center">
                          Points Redeemed
                          <span className="ml-1 inline-flex items-center justify-center w-4 h-4 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                            i
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Total IOU Outstanding */}
                  <div>
                    <h3 className="text-lg font-semibold text-gray-800 mb-4">
                      Total IOU Outstanding
                    </h3>
                    <div className="text-center">
                      <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center text-white text-sm font-bold mb-2">
                        $0.00
                      </div>
                      <div className="text-sm text-gray-600 flex items-center justify-center">
                        IOUs Outstanding
                        <span className="ml-1 inline-flex items-center justify-center w-4 h-4 bg-blue-100 text-blue-600 rounded-full text-xs font-medium">
                          i
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Bottom buttons */}
      {hasRunReport && !loading && !error && (
        <div className="flex justify-end gap-4 mt-6">
          <button
            onClick={handleExport}
            className="px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700 flex items-center font-medium shadow-sm"
          >
            Export
            <span className="ml-1">▼</span>
          </button>
          <button
            onClick={handlePrint}
            className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium"
          >
            Print
          </button>
        </div>
      )}
    </div>
  );
}

export default SalesSummary;
