import React from 'react'

/**
 * WorkingHoursOverlay - Renders working hours background highlight
 */
const WorkingHoursOverlay = ({
  date,
  employeeId,
  config,
  getWorkingHoursForEmployee
}) => {
  if (!config.showWorkingHours || !employeeId) return null
  
  const workingHours = getWorkingHoursForEmployee(employeeId, date)
  if (!workingHours || !workingHours.isWorking) return null
  
  const startHour = parseInt(workingHours.start.split(':')[0])
  const startMinute = parseInt(workingHours.start.split(':')[1])
  const endHour = parseInt(workingHours.end.split(':')[0])
  const endMinute = parseInt(workingHours.end.split(':')[1])
  
  const slotsPerHour = 60 / config.timeResolution
  const slotHeight = config.gridHeight / slotsPerHour
  
  const startSlot = (startHour - config.displayHourStart) * slotsPerHour + (startMinute / config.timeResolution)
  const endSlot = (endHour - config.displayHourStart) * slotsPerHour + (endMinute / config.timeResolution)
  
  return (
    <div
      className="absolute pointer-events-none"
      style={{
        top: `${startSlot * slotHeight}px`,
        height: `${(endSlot - startSlot) * slotHeight}px`,
        left: '2px',
        right: '2px',
        borderRadius: '4px',
        backgroundColor: 'rgba(255,255,255,0.75)'
      }}
    />
  )
}

export default WorkingHoursOverlay 