# Logging Configuration
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'detailed': {
            'format': '{levelname} {asctime} {module} {funcName} {lineno} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
        'json': {
            'format': '%(levelname)s %(asctime)s %(module)s %(message)s',
        },
    },
    'filters': {
        'require_debug_false': {
            '()': 'django.utils.log.RequireDebugFalse',
        },
        'require_debug_true': {
            '()': 'django.utils.log.RequireDebugTrue',
        },
    },
    'handlers': {
        'console': {
            'level': 'WARNING',  # Only warnings and errors in console
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        'console_debug': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'detailed',
            'filters': ['require_debug_true'],
        },
        'file_debug': {
            'level': 'DEBUG',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'debug.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 5,
            'formatter': 'detailed',
        },
        'file_error': {
            'level': 'ERROR',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'error.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 5,
            'formatter': 'detailed',
        },
        'file_requests': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'requests.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 5,
            'formatter': 'json',
        },
        'file_signatures': {
            'level': 'INFO',
            'class': 'logging.handlers.RotatingFileHandler',
            'filename': BASE_DIR / 'logs' / 'signatures.log',
            'maxBytes': 50 * 1024 * 1024,  # 50MB
            'backupCount': 5,
            'formatter': 'detailed',
        },
        'mail_admins': {
            'level': 'ERROR',
            'class': 'django.utils.log.AdminEmailHandler',
            'filters': ['require_debug_false'],
            'formatter': 'verbose',
        },
    },
    'loggers': {
        # Django core loggers
        'django': {
            'handlers': ['file_debug'],
            'level': 'WARNING',  # Reduce Django noise
            'propagate': False,
        },
        'django.request': {
            'handlers': ['file_error', 'mail_admins', 'console'],
            'level': 'ERROR',
            'propagate': False,
        },
        'django.security': {
            'handlers': ['file_error', 'mail_admins', 'console'],
            'level': 'ERROR',
            'propagate': False,
        },
        'django.server': {
            'handlers': ['file_requests'],  # Move server logs to requests file
            'level': 'INFO',
            'propagate': False,
        },
        'django.db.backends': {
            'handlers': ['file_debug'],
            'level': 'WARNING',  # Reduce DB query noise
            'propagate': False,
        },
        # Application loggers
        'appointments': {
            'handlers': ['file_debug'],
            'level': 'WARNING',  # Only warnings and errors
            'propagate': True,
        },
        'employees': {
            'handlers': ['file_debug'],
            'level': 'WARNING',  # Reduce employee admin noise
            'propagate': True,
        },
        'employees.admin': {
            'handlers': ['file_debug'],
            'level': 'ERROR',  # Only errors from employee admin
            'propagate': False,
        },
        'api': {
            'handlers': ['console', 'file_debug', 'file_error'],
            'level': 'WARNING',  # Only warnings and errors
            'propagate': True,
        },
        'api.views.files': {
            'handlers': ['console', 'file_debug', 'file_error'],
            'level': 'INFO',  # Keep file operations visible
            'propagate': False,
        },
        'api.views.signatures': {
            'handlers': ['console', 'file_signatures', 'file_error'],
            'level': 'INFO',  # Keep signature operations visible
            'propagate': False,
        },
        'api.middleware': {
            'handlers': ['console', 'file_debug', 'file_error'],
            'level': 'WARNING',  # Only warnings and errors
            'propagate': False,
        },
        'aws_services': {
            'handlers': ['console', 'file_debug', 'file_error'],
            'level': 'WARNING',  # Only warnings and errors for AWS
            'propagate': False,
        },
        'scripts.generate_signature_pdf': {
            'handlers': ['console', 'file_signatures', 'file_error'],
            'level': 'INFO',  # Keep signature generation visible
            'propagate': False,
        },
        'requests_logger': {
            'handlers': ['file_requests'],
            'level': 'INFO',
            'propagate': False,
        },
        # Root logger
        '': {
            'handlers': ['console', 'file_debug', 'file_error'],
            'level': 'WARNING',  # Only warnings and errors by default
        },
    },
} 