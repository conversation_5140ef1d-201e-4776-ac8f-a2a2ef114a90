/* Header Styles */
.profile-header {
    padding: 30px 0;
    background-color: #f4f1eb;
}

.profile-card {
    position: relative;
    display: flex;
    flex-direction: column;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    background-color: #f4f1eb;
    border-radius: 5px;
}

.auth-section {
    position: absolute;
    top: 20px;
    right: 20px;
    z-index: 10000;
}

.profile-content {
    display: flex;
    align-items: center;
    width: 100%;
}

.profile-image {
    width: 140px;
    height: 140px;
    border-radius: 5px;
    overflow: hidden;
    margin-right: 30px;
    background-color: transparent;
    display: flex;
    align-items: center;
    justify-content: center;
}

.profile-image img {
    width: 100%;
    height: auto;
    object-fit: contain;
}

.profile-info h1 {
    font-size: 2.2rem;
    margin-bottom: 5px;
    color: #333;
}

.profile-info p {
    color: #555;
    margin-bottom: 15px;
}

.book-now-btn {
    display: inline-block;
    padding: 10px 25px;
    background-color: #9c7b65;
    color: white;
    text-decoration: none;
    border: none;
    border-radius: 4px;
    font-weight: 500;
    font-size: 1rem;
    cursor: pointer;
    transition: background-color 0.3s;
}

.book-now-btn:hover {
    background-color: #8a6c58;
}

/* Authentication Styles */
.login-btn {
    display: inline-block;
    padding: 8px 16px;
    background-color: #4f46e5;
    color: white;
    text-decoration: none;
    border-radius: 6px;
    font-weight: 500;
    font-size: 0.875rem;
    transition: background-color 0.2s;
}

.login-btn:hover {
    background-color: #4338ca;
}

.user-menu-container {
    position: relative;
    z-index: 10001;
}

.user-menu-button {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0;
}

.user-avatar {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background-color: #6b7280;
    color: white;
    font-size: 0.875rem;
    font-weight: 500;
    transition: background-color 0.2s;
}

.user-avatar:hover {
    background-color: #4b5563;
}

.user-dropdown {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: 8px;
    width: 192px;
    background-color: white;
    border-radius: 6px;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    border: 1px solid #e5e7eb;
    padding: 4px 0;
    z-index: 9999;
}

.user-info {
    padding: 12px 16px;
}

.user-name {
    font-weight: 500;
    color: #111827;
    margin: 0 0 4px 0;
    font-size: 0.875rem;
}

.user-email {
    color: #6b7280;
    margin: 0;
    font-size: 0.75rem;
}

.dropdown-divider {
    height: 1px;
    background-color: #e5e7eb;
    margin: 4px 0;
}

.dropdown-item {
    display: block;
    width: 100%;
    padding: 8px 16px;
    text-align: left;
    color: #374151;
    text-decoration: none;
    font-size: 0.875rem;
    border: none;
    background: none;
    cursor: pointer;
    transition: background-color 0.2s;
}

.dropdown-item:hover {
    background-color: #f3f4f6;
}

.logout-btn {
    color: #dc2626;
}

.logout-btn:hover {
    background-color: #fef2f2;
}

/* Navigation */
.main-nav {
    background-color: #f9f9f9;
    border-bottom: 1px solid #e1e1e1;
    position: sticky;
    top: 0;
    z-index: 100;
}

.main-nav ul {
    display: flex;
    list-style: none;
    max-width: 800px;
    margin: 0 auto;
    padding: 0;
}

.main-nav li {
    padding: 15px 30px;
    position: relative;
}

.main-nav li.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 30px;
    width: 40px;
    height: 3px;
    background-color: #9c7b65;
}

.main-nav a {
    text-decoration: none;
    color: #555;
    font-weight: 500;
    transition: color 0.3s;
}

.main-nav a:hover {
    color: #9c7b65;
}

/* Footer */
.main-footer {
    text-align: center;
    padding: 20px 0;
    color: #777;
    font-size: 0.9rem;
    border-top: 1px solid #eee;
    margin-top: 40px;
    background-color: #f9f9f9;
}

.footer-links {
    margin-top: 10px;
}

.footer-links a {
    margin: 0 10px;
    color: #555;
    text-decoration: none;
    font-size: 0.85rem;
    transition: color 0.3s;
}

.footer-links a:hover {
    color: #9c7b65;
    text-decoration: underline;
}

/* Responsive Adjustments */
@media (max-width: 768px) {
    .profile-content {
        flex-direction: column;
        text-align: center;
    }

    .profile-image {
        margin-right: 0;
        margin-bottom: 20px;
    }

    .auth-section {
        position: static;
        margin-bottom: 16px;
        text-align: right;
    }

    .user-dropdown {
        right: auto;
        left: 0;
    }

    .main-nav ul {
        justify-content: center;
    }

    .main-nav li.active::after {
        left: 50%;
        transform: translateX(-50%);
    }

    .footer-links {
        display: flex;
        flex-direction: column;
        gap: 10px;
    }

    .footer-links a {
        margin: 0;
    }
}
