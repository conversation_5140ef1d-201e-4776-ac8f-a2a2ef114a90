/**
 * Utility function for merging class names conditionally
 * Similar to clsx but lightweight implementation
 * @param {...any} classes - Class names to merge (strings, objects, arrays)
 * @returns {string} - Merged class names
 */
export const cn = (...classes) => {
  const result = []
  
  for (let i = 0; i < classes.length; i++) {
    const cls = classes[i]
    
    if (!cls) continue
    
    if (typeof cls === 'string') {
      result.push(cls)
    } else if (typeof cls === 'object' && cls !== null) {
      if (Array.isArray(cls)) {
        // Handle arrays recursively
        const arrayResult = cn(...cls)
        if (arrayResult) result.push(arrayResult)
      } else {
        // Handle objects where keys are class names and values are booleans
        for (const key in cls) {
          if (cls[key]) {
            result.push(key)
          }
        }
      }
    }
  }
  
  return result.join(' ')
}

export default cn 