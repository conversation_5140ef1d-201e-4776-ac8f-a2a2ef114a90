// Authentication related types
export interface User {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  is_active: boolean;
  date_joined: string;
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  error: string | null;
  consentStatus: ConsentStatus | null;
}

export interface ConsentStatus {
  all_forms_completed: boolean;
  checked_at: string;
  completed_forms_count?: number;
  required_forms_count?: number;
  missing_forms?: Array<{
    form_name: string;
    form_id: number;
  }>;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  first_name: string;
  last_name: string;
  phone?: string;
}

export interface AuthResponse {
  access: string;
  user: User;
}

export interface SocialLoginData {
  provider: string;
  token: string;
}

// Auth store actions
export interface AuthActions {
  login: (credentials: LoginCredentials) => Promise<{ success: boolean; error?: string }>;
  register: (data: RegisterData) => Promise<{ success: boolean; error?: string }>;
  socialLogin: (provider: string, token: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => Promise<void>;
  clearError: () => void;
  setUser: (user: User | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  updateConsentStatus: (status: ConsentStatus) => void;
  initializeAuth: () => Promise<void>;
}

export type AuthStore = AuthState & AuthActions;
