// Employee Services
export * from './services'

// Employee Hooks
export * from './hooks'

// Re-exports for convenience
export { default as employeeApiService } from './services'
export { default as useEmployees } from './hooks'

/**
 * Employees Feature Module
 * 
 * This module provides Django API integration for employee management.
 * It includes:
 * - Employee API service for CRUD operations
 * - Working hours management
 * - Calendar configurations
 * - React hooks for state management
 * 
 * Example usage:
 * ```
 * import { useEmployees, employeeApiService } from '@/features/employees'
 * 
 * const { employees, currentEmployee, loading } = useEmployees()
 * ```
 */ 