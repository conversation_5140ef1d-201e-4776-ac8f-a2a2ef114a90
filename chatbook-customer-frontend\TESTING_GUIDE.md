# Testing Guide - Customer Frontend

## Test Credentials

**Email:** `<EMAIL>`  
**Password:** `<PERSON><PERSON>26`

These credentials work with the Django backend API at `http://127.0.0.1:8000/api/v1/`.

## Development Server

Start the development server:
```bash
npm run dev
```

The app typically runs on `http://localhost:3003` (or next available port).

## Testing the Booking Flow

### 1. Basic Authentication Test
1. Navigate to `http://localhost:3003`
2. Click "Login" in the top-right header
3. Enter test credentials above
4. Click "Sign in"
5. Should redirect to homepage with user avatar (initials "<PERSON>") in header

### 2. Complete Booking Flow Test
1. **Start at homepage** (logged in)
2. **Select a service:**
   - Scroll to "Services" section
   - Find "Classic Lash Fullset(C01-C02)" - $199.00
   - Click "Book Now" button
3. **Select date and time:**
   - Choose any available date (today or future)
   - Select any available time slot
4. **Should redirect to consent form** (if logged in)
5. **Test consent form layout:**
   - Should show header with user profile
   - Main content on left with consent text
   - Order summary sidebar on right (grey background)
   - Back button at top-left of main content

### 3. Testing Layout Changes
To test the consent form layout specifically:
1. Complete steps 1-4 above to reach consent form
2. Check that layout matches target design:
   - ✅ Header with profile info at top
   - ✅ Container with white background and subtle border/shadow
   - ✅ Two-column layout: main content left, order summary right
   - ✅ Order summary has grey background
   - ✅ Back arrow in correct position

### 4. Alternative Testing (Direct Navigation)
If booking flow has issues, you can test pages directly:
- Navigate to `/consent-form` (requires login + booking data)
- Navigate to `/review-booking` (requires login + booking data)

**Note:** Direct navigation to consent/review pages may redirect to login or show loading states if booking data is missing.

## Common Issues

### Service Selection Not Working
If clicking "Book Now" on services doesn't work:
- Check browser console for JavaScript errors
- Ensure development server is running
- Try refreshing the page
- Check if there are CSS z-index issues preventing clicks

### Authentication Issues
If login fails:
- Verify Django backend is running on `http://127.0.0.1:8000`
- Check browser network tab for API call failures
- Ensure test credentials are correct
- Check browser console for authentication errors

### Layout Issues
If layout doesn't match expected design:
- Check that Header component is being imported and used
- Verify Tailwind CSS classes are applied correctly
- Check that container structure matches target (white bg, shadow, two columns)
- Ensure order summary has grey background and proper positioning

## File Structure for Testing

Key files to check when testing:
- `src/pages/ConsentFormPage.jsx` - Consent form layout
- `src/pages/ReviewBookingPage.jsx` - Review booking layout  
- `src/components/layout/Header.jsx` - Header with profile
- `src/components/booking/AppointmentSummary.jsx` - Order summary sidebar

## Browser Developer Tools

Useful for debugging:
- **Console tab:** Check for JavaScript errors
- **Network tab:** Monitor API calls to Django backend
- **Elements tab:** Inspect HTML structure and CSS classes
- **Application tab:** Check localStorage for auth tokens
