import { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import Button from '../../components/Button'
import { useApi } from '../../hooks/useApi'

function Signup() {
  const navigate = useNavigate()
  const [form, setForm] = useState({
    first_name: '',
    last_name: '',
    email: '',
    phone_number: '',
    password1: '',
    password2: '',
  })
  const [error, setError] = useState(null)
  const signupApi = useApi('/auth/signup/')

  const handleChange = (e) => {
    const { name, value } = e.target
    setForm(prev => ({ ...prev, [name]: value }))
  }

  const validateForm = () => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!form.first_name.trim() || !form.last_name.trim()) {
      return 'First and last name are required'
    }
    if (!emailRegex.test(form.email)) {
      return 'Invalid email address'
    }
    if (!/^[0-9]{10}$/.test(form.phone_number)) {
      return 'Phone number must be 10 digits'
    }
    if (form.password1.length < 8) {
      return 'Password must be at least 8 characters'
    }
    if (form.password1 !== form.password2) {
      return 'Passwords do not match'
    }
    return null
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    const validationError = validateForm()
    if (validationError) {
      setError(validationError)
      return
    }

    // backend expects same field names according spec
    const res = await signupApi.createData(form)
    if (!res || res.error) {
      setError(res?.detail || 'Failed to sign up')
    } else {
      // Could auto-login or redirect to login
      navigate('/auth/login')
    }
  }

  return (
    <div className="container flex justify-center items-center py-16">
      <div className="w-full max-w-md bg-white rounded-lg shadow-md p-8">
        <h2 className="text-2xl font-bold text-center text-gray-900 mb-6">Create your account</h2>

        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded mb-4 text-sm">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="flex gap-4">
            <div className="flex-1">
              <label htmlFor="first_name" className="block text-sm font-medium text-gray-700">First Name <span className="text-red-500">*</span></label>
              <input
                id="first_name"
                name="first_name"
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
                value={form.first_name}
                onChange={handleChange}
                required
              />
            </div>
            <div className="flex-1">
              <label htmlFor="last_name" className="block text-sm font-medium text-gray-700">Last Name <span className="text-red-500">*</span></label>
              <input
                id="last_name"
                name="last_name"
                type="text"
                className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
                value={form.last_name}
                onChange={handleChange}
                required
              />
            </div>
          </div>

          <div>
            <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email <span className="text-red-500">*</span></label>
            <input
              id="email"
              name="email"
              type="email"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
              value={form.email}
              onChange={handleChange}
              required
            />
          </div>

          <div>
            <label htmlFor="phone_number" className="block text-sm font-medium text-gray-700">Phone <span className="text-red-500">*</span></label>
            <input
              id="phone_number"
              name="phone_number"
              type="tel"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="5551234567"
              value={form.phone_number}
              onChange={handleChange}
              required
            />
          </div>

          <div>
            <label htmlFor="password1" className="block text-sm font-medium text-gray-700">Password <span className="text-red-500">*</span></label>
            <input
              id="password1"
              name="password1"
              type="password"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
              value={form.password1}
              onChange={handleChange}
              required
            />
          </div>

          <div>
            <label htmlFor="password2" className="block text-sm font-medium text-gray-700">Confirm Password <span className="text-red-500">*</span></label>
            <input
              id="password2"
              name="password2"
              type="password"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
              value={form.password2}
              onChange={handleChange}
              required
            />
          </div>

          <Button type="submit" className="w-full" disabled={signupApi.loading}>
            {signupApi.loading ? 'Creating account...' : 'Sign up'}
          </Button>
        </form>

        {/* Divider */}
        <div className="flex items-center my-6">
          <hr className="flex-1 border-gray-300" />
          <span className="px-2 text-sm text-gray-500">Or continue with</span>
          <hr className="flex-1 border-gray-300" />
        </div>

        {/* Google social login */}
        <a
          href="/accounts/google/login/"
          className="flex items-center justify-center gap-2 border border-gray-300 rounded-md px-4 py-2 hover:bg-gray-50 w-full text-sm font-medium text-gray-700"
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" className="w-5 h-5">
            <path fill="#FFC107" d="M43.611 20.083H42V20H24v8h11.292C33.323 33.73 29.064 37 24 37c-7.18 0-13-5.82-13-13s5.82-13 13-13c3.162 0 6.05 1.123 8.299 2.966l5.657-5.657C34.304 5.092 29.393 3 24 3 12.402 3 3 12.402 3 24s9.402 21 21 21 21-9.402 21-21c0-1.407-.144-2.781-.389-4.117z"/>
            <path fill="#FF3D00" d="M6.306 14.691l6.571 4.817C14.37 16.597 18.91 13 24 13c3.162 0 6.05 1.123 8.299 2.966l5.657-5.657C34.304 5.092 29.393 3 24 3c-7.18 0-13 5.82-13 13 0 2.445.675 4.733 1.849 6.694z"/>
            <path fill="#4CAF50" d="M24 45c5.093 0 9.879-1.965 13.463-5.182l-6.207-5.238C29.822 35.43 27.019 37 24 37c-5.04 0-9.284-3.261-10.794-7.787l-6.581 5.08C11.046 40.257 17.074 45 24 45z"/>
            <path fill="#1976D2" d="M43.611 20.083H42V20H24v8h11.292A11.956 11.956 0 0 1 24 37c-5.04 0-9.284-3.261-10.794-7.787l-6.581 5.08C11.046 40.257 17.074 45 24 45c11.598 0 21-9.402 21-21 0-1.407-.144-2.781-.389-4.117z"/>
          </svg>
          Sign up with Google
        </a>

        {/* Footer links */}
        <p className="text-center text-sm text-gray-600 mt-6">
          Already have an account?{' '}
          <Link to="/auth/login" className="text-primary-600 hover:underline">Sign in</Link>
        </p>
      </div>
    </div>
  )
}

export default Signup 