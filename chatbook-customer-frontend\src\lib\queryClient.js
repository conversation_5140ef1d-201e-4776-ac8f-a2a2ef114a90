import { QueryClient } from '@tanstack/react-query';

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // Stale time: how long data is considered fresh
      staleTime: 5 * 60 * 1000, // 5 minutes
      
      // Cache time: how long data stays in cache after component unmounts
      gcTime: 10 * 60 * 1000, // 10 minutes (was cacheTime in v4)
      
      // Retry failed requests
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors (client errors)
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false;
        }
        // Retry up to 3 times for other errors
        return failureCount < 3;
      },
      
      // Refetch on window focus
      refetchOnWindowFocus: false,
      
      // Refetch on reconnect
      refetchOnReconnect: true,
    },
    mutations: {
      // Retry failed mutations
      retry: (failureCount, error) => {
        // Don't retry on 4xx errors
        if (error?.response?.status >= 400 && error?.response?.status < 500) {
          return false;
        }
        return failureCount < 2;
      },
    },
  },
});

// Query keys factory for consistent key management
export const queryKeys = {
  // Auth related queries
  auth: {
    user: ['auth', 'user'],
    consentStatus: (userId) => ['auth', 'consent', userId],
  },
  
  // Booking related queries
  booking: {
    services: ['booking', 'services'],
    employees: ['booking', 'employees'],
    availability: (employeeId, date) =>
      ['booking', 'availability', employeeId, date],
    addOns: ['booking', 'addons'],
  },
  
  // Business related queries
  business: {
    info: ['business', 'info'],
    forms: (userId) => ['business', 'forms', userId],
  },
};
