import { cn } from '../../../../../utils'
import { useAppointmentStyles } from '../../../hooks/useAppointmentStyles'

/**
 * StylingFunctions - Handles styling calculations for appointments
 */
export const useStylingFunctions = (
  appointment,
  appointmentHeight,
  dragState,
  resizeState,
  isGloballyDragged,
  isGloballyResized,
  globalDragOffset,
  globalResizeOffset,
  isHovered
) => {
  const {
    getAppointmentClasses,
    getAppointmentContentClasses,
    getEmployeeStatusIndicator,
    getDragIndicator
  } = useAppointmentStyles()

  // Buffer zone styling functions
  const getServiceZoneClasses = (bufferLayout) => {
    return cn(
      "flex flex-col justify-start",
      "transition-all duration-200",
      // Removed relative, overflow-hidden, w-full redundancies - parent handles these
      bufferLayout.hasBuffer ? "flex-1" : ""
    )
  }

  const getBufferZoneClasses = () => {
    return cn(
      "bg-black/15",
      "border-t border-black/10 rounded-b-lg",
      "transition-all duration-200",
      "group-hover:bg-black/20",
      "flex items-center justify-center",
      // Buffer zone extends to full width and bottom alignment
      "absolute bottom-0 left-0 right-0"
    )
  }

  // Get appointment styling using the original system
  const getAppointmentStyling = (isGhostCopy = false) => {
    const appointmentStyling = getAppointmentClasses(appointment, {
      isDragging: dragState.isDragging && !isGhostCopy, // Ghost copy should not appear as dragging
      isDimmed: isGhostCopy, // Ghost copy should be dimmed
      isHovered: isHovered && !isGhostCopy, // Ghost copy should not respond to hover
      isSelected: false,
      isConflicted: false,
      height: appointmentHeight
    })

    // Calculate transforms (ghost copy should not have drag transforms)
    const effectiveDragOffset = isGloballyDragged ? globalDragOffset : dragState.dragOffset
    const shouldApplyDragTransform = !isGhostCopy && (dragState.isDragging || dragState.wasDragged || isGloballyDragged)
    
    const dragTransform = shouldApplyDragTransform
      ? `translate(${effectiveDragOffset.x}px, ${effectiveDragOffset.y}px)`
      : ''

    const shouldApplyResizeTransform = !isGhostCopy && (resizeState.isResizing || resizeState.wasResized || isGloballyResized)
    const resizeTransform = shouldApplyResizeTransform
      ? `scale(1.02)`
      : ''

    const combinedTransform = [dragTransform, resizeTransform].filter(Boolean).join(' ')

    const inlineStyles = {
      backgroundColor: appointment.serviceColor || undefined,
      borderColor: appointment.serviceColor || undefined,
      transform: combinedTransform,
      willChange: (shouldApplyDragTransform || shouldApplyResizeTransform) ? 'transform' : 'auto',
      ...appointmentStyling.style // Merge status border styling
    }

    return {
      className: cn(
        appointmentStyling.className,
        "group",
        "flex flex-col",
        "w-full"
        // Removed overflow-hidden redundancy - base classes handle it
      ),
      style: inlineStyles
    }
  }

  const getContentClasses = () => {
    return getAppointmentContentClasses(appointmentHeight, appointment.serviceColor)
  }

  const getIndicators = (employeeId) => {
    const dragIndicator = getDragIndicator(dragState.isDragging)

    return {
      statusIndicator: null, // No longer using status indicator
      dragIndicator
    }
  }

  return {
    getServiceZoneClasses,
    getBufferZoneClasses,
    getAppointmentStyling,
    getContentClasses,
    getIndicators
  }
} 