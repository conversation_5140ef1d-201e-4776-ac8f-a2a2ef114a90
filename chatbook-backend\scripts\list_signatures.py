#!/usr/bin/env python
"""
List available signatures in the database.

Usage:
    python scripts/list_signatures.py
"""
import os
import sys
import django

# Add the project root directory to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Set up Django with our custom settings
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'scripts.settings_for_scripts')

try:
    django.setup()
    print("Django setup successful")
except Exception as e:
    print(f"Error setting up Django: {e}")
    sys.exit(1)

# Import necessary models
try:
    from forms.models import Signature
    print("Imports successful")
except Exception as e:
    print(f"Error importing modules: {e}")
    sys.exit(1)


def list_signatures():
    """List all signatures in the database"""
    signatures = Signature.objects.all()
    
    if not signatures:
        print("No signatures found in the database.")
        return
    
    print(f"Found {signatures.count()} signatures:")
    print("-" * 80)
    print(f"{'ID':<5} | {'Signer Type':<15} | {'Business':<20} | {'Customer/Employee':<20} | {'Created':<20}")
    print("-" * 80)
    
    for sig in signatures:
        # Get signer info
        if sig.signer_type == 'customer':
            signer = str(sig.customer) if sig.customer else "Unknown customer"
        else:
            signer = str(sig.employee) if sig.employee else "Unknown employee"
        
        # Get business info
        business = str(sig.business) if sig.business else "Unknown business"
        
        print(f"{sig.id:<5} | {sig.signer_type:<15} | {business[:20]:<20} | {signer[:20]:<20} | {sig.created_at.strftime('%Y-%m-%d %H:%M')}")


if __name__ == '__main__':
    list_signatures() 