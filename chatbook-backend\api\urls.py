"""
API URLs - Legacy/Deprecated

This file is kept for backward compatibility but should not be used for new endpoints.
All new API endpoints should be added to api/v1/urls.py following proper versioning.

DEPRECATED: This file will be removed in a future version.
All functionality has been moved to api/v1/
"""
from django.urls import path, include

# Redirect all traffic to v1 - this maintains backward compatibility
# while encouraging proper versioning
urlpatterns = [
    # All API traffic should go through versioned endpoints
    path('', include('api.v1.urls')),
]