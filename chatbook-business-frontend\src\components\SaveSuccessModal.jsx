import { useEffect } from 'react'

export default function SaveSuccessModal({ open, status, onClose }) {
  // Early return if not open
  if (!open) return null

  // Optional: Lock background scroll while modal open
  useEffect(() => {
    document.body.style.overflow = 'hidden'
    return () => {
      document.body.style.overflow = 'auto'
    }
  }, [])

  const isPublished = status === 'Published'
  const isDeleted = status === 'Deleted'

  // Determine modal content based on status
  let heading, subText, colorClasses

  if (isDeleted) {
    heading = 'Form Deleted!'
    subText = 'The form has been successfully deleted.'
    colorClasses = {
      bg: 'bg-red-100',
      icon: 'text-red-600',
      heading: 'text-red-700',
    }
  } else if (isPublished) {
    heading = 'Form Published!'
    subText = 'Your form has been successfully published and is now live.'
    colorClasses = {
      bg: 'bg-green-100',
      icon: 'text-green-600',
      heading: 'text-green-700',
    }
  } else {
    heading = 'Draft Saved!'
    subText = 'Your form draft has been saved. You can continue editing it anytime.'
    colorClasses = {
      bg: 'bg-green-100',
      icon: 'text-green-600',
      heading: 'text-green-700',
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 backdrop-blur-sm">
      <div className="bg-white rounded-xl shadow-2xl w-11/12 max-w-md p-8 transform transition-all duration-300">
        {/* Checkmark icon */}
        <div className={`flex items-center justify-center w-16 h-16 mx-auto mb-4 ${colorClasses.bg} rounded-full`}>
          <svg xmlns="http://www.w3.org/2000/svg" className={`h-8 w-8 ${colorClasses.icon}`} fill="none" viewBox="0 0 24 24" stroke="currentColor" strokeWidth={2}>
            <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
          </svg>
        </div>
        <h3 className={`text-2xl font-semibold text-center ${colorClasses.heading} mb-2`}>{heading}</h3>
        <p className="text-gray-600 text-center mb-6">{subText}</p>
        <div className="flex justify-center gap-3">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-blue-600 text-white rounded shadow hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400"
          >
            OK
          </button>
        </div>
      </div>
    </div>
  )
} 