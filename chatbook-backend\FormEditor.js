import React, { useState, useEffect } from 'react';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useForms } from './FormsContext';
import axios from 'axios';

// Using direct connection to Django backend
const api = axios.create({
  baseURL: 'http://localhost:8000',
  withCredentials: true,
});

function FormEditor() {
  const { id } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { forms, updateForm, addForm, currentUser } = useForms();
  
  const [formData, setFormData] = useState({
    name: '',
    documentType: 'service',
    status: 'Draft',
    questions: {}
  });
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isSaving, setIsSaving] = useState(false);
  const [successMessage, setSuccessMessage] = useState('');
  const [formHistory, setFormHistory] = useState([]);
  
  // Load form data when component mounts
  useEffect(() => {
    const loadFormData = async () => {
      try {
        setLoading(true);
        
        // If editing an existing form
        if (id && id !== 'new') {
          // First try to find it in the forms context
          const existingForm = forms.find(f => f.id === parseInt(id));
          
          if (existingForm) {
            console.log('Found form in context:', existingForm);
            setFormData({
              name: existingForm.name,
              documentType: existingForm.documentType,
              status: existingForm.status,
              questions: existingForm.questions || {}
            });
            setFormHistory(existingForm.history || []);
          } else {
            // If not in context, fetch directly from API
            console.log('Fetching form from API...');
            const res = await api.get(`/api/forms/templates/${id}/`);
            console.log('API response:', res.data);
            
            setFormData({
              name: res.data.name,
              documentType: res.data.document_type,
              status: res.data.status.charAt(0).toUpperCase() + res.data.status.slice(1),
              questions: res.data.content || {}
            });
            setFormHistory(res.data.history || []);
            
            // Also fetch history
            try {
              const historyRes = await api.get(`/api/forms/templates/${id}/history/`);
              setFormHistory(historyRes.data.history || []);
            } catch (historyErr) {
              console.error("Error fetching history:", historyErr);
            }
          }
        } 
        // If creating from a template
        else if (location.state?.templateId) {
          const templateId = location.state.templateId;
          const template = forms.find(f => f.id === parseInt(templateId));
          
          if (template) {
            setFormData({
              name: template.name + ' (Copy)',
              documentType: template.documentType,
              status: 'Draft',
              questions: template.questions || {}
            });
          }
        }
        
        setLoading(false);
      } catch (err) {
        console.error("Error loading form:", err);
        setError(`Failed to load form: ${err.message}`);
        setLoading(false);
      }
    };
    
    loadFormData();
  }, [id, forms, location.state]);
  
  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear success message when form is edited
    if (successMessage) {
      setSuccessMessage('');
    }
  };
  
  // Handle adding a question
  const handleAddQuestion = (questionType) => {
    const newQuestionId = `q${Object.keys(formData.questions).length + 1}`;
    
    setFormData(prev => {
      const updatedQuestions = {
        ...prev.questions,
        [newQuestionId]: {
          type: questionType,
          text: questionType === 'text' ? 'Text' : 
                questionType === 'longAnswer' ? 'Long Answer' : 
                questionType === 'shortAnswer' ? 'Short Answer' : 
                questionType === 'multipleChoice' ? 'Multiple Choice' : 
                'Signature',
          required: false,
          options: questionType === 'multipleChoice' ? ['Option 1', 'Option 2'] : []
        }
      };
      
      return {
        ...prev,
        questions: updatedQuestions
      };
    });
  };
  
  // Handle updating a question
  const handleUpdateQuestion = (questionId, questionData) => {
    setFormData(prev => ({
      ...prev,
      questions: {
        ...prev.questions,
        [questionId]: questionData
      }
    }));
  };
  
  // Handle removing a question
  const handleRemoveQuestion = (questionId) => {
    setFormData(prev => {
      const updatedQuestions = { ...prev.questions };
      delete updatedQuestions[questionId];
      
      return {
        ...prev,
        questions: updatedQuestions
      };
    });
  };
  
  // Handle form submission
  const handleSubmit = async (e, saveAsDraft = false) => {
    e.preventDefault();
    
    try {
      setIsSaving(true);
      
      const dataToSubmit = {
        ...formData,
        status: saveAsDraft ? 'Draft' : 'Published'
      };
      
      if (id && id !== 'new') {
        // Update existing form
        await updateForm(id, dataToSubmit);
        setSuccessMessage(`Form ${saveAsDraft ? 'saved as draft' : 'published'} successfully!`);
        
        // Add to history
        const newHistoryEntry = {
          timestamp: new Date().toISOString(),
          user: currentUser.email,
          action: saveAsDraft ? 'saved as draft' : 'published'
        };
        setFormHistory([...formHistory, newHistoryEntry]);
      } else {
        // Create new form
        await addForm(dataToSubmit);
        setSuccessMessage(`Form ${saveAsDraft ? 'saved as draft' : 'published'} successfully!`);
        
        // Navigate back to forms list after short delay
        setTimeout(() => {
          navigate('/forms');
        }, 2000);
      }
      
      setIsSaving(false);
    } catch (err) {
      console.error("Error saving form:", err);
      setError(`Failed to save form: ${err.message}`);
      setIsSaving(false);
    }
  };
  
  // Handle cancel
  const handleCancel = () => {
    navigate('/forms');
  };
  
  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }
  
  if (error) {
    return (
      <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
        <p>{error}</p>
        <button 
          onClick={() => navigate('/forms')}
          className="mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700"
        >
          Back to Forms
        </button>
      </div>
    );
  }
  
  return (
    <div className="bg-white rounded-lg shadow-md p-6">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-2xl font-semibold text-gray-900">
          {id && id !== 'new' ? 'Edit Form' : 'New Form'}
        </h2>
        <div className="flex items-center gap-4">
          <button 
            onClick={handleCancel}
            className="px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-50"
          >
            Cancel
          </button>
          <button 
            onClick={(e) => handleSubmit(e, true)}
            disabled={isSaving}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 disabled:opacity-50"
          >
            {isSaving ? 'Saving...' : 'Save for Later'}
          </button>
          <button 
            onClick={(e) => handleSubmit(e, false)}
            disabled={isSaving}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {isSaving ? 'Publishing...' : 'Publish'}
          </button>
        </div>
      </div>
      
      {successMessage && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          {successMessage}
        </div>
      )}
      
      <div className="mb-6">
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">Form Name</label>
          <input 
            type="text" 
            name="name"
            value={formData.name}
            onChange={handleInputChange}
            className="w-full border border-gray-300 rounded-md px-3 py-2"
            placeholder="Enter form name"
          />
        </div>
        
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">Document Type</label>
          <select 
            name="documentType"
            value={formData.documentType}
            onChange={handleInputChange}
            className="w-full border border-gray-300 rounded-md px-3 py-2"
          >
            <option value="service">Service Form</option>
            <option value="intake">Intake Form</option>
            <option value="consent">Consent Form</option>
            <option value="feedback">Feedback Form</option>
          </select>
        </div>
        
        {formHistory.length > 0 && (
          <div className="mb-4 p-4 bg-gray-50 rounded-lg">
            <h3 className="text-sm font-medium text-gray-700 mb-2">Form History</h3>
            <div className="text-xs text-gray-500">
              <p>Last edited: {new Date(formHistory[formHistory.length - 1]?.timestamp || new Date()).toLocaleString()}</p>
              <p>By: {formHistory[formHistory.length - 1]?.user || 'Unknown'}</p>
            </div>
          </div>
        )}
      </div>
      
      <div className="border-t border-gray-200 pt-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4">Form Questions</h3>
        
        <div className="grid grid-cols-5 gap-4 mb-6">
          <button 
            onClick={() => handleAddQuestion('shortAnswer')}
            className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 flex flex-col items-center"
          >
            <span className="text-2xl mb-2">≡</span>
            <span>Short Answer</span>
          </button>
          
          <button 
            onClick={() => handleAddQuestion('longAnswer')}
            className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 flex flex-col items-center"
          >
            <span className="text-2xl mb-2">≡≡</span>
            <span>Long Answer</span>
          </button>
          
          <button 
            onClick={() => handleAddQuestion('multipleChoice')}
            className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 flex flex-col items-center"
          >
            <span className="text-2xl mb-2">☑</span>
            <span>Multiple Choice</span>
          </button>
          
          <button 
            onClick={() => handleAddQuestion('text')}
            className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 flex flex-col items-center"
          >
            <span className="text-2xl mb-2">T</span>
            <span>Text</span>
          </button>
          
          <button 
            onClick={() => handleAddQuestion('signature')}
            className="p-4 border border-gray-300 rounded-lg hover:bg-gray-50 flex flex-col items-center"
          >
            <span className="text-2xl mb-2">✓</span>
            <span>Signature</span>
          </button>
        </div>
        
        <div className="space-y-6">
          {Object.keys(formData.questions).length === 0 ? (
            <div className="text-center py-12 border-2 border-dashed border-gray-300 rounded-lg">
              <p className="text-gray-500">Drag and drop a question type here to add it to your form</p>
            </div>
          ) : (
            Object.entries(formData.questions).map(([questionId, question]) => (
              <div key={questionId} className="border border-gray-300 rounded-lg p-4">
                <div className="flex justify-between items-start mb-4">
                  <div className="flex-1">
                    <input 
                      type="text"
                      value={question.text}
                      onChange={(e) => handleUpdateQuestion(questionId, { ...question, text: e.target.value })}
                      className="w-full border-b border-gray-300 pb-1 text-lg font-medium focus:outline-none focus:border-blue-500"
                      placeholder="Question text"
                    />
                    <div className="text-sm text-gray-500 mt-1">
                      {question.type === 'shortAnswer' ? 'Short answer text' : 
                       question.type === 'longAnswer' ? 'Long answer text' :
                       question.type === 'multipleChoice' ? 'Multiple choice selection' :
                       question.type === 'text' ? 'Text display' :
                       'Signature field'}
                    </div>
                  </div>
                  <button 
                    onClick={() => handleRemoveQuestion(questionId)}
                    className="text-red-600 hover:text-red-800"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                  </button>
                </div>
                
                {question.type === 'multipleChoice' && (
                  <div className="mt-4">
                    {question.options.map((option, index) => (
                      <div key={index} className="flex items-center mb-2">
                        <div className="w-6 h-6 border border-gray-300 rounded-full mr-2"></div>
                        <input 
                          type="text"
                          value={option}
                          onChange={(e) => {
                            const newOptions = [...question.options];
                            newOptions[index] = e.target.value;
                            handleUpdateQuestion(questionId, { ...question, options: newOptions });
                          }}
                          className="flex-1 border-b border-gray-300 focus:outline-none focus:border-blue-500"
                          placeholder={`Option ${index + 1}`}
                        />
                        <button 
                          onClick={() => {
                            const newOptions = question.options.filter((_, i) => i !== index);
                            handleUpdateQuestion(questionId, { ...question, options: newOptions });
                          }}
                          className="ml-2 text-red-600 hover:text-red-800"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                          </svg>
                        </button>
                      </div>
                    ))}
                    
                    <button 
                      onClick={() => {
                        const newOptions = [...question.options, `Option ${question.options.length + 1}`];
                        handleUpdateQuestion(questionId, { ...question, options: newOptions });
                      }}
                      className="text-blue-600 hover:text-blue-800 text-sm flex items-center mt-2"
                    >
                      <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                      Add Option
                    </button>
                  </div>
                )}
                
                {question.type === 'text' && (
                  <div className="mt-4">
                    <textarea
                      value={question.content || ''}
                      onChange={(e) => handleUpdateQuestion(questionId, { ...question, content: e.target.value })}
                      className="w-full border border-gray-300 rounded-md px-3 py-2 h-24"
                      placeholder="Enter text content here"
                    ></textarea>
                  </div>
                )}
                
                <div className="mt-4 flex items-center">
                  <input 
                    type="checkbox"
                    id={`required-${questionId}`}
                    checked={question.required || false}
                    onChange={(e) => handleUpdateQuestion(questionId, { ...question, required: e.target.checked })}
                    className="mr-2"
                  />
                  <label htmlFor={`required-${questionId}`} className="text-sm text-gray-700">Required</label>
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
}

export default FormEditor; 