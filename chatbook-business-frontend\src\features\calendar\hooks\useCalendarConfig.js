import { useState, useEffect, useCallback } from 'react'

// Default calendar configuration matching iOS implementation
const DEFAULT_CONFIG = {
  // Time settings
  timeResolution: 30, // 5, 15, or 30 minutes
  displayHourStart: 7,  // 7 AM
  displayHourEnd: 22,   // 10 PM
  weekStartDay: 1,      // Monday (0=Sunday, 1=Monday)
  
  // View settings  
  defaultView: 'week',  // 'day' or 'week'
  gridHeight: 64,       // Height per hour slot in pixels
  
  // Visual settings
  showWorkingHours: true,
  showNonWorkingDays: true,
  showWeekends: true,
  
  // Interaction settings
  allowDragDrop: true,
  allowResize: true,
  clickToCreate: true,
  doubleClickToEdit: true,
  
  // Business rules
  preventOverlap: true,
  enforceWorkingHours: false,
  allowBackToBack: true,
  bufferTime: 0, // minutes between appointments
  
  // Scroll behavior
  scrollToCurrentTime: true,
  rememberScrollPosition: true,
  autoScrollOnDateChange: true
}

// Time resolution options
export const TIME_RESOLUTIONS = [
  { value: 5, label: '5 minutes', gridHeight: 300 },   // 12 slots × 15px = more granular
  { value: 15, label: '15 minutes', gridHeight: 120 }, // 4 slots × 30px
  { value: 30, label: '30 minutes', gridHeight: 100 }  // 2 slots × 50px
]

// Week start day options - All days of the week
export const WEEK_START_OPTIONS = [
  { value: 0, label: 'Sunday' },
  { value: 1, label: 'Monday' },
  { value: 2, label: 'Tuesday' },
  { value: 3, label: 'Wednesday' },
  { value: 4, label: 'Thursday' },
  { value: 5, label: 'Friday' },
  { value: 6, label: 'Saturday' }
]

// Display hours presets
export const DISPLAY_HOURS_PRESETS = [
  { name: 'Early Bird', start: 6, end: 18, label: '6 AM - 6 PM' },
  { name: 'Standard', start: 7, end: 22, label: '7 AM - 10 PM' },
  { name: 'Extended', start: 6, end: 24, label: '6 AM - 12 AM' },
  { name: '24 Hour', start: 0, end: 24, label: '24 Hours' }
]

export const useCalendarConfig = () => {
  const [config, setConfig] = useState(() => {
    // Try to load from localStorage
    const saved = localStorage.getItem('calendar-config')
    return saved ? { ...DEFAULT_CONFIG, ...JSON.parse(saved) } : DEFAULT_CONFIG
  })

  // Save to localStorage whenever config changes
  useEffect(() => {
    localStorage.setItem('calendar-config', JSON.stringify(config))
  }, [config])

  // Update specific config values
  const updateConfig = useCallback((updates) => {
    setConfig(prev => ({ ...prev, ...updates }))
  }, [])

  // Reset to defaults
  const resetConfig = useCallback(() => {
    setConfig(DEFAULT_CONFIG)
    localStorage.removeItem('calendar-config')
  }, [])

  // Time resolution management
  const setTimeResolution = useCallback((resolution) => {
    const resolutionConfig = TIME_RESOLUTIONS.find(r => r.value === resolution)
    if (resolutionConfig) {
      updateConfig({
        timeResolution: resolution,
        gridHeight: resolutionConfig.gridHeight
      })
    }
  }, [updateConfig])

  // Display hours management
  const setDisplayHours = useCallback((start, end) => {
    if (start >= 0 && start < 24 && end > start && end <= 24) {
      updateConfig({
        displayHourStart: start,
        displayHourEnd: end
      })
    }
  }, [updateConfig])

  // Week start day management
  const setWeekStartDay = useCallback((day) => {
    if (day >= 0 && day <= 6) {
      updateConfig({ weekStartDay: day })
    }
  }, [updateConfig])

  // View mode management
  const setDefaultView = useCallback((view) => {
    if (['day', 'week'].includes(view)) {
      updateConfig({ defaultView: view })
    }
  }, [updateConfig])

  // Working hours display
  const toggleWorkingHours = useCallback(() => {
    updateConfig({ showWorkingHours: !config.showWorkingHours })
  }, [config.showWorkingHours, updateConfig])

  // Drag and drop settings
  const toggleDragDrop = useCallback(() => {
    updateConfig({ allowDragDrop: !config.allowDragDrop })
  }, [config.allowDragDrop, updateConfig])

  // Resize settings
  const toggleResize = useCallback(() => {
    updateConfig({ allowResize: !config.allowResize })
  }, [config.allowResize, updateConfig])

  // Business rules
  const togglePreventOverlap = useCallback(() => {
    updateConfig({ preventOverlap: !config.preventOverlap })
  }, [config.preventOverlap, updateConfig])

  const toggleEnforceWorkingHours = useCallback(() => {
    updateConfig({ enforceWorkingHours: !config.enforceWorkingHours })
  }, [config.enforceWorkingHours, updateConfig])

  // Helper functions
  const getTimeSlots = useCallback(() => {
    const slots = []
    const startHour = config.displayHourStart
    const endHour = config.displayHourEnd
    const resolution = config.timeResolution

    for (let hour = startHour; hour < endHour; hour++) {
      for (let minute = 0; minute < 60; minute += resolution) {
        // Generate readable label based on resolution
        let label = ''
        
        if (minute === 0) {
          // Always show hour labels
          const hour12 = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour
          const period = hour < 12 ? 'AM' : 'PM'
          label = `${hour12} ${period}`
        } else if (resolution === 30 && minute === 30) {
          // Show 30-minute marker for 30-minute resolution
          label = '30'
        } else if (resolution === 15 && minute % 15 === 0) {
          // Show quarter-hour markers for 15-minute resolution
          label = minute.toString()
        } else if (resolution === 5 && minute % 15 === 0) {
          // Show quarter-hour markers for 5-minute resolution (every 15 minutes)
          label = minute === 0 ? '' : minute.toString() // Don't duplicate hour label
        }

        slots.push({
          hour,
          minute,
          time: `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`,
          label
        })
      }
    }

    return slots
  }, [config.displayHourStart, config.displayHourEnd, config.timeResolution])

  const getDisplayHours = useCallback(() => {
    const hours = []
    for (let hour = config.displayHourStart; hour < config.displayHourEnd; hour++) {
      hours.push({
        hour,
        label: `${hour.toString().padStart(2, '0')}:00`,
        is12Hour: hour === 0 ? '12 AM' : hour === 12 ? '12 PM' : hour > 12 ? `${hour - 12} PM` : `${hour} AM`
      })
    }
    return hours
  }, [config.displayHourStart, config.displayHourEnd])

  const isWorkingHour = useCallback((hour, employeeId, date) => {
    // This will be implemented with working hours integration
    return hour >= 9 && hour < 17 // Default business hours
  }, [])

  const getGridHeight = useCallback(() => {
    return config.gridHeight
  }, [config.gridHeight])

  const getSlotsPerHour = useCallback(() => {
    return 60 / config.timeResolution
  }, [config.timeResolution])

  return {
    // Config state
    config,
    
    // Update functions
    updateConfig,
    resetConfig,
    setTimeResolution,
    setDisplayHours,
    setWeekStartDay,
    setDefaultView,
    
    // Toggle functions
    toggleWorkingHours,
    toggleDragDrop,
    toggleResize,
    togglePreventOverlap,
    toggleEnforceWorkingHours,
    
    // Helper functions
    getTimeSlots,
    getDisplayHours,
    isWorkingHour,
    getGridHeight,
    getSlotsPerHour,
    
    // Constants
    TIME_RESOLUTIONS,
    WEEK_START_OPTIONS,
    DISPLAY_HOURS_PRESETS
  }
} 