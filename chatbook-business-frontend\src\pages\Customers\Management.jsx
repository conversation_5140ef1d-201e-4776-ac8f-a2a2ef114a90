import { useState, useEffect } from 'react'
import useCustomerData from '../../features/customers/hooks/useCustomerData'
import CustomerFilters from '../../features/customers/components/CustomerFilters'
import CustomerTable from '../../features/customers/components/CustomerTable'
import CustomerPagination from '../../features/customers/components/CustomerPagination'
import AddCustomerModal from '../../features/customers/components/AddCustomerModal'
import { customerApi } from '../../features/customers/services/customerApi'

function CustomerManagement() {
  const [showAddModal, setShowAddModal] = useState(false)
  const [customers, setCustomers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  
  // Fetch customers from backend
  useEffect(() => {
    const fetchCustomers = async () => {
      try {
        setLoading(true)
        setError(null)
        
        // Try to fetch from backend first
        const response = await customerApi.getCustomers()
        
        // Handle different possible response structures
        let customersData = []
        if (Array.isArray(response)) {
          customersData = response
        } else if (response.customers && Array.isArray(response.customers)) {
          customersData = response.customers
        } else if (response.results && Array.isArray(response.results)) {
          customersData = response.results
        } else if (response.data && Array.isArray(response.data)) {
          customersData = response.data
        }
        
        console.log('📊 Fetched customers from backend:', customersData.length)

        // Transform backend fields to match frontend expectations
        const transformed = customersData.map((c) => {
          // extract potential nested user object
          const user = c.user || c.customer_profile?.user || {}
          let fullName = c.name || user.full_name
          if (!fullName) {
            const first = c.first_name || user.first_name || user.firstname || ''
            const last = c.last_name || user.last_name || user.lastname || ''
            fullName = `${first} ${last}`.trim()
          }

          return {
            // Keep original props
            ...c,
            // Map/derive fields used by table & filters
            id: c.id || c.pk || c.uuid || user.id,
            name: fullName,
            email: c.email || c.email_address || c.primary_email || user.email || '',
            phone: c.phone || c.phone_number || c.mobile_phone || c.mobile || user.phone_number || '',
            avatar: c.avatar || c.profile_pic || c.profile_picture || 'https://ui-avatars.com/api/?name=' + encodeURIComponent(fullName),
            status: c.status || c.state || 'active',
            joinDate: c.join_date || c.joinDate || c.created_at || c.createdAt || user.created_at,
            lastOrder: c.last_order || c.lastOrder,
            totalOrders: c.total_orders || c.totalOrders || 0,
            address: c.address || {
              street: c.street || '',
              postalCode: c.postal_code || c.zip || '',
              city: c.city || '',
              country: c.country || ''
            }
          }
        })

        setCustomers(transformed)
        
      } catch (error) {
        console.error('❌ Failed to fetch customers:', error)
        setError(error.message)
        // No fallback data - keep customers array empty to show proper error state
      } finally {
        setLoading(false)
      }
    }
    
    fetchCustomers()
  }, [])

  const {
    filteredCustomers,
    searchTerm,
    setSearchTerm,
    sortBy,
    sortOrder,
    handleSort,
    currentPage,
    totalPages,
    totalItems,
    startItem,
    endItem,
    itemsPerPage,
    goToPage,
    goToNextPage,
    goToPrevPage,
  } = useCustomerData(customers)

  const handleAddCustomer = (newCustomer) => {
    setCustomers(prev => [newCustomer, ...prev])
    setShowAddModal(false)
  }

  const openAddModal = () => {
    setShowAddModal(true)
  }

  const closeAddModal = () => {
    setShowAddModal(false)
  }

  return (
    <div className="container py-10">
      {/* Page header */}
      <header className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900">Customer Management</h1>
        <p className="text-gray-600 mt-2">Manage and organize your customer database</p>
        
        {/* Loading/Error States */}
        {loading && (
          <div className="mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"></div>
              <span className="text-blue-800">Loading customers from backend...</span>
            </div>
          </div>
        )}
        
        {error && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <div className="flex items-center">
              <svg className="w-4 h-4 text-red-600 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <span className="text-red-800">
                Failed to load customer data from server. Please check your connection and try again.
              </span>
            </div>
          </div>
        )}
      </header>

      {/* Search and Actions Bar */}
      <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          {/* Search Section */}
          <div className="flex-1">
            <CustomerFilters
              searchTerm={searchTerm}
              setSearchTerm={setSearchTerm}
            />
          </div>
          
          {/* Actions Section */}
          <div className="flex items-center space-x-3">
            <button className="inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export
            </button>
            <button 
              onClick={openAddModal}
              className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors shadow-sm"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
              Add Customer
            </button>
          </div>
        </div>
      </div>

      {/* Table */}
      {!error ? (
        <>
          <CustomerTable
            customers={filteredCustomers}
            onSort={handleSort}
            sortBy={sortBy}
            sortOrder={sortOrder}
          />

          {/* Pagination */}
          <CustomerPagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={totalItems}
            startItem={startItem}
            endItem={endItem}
            itemsPerPage={itemsPerPage}
            onPageChange={goToPage}
            onNextPage={goToNextPage}
            onPrevPage={goToPrevPage}
          />
        </>
      ) : (
        /* Error State */
        <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-12 text-center">
          <svg className="mx-auto h-16 w-16 text-red-400 mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Unable to Load Customer Data</h3>
          <p className="text-gray-600 mb-6">There was a problem connecting to the server. Please try refreshing the page or contact support if the issue persists.</p>
          <button 
            onClick={() => window.location.reload()}
            className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors"
          >
            <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
            </svg>
            Retry
          </button>
        </div>
      )}

      {/* Add Customer Modal */}
      <AddCustomerModal
        isOpen={showAddModal}
        onClose={closeAddModal}
        onAddCustomer={handleAddCustomer}
      />
    </div>
  )
}

export default CustomerManagement 