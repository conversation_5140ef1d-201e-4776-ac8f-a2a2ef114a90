# Checkout 系统集成指南

## 🎯 概述

本指南说明如何将您现有的 checkout 组件与新的 API 系统集成，从 mock 数据转换为真实的后端 API 调用。

---

## 📁 文件结构

```
src/features/checkout/
├── services/
│   └── checkoutApi.js          # API服务类
├── hooks/
│   └── useCheckout.js          # 状态管理Hook
└── components/
    └── CheckoutIntegration.jsx  # 集成示例组件
```

---

## 🔧 集成步骤

### 1. 环境配置

首先在您的 `.env` 文件中添加 API 配置：

```env
# .env
VITE_API_URL=http://localhost:8000/api
VITE_STRIPE_PUBLIC_KEY=pk_test_...  # 如果使用Stripe
```

### 2. 更新现有 Checkout 组件

在您的 `src/pages/Checkout/index.jsx` 中集成新的 API：

```javascript
// 在文件顶部添加导入
import { useCheckout } from "../../features/checkout/hooks/useCheckout";

function Checkout() {
  // 使用新的checkout hook替换现有状态
  const {
    // 数据
    services,
    products,
    staff,
    checkoutSettings,
    cartItems,
    orderTotal,
    loading,
    error,

    // API操作
    loadServices,
    loadProducts,
    loadStaff,
    scanProduct,

    // 购物车管理
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,

    // 结账处理
    completeCheckout,
  } = useCheckout();

  // 替换现有的mock数据
  useEffect(() => {
    loadServices();
    loadProducts();
    loadStaff();
  }, []);

  // 更新条形码扫描处理
  const handleBarcodeSubmit = async (barcode) => {
    if (!barcode.trim()) return;

    const product = await scanProduct(barcode);
    if (product) {
      addToCart({
        type: "product",
        product_id: product.id,
        name: product.name,
        price: product.price,
        quantity: 1,
      });
      setBarcodeInput("");
    }
  };

  // 更新服务添加处理
  const handleAddService = () => {
    if (!selectedCustomer) {
      setShowCustomerRequiredAlert(true);
      return;
    }

    const selectedService = services.find(
      (s) => s.name === serviceForm.service
    );
    const selectedStaff = staff.find((s) => s.name === serviceForm.provider);

    if (selectedService && selectedStaff) {
      addToCart({
        type: "service",
        service_id: selectedService.id,
        staff_id: selectedStaff.id,
        name: selectedService.name,
        price: selectedService.price,
        quantity: 1,
        start_time: serviceForm.startTime,
        provider: serviceForm.provider,
      });
    }

    setShowServiceModal(false);
  };

  // 更新结账处理
  const handleCheckout = async () => {
    if (!selectedCustomer) {
      setShowCustomerRequiredAlert(true);
      return;
    }

    try {
      const paymentMethods = Object.entries(paymentAmounts)
        .filter(([_, amount]) => parseFloat(amount) > 0)
        .map(([type, amount]) => ({
          type,
          amount: parseFloat(amount),
        }));

      const result = await completeCheckout(selectedCustomer, paymentMethods, {
        notes: "POS订单",
      });

      alert(`订单创建成功! 订单号: ${result.order.order_number}`);

      // 重置界面
      setSelectedCustomer("");
      setPaymentAmounts({
        cash: 0,
        check: 0,
        iou: 0,
        creditCard: 0,
        giftCard: 0,
        other: 0,
      });
    } catch (error) {
      alert(`结账失败: ${error.message}`);
    }
  };

  // 更新服务选择下拉菜单
  const renderServiceOptions = () => {
    return services.map((service) => (
      <option key={service.id} value={service.name}>
        {service.name} - ${service.price}
      </option>
    ));
  };

  // 更新员工选择下拉菜单
  const renderStaffOptions = () => {
    return staff.map((member) => (
      <option key={member.id} value={member.name}>
        {member.name}
      </option>
    ));
  };

  // 显示加载状态
  if (loading.services || loading.products || loading.staff) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-xl">加载中...</div>
      </div>
    );
  }

  // 显示错误
  if (error) {
    return (
      <div className="flex justify-center items-center h-screen">
        <div className="text-red-600 text-xl">错误: {error}</div>
      </div>
    );
  }

  // 原有的渲染逻辑保持不变，只需要更新数据源
  return (
    <div className="flex bg-gray-50" style={{ height: "calc(100vh - 64px)" }}>
      {/* 现有的UI保持不变 */}

      {/* 更新购物车显示 */}
      {cartItems.length > 0 && (
        <div className="flex-1 overflow-auto">
          <div className="bg-white rounded-lg shadow-sm border border-gray-200">
            {/* 购物车表格 */}
            {cartItems.map((item) => (
              <div key={item.id} className="grid grid-cols-11 gap-2 p-3">
                <div className="col-span-2 text-blue-600 font-medium">
                  {item.name}
                </div>
                <div className="text-gray-600">{item.type.toUpperCase()}</div>
                <div>{item.provider || "-"}</div>
                <div className="text-center">{item.quantity}</div>
                <div>${parseFloat(item.price || 0).toFixed(2)}</div>
                <div>${parseFloat(item.discount || 0).toFixed(2)}</div>
                <div>-</div>
                <div className="font-medium">${item.subtotal.toFixed(2)}</div>
                <div className="text-center">-</div>
                <div className="text-center">-</div>
                <div className="text-center">
                  <button
                    onClick={() => removeFromCart(item.id)}
                    className="text-red-600 hover:text-red-800"
                  >
                    ×
                  </button>
                </div>
              </div>
            ))}

            {/* 总计行 */}
            <div className="bg-gray-50 grid grid-cols-11 gap-2 p-3 text-sm font-bold">
              <div className="col-span-8">总计</div>
              <div>${orderTotal.subtotal.toFixed(2)}</div>
              <div className="col-span-2"></div>
            </div>
          </div>
        </div>
      )}

      {/* 右侧价格明细 */}
      <div className="w-72 bg-white border-l border-gray-200 flex flex-col">
        <div className="flex-1 p-3">
          <div className="space-y-2">
            <div className="flex justify-between items-center">
              <span className="text-gray-700 text-sm">小计</span>
              <span className="font-medium text-sm">
                ${orderTotal.subtotal.toFixed(2)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-700 text-sm">税费</span>
              <span className="font-medium text-sm">
                ${orderTotal.tax.toFixed(2)}
              </span>
            </div>
            <div className="flex justify-between items-center">
              <span className="text-gray-700 text-sm">小费</span>
              <span className="font-medium text-sm">
                ${orderTotal.tip.toFixed(2)}
              </span>
            </div>
            <div className="border-t pt-2">
              <div className="flex justify-between items-center bg-green-100 px-2 py-2 rounded">
                <span className="text-base font-semibold text-green-800">
                  总计
                </span>
                <span className="text-base font-bold text-green-800">
                  ${orderTotal.total.toFixed(2)}
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* 结账按钮 */}
        <div className="border-t border-gray-200 p-3">
          <button
            onClick={handleCheckout}
            disabled={loading.order || loading.payment}
            className="w-full bg-green-600 hover:bg-green-700 text-white py-2 rounded-lg font-medium text-sm disabled:opacity-50"
          >
            {loading.order || loading.payment ? "处理中..." : "结账"}
          </button>
        </div>
      </div>
    </div>
  );
}
```

### 3. 更新其他 Checkout 页面

#### 发票页面 (`src/pages/Checkout/Invoices.jsx`)

```javascript
import { useState, useEffect } from "react";
import { checkoutApi } from "../../features/checkout/services/checkoutApi";

function Invoices() {
  const [invoices, setInvoices] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  useEffect(() => {
    loadInvoices();
  }, []);

  const loadInvoices = async () => {
    setLoading(true);
    try {
      const response = await checkoutApi.getInvoices();
      setInvoices(response.results || response);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const downloadInvoice = async (invoiceId) => {
    try {
      const blob = await checkoutApi.downloadInvoicePdf(invoiceId);
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `invoice-${invoiceId}.pdf`;
      a.click();
      window.URL.revokeObjectURL(url);
    } catch (err) {
      alert("下载发票失败: " + err.message);
    }
  };

  if (loading) return <div className="container py-10">加载中...</div>;
  if (error)
    return <div className="container py-10 text-red-600">错误: {error}</div>;

  return (
    <div className="container py-10">
      <h1 className="text-3xl mb-6">发票管理</h1>
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="overflow-x-auto">
          <table className="w-full table-auto">
            <thead>
              <tr className="bg-gray-100">
                <th className="px-4 py-2 text-left">发票号</th>
                <th className="px-4 py-2 text-left">客户</th>
                <th className="px-4 py-2 text-left">金额</th>
                <th className="px-4 py-2 text-left">状态</th>
                <th className="px-4 py-2 text-left">日期</th>
                <th className="px-4 py-2 text-left">操作</th>
              </tr>
            </thead>
            <tbody>
              {invoices.map((invoice) => (
                <tr key={invoice.id} className="border-b">
                  <td className="px-4 py-2">{invoice.invoice_number}</td>
                  <td className="px-4 py-2">{invoice.customer.name}</td>
                  <td className="px-4 py-2">${invoice.total_amount}</td>
                  <td className="px-4 py-2">
                    <span
                      className={`px-2 py-1 rounded text-xs ${
                        invoice.status === "paid"
                          ? "bg-green-100 text-green-800"
                          : invoice.status === "pending"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-red-100 text-red-800"
                      }`}
                    >
                      {invoice.status}
                    </span>
                  </td>
                  <td className="px-4 py-2">
                    {new Date(invoice.created_at).toLocaleDateString()}
                  </td>
                  <td className="px-4 py-2">
                    <button
                      onClick={() => downloadInvoice(invoice.id)}
                      className="text-blue-600 hover:text-blue-800"
                    >
                      下载
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}

export default Invoices;
```

#### 退款页面 (`src/pages/Checkout/Refunds.jsx`)

```javascript
import { useState, useEffect } from "react";
import { checkoutApi } from "../../features/checkout/services/checkoutApi";

function RefundCustomer() {
  const [orders, setOrders] = useState([]);
  const [selectedOrder, setSelectedOrder] = useState(null);
  const [refundAmount, setRefundAmount] = useState("");
  const [refundReason, setRefundReason] = useState("");
  const [loading, setLoading] = useState(false);

  const handleRefund = async (paymentId) => {
    if (!refundAmount || !refundReason) {
      alert("请输入退款金额和原因");
      return;
    }

    setLoading(true);
    try {
      await checkoutApi.processRefund(paymentId, {
        amount: parseFloat(refundAmount),
        reason: refundReason,
      });

      alert("退款处理成功");
      setSelectedOrder(null);
      setRefundAmount("");
      setRefundReason("");
    } catch (err) {
      alert("退款失败: " + err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container py-10">
      <h1 className="text-3xl mb-6">退款处理</h1>
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">订单搜索</h3>
            {/* 订单搜索和选择界面 */}
          </div>

          <div>
            <h3 className="text-lg font-semibold mb-4">退款处理</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  退款金额
                </label>
                <input
                  type="number"
                  value={refundAmount}
                  onChange={(e) => setRefundAmount(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg"
                  placeholder="输入退款金额"
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  退款原因
                </label>
                <textarea
                  value={refundReason}
                  onChange={(e) => setRefundReason(e.target.value)}
                  className="w-full px-3 py-2 border rounded-lg"
                  rows={3}
                  placeholder="输入退款原因"
                />
              </div>

              <button
                onClick={() =>
                  selectedOrder && handleRefund(selectedOrder.payment_id)
                }
                disabled={loading || !selectedOrder}
                className="w-full bg-red-600 hover:bg-red-700 text-white py-2 rounded-lg disabled:opacity-50"
              >
                {loading ? "处理中..." : "处理退款"}
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default RefundCustomer;
```

#### 设置页面 (`src/pages/Checkout/Settings.jsx`)

```javascript
import { useState, useEffect } from "react";
import { checkoutApi } from "../../features/checkout/services/checkoutApi";

function CheckoutSettings() {
  const [settings, setSettings] = useState({
    tax_rate: 0.08,
    auto_calculate_tax: true,
    default_payment_method: "cash",
    accept_tips: true,
    tip_suggestions: [15, 18, 20],
    auto_print_receipt: true,
  });
  const [loading, setLoading] = useState(false);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    loadSettings();
  }, []);

  const loadSettings = async () => {
    setLoading(true);
    try {
      const response = await checkoutApi.getCheckoutSettings();
      setSettings(response);
    } catch (err) {
      console.error("Failed to load settings:", err);
    } finally {
      setLoading(false);
    }
  };

  const handleSave = async () => {
    setSaving(true);
    try {
      await checkoutApi.updateCheckoutSettings(settings);
      alert("设置保存成功");
    } catch (err) {
      alert("保存失败: " + err.message);
    } finally {
      setSaving(false);
    }
  };

  if (loading) return <div className="container py-10">加载中...</div>;

  return (
    <div className="container py-10">
      <h1 className="text-3xl mb-6">收银设置</h1>
      <div className="bg-white p-6 rounded-lg shadow-md">
        <div className="space-y-6">
          <div>
            <label className="block text-sm font-medium mb-2">税率</label>
            <input
              type="number"
              step="0.01"
              min="0"
              max="1"
              value={settings.tax_rate}
              onChange={(e) =>
                setSettings({
                  ...settings,
                  tax_rate: parseFloat(e.target.value) || 0,
                })
              }
              className="w-full px-3 py-2 border rounded-lg"
            />
          </div>

          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.auto_calculate_tax}
                onChange={(e) =>
                  setSettings({
                    ...settings,
                    auto_calculate_tax: e.target.checked,
                  })
                }
                className="w-4 h-4"
              />
              <span>自动计算税费</span>
            </label>
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">
              默认支付方式
            </label>
            <select
              value={settings.default_payment_method}
              onChange={(e) =>
                setSettings({
                  ...settings,
                  default_payment_method: e.target.value,
                })
              }
              className="w-full px-3 py-2 border rounded-lg"
            >
              <option value="cash">现金</option>
              <option value="credit_card">信用卡</option>
              <option value="check">支票</option>
            </select>
          </div>

          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.accept_tips}
                onChange={(e) =>
                  setSettings({ ...settings, accept_tips: e.target.checked })
                }
                className="w-4 h-4"
              />
              <span>接受小费</span>
            </label>
          </div>

          <div>
            <label className="flex items-center space-x-2">
              <input
                type="checkbox"
                checked={settings.auto_print_receipt}
                onChange={(e) =>
                  setSettings({
                    ...settings,
                    auto_print_receipt: e.target.checked,
                  })
                }
                className="w-4 h-4"
              />
              <span>自动打印收据</span>
            </label>
          </div>

          <button
            onClick={handleSave}
            disabled={saving}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-lg disabled:opacity-50"
          >
            {saving ? "保存中..." : "保存设置"}
          </button>
        </div>
      </div>
    </div>
  );
}

export default CheckoutSettings;
```

---

## 🔄 迁移清单

### 必需的更改：

- [ ] 添加环境变量配置
- [ ] 在主 checkout 组件中导入和使用 `useCheckout` hook
- [ ] 更新服务和员工数据源（从 mock 数据改为 API）
- [ ] 实现条形码扫描 API 调用
- [ ] 集成真实的结账流程
- [ ] 更新发票、退款、设置页面

### 可选的增强：

- [ ] 添加离线支持
- [ ] 实现实时库存更新
- [ ] 添加收据打印功能
- [ ] 集成支付网关（Stripe、Square 等）
- [ ] 添加客户积分系统
- [ ] 实现礼品卡功能

---

## 🚨 注意事项

1. **错误处理**: 确保所有 API 调用都有适当的错误处理
2. **Loading 状态**: 在 API 调用期间显示 loading 状态
3. **数据验证**: 在发送到 API 之前验证所有用户输入
4. **安全性**: 确保所有敏感操作都有适当的权限检查
5. **性能**: 考虑缓存频繁访问的数据（如服务列表）

---

## 📞 支持

如果您在集成过程中遇到问题，请查看：

1. API 文档: `CHECKOUT_API_SPEC.md`
2. 错误日志: 浏览器开发者工具的 Console
3. 网络请求: 开发者工具的 Network 标签页

记住先实现核心功能，然后再添加高级特性！
