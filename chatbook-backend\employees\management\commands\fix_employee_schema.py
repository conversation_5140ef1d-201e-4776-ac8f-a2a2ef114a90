from django.core.management.base import BaseCommand
from django.db import connection
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Fixes the employees_employee table schema by adding the missing user_id column'

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS('Starting schema repair...'))
        
        with connection.cursor() as cursor:
            # First check if the column exists
            cursor.execute("""
                PRAGMA table_info(employees_employee);
            """)
            columns = cursor.fetchall()
            column_names = [col[1] for col in columns]
            
            if 'user_id' not in column_names:
                self.stdout.write(self.style.WARNING('user_id column missing, adding it...'))
                
                # Add the user_id column
                try:
                    cursor.execute("""
                        ALTER TABLE employees_employee 
                        ADD COLUMN user_id varchar(36) REFERENCES auth_user(id);
                    """)
                    self.stdout.write(self.style.SUCCESS('Added user_id column successfully'))
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'Error adding column: {str(e)}'))
            else:
                self.stdout.write(self.style.SUCCESS('user_id column already exists, no action needed'))
                
        self.stdout.write(self.style.SUCCESS('Schema repair completed')) 