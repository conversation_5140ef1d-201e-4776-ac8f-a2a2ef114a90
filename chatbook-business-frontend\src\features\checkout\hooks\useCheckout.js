import { useState, useEffect } from "react";
import { checkoutApi } from "../services/checkoutApi";

/**
 * Custom hook for managing checkout/POS functionality
 * Provides state management and API integration for the checkout system
 */
export function useCheckout() {
  // ========== State Management ==========

  // Data states
  const [services, setServices] = useState([]);
  const [products, setProducts] = useState([]);
  const [staff, setStaff] = useState([]);
  const [checkoutSettings, setCheckoutSettings] = useState(null);

  // Cart and order states
  const [cartItems, setCartItems] = useState([]);
  const [currentOrder, setCurrentOrder] = useState(null);
  const [orderTotal, setOrderTotal] = useState({
    subtotal: 0,
    discount: 0,
    tax: 0,
    tip: 0,
    total: 0,
  });

  // Loading states
  const [loading, setLoading] = useState({
    services: false,
    products: false,
    staff: false,
    order: false,
    payment: false,
  });

  // Error states
  const [error, setError] = useState(null);

  // ========== API Operations ==========

  // 加载服务列表
  const loadServices = async (params = {}) => {
    setLoading((prev) => ({ ...prev, services: true }));
    setError(null);

    try {
      const response = await checkoutApi.getServices(params);
      setServices(response.results || response);
      return response;
    } catch (err) {
      setError(err.message);
      console.error("Failed to load services:", err);
    } finally {
      setLoading((prev) => ({ ...prev, services: false }));
    }
  };

  // 加载产品列表
  const loadProducts = async (params = {}) => {
    setLoading((prev) => ({ ...prev, products: true }));
    setError(null);

    try {
      const response = await checkoutApi.getProducts(params);
      setProducts(response.results || response);
      return response;
    } catch (err) {
      setError(err.message);
      console.error("Failed to load products:", err);
    } finally {
      setLoading((prev) => ({ ...prev, products: false }));
    }
  };

  // 加载员工列表
  const loadStaff = async (params = {}) => {
    setLoading((prev) => ({ ...prev, staff: true }));
    setError(null);

    try {
      const response = await checkoutApi.getStaff(params);
      setStaff(response.results || response);
      return response;
    } catch (err) {
      setError(err.message);
      console.error("Failed to load staff:", err);
    } finally {
      setLoading((prev) => ({ ...prev, staff: false }));
    }
  };

  // 扫码获取产品
  const scanProduct = async (barcode) => {
    setLoading((prev) => ({ ...prev, products: true }));
    setError(null);

    try {
      const product = await checkoutApi.scanProduct(barcode);
      return product;
    } catch (err) {
      setError(err.message);
      console.error("Failed to scan product:", err);
      return null;
    } finally {
      setLoading((prev) => ({ ...prev, products: false }));
    }
  };

  // 获取员工可用时间
  const getStaffAvailability = async (staffId, date, serviceId) => {
    try {
      const availability = await checkoutApi.getStaffAvailability(
        staffId,
        date,
        serviceId
      );
      return availability;
    } catch (err) {
      setError(err.message);
      console.error("Failed to get staff availability:", err);
      return null;
    }
  };

  // ========== Cart Management ==========

  // 添加项目到购物车
  const addToCart = (item) => {
    const cartItem = {
      id: Date.now(),
      ...item,
      quantity: item.quantity || 1,
      subtotal: calculateItemSubtotal(item),
    };

    setCartItems((prev) => [...prev, cartItem]);
    updateOrderTotal([...cartItems, cartItem]);
  };

  // 更新购物车项目
  const updateCartItem = (itemId, updates) => {
    setCartItems((prev) =>
      prev.map((item) =>
        item.id === itemId
          ? {
              ...item,
              ...updates,
              subtotal: calculateItemSubtotal({ ...item, ...updates }),
            }
          : item
      )
    );
  };

  // 移除购物车项目
  const removeFromCart = (itemId) => {
    setCartItems((prev) => prev.filter((item) => item.id !== itemId));
  };

  // 清空购物车
  const clearCart = () => {
    setCartItems([]);
    setOrderTotal({
      subtotal: 0,
      discount: 0,
      tax: 0,
      tip: 0,
      total: 0,
    });
  };

  // 计算项目小计
  const calculateItemSubtotal = (item) => {
    const price = parseFloat(item.price) || 0;
    const discount = parseFloat(item.discount) || 0;
    const quantity = parseInt(item.quantity) || 1;
    return (price - discount) * quantity;
  };

  // 更新订单总计
  const updateOrderTotal = (items = cartItems) => {
    const subtotal = items.reduce((sum, item) => sum + item.subtotal, 0);

    // 这里可以从设置中获取税率
    const taxRate = checkoutSettings?.tax_rate || 0.08;
    const tax = subtotal * taxRate;

    const total = subtotal + tax + orderTotal.tip - orderTotal.discount;

    setOrderTotal({
      subtotal,
      discount: orderTotal.discount,
      tax,
      tip: orderTotal.tip,
      total,
    });
  };

  // ========== Order Processing ==========

  // 创建订单
  const createOrder = async (orderData) => {
    setLoading((prev) => ({ ...prev, order: true }));
    setError(null);

    try {
      const order = await checkoutApi.createOrder({
        ...orderData,
        items: cartItems.map((item) => ({
          type: item.type,
          service_id: item.service_id,
          product_id: item.product_id,
          staff_id: item.staff_id,
          quantity: item.quantity,
          price: item.price,
          discount: item.discount,
          start_time: item.start_time,
          notes: item.notes,
        })),
      });

      setCurrentOrder(order);
      return order;
    } catch (err) {
      setError(err.message);
      console.error("Failed to create order:", err);
      throw err;
    } finally {
      setLoading((prev) => ({ ...prev, order: false }));
    }
  };

  // 处理支付
  const processPayment = async (paymentData) => {
    setLoading((prev) => ({ ...prev, payment: true }));
    setError(null);

    try {
      const payment = await checkoutApi.processPayment(paymentData);
      return payment;
    } catch (err) {
      setError(err.message);
      console.error("Failed to process payment:", err);
      throw err;
    } finally {
      setLoading((prev) => ({ ...prev, payment: false }));
    }
  };

  // 完成结账流程
  const completeCheckout = async (
    customerId,
    paymentMethods,
    orderOptions = {}
  ) => {
    try {
      // 1. 创建订单
      const orderData = {
        customer_id: customerId,
        tax_rate: checkoutSettings?.tax_rate || 0.08,
        tip_amount: orderTotal.tip,
        notes: orderOptions.notes || "",
        ...orderOptions,
      };

      const order = await createOrder(orderData);

      // 2. 处理支付
      const paymentData = {
        order_id: order.id,
        payment_methods: paymentMethods,
        customer_id: customerId,
      };

      const payment = await processPayment(paymentData);

      // 3. 清空购物车
      clearCart();

      return { order, payment };
    } catch (err) {
      console.error("Checkout failed:", err);
      throw err;
    }
  };

  // ========== Settings Management ==========

  // 加载收银设置
  const loadCheckoutSettings = async () => {
    try {
      const settings = await checkoutApi.getCheckoutSettings();
      setCheckoutSettings(settings);
      return settings;
    } catch (err) {
      setError(err.message);
      console.error("Failed to load checkout settings:", err);
    }
  };

  // 更新收银设置
  const updateSettings = async (settingsData) => {
    try {
      const settings = await checkoutApi.updateCheckoutSettings(settingsData);
      setCheckoutSettings(settings);
      return settings;
    } catch (err) {
      setError(err.message);
      console.error("Failed to update checkout settings:", err);
    }
  };

  // ========== Gift Cards and Points ==========

  // 验证礼品卡
  const validateGiftCard = async (cardNumber) => {
    try {
      const result = await checkoutApi.validateGiftCard(cardNumber);
      return result;
    } catch (err) {
      setError(err.message);
      console.error("Failed to validate gift card:", err);
      return null;
    }
  };

  // 使用礼品卡
  const useGiftCard = async (cardNumber, amount) => {
    try {
      const result = await checkoutApi.useGiftCard(cardNumber, amount);
      return result;
    } catch (err) {
      setError(err.message);
      console.error("Failed to use gift card:", err);
      return null;
    }
  };

  // 获取客户积分
  const getCustomerPoints = async (customerId) => {
    try {
      const result = await checkoutApi.getCustomerPoints(customerId);
      return result;
    } catch (err) {
      setError(err.message);
      console.error("Failed to get customer points:", err);
      return null;
    }
  };

  // 使用积分
  const usePoints = async (customerId, points) => {
    try {
      const result = await checkoutApi.usePoints(customerId, points);
      return result;
    } catch (err) {
      setError(err.message);
      console.error("Failed to use points:", err);
      return null;
    }
  };

  // ========== Effects ==========

  // 当购物车项目改变时更新总计
  useEffect(() => {
    updateOrderTotal();
  }, [cartItems]);

  // 初始化时加载设置
  useEffect(() => {
    loadCheckoutSettings();
  }, []);

  // ========== Return Values ==========

  return {
    // Data
    services,
    products,
    staff,
    checkoutSettings,
    cartItems,
    currentOrder,
    orderTotal,

    // Loading states
    loading,
    error,

    // API operations
    loadServices,
    loadProducts,
    loadStaff,
    scanProduct,
    getStaffAvailability,

    // Cart management
    addToCart,
    updateCartItem,
    removeFromCart,
    clearCart,

    // Order processing
    createOrder,
    processPayment,
    completeCheckout,

    // Settings
    loadCheckoutSettings,
    updateSettings,

    // Gift cards and points
    validateGiftCard,
    useGiftCard,
    getCustomerPoints,
    usePoints,

    // Utilities
    setError,
    setOrderTotal,
  };
}

export default useCheckout;
