import { calculateAppointmentHeight } from '../../../utils/timeGridCalculations'

/**
 * BufferTimeCalculations - Handles buffer time layout calculations for appointments
 */
export const useBufferTimeCalculations = (appointment, config) => {
  const appointmentHeight = calculateAppointmentHeight(appointment, config)
  
  // Calculate buffer time and service time heights (Tailwind-only approach)
  const calculateBufferTimeLayout = (effectiveHeight, isTransforming = false) => {
    // Get buffer time from appointment data (in minutes)
    const bufferTime = appointment.bufferTime || appointment.buffer_time || 0
    const serviceDuration = appointment.serviceDuration || appointment.duration || 60
    const totalDuration = appointment.totalDuration || (serviceDuration + bufferTime)
    
    // Calculate proportional heights based on time durations
    const serviceRatio = serviceDuration / totalDuration
    const bufferRatio = bufferTime / totalDuration
    
    // Calculate buffer height based on original appointment height (fixed during transforms)
    const baseBufferHeight = Math.round(appointmentHeight * bufferRatio)
    const fixedBufferHeight = Math.max(baseBufferHeight, bufferTime > 0 ? 20 : 0)
    
    // During drag/resize operations, keep buffer height fixed and adjust service height accordingly
    if (isTransforming && bufferTime > 0) {
      const serviceHeight = Math.max(30, effectiveHeight - fixedBufferHeight)
      return {
        hasBuffer: bufferTime > 0,
        bufferTime,
        serviceDuration,
        totalDuration,
        serviceHeight,
        bufferHeight: fixedBufferHeight,
        serviceRatio,
        bufferRatio
      }
    }
    
    // Normal calculation for static display
    const serviceHeight = Math.round(effectiveHeight * serviceRatio)
    const bufferHeight = Math.round(effectiveHeight * bufferRatio)
    
    // Ensure minimum heights for visibility
    const minServiceHeight = 30
    const minBufferHeight = bufferTime > 0 ? 20 : 0
    
    return {
      hasBuffer: bufferTime > 0,
      bufferTime,
      serviceDuration,
      totalDuration,
      serviceHeight: Math.max(serviceHeight, minServiceHeight),
      bufferHeight: Math.max(bufferHeight, minBufferHeight),
      serviceRatio,
      bufferRatio
    }
  }

  return {
    appointmentHeight,
    calculateBufferTimeLayout
  }
} 