"""
Development settings for the project.
This file imports all settings from the main settings file.
"""
import os
import sys
from pathlib import Path

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent.parent

# Import all settings from the main settings file
sys.path.insert(0, str(BASE_DIR))
from config.settings import *

# Override settings for development
DEBUG = True

# Make sure all required apps are in INSTALLED_APPS
if 'INSTALLED_APPS' not in locals():
    INSTALLED_APPS = []

# Add required apps if not already included
REQUIRED_APPS = [
    'business',
    'customers',
    'employees',
    'accounts',
    'api',
    'services',
    'appointments',
    'aws_services',
]

for app in REQUIRED_APPS:
    if app not in INSTALLED_APPS:
        INSTALLED_APPS.append(app)

# Set log levels to DEBUG for development
if 'LOGGING' in locals():
    for logger in LOGGING['loggers'].values():
        if 'level' in logger:
            logger['level'] = 'DEBUG' 