import { useState } from 'react'
import axios from 'axios'

// Base API configuration - updated fallback to match backend structure
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add request interceptor for authentication
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

/**
 * Custom hook for API interactions
 * @param {string} endpoint - API endpoint
 * @returns {Object} - API interaction methods and state
 */
export function useApi(endpoint) {
  const [data, setData] = useState(null)
  const [error, setError] = useState(null)
  const [loading, setLoading] = useState(false)

  // Fetch data from the API
  const fetchData = async (params = {}) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await api.get(endpoint, { params })
      setData(response.data)
      return response.data
    } catch (err) {
      setError(err.response?.data || { message: err.message })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Create a new resource
  const createData = async (payload) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await api.post(endpoint, payload)
      return response.data
    } catch (err) {
      setError(err.response?.data || { message: err.message })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Update an existing resource
  const updateData = async (id, payload) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await api.put(`${endpoint}/${id}`, payload)
      return response.data
    } catch (err) {
      setError(err.response?.data || { message: err.message })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Delete a resource
  const deleteData = async (id) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await api.delete(`${endpoint}/${id}`)
      return response.data
    } catch (err) {
      setError(err.response?.data || { message: err.message })
      return null
    } finally {
      setLoading(false)
    }
  }

  return {
    data,
    error,
    loading,
    fetchData,
    createData,
    updateData,
    deleteData,
  }
}

export default useApi 

// Named export so other modules can reuse the authenticated axios instance directly
export { api } 