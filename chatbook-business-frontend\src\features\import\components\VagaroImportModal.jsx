import { useState } from 'react'
import { useNavigate } from 'react-router-dom'

function VagaroImportModal({ isOpen, onClose, onProceed }) {
  const navigate = useNavigate()

  const handleProceed = () => {
    // Navigate directly with state instead of using the callback
    navigate('/customers/import', { state: { fromVagaro: true } })
    onClose() // Close the modal
  }
  if (!isOpen) return null

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div 
        className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Import from Vagaro Account</h2>
              <p className="text-sm text-gray-600 mt-1">
                Follow these steps to export your customer data from Vagaro
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="space-y-6">
            {/* Step 1 */}
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-semibold text-blue-600">1</span>
              </div>
              <div className="flex-1">
                <h3 className="text-sm font-medium text-gray-900 mb-2">Login to your Vagaro account</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Go to your Vagaro business dashboard and sign in to your account.
                </p>
                <a
                  href="https://www.vagaro.com/us/business"
                  target="_blank"
                  rel="noopener noreferrer"
                  className="inline-flex items-center px-3 py-2 text-xs font-medium text-blue-600 bg-blue-50 rounded-lg hover:bg-blue-100 transition-colors"
                >
                  Open Vagaro Business
                  <svg className="w-3 h-3 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 6H6a2 2 0 00-2 2v10a2 2 0 002 2h10a2 2 0 002-2v-4M14 4h6m0 0v6m0-6L10 14" />
                  </svg>
                </a>
              </div>
            </div>

            {/* Step 2 */}
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-semibold text-blue-600">2</span>
              </div>
              <div className="flex-1">
                <h3 className="text-sm font-medium text-gray-900 mb-2">Navigate to Customer Management</h3>
                <p className="text-sm text-gray-600">
                  In your Vagaro dashboard, go to the <strong>Customers</strong> or <strong>Client Management</strong> section.
                </p>
              </div>
            </div>

            {/* Step 3 */}
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-semibold text-blue-600">3</span>
              </div>
              <div className="flex-1">
                <h3 className="text-sm font-medium text-gray-900 mb-2">Export customer data</h3>
                <p className="text-sm text-gray-600 mb-3">
                  Look for an <strong>Export</strong> or <strong>Download</strong> option. Choose to export your customer list as an Excel file (.xlsx) or CSV file.
                </p>
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <div className="flex items-start">
                    <svg className="w-4 h-4 text-yellow-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                    </svg>
                    <div className="text-xs text-yellow-800">
                      <strong>Note:</strong> Make sure to include all customer fields like name, email, phone number, and any other relevant information you want to import.
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Step 4 */}
            <div className="flex items-start space-x-4">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-semibold text-blue-600">4</span>
              </div>
              <div className="flex-1">
                <h3 className="text-sm font-medium text-gray-900 mb-2">Upload the exported file</h3>
                <p className="text-sm text-gray-600">
                  Once you've downloaded the customer data file from Vagaro, return here and use the <strong>Upload Exported File</strong> section to upload your Excel or CSV file directly.
                </p>
              </div>
            </div>
          </div>

          {/* Help section */}
          <div className="mt-8 p-4 bg-gray-50 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 mb-2">Need help?</h4>
            <p className="text-sm text-gray-600">
              If you can't find the export option in your Vagaro account, check their help documentation or contact Vagaro support for assistance with exporting customer data.
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="p-6 border-t border-gray-200 bg-gray-50 rounded-b-xl">
          <div className="flex justify-end">
            <button
              onClick={handleProceed}
              className="px-6 py-3 text-sm font-medium text-white bg-red-600 rounded-lg hover:bg-red-700 transition-colors"
            >
              Continue to Import
            </button>
          </div>
        </div>
      </div>
    </div>
  )
}

export default VagaroImportModal 