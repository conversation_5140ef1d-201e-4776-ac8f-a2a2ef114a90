"""
Appointment History Tracking Utilities

This module provides utilities for tracking changes to appointments and related models.
It includes signal handlers, change detection, and delta comparison functionality.
"""
import json
from typing import Dict, Any, Optional, Tuple, List
from contextlib import contextmanager
from threading import local
from django.db.models.signals import post_save, post_delete, pre_save
from django.dispatch import receiver
from django.contrib.auth import get_user_model
from django.utils import timezone
from .models import Appointment, AppointmentService, AppointmentAddOn, AppointmentHistory

User = get_user_model()

# Thread-local storage for batching history entries
_thread_local = local()


class AppointmentHistoryTracker:
    """
    Utility class for tracking appointment changes and creating history entries.
    """

    @staticmethod
    def get_current_user_context():
        """
        Get the current user context from thread-local storage or request.
        This is set by the AppointmentHistoryMiddleware.
        """
        from .middleware import get_current_user_context
        return get_current_user_context()

    @staticmethod
    def start_batch_tracking():
        """Start batching history entries for a single request"""
        if not hasattr(_thread_local, 'batch_changes'):
            _thread_local.batch_changes = {}

    @staticmethod
    def end_batch_tracking():
        """End batching and create consolidated history entries"""
        if hasattr(_thread_local, 'batch_changes'):
            batch_changes = _thread_local.batch_changes
            del _thread_local.batch_changes

            # Create consolidated history entries for each appointment
            for appointment_id, changes in batch_changes.items():
                try:
                    appointment = Appointment.objects.get(id=appointment_id)
                    AppointmentHistoryTracker._create_consolidated_history_entry(appointment, changes)
                except Appointment.DoesNotExist:
                    pass

    @staticmethod
    def _create_consolidated_history_entry(appointment, changes):
        """Create a single consolidated history entry for all changes"""
        if not changes:
            return

        # Determine the primary action
        actions = [change['action'] for change in changes]
        if 'created' in actions:
            primary_action = 'created'
        elif any(action in ['service_added', 'service_removed', 'service_modified',
                           'addon_added', 'addon_removed', 'addon_modified'] for action in actions):
            primary_action = 'updated'
        elif 'status_changed' in actions:
            primary_action = 'status_changed'
        elif 'rescheduled' in actions:
            primary_action = 'rescheduled'
        else:
            primary_action = 'updated'

        # Create consolidated change summary
        summaries = []
        all_changes = {}

        for change in changes:
            summaries.append(change['summary'])
            if change.get('field_name'):
                all_changes[change['field_name']] = {
                    'old_value': change.get('old_value'),
                    'new_value': change.get('new_value')
                }

        consolidated_summary = '; '.join(summaries)

        # Use the user context from the first change
        user_context = changes[0].get('user_context', AppointmentHistoryTracker.get_current_user_context())

        # Create the consolidated history entry
        AppointmentHistoryTracker.create_history_entry(
            appointment=appointment,
            action=primary_action,
            change_summary=consolidated_summary,
            field_name='multiple' if len(all_changes) > 1 else list(all_changes.keys())[0] if all_changes else '',
            old_value=all_changes if len(all_changes) > 1 else list(all_changes.values())[0].get('old_value') if all_changes else None,
            new_value=all_changes if len(all_changes) > 1 else list(all_changes.values())[0].get('new_value') if all_changes else None,
            user_context=user_context
        )

    @staticmethod
    def add_change_to_batch(appointment_id, action, summary, field_name='', old_value=None, new_value=None, user_context=None):
        """Add a change to the current batch"""
        if hasattr(_thread_local, 'batch_changes'):
            if appointment_id not in _thread_local.batch_changes:
                _thread_local.batch_changes[appointment_id] = []

            _thread_local.batch_changes[appointment_id].append({
                'action': action,
                'summary': summary,
                'field_name': field_name,
                'old_value': old_value,
                'new_value': new_value,
                'user_context': user_context
            })
            return True  # Change was batched
        return False  # Not in batch mode, create individual entry
    
    @staticmethod
    def create_history_entry(
        appointment: Appointment,
        action: str,
        change_summary: str,
        field_name: str = '',
        old_value: Any = None,
        new_value: Any = None,
        user_context: Optional[Dict] = None
    ) -> AppointmentHistory:
        """
        Create a new appointment history entry.
        
        Args:
            appointment: The appointment that was changed
            action: The type of action (from ACTION_CHOICES)
            change_summary: Human-readable description of the change
            field_name: Name of the field that changed (optional)
            old_value: Previous value (optional)
            new_value: New value (optional)
            user_context: User context information (optional)
        
        Returns:
            AppointmentHistory: The created history entry
        """
        if user_context is None:
            user_context = AppointmentHistoryTracker.get_current_user_context()
        
        # Serialize complex objects for JSON storage
        old_value_json = AppointmentHistoryTracker._serialize_value(old_value)
        new_value_json = AppointmentHistoryTracker._serialize_value(new_value)
        
        # Determine the user display name
        user = user_context.get('user')
        if user and user.is_authenticated:
            modified_by = f"{user.first_name} {user.last_name}".strip() or user.email
        else:
            modified_by = "System"

        history_entry = AppointmentHistory.objects.create(
            appointment=appointment,
            action=action,
            modified_by=modified_by,
            field_name=field_name,
            old_value=old_value_json,
            new_value=new_value_json,
            change_summary=change_summary,
            ip_address=user_context.get('ip_address'),
            user_agent=user_context.get('user_agent') or '',
        )
        
        return history_entry
    
    @staticmethod
    def _serialize_value(value: Any) -> Any:
        """
        Serialize a value for JSON storage, handling complex objects.
        """
        if value is None:
            return None
        
        # Handle datetime objects
        if hasattr(value, 'isoformat'):
            return value.isoformat()
        
        # Handle model instances
        if hasattr(value, '_meta'):
            return {
                'model': f"{value._meta.app_label}.{value._meta.model_name}",
                'pk': str(value.pk),
                'str': str(value)
            }
        
        # Handle querysets or lists of model instances
        if hasattr(value, '__iter__') and not isinstance(value, (str, dict)):
            try:
                return [AppointmentHistoryTracker._serialize_value(item) for item in value]
            except:
                return str(value)
        
        # For other types, try to serialize directly or convert to string
        try:
            json.dumps(value)  # Test if it's JSON serializable
            return value
        except (TypeError, ValueError):
            return str(value)
    
    @staticmethod
    def compare_appointment_fields(old_appointment: Dict, new_appointment: Dict) -> Dict[str, Tuple[Any, Any]]:
        """
        Compare two appointment dictionaries and return the differences.

        Args:
            old_appointment: Dictionary of old appointment field values
            new_appointment: Dictionary of new appointment field values

        Returns:
            Dict mapping field names to (old_value, new_value) tuples
        """
        changes = {}

        # Fields to track for appointments
        tracked_fields = [
            'customer', 'employee', 'start_time', 'status', 'payment_status',
            'source', 'notes_from_customer', 'cancellation_reason'
        ]

        for field in tracked_fields:
            old_val = old_appointment.get(field)
            new_val = new_appointment.get(field)

            if old_val != new_val:
                changes[field] = (old_val, new_val)

        return changes

    @staticmethod
    def _compare_service_fields_dict(old_state, new_state):
        """
        Compare service field states and return changes.
        """
        changes = {}
        for field_name in old_state.keys():
            old_value = old_state.get(field_name)
            new_value = new_state.get(field_name)
            if old_value != new_value:
                changes[field_name] = (old_value, new_value)
        return changes

    @staticmethod
    def get_field_display_name(field_name):
        """Get human-readable field names"""
        field_names = {
            'quantity': 'Quantity',
            'base_price': 'Price',
            'price_override': 'Price Override',
            'duration': 'Duration',
            'buffer_time': 'Buffer Time',
            'notes': 'Notes',
            'add_on_price': 'Price',
        }
        return field_names.get(field_name, field_name.replace('_', ' ').title())

    @staticmethod
    def format_value_for_display(value):
        """Format values for display in history"""
        if value is None:
            return 'None'
        elif isinstance(value, (int, float)) and 'price' in str(value).lower():
            return f"${value}"
        elif isinstance(value, int) and 'duration' in str(value).lower():
            return f"{value} min"
        elif isinstance(value, int) and 'buffer' in str(value).lower():
            return f"{value} min"
        else:
            return str(value)

    @staticmethod
    def compare_appointment_services(old_services: list, new_services: list) -> Dict[str, Any]:
        """
        Compare appointment services and detect additions, removals, and modifications.

        Args:
            old_services: List of old AppointmentService instances or dicts
            new_services: List of new AppointmentService instances or dicts

        Returns:
            Dict with 'added', 'removed', and 'modified' keys containing change details
        """
        changes = {
            'added': [],
            'removed': [],
            'modified': []
        }

        # Convert to dictionaries for easier comparison
        old_services_dict = {}
        new_services_dict = {}

        for service in old_services:
            key = service.service.id if hasattr(service, 'service') else service['service_id']
            old_services_dict[key] = service

        for service in new_services:
            key = service.service.id if hasattr(service, 'service') else service['service_id']
            new_services_dict[key] = service

        # Find added services
        for service_id, service in new_services_dict.items():
            if service_id not in old_services_dict:
                changes['added'].append(service)

        # Find removed services
        for service_id, service in old_services_dict.items():
            if service_id not in new_services_dict:
                changes['removed'].append(service)

        # Find modified services
        for service_id in old_services_dict:
            if service_id in new_services_dict:
                old_service = old_services_dict[service_id]
                new_service = new_services_dict[service_id]

                # Compare relevant fields
                service_changes = AppointmentHistoryTracker._compare_service_fields(old_service, new_service)
                if service_changes:
                    changes['modified'].append({
                        'service': new_service,
                        'changes': service_changes
                    })

        return changes

    @staticmethod
    def _compare_service_fields(old_service, new_service) -> Dict[str, Tuple[Any, Any]]:
        """
        Compare individual service fields for changes.
        """
        changes = {}
        fields_to_compare = ['quantity', 'base_price', 'price_override', 'duration', 'buffer_time', 'notes']

        for field in fields_to_compare:
            old_val = getattr(old_service, field, None) if hasattr(old_service, field) else old_service.get(field)
            new_val = getattr(new_service, field, None) if hasattr(new_service, field) else new_service.get(field)

            if old_val != new_val:
                changes[field] = (old_val, new_val)

        return changes

    @staticmethod
    def get_field_display_name(field_name: str) -> str:
        """
        Get a human-readable display name for a field.
        """
        field_display_names = {
            'customer': 'Customer',
            'employee': 'Employee',
            'start_time': 'Start Time',
            'status': 'Status',
            'payment_status': 'Payment Status',
            'source': 'Source',
            'notes_from_customer': 'Customer Notes',
            'cancellation_reason': 'Cancellation Reason',
            'quantity': 'Quantity',
            'base_price': 'Base Price',
            'price_override': 'Price Override',
            'duration': 'Duration',
            'buffer_time': 'Buffer Time',
            'notes': 'Notes',
        }
        return field_display_names.get(field_name, field_name.replace('_', ' ').title())

    @staticmethod
    def format_value_for_display(value: Any) -> str:
        """
        Format a value for human-readable display in change summaries.
        """
        if value is None:
            return 'None'

        # Handle datetime objects
        if hasattr(value, 'strftime'):
            return value.strftime('%Y-%m-%d %H:%M:%S')

        # Handle model instances
        if hasattr(value, '_meta'):
            return str(value)

        # Handle decimal/money values
        if hasattr(value, 'quantize'):
            return f"${value}"

        # Handle boolean values
        if isinstance(value, bool):
            return 'Yes' if value else 'No'

        return str(value)
    
    @staticmethod
    def generate_change_summary(field_name: str, old_value: Any, new_value: Any, action: str = 'updated') -> str:
        """
        Generate a human-readable summary of a field change.
        """
        if action == 'created':
            return f"Appointment created"
        elif action == 'deleted':
            return f"Appointment deleted"
        elif action == 'cancelled':
            return f"Appointment cancelled"
        elif action == 'rescheduled':
            return f"Appointment rescheduled from {old_value} to {new_value}"
        elif action == 'status_changed':
            return f"Status changed from '{old_value}' to '{new_value}'"
        elif action == 'payment_updated':
            return f"Payment status changed from '{old_value}' to '{new_value}'"
        elif field_name == 'customer':
            return f"Customer changed from {old_value} to {new_value}"
        elif field_name == 'employee':
            return f"Employee changed from {old_value} to {new_value}"
        elif field_name == 'start_time':
            return f"Start time changed from {old_value} to {new_value}"
        elif field_name == 'notes_from_customer':
            return f"Customer notes updated"
        elif field_name == 'cancellation_reason':
            return f"Cancellation reason updated: {new_value}"
        else:
            return f"{field_name.replace('_', ' ').title()} changed from '{old_value}' to '{new_value}'"


# Global variables to store state before changes
_appointment_pre_save_state = {}
_service_pre_save_state = {}
_addon_pre_save_state = {}


@receiver(pre_save, sender=Appointment)
def capture_appointment_pre_save_state(sender, instance, **kwargs):
    """
    Capture the state of an appointment before it's saved.
    This allows us to compare old vs new values in post_save.
    """
    if instance.pk:  # Only for existing appointments
        try:
            old_appointment = Appointment.objects.get(pk=instance.pk)
            _appointment_pre_save_state[instance.pk] = {
                'customer': old_appointment.customer,
                'employee': old_appointment.employee,
                'start_time': old_appointment.start_time,
                'status': old_appointment.status,
                'payment_status': old_appointment.payment_status,
                'source': old_appointment.source,
                'notes_from_customer': old_appointment.notes_from_customer,
                'cancellation_reason': old_appointment.cancellation_reason,
            }
        except Appointment.DoesNotExist:
            pass


@receiver(pre_save, sender=AppointmentService)
def capture_service_pre_save_state(sender, instance, **kwargs):
    """
    Capture the state of a service before it's saved.
    """
    if instance.pk:  # Only for existing services
        try:
            old_service = AppointmentService.objects.get(pk=instance.pk)
            _service_pre_save_state[instance.pk] = {
                'quantity': old_service.quantity,
                'base_price': old_service.base_price,
                'price_override': old_service.price_override,
                'duration': old_service.duration,
                'buffer_time': old_service.buffer_time,
                'notes': old_service.notes,
            }
        except AppointmentService.DoesNotExist:
            pass


@receiver(pre_save, sender=AppointmentAddOn)
def capture_addon_pre_save_state(sender, instance, **kwargs):
    """
    Capture the state of an add-on before it's saved.
    """
    if instance.pk:  # Only for existing add-ons
        try:
            old_addon = AppointmentAddOn.objects.get(pk=instance.pk)
            _addon_pre_save_state[instance.pk] = {
                'add_on_price': old_addon.add_on_price,
                'duration': old_addon.duration,
            }
        except AppointmentAddOn.DoesNotExist:
            pass


@receiver(post_save, sender=Appointment)
def track_appointment_changes(sender, instance, created, **kwargs):
    """
    Track changes to appointments and create history entries.
    """
    user_context = AppointmentHistoryTracker.get_current_user_context()

    if created:
        # New appointment created
        if not AppointmentHistoryTracker.add_change_to_batch(
            instance.id, 'created',
            f"Appointment created for {instance.customer} with {instance.employee}",
            user_context=user_context
        ):
            AppointmentHistoryTracker.create_history_entry(
                appointment=instance,
                action='created',
                change_summary=f"Appointment created for {instance.customer} with {instance.employee}",
                user_context=user_context
            )
    else:
        # Existing appointment updated
        old_state = _appointment_pre_save_state.get(instance.pk, {})
        if old_state:
            new_state = {
                'customer': instance.customer,
                'employee': instance.employee,
                'start_time': instance.start_time,
                'status': instance.status,
                'payment_status': instance.payment_status,
                'source': instance.source,
                'notes_from_customer': instance.notes_from_customer,
                'cancellation_reason': instance.cancellation_reason,
            }

            changes = AppointmentHistoryTracker.compare_appointment_fields(old_state, new_state)

            for field_name, (old_value, new_value) in changes.items():
                # Determine action type based on field and values
                if field_name == 'status':
                    action = 'cancelled' if new_value == 'cancelled' else 'status_changed'
                elif field_name == 'payment_status':
                    action = 'payment_updated'
                elif field_name == 'start_time':
                    action = 'rescheduled'
                else:
                    action = 'updated'

                change_summary = AppointmentHistoryTracker.generate_change_summary(
                    field_name, old_value, new_value, action
                )

                if not AppointmentHistoryTracker.add_change_to_batch(
                    instance.id, action, change_summary, field_name, old_value, new_value, user_context
                ):
                    AppointmentHistoryTracker.create_history_entry(
                        appointment=instance,
                        action=action,
                        change_summary=change_summary,
                        field_name=field_name,
                        old_value=old_value,
                        new_value=new_value,
                        user_context=user_context
                    )

            # Clean up the stored state
            del _appointment_pre_save_state[instance.pk]


@receiver(post_delete, sender=Appointment)
def track_appointment_deletion(sender, instance, **kwargs):
    """
    Track when appointments are deleted.
    """
    AppointmentHistoryTracker.create_history_entry(
        appointment=instance,
        action='deleted',
        change_summary=f"Appointment deleted (was scheduled for {instance.start_time})",
    )


# Track AppointmentService changes
@receiver(post_save, sender=AppointmentService)
def track_appointment_service_changes(sender, instance, created, **kwargs):
    """
    Track changes to appointment services.
    """
    user_context = AppointmentHistoryTracker.get_current_user_context()

    if created:
        change_summary = f"Service '{instance.service.name}' added (Qty: {instance.quantity}, Price: ${instance.base_price})"
        new_value = {
            'service_id': instance.service.id,
            'service_name': instance.service.name,
            'quantity': instance.quantity,
            'base_price': str(instance.base_price),
            'duration': instance.duration,
            'buffer_time': instance.buffer_time,
        }

        if not AppointmentHistoryTracker.add_change_to_batch(
            instance.appointment.id, 'service_added', change_summary,
            'appointment_services', None, new_value, user_context
        ):
            AppointmentHistoryTracker.create_history_entry(
                appointment=instance.appointment,
                action='service_added',
                change_summary=change_summary,
                field_name='appointment_services',
                new_value=new_value,
                user_context=user_context
            )
    else:
        # Check what actually changed using pre_save state
        old_state = _service_pre_save_state.get(instance.pk, {})
        if old_state:
            new_state = {
                'quantity': instance.quantity,
                'base_price': instance.base_price,
                'price_override': instance.price_override,
                'duration': instance.duration,
                'buffer_time': instance.buffer_time,
                'notes': instance.notes,
            }

            changes = AppointmentHistoryTracker._compare_service_fields_dict(old_state, new_state)

            if changes:
                # Create detailed change summary
                change_parts = []
                for field_name, (old_value, new_value) in changes.items():
                    display_name = AppointmentHistoryTracker.get_field_display_name(field_name)
                    old_display = AppointmentHistoryTracker.format_value_for_display(old_value)
                    new_display = AppointmentHistoryTracker.format_value_for_display(new_value)
                    change_parts.append(f"{display_name}: {old_display} → {new_display}")

                change_summary = f"Service '{instance.service.name}' modified ({', '.join(change_parts)})"

                if not AppointmentHistoryTracker.add_change_to_batch(
                    instance.appointment.id, 'service_modified', change_summary,
                    'appointment_services', old_state, new_state, user_context
                ):
                    AppointmentHistoryTracker.create_history_entry(
                        appointment=instance.appointment,
                        action='service_modified',
                        change_summary=change_summary,
                        field_name='appointment_services',
                        old_value=old_state,
                        new_value=new_state,
                        user_context=user_context
                    )

            # Clean up the stored state
            del _service_pre_save_state[instance.pk]


@receiver(post_delete, sender=AppointmentService)
def track_appointment_service_deletion(sender, instance, **kwargs):
    """
    Track when appointment services are removed.
    """
    AppointmentHistoryTracker.create_history_entry(
        appointment=instance.appointment,
        action='service_removed',
        change_summary=f"Service '{instance.service.name}' removed (was Qty: {instance.quantity}, Price: ${instance.base_price})",
        field_name='appointment_services',
        old_value={
            'service_id': instance.service.id,
            'service_name': instance.service.name,
            'quantity': instance.quantity,
            'base_price': str(instance.base_price),
            'duration': instance.duration,
            'buffer_time': instance.buffer_time,
        }
    )


# Track AppointmentAddOn changes
@receiver(post_save, sender=AppointmentAddOn)
def track_appointment_addon_changes(sender, instance, created, **kwargs):
    """
    Track changes to appointment add-ons.
    """
    user_context = AppointmentHistoryTracker.get_current_user_context()

    if created:
        change_summary = f"Add-on '{instance.add_on.name}' added (Price: ${instance.add_on_price}, Duration: {instance.duration} min)"
        new_value = {
            'addon_id': instance.add_on.id,
            'addon_name': instance.add_on.name,
            'price': str(instance.add_on_price),
            'duration': instance.duration,
        }

        if not AppointmentHistoryTracker.add_change_to_batch(
            instance.appointment.id, 'addon_added', change_summary,
            'appointment_add_ons', None, new_value, user_context
        ):
            AppointmentHistoryTracker.create_history_entry(
                appointment=instance.appointment,
                action='addon_added',
                change_summary=change_summary,
                field_name='appointment_add_ons',
                new_value=new_value,
                user_context=user_context
            )
    else:
        # Check what actually changed using pre_save state
        old_state = _addon_pre_save_state.get(instance.pk, {})
        if old_state:
            new_state = {
                'add_on_price': instance.add_on_price,
                'duration': instance.duration,
            }

            changes = AppointmentHistoryTracker._compare_service_fields_dict(old_state, new_state)

            if changes:
                # Create detailed change summary
                change_parts = []
                for field_name, (old_value, new_value) in changes.items():
                    display_name = AppointmentHistoryTracker.get_field_display_name(field_name)
                    old_display = AppointmentHistoryTracker.format_value_for_display(old_value)
                    new_display = AppointmentHistoryTracker.format_value_for_display(new_value)
                    change_parts.append(f"{display_name}: {old_display} → {new_display}")

                change_summary = f"Add-on '{instance.add_on.name}' modified ({', '.join(change_parts)})"

                if not AppointmentHistoryTracker.add_change_to_batch(
                    instance.appointment.id, 'addon_modified', change_summary,
                    'appointment_add_ons', old_state, new_state, user_context
                ):
                    AppointmentHistoryTracker.create_history_entry(
                        appointment=instance.appointment,
                        action='addon_modified',
                        change_summary=change_summary,
                        field_name='appointment_add_ons',
                        old_value=old_state,
                        new_value=new_state,
                        user_context=user_context
                    )

            # Clean up the stored state
            del _addon_pre_save_state[instance.pk]


@receiver(post_delete, sender=AppointmentAddOn)
def track_appointment_addon_deletion(sender, instance, **kwargs):
    """
    Track when appointment add-ons are removed.
    """
    AppointmentHistoryTracker.create_history_entry(
        appointment=instance.appointment,
        action='addon_removed',
        change_summary=f"Add-on '{instance.add_on.name}' removed (was Price: ${instance.add_on_price}, Duration: {instance.duration} min)",
        field_name='appointment_add_ons',
        old_value={
            'addon_id': instance.add_on.id,
            'addon_name': instance.add_on.name,
            'price': str(instance.add_on_price),
            'duration': instance.duration,
        }
    )
