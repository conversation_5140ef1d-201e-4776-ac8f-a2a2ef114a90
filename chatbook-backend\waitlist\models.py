from django.db import models
from django_softdelete.models import SoftDeleteModel  # django-soft-delete
from business.models import Business
from services.models import Service, AddOn
from employees.models import Employee


class WaitlistEntry(SoftDeleteModel):
    STATUS_CHOICES = [
        ('current', 'Current'),
        ('expired', 'Expired'),
    ]
    PRIORITY_CHOICES = [
        ('manual', 'Manual'),
    ]

    business        = models.ForeignKey(Business, on_delete=models.CASCADE, related_name='waitlist_entries')
    customer_name   = models.CharField(max_length=255)
    phone_number    = models.CharField(max_length=20)
    email           = models.EmailField()
    services        = models.ManyToManyField(Service, blank=True, related_name='waitlist_entries')
    add_ons         = models.ManyToManyField(AddOn, blank=True, related_name='waitlist_entries')
    employees       = models.ManyToManyField(Employee, blank=True, related_name='waitlist_entries', help_text='Preferred employees for this waitlist entry')
    notes           = models.TextField(blank=True, help_text='Special requests or notes for this waitlist entry')
    status          = models.CharField(max_length=10, choices=STATUS_CHOICES, default='current')
    priority_rule   = models.CharField(max_length=10, choices=PRIORITY_CHOICES, default='manual')
    created_at      = models.DateTimeField(auto_now_add=True)
    updated_at      = models.DateTimeField(auto_now=True)
    expired_at      = models.DateTimeField(null=True, blank=True)

    def __str__(self):
        return f"{self.customer_name} - {self.status}"

    @property
    def total_price(self):
        """Get the total estimated price for all services and add-ons in this waitlist entry"""
        service_total = sum(service.base_price for service in self.services.all())
        addon_total = sum(addon.base_price for addon in self.add_ons.all())
        return service_total + addon_total

    @property
    def total_duration(self):
        """Get the total estimated duration in minutes for all services and add-ons including buffer time"""
        # For services: base_duration + buffer_time
        service_duration = 0
        for service in self.services.all():
            service_minutes = int(service.base_duration.total_seconds() // 60)
            buffer_minutes = int(service.buffer_time.total_seconds() // 60)
            service_duration += service_minutes + buffer_minutes

        # For add-ons: just base_duration (no buffer time)
        addon_duration = sum(int(addon.base_duration.total_seconds() // 60) for addon in self.add_ons.all())

        return service_duration + addon_duration

    class Meta:
        verbose_name = "Waitlist Entry"
        verbose_name_plural = "Waitlist Entries"
        ordering = ['-created_at']


class PreferredWindow(models.Model):
    entry            = models.ForeignKey(
                         WaitlistEntry,
                         related_name="windows",
                         on_delete=models.CASCADE
                       )
    start_datetime   = models.DateTimeField()
    end_datetime     = models.DateTimeField()

    def __str__(self):
        return f"{self.entry.customer_name} - {self.start_datetime} to {self.end_datetime}"

    class Meta:
        indexes = [
            models.Index(fields=['start_datetime', 'end_datetime']),
        ]
