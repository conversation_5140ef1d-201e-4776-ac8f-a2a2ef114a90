document.addEventListener('DOMContentLoaded', function() {
    const buttons = document.querySelectorAll('.copy-hours-btn');
    const errorMessageDiv = document.getElementById('error-message');

    function getCookie(name) {
        let cookieValue = null;
        if (document.cookie && document.cookie !== '') {
            const cookies = document.cookie.split(';');
            for (let i = 0; i < cookies.length; i++) {
                const cookie = cookies[i].trim();
                if (cookie.substring(0, name.length + 1) === (name + '=')) {
                    cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                    break;
                }
            }
        }
        return cookieValue;
    }

    function showError(message) {
        errorMessageDiv.textContent = message;
        errorMessageDiv.style.display = 'block';
        setTimeout(() => {
            errorMessageDiv.style.display = 'none';
        }, 5000);
    }

    async function copyHours(button) {
        const url = button.dataset.url;
        const employeeId = button.dataset.employeeId;

        // Disable all buttons during operation
        buttons.forEach(btn => btn.disabled = true);

        try {
            const confirmed = confirm('Are you sure you want to apply these hours to all other days?');
            if (!confirmed) {
                buttons.forEach(btn => btn.disabled = false);
                return;
            }

            const response = await fetch(url, {
                method: 'POST',
                headers: {
                    'X-CSRFToken': getCookie('csrftoken'),
                    'Content-Type': 'application/json',
                    'X-Requested-With': 'XMLHttpRequest'
                },
                credentials: 'same-origin'
            });

            const data = await response.json();

            if (response.ok) {
                console.log('Working hours copied successfully:', data);
                // Update the UI to reflect the changes
                const row = button.closest('tr');
                const workDaysCell = row.querySelector('td:nth-child(2)');
                if (workDaysCell && data.work_days) {
                    workDaysCell.textContent = data.work_days;
                }
                // Show success message
                showError('Hours successfully copied to all days');
                buttons.forEach(btn => btn.disabled = false);
            } else {
                throw new Error(data.error || 'Failed to copy working hours');
            }
        } catch (error) {
            console.error('Error copying working hours:', error);
            showError(error.message || 'An error occurred while copying working hours. Please try again.');
            buttons.forEach(btn => btn.disabled = false);
        }
    }

    buttons.forEach(button => {
        button.addEventListener('click', () => copyHours(button));
    });
}); 