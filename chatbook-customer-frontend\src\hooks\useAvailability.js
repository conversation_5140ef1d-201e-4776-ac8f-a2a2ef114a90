import { useQuery } from '@tanstack/react-query';
import { getAvailableTimeSlots } from '../api/bookingApi';
import { queryKeys } from '../lib/queryClient';

export const useAvailability = (employeeId, date, serviceId, businessId = 1) => {
  return useQuery({
    queryKey: queryKeys.booking.availability(employeeId || 'any', date),
    queryFn: () => {
      console.log('🔍 Fetching availability with params:', {
        date,
        serviceId,
        businessId,
        employeeId: employeeId || 'null (Any Employee)'
      });
      return getAvailableTimeSlots(date, serviceId, businessId, employeeId); // Fixed parameter order
    },
    enabled: !!(date && serviceId), // Only run query if we have required params
    staleTime: 2 * 60 * 1000, // 2 minutes (availability changes frequently)
    gcTime: 5 * 60 * 1000, // 5 minutes
    retry: 1,
    refetchOnWindowFocus: true, // Refetch when user returns to tab
  });
};

export const useAvailabilityWithFallback = (employeeId, date, serviceId, businessId = 1) => {
  const query = useAvailability(employeeId, date, serviceId, businessId);
  
  // Provide fallback data if API fails
  const fallbackSlots = {
    'Any Employee': [
      '9:00 AM', '10:00 AM', '11:00 AM', 
      '1:00 PM', '2:00 PM', '3:00 PM', '4:00 PM'
    ]
  };

  return {
    ...query,
    data: query.data || (query.isError ? fallbackSlots : undefined),
    isLoading: query.isLoading && !query.isError,
  };
};
