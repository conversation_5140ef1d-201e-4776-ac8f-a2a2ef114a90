@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom drag and drop styles */
.drag-highlight {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(147, 51, 234, 0.1) 100%) !important;
  border: 2px dashed #3B82F6 !important;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@layer base {
  body {
    @apply bg-gray-50 text-gray-900;
  }
  
  h1, h2, h3, h4, h5, h6 {
    @apply font-bold;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md transition-colors;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700;
  }
  
  .container {
    @apply mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }
}

@layer utilities {
  /* Hide scrollbar while maintaining functionality */
  .scrollbar-none {
    /* Hide scrollbar for webkit browsers */
    -webkit-scrollbar: none;
    /* Hide scrollbar for Firefox */
    scrollbar-width: none;
    /* Hide scrollbar for IE/Edge */
    -ms-overflow-style: none;
  }
  
  /* Additional scrollbar hiding for calendar containers */
  .calendar-scroll-container {
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE/Edge */
  }
  
  .calendar-scroll-container::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }
} 