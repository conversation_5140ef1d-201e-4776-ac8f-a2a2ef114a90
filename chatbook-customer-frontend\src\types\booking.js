// Booking related types
export interface Service {
  id: number;
  name: string;
  description: string;
  duration: number;
  price: string;
  category?: string;
}

export interface Employee {
  id: number;
  full_name: string;
  title: string;
  profile_image?: string;
  bio?: string;
  specialties?: string[];
}

export interface AddOn {
  id: number;
  name: string;
  description: string;
  price: string;
  duration?: number;
}

export interface CustomerInfo {
  name: string;
  email: string;
  phone: string;
}

export interface ConsentData {
  consentAgreed: boolean;
  signature: string;
  agreedAt?: string;
}

export interface PaymentInfo {
  cardNumber: string;
  expiryDate: string;
  cvv: string;
  cardholderName: string;
  billingAddress?: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
}

export interface BookingData {
  // Selection data
  selectedService: Service | null;
  selectedEmployee: Employee | null;
  selectedAddOns: AddOn[];
  selectedDate: string | null;
  selectedTime: string | null;
  
  // Customer data
  customerInfo: CustomerInfo;
  consentData: ConsentData;
  paymentInfo: PaymentInfo;
  
  // Flow control
  step: BookingStep;
  
  // Calculated values
  subtotal: number;
  tax: number;
  total: number;
}

export type BookingStep = 
  | 'service'
  | 'employee' 
  | 'datetime'
  | 'consent'
  | 'review'
  | 'payment'
  | 'confirmation';

export interface BookingState {
  bookingData: BookingData;
  loading: boolean;
  error: string | null;
  consentStatus: 'not_signed' | 'signed' | 'checking';
}

// Booking store actions
export interface BookingActions {
  // Data updates
  updateBookingData: (updates: Partial<BookingData>) => void;
  setSelectedService: (service: Service | null) => void;
  setSelectedEmployee: (employee: Employee | null) => void;
  setSelectedAddOns: (addOns: AddOn[]) => void;
  setSelectedDateTime: (date: string | null, time: string | null) => void;
  setCustomerInfo: (info: Partial<CustomerInfo>) => void;
  setConsentData: (consent: Partial<ConsentData>) => void;
  setPaymentInfo: (payment: Partial<PaymentInfo>) => void;
  
  // Flow control
  setStep: (step: BookingStep) => void;
  nextStep: () => void;
  previousStep: () => void;
  
  // Validation
  canProceedToStep: (step: BookingStep) => boolean;
  
  // State management
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;
  clearError: () => void;
  clearBookingData: () => void;
  
  // Consent
  checkConsentStatus: () => Promise<'signed' | 'not_signed'>;
  setConsentStatus: (status: 'not_signed' | 'signed' | 'checking') => void;
  
  // Calculations
  calculateTotals: () => void;
}

export type BookingStore = BookingState & BookingActions;
