from rest_framework import serializers
from waitlist.models import WaitlistEntry, PreferredWindow
from services.models import Service, AddOn
from employees.models import Employee


class PreferredWindowSerializer(serializers.ModelSerializer):
    class Meta:
        model = PreferredWindow
        fields = ['id', 'start_datetime', 'end_datetime']


class WaitlistServiceSerializer(serializers.ModelSerializer):
    """Simplified service serializer for waitlist entries"""
    duration = serializers.SerializerMethodField()
    buffer_time = serializers.SerializerMethodField()
    price = serializers.DecimalField(source='base_price', max_digits=10, decimal_places=2, coerce_to_string=True)

    class Meta:
        model = Service
        fields = ['id', 'name', 'price', 'duration', 'buffer_time']

    def get_duration(self, obj):
        """Convert DurationField to minutes for API compatibility"""
        return int(obj.base_duration.total_seconds() / 60)

    def get_buffer_time(self, obj):
        """Convert buffer_time DurationField to minutes"""
        return int(obj.buffer_time.total_seconds() / 60)


class WaitlistAddOnSerializer(serializers.ModelSerializer):
    """Simplified add-on serializer for waitlist entries"""
    duration = serializers.SerializerMethodField()
    price = serializers.DecimalField(source='base_price', max_digits=10, decimal_places=2, coerce_to_string=True)

    class Meta:
        model = AddOn
        fields = ['id', 'name', 'price', 'duration']

    def get_duration(self, obj):
        """Convert DurationField to minutes for API compatibility"""
        return int(obj.base_duration.total_seconds() / 60)


class WaitlistEmployeeSerializer(serializers.ModelSerializer):
    """Simplified employee serializer for waitlist entries"""
    name = serializers.SerializerMethodField()

    class Meta:
        model = Employee
        fields = ['id', 'name']

    def get_name(self, obj):
        """Get employee's full name"""
        return obj.full_name


class WaitlistEntrySerializer(serializers.ModelSerializer):
    availability = PreferredWindowSerializer(source='windows', many=True, read_only=True)
    services = WaitlistServiceSerializer(many=True, read_only=True)
    add_ons = WaitlistAddOnSerializer(many=True, read_only=True)
    employees = WaitlistEmployeeSerializer(many=True, read_only=True)
    business_name = serializers.CharField(source='business.name', read_only=True)
    total_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    total_duration = serializers.IntegerField(read_only=True)

    class Meta:
        model = WaitlistEntry
        fields = [
            'id', 'business', 'business_name', 'customer_name', 'phone_number', 'email',
            'services', 'add_ons', 'employees', 'notes', 'status', 'priority_rule',
            'total_price', 'total_duration', 'created_at', 'updated_at', 'expired_at', 'availability'
        ]
        read_only_fields = ['created_at', 'updated_at', 'total_price', 'total_duration']


class WaitlistEntryCreateSerializer(serializers.ModelSerializer):
    availability = PreferredWindowSerializer(source='windows', many=True, required=False)
    service_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        write_only=True,
        help_text="List of service IDs"
    )
    add_on_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        write_only=True,
        help_text="List of add-on IDs"
    )
    employee_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=False,
        write_only=True,
        help_text="List of preferred employee IDs"
    )

    class Meta:
        model = WaitlistEntry
        fields = [
            'business', 'customer_name', 'phone_number', 'email',
            'service_ids', 'add_on_ids', 'employee_ids', 'notes', 'status', 'priority_rule',
            'expired_at', 'availability'
        ]

    def create(self, validated_data):
        availability_data = validated_data.pop('windows', [])  # Note: source mapping means this is still 'windows' in validated_data
        service_ids = validated_data.pop('service_ids', [])
        add_on_ids = validated_data.pop('add_on_ids', [])
        employee_ids = validated_data.pop('employee_ids', [])

        entry = WaitlistEntry.objects.create(**validated_data)

        # Set services, add-ons, and employees using ManyToMany
        if service_ids:
            entry.services.set(service_ids)
        if add_on_ids:
            entry.add_ons.set(add_on_ids)
        if employee_ids:
            entry.employees.set(employee_ids)

        # Create preferred windows (availability)
        for window_data in availability_data:
            PreferredWindow.objects.create(entry=entry, **window_data)

        return entry
