#!/usr/bin/env python3
"""
Script to inspect and import ICS calendar files to Django appointments
"""

import os
import sys
import django
import requests
from datetime import datetime
from collections import defaultdict
import argparse
import json

# Setup Django
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

def parse_ics_file(file_path):
    """
    Simple ICS file parser to inspect the structure and fields
    """
    print(f"🔍 Inspecting ICS file: {file_path}")
    print("=" * 60)
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    
    events = []
    current_event = {}
    in_event = False
    field_counts = defaultdict(int)
    
    with open(file_path, 'r', encoding='utf-8') as file:
        for line_num, line in enumerate(file, 1):
            line = line.strip()
            
            if line == 'BEGIN:VEVENT':
                in_event = True
                current_event = {}
                continue
            elif line == 'END:VEVENT':
                in_event = False
                if current_event:
                    events.append(current_event.copy())
                current_event = {}
                continue
            
            if in_event and ':' in line:
                # Split on first colon to handle timestamps with colons
                key, value = line.split(':', 1)
                current_event[key] = value
                field_counts[key] += 1
    
    print(f"📊 Total events found: {len(events)}")
    print(f"📄 Total lines processed: {line_num}")
    print()
    
    # Show field statistics
    print("📋 Field Analysis:")
    print("-" * 40)
    for field, count in sorted(field_counts.items()):
        print(f"{field:20} | Count: {count:3d} | Usage: {count/len(events)*100:.1f}%")
    
    print()
    print("🔍 Sample Events (first 5):")
    print("=" * 60)
    
    for i, event in enumerate(events[:5], 1):
        print(f"\n📅 Event #{i}:")
        print("-" * 30)
        for key, value in event.items():
            # Format datetime fields for better readability
            if key.startswith('DT') and 'T' in value:
                try:
                    # Parse UTC timestamp (format: 20250701T070000Z)
                    if value.endswith('Z'):
                        dt = datetime.strptime(value, '%Y%m%dT%H%M%SZ')
                        formatted_value = f"{value} ({dt.strftime('%Y-%m-%d %H:%M:%S UTC')})"
                    else:
                        formatted_value = value
                except:
                    formatted_value = value
            else:
                formatted_value = value
            
            print(f"  {key:12} | {formatted_value}")
    
    # Show unique summaries (appointment types)
    print("\n🏷️  Unique Appointment Types (SUMMARY field):")
    print("-" * 50)
    summaries = set()
    for event in events:
        if 'SUMMARY' in event:
            summaries.add(event['SUMMARY'])
    
    for i, summary in enumerate(sorted(summaries), 1):
        print(f"{i:2d}. {summary}")
    
    # Show date range
    print(f"\n📅 Date Range Analysis:")
    print("-" * 30)
    start_dates = []
    for event in events:
        if 'DTSTART' in event:
            try:
                dt_str = event['DTSTART']
                if dt_str.endswith('Z'):
                    dt = datetime.strptime(dt_str, '%Y%m%dT%H%M%SZ')
                    start_dates.append(dt)
            except:
                pass
    
    if start_dates:
        start_dates.sort()
        print(f"Earliest: {start_dates[0].strftime('%Y-%m-%d %H:%M:%S UTC')}")
        print(f"Latest:   {start_dates[-1].strftime('%Y-%m-%d %H:%M:%S UTC')}")
        print(f"Total days span: {(start_dates[-1] - start_dates[0]).days + 1} days")
    
    return events

def parse_appointment_summary(summary):
    """
    Parse the SUMMARY field into structured data
    Returns a dictionary with parsed appointment details
    """
    if not summary or summary.strip() == '':
        return {'type': 'unknown', 'raw': summary}
    
    summary = summary.strip()
    
    # Handle empty or placeholder events
    if summary in ['---', '']:
        return {'type': 'placeholder', 'raw': summary}
    
    # Handle personal events (no customer name)
    personal_events = ['Workout', 'Badminton', 'Badminton lesson', 'Lunch', 'Dental', 'Vision', 
                      'Airport pick up', 'Pick up kids']
    if summary in personal_events:
        return {
            'type': 'personal',
            'activity': summary,
            'raw': summary
        }
    
    # Handle staff transition events (Cait>Carol format)
    if '>' in summary and len(summary.split('>')) == 2:
        from_staff, to_staff = summary.split('>')
        return {
            'type': 'staff_transition',
            'from_staff': from_staff.strip(),
            'to_staff': to_staff.strip(),
            'raw': summary
        }
    
    # Handle client appointments (Customer Name - Service Type)
    if ' - ' in summary:
        parts = summary.split(' - ', 1)
        if len(parts) == 2:
            customer_name = parts[0].strip()
            service_info = parts[1].strip()
            
            # Parse service details
            parsed_service = parse_service_details(service_info)
            
            return {
                'type': 'client_appointment',
                'customer_name': customer_name,
                'service': parsed_service,
                'raw': summary
            }
    
    # Handle special cases or unrecognized format
    return {
        'type': 'other',
        'content': summary,
        'raw': summary
    }

def parse_service_details(service_info):
    """
    Parse service details into structured format
    """
    service_data = {
        'name': service_info,
        'category': None,
        'type': None,
        'add_ons': [],
        'has_consultation': False,
        'raw': service_info
    }
    
    # Extract add-ons
    add_on_patterns = [
        'with Bottom Lash(as an add-on service)',
        'with Consulting Session', 
        'with Lash Removal',
        'with 2 add-ons',
        'with 3 add-ons'
    ]
    
    for pattern in add_on_patterns:
        if pattern in service_info:
            if 'Bottom Lash' in pattern:
                service_data['add_ons'].append('Bottom Lash')
            elif 'Consulting Session' in pattern:
                service_data['add_ons'].append('Consulting Session')
                service_data['has_consultation'] = True
            elif 'Lash Removal' in pattern:
                service_data['add_ons'].append('Lash Removal')
            elif '2 add-ons' in pattern:
                service_data['add_ons'].append('Multiple (2)')
            elif '3 add-ons' in pattern:
                service_data['add_ons'].append('Multiple (3)')
            
            # Remove the add-on text to get base service
            service_info = service_info.replace(pattern, '').strip()
    
    # Determine service category
    if 'Classic' in service_info:
        service_data['category'] = 'Classic'
        if 'Fullset' in service_info:
            service_data['type'] = 'Fullset'
        elif 'Within' in service_info:
            service_data['type'] = 'Refill'
            # Extract refill period
            if '2-week' in service_info:
                service_data['refill_period'] = '2-week'
            elif '3-week' in service_info:
                service_data['refill_period'] = '3-week'
            elif '4-week' in service_info:
                service_data['refill_period'] = '4-week'
    
    elif 'Styling' in service_info:
        service_data['category'] = 'Styling'
        if 'Fullset' in service_info:
            service_data['type'] = 'Fullset'
        elif 'Within' in service_info:
            service_data['type'] = 'Refill'
            if '2-week' in service_info:
                service_data['refill_period'] = '2-week'
            elif '3-week' in service_info:
                service_data['refill_period'] = '3-week'
            elif '4-week' in service_info:
                service_data['refill_period'] = '4-week'
    
    elif 'Volume' in service_info:
        service_data['category'] = 'Volume'
        if 'Fullset' in service_info:
            service_data['type'] = 'Fullset'
        elif 'Within' in service_info:
            service_data['type'] = 'Refill'
            if '2-week' in service_info:
                service_data['refill_period'] = '2-week'
            elif '3-week' in service_info:
                service_data['refill_period'] = '3-week'
    
    elif 'Premium' in service_info:
        service_data['category'] = 'Premium'
        service_data['type'] = 'Fullset'
    
    elif 'Glue test' in service_info:
        service_data['category'] = 'Consultation'
        service_data['type'] = 'Glue Test'
    
    elif 'Courtesy Client Care' in service_info:
        service_data['category'] = 'Service'
        service_data['type'] = 'Client Care'
    
    elif 'Lash Removal' in service_info:
        service_data['category'] = 'Service'
        service_data['type'] = 'Removal'
    
    # Extract service codes (C01-C02, S01-S08, P01-P02)
    import re
    code_match = re.search(r'\(([CSPR]\d{2}-[CSPR]\d{2})\)', service_info)
    if code_match:
        service_data['service_code'] = code_match.group(1)
    
    return service_data

def analyze_parsed_data(events):
    """
    Analyze the parsed appointment data and show statistics
    """
    print("\n🔍 PARSED DATA ANALYSIS")
    print("=" * 60)
    
    appointment_types = {}
    service_categories = {}
    customers = set()
    add_ons_used = {}
    
    client_appointments = []
    
    for event in events:
        summary = event.get('SUMMARY', '')
        parsed = parse_appointment_summary(summary)
        
        # Add parsed data to the event
        event['parsed'] = parsed
        
        # Count appointment types
        appointment_types[parsed['type']] = appointment_types.get(parsed['type'], 0) + 1
        
        if parsed['type'] == 'client_appointment':
            client_appointments.append(event)
            customers.add(parsed['customer_name'])
            
            service = parsed['service']
            if service['category']:
                service_categories[service['category']] = service_categories.get(service['category'], 0) + 1
            
            for add_on in service['add_ons']:
                add_ons_used[add_on] = add_ons_used.get(add_on, 0) + 1
    
    # Show statistics
    print(f"📊 Appointment Type Breakdown:")
    for type_name, count in sorted(appointment_types.items()):
        print(f"   {type_name:20} | {count:3d} events")
    
    print(f"\n🏷️  Service Category Distribution:")
    for category, count in sorted(service_categories.items()):
        print(f"   {category:20} | {count:3d} appointments")
    
    print(f"\n👥 Customer Analysis:")
    print(f"   Total unique customers: {len(customers)}")
    
    print(f"\n🔧 Add-on Services Used:")
    for add_on, count in sorted(add_ons_used.items()):
        print(f"   {add_on:20} | {count:3d} times")
    
    # Show sample parsed appointments
    print(f"\n📋 SAMPLE PARSED APPOINTMENTS (first 5 client appointments):")
    print("-" * 60)
    
    for i, event in enumerate(client_appointments[:5], 1):
        parsed = event['parsed']
        print(f"\n{i}. {parsed['customer_name']}")
        print(f"   📅 Time: {event['DTSTART']} → {event['DTEND']}")
        print(f"   🏷️  Service: {parsed['service']['category']} {parsed['service']['type']}")
        if parsed['service'].get('refill_period'):
            print(f"   ⏰ Period: {parsed['service']['refill_period']}")
        if parsed['service']['add_ons']:
            print(f"   🔧 Add-ons: {', '.join(parsed['service']['add_ons'])}")
        if parsed['service'].get('service_code'):
            print(f"   🏷️  Code: {parsed['service']['service_code']}")
        print(f"   📝 Raw: {parsed['raw']}")
    
    return client_appointments

def show_dictionary_examples(client_appointments):
    """
    Show example appointments in clean dictionary format
    """
    print(f"\n📝 DICTIONARY FORMAT EXAMPLES")
    print("=" * 60)
    
    import json
    
    for i, event in enumerate(client_appointments[:3], 1):
        print(f"\n🔍 Example {i}: {event['parsed']['customer_name']}")
        print("-" * 40)
        
        # Create clean dictionary for display
        appointment_dict = {
            'customer_name': event['parsed']['customer_name'],
            'start_time': event['DTSTART'],
            'end_time': event['DTEND'],
            'service': {
                'category': event['parsed']['service']['category'],
                'type': event['parsed']['service']['type'],
                'add_ons': event['parsed']['service']['add_ons'],
                'has_consultation': event['parsed']['service']['has_consultation'],
            },
            'original_summary': event['SUMMARY']
        }
        
        # Add optional fields if they exist
        if event['parsed']['service'].get('refill_period'):
            appointment_dict['service']['refill_period'] = event['parsed']['service']['refill_period']
        if event['parsed']['service'].get('service_code'):
            appointment_dict['service']['service_code'] = event['parsed']['service']['service_code']
        
        # Pretty print the dictionary
        print(json.dumps(appointment_dict, indent=2))

def import_appointments_to_django(ics_file_path, business_id, default_employee_id, dry_run=True, 
                                create_missing_customers=True, create_missing_services=False):
    """
    Import appointments directly using Django models (bypassing API)
    """
    print(f"\n🚀 {'DRY RUN' if dry_run else 'IMPORTING'} APPOINTMENTS TO DJANGO")
    print("=" * 60)
    
    try:
        from business.models import Business, BusinessCustomer
        from employees.models import Employee
        from customers.models import CustomerProfile
        from services.models import Service, ServiceCategory
        from appointments.models import Appointment, AppointmentService
        from accounts.models import User  # Use custom User model
        from django.db import transaction
        from django.utils import timezone
        import pytz
        
        # Validate business and employee
        try:
            business = Business.objects.get(id=business_id)
            default_employee = Employee.objects.get(id=default_employee_id, business=business)
            print(f"📊 Target Business: {business.name}")
            print(f"👤 Default Employee: {default_employee.user.first_name} {default_employee.user.last_name}")
        except (Business.DoesNotExist, Employee.DoesNotExist) as e:
            print(f"❌ Error: {e}")
            return False
        
        # Parse ICS file
        events = parse_ics_file(ics_file_path)
        if not events:
            print("❌ No events found in ICS file")
            return False
        
        # Filter client appointments
        client_appointments = []
        for event in events:
            summary = event.get('SUMMARY', '')
            parsed = parse_appointment_summary(summary)
            
            if parsed['type'] == 'client_appointment':
                event['parsed'] = parsed
                client_appointments.append(event)
        
        print(f"📋 Found {len(client_appointments)} client appointments to import")
        
        # Import statistics
        stats = {
            'total_events': len(events),
            'client_appointments': len(client_appointments),
            'created_customers': 0,
            'matched_customers': 0,
            'created_services': 0,
            'matched_services': 0,
            'created_appointments': 0,
            'skipped_appointments': 0,
            'errors': [],
            'warnings': []
        }
        
        # Process appointments
        if dry_run:
            print("\n🔍 DRY RUN MODE - Validating only, no data will be saved")
            _validate_appointments_django(client_appointments, business, default_employee, 
                                        create_missing_customers, create_missing_services, stats)
        else:
            print("\n💾 IMPORT MODE - Actually creating appointments")
            with transaction.atomic():
                _import_appointments_django(client_appointments, business, default_employee,
                                          create_missing_customers, create_missing_services, stats)
        
        # Show results
        print(f"\n📊 IMPORT RESULTS:")
        print(f"   Total events processed: {stats['total_events']}")
        print(f"   Client appointments: {stats['client_appointments']}")
        print(f"   Customers created: {stats['created_customers']}")
        print(f"   Customers matched: {stats['matched_customers']}")
        print(f"   Services created: {stats['created_services']}")
        print(f"   Services matched: {stats['matched_services']}")
        print(f"   Appointments {'validated' if dry_run else 'created'}: {stats['created_appointments']}")
        print(f"   Appointments skipped: {stats['skipped_appointments']}")
        
        if stats['errors']:
            print(f"\n❌ ERRORS ({len(stats['errors'])}):")
            for error in stats['errors'][:10]:  # Show first 10 errors
                print(f"   • {error}")
            if len(stats['errors']) > 10:
                print(f"   ... and {len(stats['errors']) - 10} more errors")
        
        if stats['warnings']:
            print(f"\n⚠️  WARNINGS ({len(stats['warnings'])}):")
            for warning in stats['warnings'][:10]:  # Show first 10 warnings
                print(f"   • {warning}")
            if len(stats['warnings']) > 10:
                print(f"   ... and {len(stats['warnings']) - 10} more warnings")
        
        success = len(stats['errors']) == 0
        print(f"\n{'✅ SUCCESS' if success else '⚠️  PARTIAL SUCCESS'}: {'Validation completed' if dry_run else 'Import completed'}")
        return success
        
    except Exception as e:
        print(f"❌ Import failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def _validate_appointments_django(client_appointments, business, default_employee, 
                                create_missing_customers, create_missing_services, stats):
    """
    Validate appointments without saving (dry run mode)
    """
    from business.models import BusinessCustomer
    from customers.models import CustomerProfile
    from accounts.models import User  # Use custom User model
    from services.models import Service
    
    for event in client_appointments:
        try:
            parsed = event['parsed']
            customer_name = parsed['customer_name']
            
            # Check if customer exists
            name_parts = customer_name.split(' ')
            first_name = name_parts[0] if name_parts else customer_name
            last_name = ' '.join(name_parts[1:]) if len(name_parts) > 1 else ''
            
            try:
                business_customer = BusinessCustomer.objects.select_related(
                    'customer__user'
                ).get(
                    customer__user__first_name__iexact=first_name,
                    customer__user__last_name__iexact=last_name,
                    business=business
                )
                stats['matched_customers'] += 1
            except BusinessCustomer.DoesNotExist:
                if create_missing_customers:
                    stats['created_customers'] += 1
                    stats['warnings'].append(f"Will create new customer: {customer_name}")
                else:
                    stats['errors'].append(f"Customer not found: {customer_name}")
                    continue
            
            # Check service
            service_name = f"{parsed['service']['category']} {parsed['service']['type']}"
            try:
                service = Service.objects.filter(
                    name__icontains=parsed['service']['category'],
                    business=business
                ).first()
                
                if service:
                    stats['matched_services'] += 1
                else:
                    raise Service.DoesNotExist()
                    
            except Service.DoesNotExist:
                if create_missing_services:
                    stats['created_services'] += 1
                    stats['warnings'].append(f"Will create new service: {service_name}")
                else:
                    stats['errors'].append(f"Service not found: {service_name}")
                    continue
            
            stats['created_appointments'] += 1
            
        except Exception as e:
            stats['errors'].append(f"Error validating appointment {parsed.get('customer_name', 'Unknown')}: {str(e)}")

def _import_appointments_django(client_appointments, business, default_employee,
                              create_missing_customers, create_missing_services, stats):
    """
    Actually import appointments to Django models
    """
    from business.models import BusinessCustomer
    from customers.models import CustomerProfile
    from accounts.models import User  # Use custom User model
    from services.models import Service, ServiceCategory
    from appointments.models import Appointment, AppointmentService
    from datetime import datetime
    from django.utils import timezone
    import pytz
    
    for event in client_appointments:
        try:
            parsed = event['parsed']
            customer_name = parsed['customer_name']
            
            # Parse datetime from ICS format (20250709T170000Z)
            start_time_str = event['DTSTART']
            end_time_str = event['DTEND']
            
            # Convert from UTC to timezone-aware datetime
            start_time = datetime.strptime(start_time_str, '%Y%m%dT%H%M%SZ')
            end_time = datetime.strptime(end_time_str, '%Y%m%dT%H%M%SZ')
            start_time = pytz.UTC.localize(start_time)
            end_time = pytz.UTC.localize(end_time)
            
            # Get or create customer
            name_parts = customer_name.split(' ')
            first_name = name_parts[0] if name_parts else customer_name
            last_name = ' '.join(name_parts[1:]) if len(name_parts) > 1 else ''
            
            try:
                business_customer = BusinessCustomer.objects.select_related(
                    'customer__user'
                ).get(
                    customer__user__first_name__iexact=first_name,
                    customer__user__last_name__iexact=last_name,
                    business=business
                )
                stats['matched_customers'] += 1
            except BusinessCustomer.DoesNotExist:
                if create_missing_customers:
                    # Create User first
                    import uuid
                    import random
                    email = f"{first_name.lower()}.{last_name.lower()}@imported.local"
                    # Ensure unique email if duplicate names exist
                    base_email = email
                    counter = 1
                    while User.objects.filter(email=email).exists():
                        email = f"{first_name.lower()}.{last_name.lower()}.{counter}@imported.local"
                        counter += 1
                    
                    # Generate unique phone number for imported users
                    phone_number = f"+1555{random.randint(1000000, 9999999)}"
                    while User.objects.filter(phone_number=phone_number).exists():
                        phone_number = f"+1555{random.randint(1000000, 9999999)}"
                    
                    user = User.objects.create(
                        email=email,
                        phone_number=phone_number,
                        first_name=first_name,
                        last_name=last_name,
                    )
                    
                    # Create CustomerProfile
                    customer_profile = CustomerProfile.objects.create(
                        user=user
                    )
                    
                    # Create BusinessCustomer relationship
                    business_customer = BusinessCustomer.objects.create(
                        business=business,
                        customer=customer_profile,
                        import_source='ICS Calendar Import'
                    )
                    
                    stats['created_customers'] += 1
                else:
                    stats['errors'].append(f"Customer not found: {customer_name}")
                    continue
            
            # Get or create service
            service_name = f"{parsed['service']['category']} {parsed['service']['type']}"
            if parsed['service'].get('refill_period'):
                service_name += f" {parsed['service']['refill_period']}"
            
            try:
                service = Service.objects.filter(
                    name__icontains=parsed['service']['category'],
                    business=business
                ).first()
                
                if not service:
                    raise Service.DoesNotExist()
                
                stats['matched_services'] += 1
            except Service.DoesNotExist:
                if create_missing_services:
                    # Get or create service category
                    category, _ = ServiceCategory.objects.get_or_create(
                        name=parsed['service']['category'],
                        business=business
                    )
                    
                    # Calculate actual duration from ICS file for new service
                    actual_duration = end_time - start_time
                    duration_for_service = actual_duration if actual_duration.total_seconds() > 0 else timezone.timedelta(minutes=60)
                    
                    # Create service
                    service = Service.objects.create(
                        name=service_name,
                        business=business,
                        category=category,
                        base_duration=duration_for_service,  # Use actual ICS duration
                        base_price=100.00,  # Default price
                        description=f"Imported from calendar: {parsed['raw']}"
                    )
                    print(f"📅 Created new service '{service_name}' with duration: {int(duration_for_service.total_seconds() // 60)} minutes")
                    stats['created_services'] += 1
                else:
                    stats['errors'].append(f"Service not found: {service_name}")
                    continue
            
            # Check for duplicate appointments  
            existing_appointment = Appointment.objects.filter(
                customer=business_customer,  # Use BusinessCustomer instance
                employee=default_employee,
                start_time=start_time,
                status__in=['pending', 'confirmed']
            ).first()
            
            if existing_appointment:
                stats['skipped_appointments'] += 1
                stats['warnings'].append(f"Skipped duplicate appointment for {customer_name} at {start_time}")
                continue
            
            # Create appointment and service in a single transaction (CRITICAL FIX)
            with transaction.atomic():
                appointment = Appointment.objects.create(
                    customer=business_customer,  # Use BusinessCustomer instance
                    employee=default_employee,
                    start_time=start_time,
                    status='confirmed',
                    # notes=f"Imported from calendar: {parsed['raw']}"
                )
                
                # Create appointment service (REQUIRED - without this, appointments are invisible)
                # Calculate actual duration from ICS file (end_time - start_time)
                actual_duration = end_time - start_time
                duration_minutes = int(actual_duration.total_seconds() // 60)
                
                print(f"📅 Using actual ICS duration: {duration_minutes} minutes (from {start_time} to {end_time})")
                
                AppointmentService.objects.create(
                    appointment=appointment,
                    service=service,
                    base_price=service.base_price,
                    duration=duration_minutes
                )
                
                print(f"✅ Created appointment {appointment.id} with service {service.name}")
            
            stats['created_appointments'] += 1
            
        except Exception as e:
            stats['errors'].append(f"Error importing appointment {parsed.get('customer_name', 'Unknown')}: {str(e)}")

def delete_imported_appointments(business_id, employee_id, date_range=None):
    """
    Delete previously imported appointments and their associated data
    """
    try:
        from appointments.models import Appointment, AppointmentService
        from business.models import Business, BusinessCustomer
        from employees.models import Employee
        from customers.models import CustomerProfile
        from accounts.models import User
        from django.db import transaction
        from datetime import datetime, timezone
        
        print(f"\n🗑️  DELETING IMPORTED APPOINTMENTS")
        print("=" * 60)
        
        # Validate business and employee
        try:
            business = Business.objects.get(id=business_id)
            employee = Employee.objects.get(id=employee_id, business=business)
            print(f"📊 Target Business: {business.name}")
            print(f"👤 Target Employee: {employee.user.first_name} {employee.user.last_name}")
        except (Business.DoesNotExist, Employee.DoesNotExist) as e:
            print(f"❌ Error: {e}")
            return False
        
        # Default to July 2025 range if not specified
        if not date_range:
            july_start = datetime(2025, 7, 1, tzinfo=timezone.utc)
            august_start = datetime(2025, 8, 1, tzinfo=timezone.utc)
            date_range = (july_start, august_start)
        
        start_date, end_date = date_range
        print(f"📅 Date Range: {start_date.strftime('%Y-%m-%d')} to {end_date.strftime('%Y-%m-%d')}")
        
        # Find appointments to delete
        appointments_to_delete = Appointment.objects.filter(
            employee=employee,
            start_time__gte=start_date,
            start_time__lt=end_date
        )
        
        print(f"\n🔍 Found {appointments_to_delete.count()} appointments to delete")
        
        if appointments_to_delete.count() == 0:
            print("✅ No appointments found to delete")
            return True
        
        # Show what will be deleted
        print(f"\n📋 Appointments to be deleted:")
        for apt in appointments_to_delete[:10]:  # Show first 10
            customer_name = f"{apt.customer.customer.user.first_name} {apt.customer.customer.user.last_name}"
            print(f"   • {customer_name} - {apt.start_time.strftime('%Y-%m-%d %H:%M')}")
        
        if appointments_to_delete.count() > 10:
            print(f"   ... and {appointments_to_delete.count() - 10} more")
        
        # Confirm deletion
        print(f"\n⚠️  This will delete:")
        
        # Count related objects
        appointment_services = AppointmentService.objects.filter(appointment__in=appointments_to_delete)
        print(f"   • {appointments_to_delete.count()} appointments")
        print(f"   • {appointment_services.count()} appointment services")
        
        # Count customers created during import (those with @imported.local emails)
        imported_customers = BusinessCustomer.objects.filter(
            business=business,
            customer__user__email__endswith='@imported.local'
        )
        print(f"   • {imported_customers.count()} imported customers (with @imported.local emails)")
        
        response = input(f"\n❓ Continue with deletion? (yes/no): ").lower().strip()
        if response not in ['yes', 'y']:
            print("❌ Deletion cancelled")
            return False
        
        # Perform deletion - appointments first (separate transaction)
        print(f"\n🗑️  Deleting appointments...")
        with transaction.atomic():
            # Delete appointment services first (foreign key constraint)
            deleted_services = appointment_services.count()
            appointment_services.delete()
            print(f"   ✅ Deleted {deleted_services} appointment services")
            
            # Delete appointments
            deleted_appointments = appointments_to_delete.count()
            appointments_to_delete.delete()
            print(f"   ✅ Deleted {deleted_appointments} appointments")
        
        # Delete imported customers (optional, separate transaction to avoid rollback)
        if imported_customers.exists():
            response = input(f"\n❓ Also delete {imported_customers.count()} imported customers? (yes/no): ").lower().strip()
            if response in ['yes', 'y']:
                try:
                    with transaction.atomic():
                        # Get the user IDs before deleting BusinessCustomer
                        customer_user_ids = list(imported_customers.values_list('customer__user__id', flat=True))
                        
                        # Delete BusinessCustomer relationships
                        deleted_business_customers = imported_customers.count()
                        imported_customers.delete()
                        
                        # Delete CustomerProfile records
                        customer_profiles = CustomerProfile.objects.filter(user__id__in=customer_user_ids)
                        deleted_profiles = customer_profiles.count()
                        customer_profiles.delete()
                        
                        # Delete User records
                        users = User.objects.filter(id__in=customer_user_ids)
                        deleted_users = users.count()
                        users.delete()
                        
                        print(f"   ✅ Deleted {deleted_business_customers} business customer relationships")
                        print(f"   ✅ Deleted {deleted_profiles} customer profiles")
                        print(f"   ✅ Deleted {deleted_users} user accounts")
                except Exception as e:
                    print(f"   ⚠️  Customer deletion failed (appointments still deleted): {str(e)}")
                    print(f"   💡 This is normal - customers may be referenced by other data")
        
        print(f"\n✅ SUCCESS: Cleanup completed!")
        print(f"   You can now re-run the import with correct durations:")
        print(f"   python {__file__} import --business-id {business_id} --employee-id {employee_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Deletion failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    parser = argparse.ArgumentParser(description='Import ICS calendar files to Django appointments')
    parser.add_argument('action', choices=['inspect', 'dry-run', 'import', 'list-businesses', 'delete-imported'], 
                       help='Action to perform: inspect (view only), dry-run (validate), import (actually create), list-businesses (show available IDs), or delete-imported (remove previously imported appointments)')
    parser.add_argument('--ics-file', type=str, 
                       help='Path to ICS file (default: sample file)')
    parser.add_argument('--business-id', type=int, required=False,
                       help='Business ID to import appointments to (required for dry-run/import)')
    parser.add_argument('--employee-id', type=int, required=False,
                       help='Default employee ID to assign appointments to (required for dry-run/import)')
    parser.add_argument('--create-customers', action='store_true', default=True,
                       help='Create missing customers (default: True)')
    parser.add_argument('--create-services', action='store_true', default=False,
                       help='Create missing services (default: False)')
    
    args = parser.parse_args()
    
    # Get the script directory
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # Determine ICS file path
    if args.ics_file:
        ics_file_path = args.ics_file
    else:
        ics_file_path = os.path.join(script_dir, '../sample-import-data/DateRange_1July2025-31July2025.ics')
    
    print("📋 ICS Calendar Import Tool")
    print("=" * 60)
    print(f"Mode: {args.action.upper()}")
    print(f"ICS File: {ics_file_path}")
    print()
    
    # Special case: list businesses (no file needed)
    if args.action == 'list-businesses':
        list_businesses_and_employees()
        return
    
    # Validate file exists (for other actions)
    if not os.path.exists(ics_file_path):
        print(f"❌ ICS file not found: {ics_file_path}")
        return
    
    if args.action == 'inspect':
        # Just inspect and analyze the file
        events = parse_ics_file(ics_file_path)
        
        if events:
            print(f"\n✅ Successfully parsed {len(events)} events!")
            
            # Parse appointments into structured dictionaries
            client_appointments = analyze_parsed_data(events)
            
            # Show dictionary format examples
            show_dictionary_examples(client_appointments)
            
            print("\n💡 Key Observations:")
            print("   - This appears to be a lash extension salon calendar")
            print("   - Contains customer appointments with service types")
            print("   - Has personal events (Workout, Badminton, etc.)")
            print("   - Uses UTC timestamps")
            print("   - July 2025 date range")
            print(f"   - {len(client_appointments)} client appointments ready for import")
        else:
            print("\n❌ No events found or failed to parse")
    
    elif args.action in ['dry-run', 'import']:
        # Validate required arguments
        if not args.business_id or not args.employee_id:
            print("❌ --business-id and --employee-id are required for dry-run and import modes")
            print("\nUsage examples:")
            print("  python create_import_appointments_to_calendar.py dry-run --business-id 1 --employee-id 2")
            print("  python create_import_appointments_to_calendar.py import --business-id 1 --employee-id 2")
            return
        
        # Perform import or dry run
        dry_run = (args.action == 'dry-run')
        success = import_appointments_to_django(
            ics_file_path=ics_file_path,
            business_id=args.business_id,
            default_employee_id=args.employee_id,
            dry_run=dry_run,
            create_missing_customers=args.create_customers,
            create_missing_services=args.create_services
        )
        
        if success:
            if dry_run:
                print(f"\n✅ Ready to import! Run with 'import' action to actually create appointments:")
                print(f"python {__file__} import --business-id {args.business_id} --employee-id {args.employee_id}")
        else:
            print(f"\n❌ {'Validation' if dry_run else 'Import'} failed. Check errors above.")
    
    elif args.action == 'delete-imported':
        # Validate required arguments
        if not args.business_id or not args.employee_id:
            print("❌ --business-id and --employee-id are required for delete-imported mode")
            print("\nUsage examples:")
            print("  python create_import_appointments_to_calendar.py delete-imported --business-id 1 --employee-id 2")
            return
        
        # Perform deletion
        success = delete_imported_appointments(
            business_id=args.business_id,
            employee_id=args.employee_id
        )
        
        if success:
            print(f"\n🎯 Next step: Re-import with correct durations:")
            print(f"python {__file__} import --business-id {args.business_id} --employee-id {args.employee_id}")
        else:
            print(f"\n❌ Deletion failed. Check errors above.")

def list_businesses_and_employees():
    """
    Helper function to list available businesses and employees
    """
    try:
        from business.models import Business
        from employees.models import Employee
        
        print("📊 Available Businesses:")
        for business in Business.objects.all():
            print(f"   ID: {business.id} | Name: {business.name}")
            
            employees = Employee.objects.filter(business=business, is_active=True)
            if employees:
                print(f"   Employees:")
                for emp in employees:
                    print(f"     ID: {emp.id} | {emp.user.first_name} {emp.user.last_name}")
            print()
    except Exception as e:
        print(f"Error listing businesses: {e}")

if __name__ == "__main__":
    main()
