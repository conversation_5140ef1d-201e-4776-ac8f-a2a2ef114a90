from django.urls import path
from . import views

app_name = 'calendar_api'

urlpatterns = [
    # Calendar connections
    path('connections/', views.CalendarConnectionListView.as_view(), name='connections_list'),
    path('connections/<uuid:connection_id>/', views.CalendarConnectionDetailView.as_view(), name='connection_detail'),
    
    # OAuth flows
    path('oauth/google/initiate/', views.GoogleCalendarOAuthInitiateView.as_view(), name='google_oauth_initiate'),
    path('oauth/google/callback/', views.GoogleCalendarOAuthCallbackView.as_view(), name='google_oauth_callback'),
    
    # Sync status
    path('sync/status/', views.CalendarSyncStatusView.as_view(), name='sync_status'),
]