/**
 * Time Grid Calculations - iOS Calendar Implementation
 * Handles precise positioning and snapping for different time resolutions
 */

/**
 * Get hour minute intervals based on time resolution
 * Based on iOS CalendarViewModel.hourMinuteIntervals
 */
export const getHourMinuteIntervals = (timeResolution) => {
  switch (timeResolution) {
    case 5:
      return [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55]
    case 15:
      return [0, 15, 30, 45]
    case 30:
      return [0, 30]
    default:
      return [0, 30]
  }
}

/**
 * Calculate grid cell height based on time resolution
 * Each hour is divided into intervals based on resolution
 */
export const calculateGridCellHeight = (hourHeight, timeResolution) => {
  const intervalsPerHour = 60 / timeResolution
  return hourHeight / intervalsPerHour
}

/**
 * Calculate position relative to grid lines
 * Based on iOS TimeGridViewModel.calculatePositionRelativeToGridLines
 */
export const calculatePositionRelativeToGridLines = (hour, minute, config) => {
  const { displayHourStart, timeResolution, gridHeight } = config
  
  // Calculate total minutes from display start
  const totalMinutesFromStart = (hour - displayHourStart) * 60 + minute
  
  // Calculate which interval this represents
  const intervalIndex = totalMinutesFromStart / timeResolution
  
  // Calculate grid cell height
  const gridCellHeight = calculateGridCellHeight(gridHeight, timeResolution)
  
  // Position is interval index * cell height + half cell height for centering
  const position = intervalIndex * gridCellHeight + (gridCellHeight / 2.0)
  
  return position
}

/**
 * Calculate appointment offset for positioning
 * Based on iOS TimeGridViewModel.calculateAppointmentOffset
 */
export const calculateAppointmentOffset = (appointment, config) => {
  const startTime = new Date(appointment.start)
  const hour = startTime.getHours()
  const minute = startTime.getMinutes()
  
  return calculatePositionRelativeToGridLines(hour, minute, config)
}

/**
 * Calculate appointment height based on duration
 * Based on iOS TimeGridViewModel.calculateAppointmentHeight
 */
export const calculateAppointmentHeight = (appointment, config) => {
  const { timeResolution, gridHeight } = config
  
  // Get appointment duration in minutes
  const startTime = new Date(appointment.start)
  const endTime = new Date(appointment.end)
  const durationMinutes = (endTime - startTime) / (1000 * 60)
  
  // Calculate how many time intervals this spans
  const intervalsSpanned = durationMinutes / timeResolution
  
  // Calculate height per interval
  const gridCellHeight = calculateGridCellHeight(gridHeight, timeResolution)
  
  // Total height
  return intervalsSpanned * gridCellHeight
}

/**
 * Snap vertical offset to time grid based on resolution
 * Implementation for each time resolution scenario
 */
export const snapToTimeGrid = (verticalOffset, config) => {
  const { timeResolution, gridHeight } = config
  const gridCellHeight = calculateGridCellHeight(gridHeight, timeResolution)
  
  // Different snapping logic for each resolution
  switch (timeResolution) {
    case 5: // 5-minute resolution - 12 intervals per hour
      return snapTo5MinuteGrid(verticalOffset, gridCellHeight)
    
    case 15: // 15-minute resolution - 4 intervals per hour  
      return snapTo15MinuteGrid(verticalOffset, gridCellHeight)
    
    case 30: // 30-minute resolution - 2 intervals per hour
      return snapTo30MinuteGrid(verticalOffset, gridCellHeight)
    
    default:
      return snapTo15MinuteGrid(verticalOffset, gridCellHeight)
  }
}

/**
 * 5-minute resolution snapping (Precision Mode)
 * Grid lines every 5 minutes (12 lines per hour)
 * High precision, dense grid
 */
const snapTo5MinuteGrid = (verticalOffset, gridCellHeight) => {
  // Each grid cell represents 5 minutes
  const intervalOffset = Math.round(verticalOffset / gridCellHeight)
  const snappedOffset = intervalOffset * gridCellHeight
  
  // Additional precision: snap to half-intervals for very precise positioning
  const halfCellHeight = gridCellHeight / 2
  const halfIntervalOffset = Math.round(verticalOffset / halfCellHeight)
  const halfSnappedOffset = halfIntervalOffset * halfCellHeight
  
  // Use half-interval snapping if it's closer
  const distanceToFull = Math.abs(verticalOffset - snappedOffset)
  const distanceToHalf = Math.abs(verticalOffset - halfSnappedOffset)
  
  return distanceToHalf < distanceToFull ? halfSnappedOffset : snappedOffset
}

/**
 * 15-minute resolution snapping (Balanced Mode)
 * Grid lines every 15 minutes (4 lines per hour)
 * Optimal balance between precision and clarity
 */
const snapTo15MinuteGrid = (verticalOffset, gridCellHeight) => {
  // Standard snapping to 15-minute intervals
  const intervalOffset = Math.round(verticalOffset / gridCellHeight)
  return intervalOffset * gridCellHeight
}

/**
 * 30-minute resolution snapping (Block Mode)
 * Grid lines every 30 minutes (2 lines per hour)
 * Clean, minimal grid for block scheduling
 */
const snapTo30MinuteGrid = (verticalOffset, gridCellHeight) => {
  // Snap to 30-minute blocks
  const intervalOffset = Math.round(verticalOffset / gridCellHeight)
  return intervalOffset * gridCellHeight
}

/**
 * Calculate new time from vertical drag offset
 * Handles different time resolutions with precise calculations
 * CRITICAL: Respects edge case appointments that start between grid lines
 */
export const calculateNewTimeFromOffset = (verticalOffset, appointment, config) => {
  const { timeResolution, gridHeight, displayHourStart } = config
  const originalStart = new Date(appointment.start)
  
  // Calculate grid cell height for this resolution
  const gridCellHeight = calculateGridCellHeight(gridHeight, timeResolution)
  
  // Convert vertical offset to time change in minutes
  const intervalChange = Math.round(verticalOffset / gridCellHeight)
  const minuteChange = intervalChange * timeResolution
  
  // Apply time change to original start time
  const newStart = new Date(originalStart)
  newStart.setMinutes(newStart.getMinutes() + minuteChange)
  
  // CRITICAL: Check if original appointment starts between grid lines (edge case)
  const originalMinute = originalStart.getMinutes()
  const isOriginallyOnGridLine = originalMinute % timeResolution === 0
  
  // Only apply alignment for appointments that originally started on grid lines
  if (isOriginallyOnGridLine) {
    // NORMAL CASE: Original appointment was on grid line - apply standard snapping
    return alignTimeToResolution(newStart, timeResolution)
  } else {
    // EDGE CASE: Original appointment was between grid lines - preserve exact positioning
    // No alignment needed - just return the calculated time as-is
    return newStart
  }
}

/**
 * Align time to specific resolution intervals
 * Ensures times always fall on valid grid points
 */
export const alignTimeToResolution = (date, timeResolution) => {
  const alignedDate = new Date(date)
  const minutes = alignedDate.getMinutes()
  
  // Round to nearest interval
  const alignedMinutes = Math.round(minutes / timeResolution) * timeResolution
  
  // Handle overflow (e.g., 57 minutes rounded to 60)
  if (alignedMinutes >= 60) {
    alignedDate.setHours(alignedDate.getHours() + 1)
    alignedDate.setMinutes(alignedMinutes - 60)
  } else {
    alignedDate.setMinutes(alignedMinutes)
  }
  
  // Always set seconds to 0
  alignedDate.setSeconds(0)
  alignedDate.setMilliseconds(0)
  
  return alignedDate
}



/**
 * Find closest time points for snapping
 * Based on iOS TimeGridViewModel.findClosestTimePoints
 */
export const findClosestTimePoints = (yPosition, config) => {
  const { displayHourStart, displayHourEnd, timeResolution, gridHeight } = config
  const gridCellHeight = calculateGridCellHeight(gridHeight, timeResolution)
  
  // Calculate which interval this Y position represents
  const intervalIndex = Math.floor(yPosition / gridCellHeight)
  const totalIntervals = ((displayHourEnd - displayHourStart) * 60) / timeResolution
  
  // Clamp to valid range
  const clampedIndex = Math.max(0, Math.min(intervalIndex, totalIntervals - 1))
  const nextIndex = Math.min(clampedIndex + 1, totalIntervals - 1)
  
  // Convert back to hour/minute
  const currentMinutes = clampedIndex * timeResolution
  const nextMinutes = nextIndex * timeResolution
  
  const currentHour = displayHourStart + Math.floor(currentMinutes / 60)
  const currentMinute = currentMinutes % 60
  
  const nextHour = displayHourStart + Math.floor(nextMinutes / 60)
  const nextMinute = nextMinutes % 60
  
  return {
    above: { hour: currentHour, minute: currentMinute },
    below: { hour: nextHour, minute: nextMinute }
  }
}

/**
 * Calculate snapping distance threshold based on resolution
 * Different thresholds for different resolutions
 */
export const getSnappingThreshold = (timeResolution) => {
  switch (timeResolution) {
    case 5:
      return 15 // Tighter threshold for precision mode
    case 15:
      return 20 // Standard threshold
    case 30:
      return 25 // Looser threshold for block mode
    default:
      return 20
  }
}

/**
 * Validate time position against business rules
 * Ensures appointments stay within valid boundaries
 */
export const validateTimePosition = (newTime, config, workingHours = null) => {
  const hour = newTime.getHours()
  const minute = newTime.getMinutes()
  
  // Check display hour boundaries
  if (hour < config.displayHourStart || hour >= config.displayHourEnd) {
    return {
      isValid: false,
      reason: 'outside_display_hours',
      message: `Time must be between ${config.displayHourStart}:00 and ${config.displayHourEnd}:00`
    }
  }
  
  // Check working hours if provided
  if (workingHours && config.showWorkingHours) {
    const workingStart = parseInt(workingHours.start.split(':')[0])
    const workingEnd = parseInt(workingHours.end.split(':')[0])
    
    if (hour < workingStart || hour >= workingEnd) {
      return {
        isValid: false,
        reason: 'outside_working_hours',
        message: `Time must be within working hours (${workingHours.start} - ${workingHours.end})`
      }
    }
  }
  
  // Check if time aligns with resolution
  const intervals = getHourMinuteIntervals(config.timeResolution)
  if (!intervals.includes(minute)) {
    return {
      isValid: false,
      reason: 'invalid_time_interval',
      message: `Time must align with ${config.timeResolution}-minute intervals`
    }
  }
  
  return { isValid: true }
}

/**
 * Get time resolution display info
 * Provides metadata about each resolution mode
 */
export const getTimeResolutionInfo = (timeResolution) => {
  switch (timeResolution) {
    case 5:
      return {
        name: 'Precision Mode',
        description: '5-minute intervals',
        intervalsPerHour: 12,
        gridDensity: 'high',
        useCase: 'Medical appointments, precise scheduling',
        snappingDistance: 15
      }
    
    case 15:
      return {
        name: 'Balanced Mode',
        description: '15-minute intervals',
        intervalsPerHour: 4,
        gridDensity: 'medium',
        useCase: 'Standard business appointments',
        snappingDistance: 20
      }
    
    case 30:
      return {
        name: 'Block Mode',
        description: '30-minute intervals',
        intervalsPerHour: 2,
        gridDensity: 'low',
        useCase: 'Long appointments, block scheduling',
        snappingDistance: 25
      }
    
    default:
      return getTimeResolutionInfo(15) // Default to balanced mode
  }
}

/**
 * Advanced Appointment Border Snapping (iOS Implementation)
 * Snaps moving appointments to borders of other appointments
 */

/**
 * Calculate smart snapped position with appointment border detection
 * Based on iOS TimeGridViewModel.calculateSnappedPosition
 */
export const calculateSmartSnappedPosition = (
  currentY,
  verticalTranslation,
  lastSnappedY,
  movingAppointment,
  allAppointments,
  config
) => {
  const targetY = currentY + verticalTranslation
  let closestSnapY = targetY
  let closestDistance = Number.MAX_VALUE
  let snapType = 'none'
  
  // 1. Check appointment border snapping first (higher priority)
  if (movingAppointment && allAppointments && allAppointments.length > 0) {
    const movingHeight = calculateAppointmentHeight(movingAppointment, config)
    const movingBottomY = targetY + movingHeight
    
    for (const appointment of allAppointments) {
      if (appointment.id === movingAppointment.id) continue
      
      const appointmentStartY = calculateAppointmentOffset(appointment, config)
      const appointmentEndY = appointmentStartY + calculateAppointmentHeight(appointment, config)
      
      // Snap moving appointment's TOP to other appointment's START border
      const topToStartDistance = Math.abs(targetY - appointmentStartY)
      if (topToStartDistance < closestDistance && topToStartDistance < 20.0) {
        closestDistance = topToStartDistance
        closestSnapY = appointmentStartY
        snapType = 'top-to-start'
      }
      
      // Snap moving appointment's TOP to other appointment's END border
      const topToEndDistance = Math.abs(targetY - appointmentEndY)
      if (topToEndDistance < closestDistance && topToEndDistance < 20.0) {
        closestDistance = topToEndDistance
        closestSnapY = appointmentEndY
        snapType = 'top-to-end'
      }
      
      // Snap moving appointment's BOTTOM to other appointment's START border
      const bottomToStartDistance = Math.abs(movingBottomY - appointmentStartY)
      if (bottomToStartDistance < closestDistance && bottomToStartDistance < 20.0) {
        closestDistance = bottomToStartDistance
        closestSnapY = appointmentStartY - movingHeight
        snapType = 'bottom-to-start'
      }
      
      // Snap moving appointment's BOTTOM to other appointment's END border
      const bottomToEndDistance = Math.abs(movingBottomY - appointmentEndY)
      if (bottomToEndDistance < closestDistance && bottomToEndDistance < 20.0) {
        closestDistance = bottomToEndDistance
        closestSnapY = appointmentEndY - movingHeight
        snapType = 'bottom-to-end'
      }
    }
  }
  
  // 2. Check time grid line snapping (fallback if no appointment borders nearby)
  if (snapType === 'none') {
    const timeGridSnap = snapToTimeGrid(verticalTranslation, config)
    const timeGridSnapY = currentY + timeGridSnap
    const timeGridDistance = Math.abs(targetY - timeGridSnapY)
    
    if (timeGridDistance < closestDistance) {
      closestDistance = timeGridDistance
      closestSnapY = timeGridSnapY
      snapType = 'time-grid'
    }
  }
  
  // 3. Determine if haptic feedback should trigger
  const shouldTriggerHaptic = lastSnappedY === null || 
    Math.abs(closestSnapY - (lastSnappedY || 0)) > 0.1
  
  return {
    snappedY: closestSnapY,
    newLastSnappedY: closestSnapY,
    shouldTriggerHaptic,
    snapType,
    snapDistance: closestDistance
  }
}

/**
 * Get all appointments in current view for border detection
 * Filters appointments that could provide borders to snap to
 */
export const getAppointmentsForBorderDetection = (
  allAppointments,
  currentDate,
  displayMode,
  weekDays = [],
  selectedEmployees = []
) => {
  if (!allAppointments || allAppointments.length === 0) {
    return []
  }
  
  return allAppointments.filter(appointment => {
    const appointmentDate = new Date(appointment.start)
    
    if (displayMode === 'week') {
      // Week view: include appointments from all visible days
      return weekDays.some(day => 
        appointmentDate.toDateString() === day.toDateString()
      )
    } else if (displayMode === 'day') {
      // Day view: include appointments from current day
      if (selectedEmployees.length > 1) {
        // Multi-employee: include appointments from all selected employees
        return appointmentDate.toDateString() === currentDate.toDateString() &&
               selectedEmployees.some(emp => emp.id === appointment.employeeId)
      } else {
        // Single employee: include appointments from current day
        return appointmentDate.toDateString() === currentDate.toDateString()
      }
    }
    
    return false
  })
}

/**
 * Trigger haptic feedback (web vibration API)
 * Mimics iOS haptic feedback for snapping
 */
export const triggerHapticFeedback = (intensity = 'light') => {
  if ('vibrate' in navigator) {
    switch (intensity) {
      case 'light':
        navigator.vibrate(10) // Light tap
        break
      case 'medium':
        navigator.vibrate(20) // Medium tap
        break
      case 'heavy':
        navigator.vibrate([30, 10, 30]) // Heavy tap pattern
        break
      default:
        navigator.vibrate(10)
    }
  }
} 

/**
 * Advanced slot-based appointment positioning calculation
 * Based on useMultiEmployeeDayView.js logic for accurate cross-resolution positioning
 */
export const calculateSlotBasedPosition = (hour, minute, config) => {
  const { displayHourStart, timeResolution, gridHeight } = config
  
  // Determine the best slot based on common resolutions (5, 15, 30) - same logic as useMultiEmployeeDayView
  let appointmentSlotMinute
  
  // Check if we can determine the resolution from the minute pattern
  if (minute % 30 === 0) {
    // 30-minute resolution: [0, 30]
    appointmentSlotMinute = Math.floor(minute / 30) * 30
  } else if (minute % 15 === 0) {
    // 15-minute resolution: [0, 15, 30, 45]
    appointmentSlotMinute = Math.floor(minute / 15) * 15
  } else if (minute % 5 === 0) {
    // 5-minute resolution: [0, 5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55]
    appointmentSlotMinute = Math.floor(minute / 5) * 5
  } else {
    // Fallback to current time resolution
    appointmentSlotMinute = Math.floor(minute / timeResolution) * timeResolution
  }
  
  // Calculate the absolute position in the calendar grid
  // 1. Calculate the position of the time slot itself
  const totalMinutesFromStart = (hour - displayHourStart) * 60 + appointmentSlotMinute
  const slotIndex = totalMinutesFromStart / timeResolution
  const slotHeight = gridHeight / (60 / timeResolution)
  const slotAbsoluteTop = slotIndex * slotHeight
  
  // 2. Calculate the offset within the time slot (exact minute positioning)
  const minuteOffset = minute - appointmentSlotMinute
  const pixelOffset = (minuteOffset / timeResolution) * slotHeight
  
  // 3. Combine for exact absolute position
  const absolutePosition = slotAbsoluteTop + pixelOffset
  
  return {
    absolutePosition,
    slotAbsoluteTop,
    pixelOffset,
    appointmentSlotMinute,
    slotIndex
  }
} 

/**
 * Calculate exact appointment block top border position
 * CRITICAL: This MUST exactly replicate PositionCalculations.jsx logic
 * Handles edge cases where appointment starts between time grid lines (e.g., 10:45 in 30min resolution)
 */
export const calculateAppointmentBlockTopPosition = (hour, minute, config) => {
  const { displayHourStart, timeResolution, gridHeight } = config
  
  // EXACT REPLICATION of PositionCalculations.jsx logic:
  // 1. Find which time slot this appointment starts in
  const slotMinute = Math.floor(minute / timeResolution) * timeResolution
  
  // 2. Calculate the time slot's absolute position in the calendar
  const totalMinutesFromStart = (hour - displayHourStart) * 60 + slotMinute
  const slotIndex = totalMinutesFromStart / timeResolution
  const slotHeight = gridHeight / (60 / timeResolution)
  const slotAbsoluteTop = slotIndex * slotHeight
  
  // 3. Calculate exact pixel offset within the time slot
  // This handles appointments that start between grid lines (e.g., 10:45 in 30min resolution)
  const minuteOffset = minute - slotMinute
  const pixelOffset = (minuteOffset / timeResolution) * slotHeight
  
  // 4. Return the exact appointment block top border position
  // Account for TimeSlot padding (p-1 = 4px top padding) to match PositionCalculations.jsx
  const appointmentTopPosition = slotAbsoluteTop + pixelOffset + 4
  
  // EDGE CASE VERIFICATION: Detect appointments that fall between grid lines
  const isOnGridLine = minute % timeResolution === 0
  
  return {
    appointmentTopPosition,
    slotAbsoluteTop,
    pixelOffset,
    slotMinute,
    slotIndex,
    slotHeight,
    timeSlotPadding: 4,
    isOnGridLine,
    edgeCaseInfo: !isOnGridLine ? {
      appointmentTime: `${hour}:${minute.toString().padStart(2, '0')}`,
      slotTime: `${hour}:${slotMinute.toString().padStart(2, '0')}`,
      minuteOffset,
      pixelOffset
    } : null
  }
}

