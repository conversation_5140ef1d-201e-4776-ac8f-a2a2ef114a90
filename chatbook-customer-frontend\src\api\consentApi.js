/**
 * Consent Form API Service
 * Handles fetching structured consent form content from backend
 */

import { api as axiosInstance } from '../hooks/useApi';

const API_BASE_URL = 'http://localhost:8000/api/v1';

// Circuit breaker to prevent infinite calls
let consentStatusCallInProgress = false;
let lastConsentStatusError = null;
let lastConsentStatusErrorTime = null;
let hasTriggeredLogout = false; // Prevent multiple logout triggers

// Reset circuit breaker (for testing)
export const resetConsentStatusCircuitBreaker = () => {
    console.log('🔄 Resetting consent status circuit breaker');
    consentStatusCallInProgress = false;
    lastConsentStatusError = null;
    lastConsentStatusErrorTime = null;
    hasTriggeredLogout = false;
};

// Helper function for API requests
const fetchAPI = async (endpoint) => {
    try {
        const response = await fetch(`${API_BASE_URL}${endpoint}`);
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        return await response.json();
    } catch (error) {
        console.error('Consent API error:', error);
        throw error;
    }
};

/**
 * Fetch consent form content by service type
 * @param {string} serviceType - Type of service (e.g., 'eyelash_extensions')
 * @returns {Promise} Promise resolving to structured consent form data
 */
export const getConsentForm = async (serviceType = 'eyelash_extensions') => {
    try {
        const data = await fetchAPI(`/consent-forms/${serviceType}/`);
        return data;
    } catch (error) {
        console.warn('Using fallback consent form data');
        return getFallbackConsentForm();
    }
};

/**
 * Submit signed consent form
 * @param {Object} consentData - Signed consent form data
 * @returns {Promise} Promise resolving to submission result
 */
export const submitConsentForm = async (consentData) => {
    try {
        const response = await axiosInstance.post('/forms/submissions/', consentData);
        return response.data;
    } catch (error) {
        console.error('Error submitting consent form:', error);
        throw error;
    }
};

/**
 * Check if the authenticated user has completed all required forms/consent for a business
 * @returns {Promise} Promise resolving to user forms status object
 */
export const checkUserConsentStatus = async () => {
    // Circuit breaker: prevent multiple simultaneous calls
    if (consentStatusCallInProgress) {
        console.log('🚫 Consent status call already in progress, rejecting duplicate call');
        throw new Error('Consent status call already in progress');
    }

    // Circuit breaker: if we had a recent error, don't retry immediately
    if (lastConsentStatusError && lastConsentStatusErrorTime) {
        const timeSinceError = Date.now() - lastConsentStatusErrorTime;
        if (timeSinceError < 5000) { // 5 second cooldown
            console.log('🚫 Recent consent status error, not retrying yet');
            throw lastConsentStatusError;
        }
    }

    try {
        console.log('🔄 Making consent status API call');
        consentStatusCallInProgress = true;

        const response = await axiosInstance.get('/business-customers/me/forms/');

        // Reset error state on success
        lastConsentStatusError = null;
        lastConsentStatusErrorTime = null;

        console.log('✅ Consent status API call successful');
        return response.data;
    } catch (error) {
        console.error('❌ Error checking user consent status:', error);

        // Store error for circuit breaker
        lastConsentStatusError = error;
        lastConsentStatusErrorTime = Date.now();

        // Handle authentication errors - auto logout and redirect (ONLY ONCE)
        if (error.response?.status === 401) {
            if (hasTriggeredLogout) {
                console.log('🚫 401 error but logout already triggered, not redirecting again');
                throw new Error('Authentication expired - logout already triggered');
            }

            console.log('🔒 401 error in consent API - triggering logout (FIRST TIME)');
            hasTriggeredLogout = true;

            // Clear auth data
            localStorage.removeItem('auth_token');
            localStorage.removeItem('user_data');

            // Set a flag to prevent navigation loops
            sessionStorage.setItem('auth_logout_triggered', 'true');

            // Trigger auth store logout
            try {
                const { useAuthStore } = await import('../stores/authStore');
                useAuthStore.getState().logout();
            } catch (importError) {
                console.warn('Could not import auth store for logout');
            }

            // Redirect to login (clean URL for user)
            window.location.href = '/login';

            // Throw error to prevent further processing
            throw new Error('Authentication expired - redirecting to login');
        }

        // For other errors, throw to prevent retries
        throw error;
    } finally {
        consentStatusCallInProgress = false;
    }
};

/**
 * Fallback consent form data (matches current content)
 */
const getFallbackConsentForm = () => ({
    consent_form: {
        id: 'eyelash_procedure_consent',
        title: 'CONSENT FOR EYELASH PROCEDURE',
        version: '2.1',
        service_type: 'eyelash_extensions',
        sections: [
            {
                type: 'introduction',
                content: 'I have agreed to have eyelash extensions applied to and/or removed from my natural eyelashes. Before my licensed eyelash professional can perform this procedure, I understand I agree to be bound by the terms below:'
            },
            {
                type: 'section',
                title: '1. Waiver of Liability',
                content: 'I understand there are risks associated with having artificial eyelash extensions applied to and/or removed from my natural eyelashes, and that notwithstanding the utmost of care in the application or removal of these products, there still exists risks associated with the procedure and product itself, which include, without limitation, eye irritation, eye pain, discomfort, and, in rare cases blindness when improperly handled. As part of this procedure, I understand that a certain amount of eyelash adhesive material will be used to attach artificial eyelashes to my natural eyelashes. Even though the eyelash extension professional may apply or remove the artificial eyelashes properly, I understand adhesive material may become dislodged during or after the procedure, which may irritate my eyes or require follow-up care, at my own expense to prevent damage to my eyes. I also agree to defend, indemnify and hold harmless my service provider from any and all claims, actions, expenses, damages and'
            },
            {
                type: 'section',
                title: '2. Allergies and Reactions',
                content: 'Customers concerned with glue and animal fur allergies need to be aware of this risk. I understand that Clement Lash Lounge will seek to eliminate exposure and risk to me, but agree that Clement Lash Lounge and its stylists assume no liability for adverse reactions for supply materials including glue and eyelashes I may come in contact with while participating in their service. I HEREBY ASSUME ALL OF THE RISKS FOR MYSELF OF PARTICIPATING IN THIS ACTIVITY. I WAIVE, RELEASE, AND DISCHARGE from any and all liability'
            }
        ],
        signature_requirements: {
            signature_required: true,
            signature_label: 'Please sign here *',
            electronic_records_consent: true,
            electronic_records_label: 'I agree to receive and sign documents electronically'
        },
        legal_metadata: {
            created_at: '2024-01-15T10:00:00Z',
            updated_at: '2024-01-20T15:30:00Z',
            version_history: ['1.0', '2.0', '2.1'],
            compliance_standards: ['HIPAA', 'State Cosmetology Board']
        }
    }
});

/**
 * Get a specific form template by ID
 * @param {number} formTemplateId - The form template ID
 * @returns {Promise} Promise resolving to form template data
 */
export const getFormTemplate = async (formTemplateId) => {
    try {
        const response = await fetchAPI(`/forms/templates/${formTemplateId}/`);
        return response;
    } catch (error) {
        console.error('Error fetching form template:', error);
        throw error;
    }
};

export default {
    getConsentForm,
    submitConsentForm,
    checkUserConsentStatus,
    getFormTemplate
};
