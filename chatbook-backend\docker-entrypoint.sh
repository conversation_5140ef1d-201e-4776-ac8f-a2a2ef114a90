#!/bin/bash
set -e

# Function to wait for database (if needed)
wait_for_db() {
    echo "Waiting for database..."
    # Add database connectivity check here if needed
    # For now, just a simple sleep
    sleep 5
}

# Function to run database migrations
run_migrations() {
    echo "Running database migrations..."
    python manage.py migrate --noinput
}

# Function to start API service
start_api() {
    echo "Starting Django API server..."
    wait_for_db
    run_migrations
    
    # Use gunicorn for production or runserver for development
    if [ "${DJANGO_SETTINGS_MODULE}" = "settings.production" ]; then
        exec gunicorn --bind 0.0.0.0:8000 --workers 3 --timeout 120 wsgi:application
    else
        exec python manage.py runserver 0.0.0.0:8000
    fi
}

# Function to start worker service
start_worker() {
    echo "Starting SQS worker..."
    wait_for_db
    
    # SQS worker doesn't need migrations, but ensure DB is available
    exec python manage.py sqs_worker --max-messages=10 --wait-time=20
}

# Function to run a custom Django management command
run_command() {
    echo "Running command: $*"
    wait_for_db
    exec python manage.py "$@"
}

# Main entrypoint logic
case "${1}" in
    api)
        start_api
        ;;
    worker)
        start_worker
        ;;
    migrate)
        wait_for_db
        run_migrations
        ;;
    shell)
        wait_for_db
        exec python manage.py shell
        ;;
    collectstatic)
        exec python manage.py collectstatic --noinput --clear
        ;;
    manage)
        shift
        run_command "$@"
        ;;
    *)
        # If SERVICE_TYPE environment variable is set, use it
        if [ -n "${SERVICE_TYPE}" ]; then
            echo "Using SERVICE_TYPE environment variable: ${SERVICE_TYPE}"
            case "${SERVICE_TYPE}" in
                api)
                    start_api
                    ;;
                worker)
                    start_worker
                    ;;
                *)
                    echo "Unknown SERVICE_TYPE: ${SERVICE_TYPE}"
                    echo "Valid options: api, worker"
                    exit 1
                    ;;
            esac
        else
            # Default behavior or pass through commands
            if [ "$#" -eq 0 ]; then
                echo "No command specified and no SERVICE_TYPE set. Starting API by default."
                start_api
            else
                echo "Executing command: $*"
                exec "$@"
            fi
        fi
        ;;
esac 