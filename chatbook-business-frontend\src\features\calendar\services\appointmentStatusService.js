import { 
  AppointmentStatus, 
  validateStatusTransition, 
  getStatusLabel 
} from '../types'
import { appointmentService } from './appointmentService'

/**
 * AppointmentStatusService - Handles appointment status transitions
 * Based on iOS AppointmentStatusViewModel functionality
 */
export class AppointmentStatusService {
  
  /**
   * Map legacy status keys to Django backend status system (for backwards compatibility)
   */
  static mapLegacyStatus(status) {
    const legacyMapping = {
      // Legacy frontend constants to Django backend
      'IN_PROGRESS': 'service_started',
      'NO_SHOW': 'no_show',
      'COMPLETED': 'completed',
      'CONFIRMED': 'confirmed',
      'PENDING': 'requested',
      'CANCELLED': 'cancelled',
      'ACCEPTED': 'accepted',
      // Old hyphenated names to Django backend
      'in-progress': 'service_started',
      'no-show': 'no_show',
      'pending': 'requested'
    }
    return legacyMapping[status] || status
  }

  /**
   * Map frontend status to Django backend status (now they should be the same)
   */
  static mapToBackendStatus(frontendStatus) {
    // Since frontend now uses Django status names, mostly just handle legacy cases
    const backendMapping = {
      'pending': 'requested',
      'in-progress': 'service_started',
      'no-show': 'no_show'
    }
    return backendMapping[frontendStatus] || frontendStatus
  }

  /**
   * Update appointment status with validation
   */
  static async updateStatus(appointmentId, newStatus, reason = null) {
    try {
      // Get current appointment to validate transition
      const appointments = await appointmentService.fetchAppointments()
      const currentAppointment = appointments.find(apt => apt.id === appointmentId)
      
      if (!currentAppointment) {
        throw new Error('Appointment not found')
      }

      // Map legacy status to our system
      const currentStatus = this.mapLegacyStatus(currentAppointment.status)
      const mappedNewStatus = this.mapLegacyStatus(newStatus)

      // Validate status transition
      const validation = validateStatusTransition(currentStatus, mappedNewStatus)
      if (!validation.isValid) {
        throw new Error(validation.error)
      }

      // Prepare update data
      const updateData = {
        status: mappedNewStatus,
        statusLabel: getStatusLabel(mappedNewStatus)
      }

      // Add reason for cancellations
      if (mappedNewStatus === AppointmentStatus.CANCELLED && reason) {
        updateData.cancellationReason = reason
        updateData.notes = currentAppointment.notes 
          ? `${currentAppointment.notes}\n\nCancellation Reason: ${reason}`
          : `Cancellation Reason: ${reason}`
      }

      // Add timestamp for status changes
      updateData.lastStatusChange = new Date().toISOString()

      // Update appointment
      const updatedAppointment = await appointmentService.updateAppointment(appointmentId, updateData)

      // Log status change
      console.log(`✅ Status updated: ${appointmentId} -> ${getStatusLabel(mappedNewStatus)}`)
      
      // Trigger notifications based on status
      this.triggerStatusNotifications({ ...currentAppointment, ...updatedAppointment }, mappedNewStatus)

      return updatedAppointment
      
    } catch (error) {
      console.error('❌ Failed to update appointment status:', error)
      throw error
    }
  }

  /**
   * Confirm appointment
   */
  static async confirmAppointment(appointmentId) {
    return this.updateStatus(appointmentId, AppointmentStatus.CONFIRMED)
  }

  /**
   * Accept appointment (customer accepts)
   */
  static async acceptAppointment(appointmentId) {
    return this.updateStatus(appointmentId, AppointmentStatus.ACCEPTED)
  }

  /**
   * Check in customer
   */
  static async checkInCustomer(appointmentId) {
    return this.updateStatus(appointmentId, AppointmentStatus.CHECKED_IN)
  }

  /**
   * Start service
   */
  static async startService(appointmentId) {
    return this.updateStatus(appointmentId, AppointmentStatus.SERVICE_STARTED)
  }

  /**
   * Complete appointment
   */
  static async completeAppointment(appointmentId) {
    const updateData = {
      status: AppointmentStatus.COMPLETED,
      statusLabel: getStatusLabel(AppointmentStatus.COMPLETED),
      completedAt: new Date().toISOString()
    }
    
    return appointmentService.updateAppointment(appointmentId, updateData)
  }

  /**
   * Mark appointment as no-show
   */
  static async markNoShow(appointmentId) {
    const updateData = {
      status: AppointmentStatus.NO_SHOW,
      statusLabel: getStatusLabel(AppointmentStatus.NO_SHOW),
      noShowAt: new Date().toISOString()
    }
    
    return appointmentService.updateAppointment(appointmentId, updateData)
  }

  /**
   * Cancel appointment with reason
   */
  static async cancelAppointment(appointmentId, reason) {
    if (!reason || !reason.trim()) {
      throw new Error('Cancellation reason is required')
    }

    console.log('🔧 Cancelling appointment:', { appointmentId, reason })
    
    // Immediately update the appointment status to cancelled for instant UI feedback
    const result = await this.updateStatus(appointmentId, AppointmentStatus.CANCELLED, reason)
    
    console.log('✅ Appointment cancelled successfully:', result)
    
    return result
  }

  /**
   * Bulk status update for multiple appointments
   */
  static async bulkUpdateStatus(appointmentIds, newStatus, reason = null) {
    const results = []
    const errors = []

    for (const appointmentId of appointmentIds) {
      try {
        const result = await this.updateStatus(appointmentId, newStatus, reason)
        results.push(result)
      } catch (error) {
        errors.push({ appointmentId, error: error.message })
      }
    }

    return { results, errors }
  }

  /**
   * Get available status transitions for an appointment
   */
  static getAvailableTransitions(currentStatus) {
    const transitions = {
      [AppointmentStatus.REQUESTED]: [
        { status: AppointmentStatus.CONFIRMED, label: 'Confirm', color: 'blue' },
        { status: AppointmentStatus.CANCELLED, label: 'Cancel', color: 'red' }
      ],
      [AppointmentStatus.CONFIRMED]: [
        { status: AppointmentStatus.ACCEPTED, label: 'Accept', color: 'green' },
        { status: AppointmentStatus.CHECKED_IN, label: 'Check In', color: 'cyan' },
        { status: AppointmentStatus.CANCELLED, label: 'Cancel', color: 'red' },
        { status: AppointmentStatus.NO_SHOW, label: 'No Show', color: 'red' }
      ],
      [AppointmentStatus.ACCEPTED]: [
        { status: AppointmentStatus.CHECKED_IN, label: 'Check In', color: 'cyan' },
        { status: AppointmentStatus.CANCELLED, label: 'Cancel', color: 'red' },
        { status: AppointmentStatus.NO_SHOW, label: 'No Show', color: 'red' }
      ],
      [AppointmentStatus.CHECKED_IN]: [
        { status: AppointmentStatus.SERVICE_STARTED, label: 'Start Service', color: 'yellow' },
        { status: AppointmentStatus.CANCELLED, label: 'Cancel', color: 'red' }
      ],
      [AppointmentStatus.SERVICE_STARTED]: [
        { status: AppointmentStatus.COMPLETED, label: 'Complete', color: 'green' },
        { status: AppointmentStatus.CANCELLED, label: 'Cancel', color: 'red' }
      ],
      [AppointmentStatus.COMPLETED]: [],
      [AppointmentStatus.CANCELLED]: [],
      [AppointmentStatus.NO_SHOW]: []
    }

    return transitions[currentStatus] || []
  }

  /**
   * Trigger notifications based on status change
   */
  static triggerStatusNotifications(appointment, newStatus) {
    // In a real implementation, this would trigger email/SMS notifications
    const notifications = {
      [AppointmentStatus.CONFIRMED]: () => {
        console.log(`📧 Sending confirmation to ${appointment.customerName || appointment.clientName}`)
      },
      [AppointmentStatus.CANCELLED]: () => {
        console.log(`📧 Sending cancellation notice to ${appointment.customerName || appointment.clientName}`)
      },
      [AppointmentStatus.COMPLETED]: () => {
        console.log(`📧 Sending completion notice to ${appointment.customerName || appointment.clientName}`)
      },
      [AppointmentStatus.NO_SHOW]: () => {
        console.log(`📝 Logging no-show for ${appointment.customerName || appointment.clientName}`)
      }
    }

    const notification = notifications[newStatus]
    if (notification) {
      notification()
    }
  }

  /**
   * Get status history for an appointment
   */
  static async getStatusHistory(appointmentId) {
    // In a real implementation, this would fetch status change history from the backend
    // For now, return mock data
    return [
      {
        status: AppointmentStatus.REQUESTED,
        timestamp: new Date(Date.now() - 86400000).toISOString(), // 1 day ago
        changedBy: 'System',
        reason: 'Appointment created'
      },
      {
        status: AppointmentStatus.CONFIRMED,
        timestamp: new Date(Date.now() - 3600000).toISOString(), // 1 hour ago
        changedBy: 'Staff',
        reason: 'Customer confirmed via phone'
      }
    ]
  }

  /**
   * Check if status change requires confirmation
   */
  static requiresConfirmation(currentStatus, newStatus) {
    const confirmationRequired = [
      { from: AppointmentStatus.CONFIRMED, to: AppointmentStatus.CANCELLED },
      { from: AppointmentStatus.ACCEPTED, to: AppointmentStatus.CANCELLED },
      { from: AppointmentStatus.SERVICE_STARTED, to: AppointmentStatus.CANCELLED },
      { from: AppointmentStatus.CONFIRMED, to: AppointmentStatus.NO_SHOW },
      { from: AppointmentStatus.ACCEPTED, to: AppointmentStatus.NO_SHOW }
    ]

    return confirmationRequired.some(rule => 
      rule.from === currentStatus && rule.to === newStatus
    )
  }

  /**
   * Get status statistics for reporting
   */
  static async getStatusStatistics(startDate, endDate) {
    const appointments = await appointmentService.fetchAppointments()
    
    const stats = {
      [AppointmentStatus.REQUESTED]: 0,
      [AppointmentStatus.CONFIRMED]: 0,
      [AppointmentStatus.ACCEPTED]: 0,
      [AppointmentStatus.CHECKED_IN]: 0,
      [AppointmentStatus.SERVICE_STARTED]: 0,
      [AppointmentStatus.COMPLETED]: 0,
      [AppointmentStatus.CANCELLED]: 0,
      [AppointmentStatus.NO_SHOW]: 0
    }

    appointments.forEach(appointment => {
      const aptDate = new Date(appointment.start)
      if (aptDate >= startDate && aptDate <= endDate) {
        stats[appointment.status] = (stats[appointment.status] || 0) + 1
      }
    })

    return stats
  }
}

// Export singleton instance for convenience
export const appointmentStatusService = AppointmentStatusService

export default AppointmentStatusService 