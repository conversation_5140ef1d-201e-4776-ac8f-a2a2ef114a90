from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework import status
from django.core.exceptions import ValidationError
from django.db import IntegrityError, DatabaseError
from .models import DeviceToken
import logging

logger = logging.getLogger(__name__)


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def register_device(request):
    """
    Register a device token for push notifications.
    Based on the django-backend-implementation.md specification.

    Expected payload:
    {
        "device_token": "string"
    }
    """
    try:
        # Validate request data
        if not request.data:
            return Response(
                {'error': 'Request body is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        token = request.data.get('device_token')
        if not token:
            return Response(
                {'error': 'device_token field is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Validate token format (basic validation)
        token = token.strip()
        if len(token) < 10:  # Basic length check
            return Response(
                {'error': 'Invalid device token format'},
                status=status.HTTP_400_BAD_REQUEST
            )

        if len(token) > 200:  # Check max length
            return Response(
                {'error': 'Device token too long (max 200 characters)'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Update or create device token with specific error handling
        try:
            device_token, created = DeviceToken.objects.update_or_create(
                user=request.user,
                token=token,
                defaults={'is_active': True}
            )

            action = 'registered' if created else 'updated'
            logger.info(f"Device token {action} for user {request.user.email}")

            return Response(
                {
                    'detail': f'Device {action} successfully',
                    'device_id': device_token.id,
                    'created': created
                },
                status=status.HTTP_201_CREATED if created else status.HTTP_200_OK
            )

        except IntegrityError as e:
            # Handle unique constraint violations
            logger.error(f"Database integrity error registering device token for user {request.user.email}: {e}")
            return Response(
                {'error': 'Device token already exists for another user'},
                status=status.HTTP_409_CONFLICT
            )

        except DatabaseError as e:
            logger.error(f"Database error registering device token for user {request.user.email}: {e}")
            return Response(
                {'error': 'Database error occurred'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    except ValidationError as e:
        logger.error(f"Validation error registering device token: {e}")
        return Response(
            {'error': 'Invalid data provided'},
            status=status.HTTP_400_BAD_REQUEST
        )

    except Exception as e:
        logger.error(f"Unexpected error registering device token for user {request.user.email}: {e}", exc_info=True)
        return Response(
            {'error': 'An unexpected error occurred'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )


@api_view(['POST'])
@permission_classes([IsAuthenticated])
def deactivate_device(request):
    """
    Deactivate a device token to stop receiving notifications.

    Expected payload:
    {
        "device_token": "string"
    }
    """
    try:
        # Validate request data
        if not request.data:
            return Response(
                {'error': 'Request body is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        token = request.data.get('device_token')
        if not token:
            return Response(
                {'error': 'device_token field is required'},
                status=status.HTTP_400_BAD_REQUEST
            )

        token = token.strip()
        if not token:
            return Response(
                {'error': 'device_token cannot be empty'},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Deactivate the device token with error handling
        try:
            updated = DeviceToken.objects.filter(
                user=request.user,
                token=token
            ).update(is_active=False)

            if updated:
                logger.info(f"Device token deactivated for user {request.user.email}")
                return Response(
                    {'detail': 'Device deactivated successfully'},
                    status=status.HTTP_200_OK
                )
            else:
                logger.warning(f"Device token not found for deactivation: user {request.user.email}")
                return Response(
                    {'error': 'Device token not found for this user'},
                    status=status.HTTP_404_NOT_FOUND
                )

        except DatabaseError as e:
            logger.error(f"Database error deactivating device token for user {request.user.email}: {e}")
            return Response(
                {'error': 'Database error occurred'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    except Exception as e:
        logger.error(f"Unexpected error deactivating device token for user {request.user.email}: {e}", exc_info=True)
        return Response(
            {'error': 'An unexpected error occurred'},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )
