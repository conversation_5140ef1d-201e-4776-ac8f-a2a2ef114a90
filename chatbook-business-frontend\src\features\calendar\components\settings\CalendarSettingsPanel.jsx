import React, { useState, useEffect } from 'react'
import { 
  XMarkIcon, 
  ClockIcon, 
  CalendarDaysIcon, 
  CogIcon
} from '@heroicons/react/24/outline'
import { DAYS_OF_WEEK, workingHoursManager, workingHoursService } from '../../services/workingHoursService'
import { employeeApiService } from '../../index' // Use the re-exported service

// Import modular components
import GeneralSettings from './GeneralSettings'
import TimeDisplaySettings from './TimeDisplaySettings'

/**
 * Working Hours Editor Component
 */
const WorkingHoursEditor = ({ employeeId, onSave, onCancel, employees }) => {
  const [workingHours, setWorkingHours] = useState({})
  const [isLoading, setIsLoading] = useState(true)
  const [isSaving, setIsSaving] = useState(false)
  const [error, setError] = useState(null)

  // Find employee from the fetched employees list (do this first, before any useEffect)
  const employee = employees?.find(emp => emp.id === employeeId)

  useEffect(() => {
    const loadWorkingHours = async () => {
      try {
        setError(null)
        console.log('🔄 Loading working hours for settings panel...')
        
        // ALWAYS fetch fresh data from API, not cache
        // Use the actual employee API service
        // Pass 'me' if this employee can be edited, otherwise the API will return defaults
        const apiEmployeeId = employee?.canEdit ? 'me' : employeeId
        console.log('📡 Fetching fresh working hours from API for:', apiEmployeeId)
        
        const hours = await employeeApiService.getEmployeeWorkingHours(apiEmployeeId)
        console.log('✅ Fresh working hours loaded:', hours)
        
        setWorkingHours(hours)
      } catch (error) {
        console.error('Failed to load working hours:', error)
        setError('Failed to load working hours')
        // Set default working hours if API fails
        const defaultHours = {
          monday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
          tuesday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
          wednesday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
          thursday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
          friday: { isWorking: true, startTime: '09:00', endTime: '17:00' },
          saturday: { isWorking: false, startTime: '09:00', endTime: '17:00' },
          sunday: { isWorking: false, startTime: '09:00', endTime: '17:00' }
        }
        setWorkingHours(defaultHours)
        console.log('⚠️ Using default working hours due to API error')
      } finally {
        setIsLoading(false)
      }
    }

    // Only load working hours if we have employee data
    if (employees && employees.length > 0) {
      loadWorkingHours()
    }
  }, [employeeId, employee?.canEdit, employees])

  const updateDayHours = (day, field, value) => {
    console.log(`🔧 Updating ${day} ${field} to:`, value)
    setWorkingHours(prev => {
      const newHours = {
        ...prev,
        [day]: {
          ...prev[day],
          [field]: value
        }
      }
      console.log(`📊 Updated working hours for ${day}:`, newHours[day])
      return newHours
    })
  }

  const handleSave = async () => {
    setIsSaving(true)
    setError(null)
    try {
      // Check if this is the current user (only they can edit their hours)
      if (employeeId === 'me' || employee?.canEdit) {
        console.log('💾 Saving working hours to API...')
        console.log('📊 Working hours data being saved:')
        Object.entries(workingHours).forEach(([day, hours]) => {
          console.log(`  ${day}:`, {
            isWorking: hours.isWorking,
            startTime: hours.startTime,
            endTime: hours.endTime
          })
        })
        
        // Save to API
        await employeeApiService.updateEmployeeWorkingHours(workingHours, 'me')
        console.log('✅ Working hours saved to API successfully')
        
        // CRITICAL: Update the workingHoursManager cache immediately
        const currentEmployeeId = employee?.id || 'me'
        console.log('🔄 Updating workingHoursManager cache for employee:', currentEmployeeId)
        
        // Update cache with new working hours
        workingHoursManager.setEmployeeWorkingHours(currentEmployeeId, workingHours)
        console.log('✅ Cache updated successfully')
        
        // Also trigger a re-fetch of all working hours to ensure sync
        try {
          console.log('🔄 Triggering working hours refresh...')
          await workingHoursService.fetchAllWorkingHours()
          console.log('✅ Working hours refreshed for all employees')
          
          // Force a small delay to ensure all state updates are processed
          await new Promise(resolve => setTimeout(resolve, 100))
          console.log('🔔 Working hours save process completed, calendar should update now')
        } catch (refreshError) {
          console.warn('⚠️ Failed to refresh all working hours, but save was successful:', refreshError)
        }
        
        onSave()
      } else {
        setError('You can only edit your own working hours')
      }
    } catch (error) {
      console.error('Failed to save working hours:', error)
      setError(error.message || 'Failed to save working hours')
    } finally {
      setIsSaving(false)
    }
  }

  // If employees are not loaded yet or employee not found, show loading
  if (!employees || employees.length === 0 || !employee) {
    return (
      <div className="p-6 text-center">
        <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
        <p className="text-gray-600">Loading employee data...</p>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="p-6 text-center">
        <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
        <p className="text-gray-600">Loading working hours...</p>
      </div>
    )
  }

  return (
    <div className="p-6">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-3">
          <div 
            className="w-10 h-10 rounded-full flex items-center justify-center text-white text-sm font-semibold"
            style={{ backgroundColor: employee?.color || '#3b82f6' }}
          >
            {employee?.avatar || employee?.first_name?.[0] + employee?.last_name?.[0] || 'U'}
          </div>
                      <div>
              <h3 className="text-lg font-semibold text-gray-900">
                {employee?.canEdit ? 'Edit Working Hours' : 'View Working Hours'}
              </h3>
              <p className="text-sm text-gray-600">
                {employee?.name || employee?.full_name || 'Unknown Employee'}
                {!employee?.canEdit && (
                  <span className="ml-2 text-xs bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full">
                    Read Only
                  </span>
                )}
              </p>
            </div>
        </div>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-sm text-red-600">{error}</p>
        </div>
      )}

      {/* Days of week */}
      <div className="space-y-4 mb-6">
        {Object.values(DAYS_OF_WEEK).map(day => {
          const dayHours = workingHours[day.id] || { isWorking: false, startTime: '09:00', endTime: '17:00' }
          const canEdit = employee?.canEdit
          
          return (
            <div key={day.id} className={`flex items-center justify-between p-3 rounded-lg ${
              canEdit ? 'bg-gray-50' : 'bg-gray-100'
            }`}>
              <div className="flex items-center gap-3">
                <input
                  type="checkbox"
                  checked={dayHours.isWorking}
                  onChange={(e) => canEdit && updateDayHours(day.id, 'isWorking', e.target.checked)}
                  disabled={!canEdit}
                  className={`rounded border-gray-300 text-blue-600 focus:ring-blue-500 ${
                    !canEdit ? 'opacity-50 cursor-not-allowed' : ''
                  }`}
                />
                <span className="font-medium text-gray-900 w-20">{day.name}</span>
              </div>
              
              {dayHours.isWorking && (
                <div className="flex items-center gap-2">
                  <input
                    type="time"
                    value={dayHours.startTime}
                    onChange={(e) => canEdit && updateDayHours(day.id, 'startTime', e.target.value)}
                    disabled={!canEdit}
                    className={`text-sm border border-gray-300 rounded px-2 py-1 ${
                      !canEdit ? 'bg-gray-200 cursor-not-allowed' : ''
                    }`}
                  />
                  <span className="text-gray-500">to</span>
                  <input
                    type="time"
                    value={dayHours.endTime}
                    onChange={(e) => canEdit && updateDayHours(day.id, 'endTime', e.target.value)}
                    disabled={!canEdit}
                    className={`text-sm border border-gray-300 rounded px-2 py-1 ${
                      !canEdit ? 'bg-gray-200 cursor-not-allowed' : ''
                    }`}
                  />
                </div>
              )}
            </div>
          )
        })}
      </div>

      {/* Actions */}
      <div className="flex gap-3">
        {employee?.canEdit ? (
          <>
            <button
              onClick={handleSave}
              disabled={isSaving}
              className="flex-1 bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {isSaving ? 'Saving...' : 'Save Changes'}
            </button>
            <button
              onClick={onCancel}
              className="px-4 py-2 text-gray-600 border border-gray-300 rounded-lg font-medium hover:bg-gray-50"
            >
              Cancel
            </button>
          </>
        ) : (
          <button
            onClick={onCancel}
            className="flex-1 px-4 py-2 text-gray-600 border border-gray-300 rounded-lg font-medium hover:bg-gray-50"
          >
            Close
          </button>
        )}
      </div>
    </div>
  )
}

/**
 * Main Calendar Settings Panel
 */
const CalendarSettingsPanel = ({ 
  isOpen, 
  onClose, 
  config, 
  updateConfig,
  selectedEmployees = []
}) => {
  const [activeTab, setActiveTab] = useState('general')
  const [editingWorkingHours, setEditingWorkingHours] = useState(null)
  const [employees, setEmployees] = useState([])
  const [isLoadingEmployees, setIsLoadingEmployees] = useState(true)
  const [employeeError, setEmployeeError] = useState(null)
  const [currentEmployeeId, setCurrentEmployeeId] = useState(null)

  // Fetch employees from API when component mounts
  useEffect(() => {
    const fetchEmployees = async () => {
      try {
        setIsLoadingEmployees(true)
        setEmployeeError(null)
        
        // Fetch both all employees and current user info
        const [employees, currentEmployee] = await Promise.all([
          employeeApiService.getAllEmployees(),
          employeeApiService.getCurrentEmployee().catch(() => null) // Don't fail if current employee fetch fails
        ])
        
        // Set current employee ID
        if (currentEmployee) {
          setCurrentEmployeeId(currentEmployee.id)
        }
        
        // Transform API data to match expected format for display
        const transformedEmployees = employees.map((emp, index) => {
          // Backend returns full_name field, not separate first_name/last_name
          const fullName = emp.full_name || 'Unknown Employee'
          const nameParts = fullName.split(' ')
          const firstName = nameParts[0] || ''
          const lastName = nameParts.slice(1).join(' ') || ''
          
          return {
            id: emp.id,
            name: fullName,
            full_name: fullName,
            first_name: firstName,
            last_name: lastName,
            avatar: (firstName[0] || '') + (lastName[0] || '') || (fullName[0] || '') + (fullName[1] || '') || 'U',
            color: emp.color || ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'][index % 5], // Fallback colors
            email: emp.user_details?.email || emp.email,
            is_active: emp.is_active,
            canEdit: currentEmployee ? emp.id === currentEmployee.id : false // Only current user can edit their hours
          }
        })
        
        setEmployees(transformedEmployees)
      } catch (error) {
        console.error('Failed to fetch employees:', error)
        setEmployeeError('Failed to load employees')
        // Fallback to empty array if API fails
        setEmployees([])
      } finally {
        setIsLoadingEmployees(false)
      }
    }

    if (isOpen) {
      fetchEmployees()
    }
  }, [isOpen])

  if (!isOpen) return null

  const tabs = [
    { id: 'general', label: 'General', icon: CogIcon },
    { id: 'time', label: 'Time & Display', icon: ClockIcon },
    { id: 'working-hours', label: 'Working Hours', icon: CalendarDaysIcon }
  ]

  const renderGeneralSettings = () => (
    <GeneralSettings config={config} updateConfig={updateConfig} />
  )

  const renderTimeDisplaySettings = () => (
    <TimeDisplaySettings config={config} updateConfig={updateConfig} />
  )



  const renderWorkingHours = () => (
    <div className="space-y-4">
      {editingWorkingHours ? (
        <WorkingHoursEditor
          employeeId={editingWorkingHours}
          employees={employees}
          onSave={() => setEditingWorkingHours(null)}
          onCancel={() => setEditingWorkingHours(null)}
        />
      ) : (
        <>
          <p className="text-sm text-gray-600 mb-4">
            Manage working hours for each employee. These hours will be highlighted in the calendar.
          </p>
          
          {currentEmployeeId && (
            <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
              <div className="flex items-start gap-2">
                <div className="text-blue-600 text-sm">ℹ️</div>
                <div className="text-sm text-blue-800">
                  <strong>Note:</strong> You can only edit your own working hours. Other employees need to update their hours themselves.
                </div>
              </div>
            </div>
          )}
          
          {employeeError && (
            <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600">{employeeError}</p>
            </div>
          )}
          
          {isLoadingEmployees ? (
            <div className="text-center py-6">
              <div className="animate-spin w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full mx-auto mb-2"></div>
              <p className="text-gray-600">Loading employees...</p>
            </div>
          ) : employees.length === 0 ? (
            <div className="text-center py-6">
              <p className="text-gray-600">No employees found</p>
            </div>
          ) : (
            <div className="space-y-2">
              {employees.filter(emp => emp.is_active !== false).map(employee => (
                <div key={employee.id} className={`flex items-center justify-between p-3 rounded-lg ${
                  employee.canEdit ? 'bg-blue-50 border border-blue-200' : 'bg-gray-50 border border-gray-200'
                }`}>
                  <div className="flex items-center gap-3">
                    <div 
                      className="w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-semibold"
                      style={{ backgroundColor: employee.color }}
                    >
                      {employee.avatar}
                    </div>
                    <div>
                      <span className="font-medium text-gray-900">{employee.name}</span>
                      {employee.canEdit && (
                        <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full">
                          You
                        </span>
                      )}
                      {!employee.canEdit && (
                        <div className="text-xs text-gray-500 mt-1">
                          Contact employee to edit their hours
                        </div>
                      )}
                    </div>
                  </div>
                  <button
                    onClick={() => setEditingWorkingHours(employee.id)}
                    disabled={!employee.canEdit}
                    className={`text-sm font-medium ${
                      employee.canEdit 
                        ? 'text-blue-600 hover:text-blue-700' 
                        : 'text-gray-400 cursor-not-allowed'
                    }`}
                  >
                    {employee.canEdit ? 'Edit Hours' : 'View Hours'}
                  </button>
                </div>
              ))}
            </div>
          )}
        </>
      )}
    </div>
  )

  const renderTabContent = () => {
    switch (activeTab) {
      case 'general': return renderGeneralSettings()
      case 'time': return renderTimeDisplaySettings()
      case 'working-hours': return renderWorkingHours()
      default: return null
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex">
      {/* Backdrop */}
      <div className="absolute inset-0 bg-black bg-opacity-50" onClick={onClose} />
      
      {/* Panel */}
      <div className="relative bg-white h-full w-full max-w-lg ml-auto shadow-xl flex flex-col">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h2 className="text-xl font-semibold text-gray-900">Calendar Settings</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-gray-100 rounded-lg transition-colors"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Tabs */}
        <div className="border-b border-gray-200">
          <nav className="flex overflow-x-auto">
            {tabs.map(tab => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`flex items-center gap-2 px-4 py-3 text-sm font-medium border-b-2 whitespace-nowrap ${
                  activeTab === tab.id
                    ? 'border-blue-500 text-blue-600'
                    : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                }`}
              >
                <tab.icon className="h-4 w-4" />
                {tab.label}
              </button>
            ))}
          </nav>
        </div>

        {/* Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {renderTabContent()}
        </div>
      </div>
    </div>
  )
}

export default CalendarSettingsPanel 