/* Appointment Admin Styles */

/* Make readonly fields more distinguishable */
input[readonly], select[readonly], textarea[readonly] {
    background-color: #f8f8f8;
    border-color: #ddd;
    color: #666;
    cursor: not-allowed;
}

/* Style for summary fields */
.field-total_duration_display, .field-total_price_display {
    font-weight: bold;
    font-size: 1.1em;
}

/* Style for Django's datetime widgets */
.vDateField, .vTimeField {
    margin-right: 5px;
    padding: 6px 8px;
    border: 1px solid #ccc;
    border-radius: 4px;
    width: 100px;
}

.vTimeField {
    background-image: url('/static/admin/img/icon-clock.svg');
    background-repeat: no-repeat;
    background-position: 95% center;
    background-size: 16px 16px;
    padding-right: 25px;
}

/* Time format hint */
.time-format-hint {
    display: inline-block;
    color: #666;
    font-style: italic;
    font-size: 0.9em;
    margin-left: 10px;
}

/* Style for inline forms */
.inline-group {
    margin-top: 2em;
}

.inline-group h2 {
    background-color: #f5f5f5;
    padding: 0.5em;
    margin-bottom: 1em;
}

/* Ensure inline headers are clear */
.inline-group .tabular thead th {
    background-color: #f8f8f8;
    font-weight: bold;
}

/* Style for readonly fields in the fieldset */
.field-end_time .readonly {
    font-weight: bold;
    color: #333;
}

/* Fix calendar icon positioning */
.datetimeshortcuts {
    display: inline-block;
    margin-left: 5px;
}

/* Error message styling */
.errorlist {
    color: #ba2121;
    font-weight: bold;
    margin-top: 5px;
    padding-left: 0;
    list-style-type: none;
}

/* Service select styling */
.service-select {
    min-width: 200px;
}

/* Add-on select styling */
.addon-select {
    min-width: 180px;
}

/* Field labels */
.form-row label {
    font-weight: bold;
}

/* Required field styling */
.required label, label.required {
    font-weight: bold;
    color: #000;
}

.required input, .required select, .required textarea {
    border-left: 3px solid #d35;
}

/* Alert messages */
.service-hint {
    border-radius: 4px;
    padding: 10px;
    margin: 10px 0;
}

.service-hint.error {
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
}

.service-hint.warning {
    color: #856404;
    background-color: #fff3cd;
    border: 1px solid #ffeeba;
}

.service-hint.info {
    color: #0c5460;
    background-color: #d1ecf1;
    border: 1px solid #bee5eb;
}

/* Table row highlighting */
tr.add-row td {
    background-color: #e6f7ff !important;
    padding-top: 12px;
    padding-bottom: 12px;
}

/* Add a clearer focus style */
input:focus, select:focus, textarea:focus {
    border-color: #2b70b9;
    box-shadow: 0 0 0 2px rgba(43, 112, 185, 0.25);
    outline: none;
} 