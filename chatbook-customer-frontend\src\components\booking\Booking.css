/* Booking Page Styles */
.booking-page {
    padding: 0;
    max-width: 800px;
    margin: 0 auto;
    background-color: #f9f9f9;
}

.booking-content {
    padding: 20px;
}

/* Search Bar Styles */
.booking-search-bar {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    align-items: center;
}

.booking-search-bar .dropdown {
    position: relative;
    flex: 1;
}

.booking-search-bar select,
.booking-search-bar .custom-select,
.booking-search-bar .date-picker-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    font-size: 14px;
    color: #333;
    appearance: none;
    cursor: pointer;
}

.booking-search-bar .dropdown-arrow {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
    color: #666;
    font-size: 12px;
}

/* Custom Select Styles */
.custom-select {
    position: relative;
}

.selected-service-display {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background-color: white;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.custom-dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background-color: white;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dropdown-category {
    border-bottom: 1px solid #eee;
}

.dropdown-category:last-child {
    border-bottom: none;
}

.dropdown-category-name {
    padding: 8px 16px;
    background-color: #f8f9fa;
    font-weight: bold;
    color: #666;
    font-size: 12px;
    text-transform: uppercase;
}

.dropdown-item {
    padding: 12px 16px;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #f0f0f0;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item-price {
    font-weight: bold;
    color: #9c7b65;
}

/* Search Button */
.search-button {
    padding: 12px 24px;
    background-color: #9c7b65;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 500;
    white-space: nowrap;
}

.search-button:hover {
    background-color: #8a6c58;
}

/* Add Service Section */
.add-service-section {
    margin-bottom: 20px;
}

.add-service-btn {
    padding: 8px 16px;
    background-color: transparent;
    color: #9c7b65;
    border: 1px solid #9c7b65;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
}

.add-service-btn:hover {
    background-color: #9c7b65;
    color: white;
}

/* Calendar Section */
.calendar-section {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.calendar-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.calendar-header h3 {
    margin: 0;
    font-size: 18px;
    color: #333;
}

.this-week-label {
    color: #666;
    font-size: 14px;
}

.week-calendar {
    display: flex;
    align-items: center;
    gap: 10px;
}

.week-nav-btn {
    background: none;
    border: none;
    font-size: 18px;
    cursor: pointer;
    padding: 8px;
    color: #666;
    border-radius: 4px;
}

.week-nav-btn:hover:not(:disabled) {
    background-color: #f0f0f0;
}

.week-nav-btn:disabled {
    color: #ccc;
    cursor: not-allowed;
}

.week-days {
    display: flex;
    gap: 8px;
    flex: 1;
    justify-content: center;
}

.day {
    text-align: center;
    padding: 12px 8px;
    border-radius: 4px;
    cursor: pointer;
    min-width: 60px;
    transition: background-color 0.2s;
}

.day:hover:not(.past-date) {
    background-color: #f0f0f0;
}

.day.selected {
    background-color: #9c7b65;
    color: white;
}

.day.today {
    background-color: #9c7b65;
    color: white;
}

.day.past-date {
    color: #ccc;
    cursor: not-allowed;
}

.day-name {
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 4px;
    color: inherit;
}

.day-number {
    font-size: 16px;
    font-weight: bold;
    color: inherit;
}

/* Time Slots Section */
.time-slots-section {
    background-color: white;
    border-radius: 8px;
    padding: 20px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.no-service-message,
.loading-message,
.no-times-message {
    text-align: center;
    padding: 40px 20px;
    color: #666;
}

.no-service-message h3,
.loading-message h3,
.no-times-message h3 {
    margin: 0 0 10px 0;
    color: #333;
}

.no-service-message p,
.loading-message p,
.no-times-message p {
    margin: 0;
    font-size: 14px;
}

.time-slots-container {
    text-align: center;
}

.time-slots-container h3 {
    margin: 0 0 20px 0;
    color: #333;
}

.selected-details {
    text-align: left;
    max-width: 400px;
    margin: 20px auto;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #9c7b65;
}

.selected-details p {
    margin: 5px 0;
    color: #666;
    font-size: 14px;
}

.time-slots-container {
    margin-top: 30px;
}

.employee-slots {
    margin-bottom: 30px;
    padding: 20px;
    background-color: white;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
}

.employee-name {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
    padding-bottom: 10px;
    border-bottom: 2px solid #9c7b65;
}

.time-slots-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
    max-width: 600px;
    margin-left: auto;
    margin-right: auto;
}

.time-slot-btn {
    padding: 12px 16px;
    background-color: #f8f9fa;
    border: 1px solid #ddd;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    color: #333;
    transition: all 0.2s;
    text-align: center;
    min-height: 60px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.time-slot-btn:hover {
    background-color: #9c7b65;
    color: white;
    border-color: #9c7b65;
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(156, 123, 101, 0.3);
}

.time-slot-time {
    font-weight: 600;
    font-size: 15px;
    margin-bottom: 2px;
}

.time-slot-employee {
    font-size: 12px;
    opacity: 0.8;
    font-weight: 400;
}

/* Date Picker Styles */
.react-datepicker-wrapper {
    width: 100%;
}

.react-datepicker__input-container {
    width: 100%;
}

.date-picker-input {
    width: 100% !important;
}

/* Cancellation Policy Styles */
.cancellation-policy {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 20px;
    margin: 20px 0;
    border-left: 4px solid #9c7b65;
}

.cancellation-policy h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 16px;
}

.policy-details {
    margin-bottom: 15px;
}

.policy-summary {
    font-weight: 500;
    color: #333;
    margin-bottom: 10px;
}

.policy-content {
    color: #666;
    font-size: 14px;
    line-height: 1.5;
}

.policy-checkbox {
    margin-top: 15px;
}

.policy-checkbox label {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
}

.policy-checkbox input[type="checkbox"] {
    margin-top: 2px;
}

.policy-loading,
.policy-error {
    padding: 10px;
    text-align: center;
    font-size: 14px;
}

.policy-loading {
    color: #666;
}

.policy-error {
    color: #d32f2f;
    background-color: #ffebee;
    border-radius: 4px;
}

/* Responsive Design */
@media (max-width: 768px) {
    .booking-search-bar {
        flex-direction: column;
        gap: 15px;
    }

    .booking-search-bar .dropdown {
        flex: none;
    }

    .week-calendar {
        flex-direction: column;
        gap: 15px;
    }

    .week-days {
        gap: 4px;
    }

    .day {
        min-width: 45px;
        padding: 8px 4px;
    }

    .time-slot-grid {
        gap: 8px;
    }

    .time-slot-btn {
        padding: 8px 16px;
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .booking-content {
        padding: 15px;
    }

    .calendar-section,
    .time-slots-section {
        padding: 15px;
    }

    .week-days {
        gap: 2px;
    }

    .day {
        min-width: 40px;
        padding: 6px 2px;
    }

    .day-name {
        font-size: 10px;
    }

    .day-number {
        font-size: 14px;
    }
}

/* Review Booking Page Styles */
.review-booking-page {
    min-height: 100vh;
    background-color: #ffffff;
    padding: 20px 0;
}

.review-booking-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.review-booking-content {
    display: grid;
    grid-template-columns: 1fr 350px;
    gap: 0;
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Main Content Area */
.review-booking-main {
    padding: 40px;
    background-color: #ffffff;
}

.business-header {
    margin-bottom: 40px;
}

.business-header h1 {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

/* Review Section Styles */
.review-section {
    margin-bottom: 40px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.review-section:last-child {
    border-bottom: none;
}

.review-section h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 20px;
    font-weight: 600;
}

/* Form Field Styles */
.form-field {
    margin-bottom: 20px;
}

.form-field label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #333;
    font-size: 14px;
}

.form-field input,
.form-field textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    transition: border-color 0.2s ease;
}

.form-field input:focus,
.form-field textarea:focus {
    outline: none;
    border-color: #007bff;
}

.form-field input.error {
    border-color: #dc3545;
}

/* Form Actions */
.form-actions {
    display: flex;
    justify-content: space-between;
    gap: 15px;
    margin-top: 40px;
}

.back-btn,
.next-btn {
    padding: 12px 30px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

.back-btn {
    background-color: #6c757d;
    color: white;
}

.back-btn:hover:not(:disabled) {
    background-color: #5a6268;
}

.next-btn {
    background-color: #28a745;
    color: white;
}

.next-btn:hover:not(:disabled) {
    background-color: #218838;
}

.back-btn:disabled,
.next-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}



/* Right side appointment details panel */
.appointment-details {
    background-color: #f9f9f9;
    border-radius: 8px;
    padding: 25px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    height: fit-content;
    position: sticky;
    top: 20px;
}

.appointment-summary {
    display: flex;
    flex-direction: column;
    gap: 25px;
}

.appointment-header {
    border-bottom: 1px solid #ddd;
    padding-bottom: 20px;
}

.appointment-date-time {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.appointment-date-time .date {
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.appointment-date-time .time {
    font-size: 24px;
    font-weight: 600;
    color: #333;
}

.service-details {
    display: flex;
    flex-direction: column;
    gap: 15px;
    border-bottom: 1px solid #ddd;
    padding-bottom: 20px;
}

.service-provider-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.provider-avatar {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    object-fit: cover;
}

.service-info {
    flex: 1;
}

.provider-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin-bottom: 4px;
}

.service-name {
    font-size: 16px;
    color: #333;
}

.service-price {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.addon-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding-left: 65px; /* Align with service info */
}

.addon-info {
    flex: 1;
}

.addon-name {
    font-size: 16px;
    color: #666;
}

.addon-price {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.pricing-summary {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.total-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-bottom: 15px;
    border-bottom: 1px solid #ddd;
}

.total-label {
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.total-amount {
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

.payment-breakdown {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.due-now-line,
.due-later-line {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.due-now-line {
    gap: 10px;
}

.due-label {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.hold-badge {
    background-color: #e8f4f8;
    color: #2196f3;
    padding: 4px 12px;
    border-radius: 16px;
    font-size: 12px;
    font-weight: 500;
    border: 1px solid #2196f3;
}

.due-amount {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

/* Form fields with better spacing */
.form-field {
    margin-bottom: 20px;
}

.form-field label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.form-field input,
.form-field textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
}

/* Specific styling for the "Who Are You Booking For?" section */
.who-is-booking h3 {
    color: #333;
    font-size: 20px;
    margin-bottom: 15px;
    font-weight: 600;
}

/* Card to hold section styling */
.card-details h3 {
    color: #333;
    font-size: 20px;
    margin-bottom: 15px;
    font-weight: 600;
}

.hold-explanation {
    margin-bottom: 20px;
    color: #666;
    font-size: 14px;
}

/* Credit card section */
.credit-card {
    margin-top: 20px;
}

.credit-card h4 {
    margin-bottom: 15px;
    color: #444;
    font-size: 18px;
    font-weight: 500;
}

/* Billing address section */
.billing-address h4 {
    margin-bottom: 15px;
    color: #444;
    font-size: 18px;
    font-weight: 500;
}

.review-section {
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.review-section:last-child {
    border-bottom: none;
}

.review-section h3 {
    margin-bottom: 20px;
    color: #333;
    font-size: 20px;
    font-weight: 600;
}

/* About appointment section */
.about-appointment textarea {
    width: 100%;
    padding: 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 16px;
    resize: vertical;
    min-height: 100px;
}

/* Appointment details styling */
.appointment-info {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.appointment-date-time {
    text-align: center;
    padding: 15px;
    background-color: #f0f0f0;
    border-radius: 6px;
}

.appointment-date-time .date {
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: #333;
}

.appointment-date-time .time {
    font-size: 16px;
    margin: 0;
    color: #666;
}

.service-provider {
    text-align: center;
    padding: 15px;
}

.provider-image {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 10px;
}

.service-provider p {
    margin: 5px 0;
}

.provider-title {
    color: #666;
    font-size: 14px;
}

.service-list {
    border-top: 1px solid #eee;
    padding-top: 15px;
}

.service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.service-item.main-service {
    font-weight: 600;
}

.service-item.addon-service {
    color: #666;
    font-size: 14px;
}

.service-total {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-top: 1px solid #eee;
    margin-top: 10px;
    font-weight: 600;
    font-size: 18px;
}

/* Button styles */
.book-button {
    background-color: #9c7b65;
    color: white;
    padding: 12px 30px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: background-color 0.3s;
}

.book-button:hover {
    background-color: #8a6c58;
}

.book-button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

.back-button {
    background-color: #e0e0e0;
    color: #333;
    padding: 12px 30px;
    border: none;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    margin-right: 15px;
    transition: background-color 0.3s;
}

.back-button:hover {
    background-color: #d0d0d0;
}

.review-booking-actions {
    display: flex;
    justify-content: flex-start;
    margin-top: 30px;
}

.secure-info {
    display: flex;
    align-items: center;
    color: #888;
    font-size: 14px;
    margin-top: 20px;
}

.secure-info svg {
    margin-right: 8px;
}

.form-row {
    display: flex;
    gap: 15px;
}

.error-message {
    color: #d32f2f;
    font-size: 14px;
    margin-top: 5px;
}

.policy-checkbox {
    margin-top: 20px;
}

.policy-checkbox label {
    display: flex;
    align-items: flex-start;
    font-weight: normal;
}

.policy-checkbox input {
    margin-right: 10px;
    margin-top: 3px;
}

.form-field input.error {
    border-color: #d32f2f;
}

.booking-error {
    color: #d32f2f;
    background-color: #ffebee;
    padding: 10px;
    border-radius: 4px;
    margin: 15px 0;
    text-align: center;
}

/* Mobile Order Summary */
.mobile-order-summary {
    display: none;
}

/* Mobile Back Arrow */
.mobile-back-arrow {
    display: none;
}

.mobile-back-btn {
    background: none;
    border: none;
    padding: 15px 20px;
    cursor: pointer;
    color: #333;
    display: flex;
    align-items: center;
}

.mobile-back-btn:hover {
    background-color: #f5f5f5;
}

.mobile-back-btn svg {
    width: 24px;
    height: 24px;
}

@media (max-width: 768px) {
    .review-booking-page {
        padding: 0;
        min-height: 100vh;
    }

    .review-booking-container {
        padding: 0;
        max-width: none;
    }

    .review-booking-content {
        grid-template-columns: 1fr;
        gap: 0;
        box-shadow: none;
        border-radius: 0;
    }

    .review-booking-main {
        padding: 20px;
    }

    .business-header h1 {
        font-size: 24px;
    }

    .review-section h3 {
        font-size: 18px;
    }

    .form-actions {
        flex-direction: column;
    }

    .back-btn,
    .next-btn {
        width: 100%;
        padding: 15px;
    }

    .form-row {
        flex-direction: column;
        gap: 0;
    }

    .appointment-date-time {
        flex-direction: column;
        gap: 10px;
        text-align: center;
    }

    .service-provider-info {
        flex-direction: column;
        text-align: center;
        gap: 10px;
    }

    .addon-item {
        padding-left: 0;
        justify-content: center;
        text-align: center;
    }

    /* Show mobile components and hide desktop sidebar */
    .mobile-back-arrow {
        display: block;
    }

    .mobile-order-summary {
        display: block;
    }

    .appointment-summary-sidebar {
        display: none;
    }
}
