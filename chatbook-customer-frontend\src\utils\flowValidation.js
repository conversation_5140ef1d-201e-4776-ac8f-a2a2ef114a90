/**
 * Booking Flow Validation Utilities
 * Provides validation functions to test the booking flow in browser
 */

import { useAuthStore } from '../stores/authStore';
import { useBookingStore } from '../stores/bookingStore';
import { canProceedToBookingStep, getBookingRedirectRoute } from './navigationUtils';
import { debugBookingState, setDebugMode } from './debugUtils';

/**
 * Validate current booking flow state
 * @returns {Object} Validation results
 */
export const validateBookingFlow = () => {
  const authState = useAuthStore.getState();
  const bookingState = useBookingStore.getState();
  
  const { user, isAuthenticated } = authState;
  const { bookingData } = bookingState;

  const results = {
    timestamp: new Date().toISOString(),
    authentication: {
      isAuthenticated,
      hasUser: !!user,
      userEmail: user?.email || 'None'
    },
    bookingData: {
      hasService: !!bookingData.selectedService,
      hasEmployee: !!bookingData.selectedEmployee,
      hasDate: !!bookingData.selectedDate,
      hasTime: !!bookingData.selectedTime,
      hasConsent: authState.hasCompletedAllForms(), // Single source of truth from auth store
      hasCustomerInfo: !!(bookingData.customerInfo?.name &&
                          bookingData.customerInfo?.email &&
                          bookingData.customerInfo?.phone)
    },
    flowValidation: {
      canProceedToConsent: canProceedToBookingStep('consent', bookingData, user),
      canProceedToReview: canProceedToBookingStep('review', bookingData, user),
      canProceedToPayment: canProceedToBookingStep('payment', bookingData, user),
      recommendedRoute: getBookingRedirectRoute(bookingData, user)
    },
    issues: []
  };

  // Check for common issues
  if (!results.authentication.isAuthenticated &&
      (results.bookingData.hasService || results.bookingData.hasDate)) {
    results.issues.push('User has booking data but is not authenticated');
  }

  if (results.bookingData.hasService && !results.bookingData.hasEmployee) {
    results.issues.push('Service selected but no employee selected');
  }

  if (results.bookingData.hasDate && !results.bookingData.hasTime) {
    results.issues.push('Date selected but no time selected');
  }

  if (results.flowValidation.canProceedToReview && !results.bookingData.hasConsent) {
    results.issues.push('Can proceed to review but consent not completed (check auth store)');
  }

  // Add consent status details for debugging
  const consentStatus = authState.getConsentStatus();
  if (consentStatus) {
    results.consentDetails = {
      all_forms_completed: consentStatus.all_forms_completed,
      missing_forms: consentStatus.missing_forms || [],
      checked_at: consentStatus.checked_at
    };
  } else {
    results.issues.push('No consent status found in auth store');
  }

  return results;
};

/**
 * Test booking flow with mock data
 * @param {string} scenario - Test scenario ('new-user', 'returning-user', 'incomplete-booking')
 */
export const testBookingFlow = (scenario = 'new-user') => {
  console.group(`🧪 Testing Booking Flow: ${scenario}`);

  const mockScenarios = {
    'new-user': {
      user: { id: 1, email: '<EMAIL>' },
      bookingData: {
        selectedService: { id: 1, name: 'Lash Extensions' },
        selectedEmployee: { id: 1, display_name: 'Test Stylist' },
        selectedDate: '2024-01-15',
        selectedTime: '10:00',
        consentData: { consentAgreed: false },
        customerInfo: { name: '', email: '', phone: '' }
      }
    },
    'returning-user': {
      user: { id: 2, email: '<EMAIL>' },
      bookingData: {
        selectedService: { id: 1, name: 'Lash Extensions' },
        selectedEmployee: { id: 1, display_name: 'Test Stylist' },
        selectedDate: '2024-01-15',
        selectedTime: '10:00',
        consentData: { consentAgreed: true },
        customerInfo: { name: 'Test User', email: '<EMAIL>', phone: '************' }
      }
    },
    'incomplete-booking': {
      user: { id: 3, email: '<EMAIL>' },
      bookingData: {
        selectedService: { id: 1, name: 'Lash Extensions' },
        selectedEmployee: null,
        selectedDate: null,
        selectedTime: null,
        consentData: { consentAgreed: false },
        customerInfo: { name: '', email: '', phone: '' }
      }
    }
  };

  const testData = mockScenarios[scenario];
  if (!testData) {
    console.error('❌ Unknown scenario:', scenario);
    console.groupEnd();
    return;
  }

  // Test navigation logic
  const canConsent = canProceedToBookingStep('consent', testData.bookingData, testData.user);
  const canReview = canProceedToBookingStep('review', testData.bookingData, testData.user);
  const canPayment = canProceedToBookingStep('payment', testData.bookingData, testData.user);
  const redirectRoute = getBookingRedirectRoute(testData.bookingData, testData.user);

  console.log('📊 Test Results:');
  console.log('  Can proceed to consent:', canConsent);
  console.log('  Can proceed to review:', canReview);
  console.log('  Can proceed to payment:', canPayment);
  console.log('  Recommended route:', redirectRoute);

  // Validate expected results
  const expectedResults = {
    'new-user': {
      canConsent: true,
      canReview: false,
      canPayment: false,
      redirectRoute: '/consent-form'
    },
    'returning-user': {
      canConsent: true,
      canReview: true,
      canPayment: false, // Missing customer info
      redirectRoute: '/review-booking'
    },
    'incomplete-booking': {
      canConsent: false,
      canReview: false,
      canPayment: false,
      redirectRoute: '/'
    }
  };

  const expected = expectedResults[scenario];
  const passed = (
    canConsent === expected.canConsent &&
    canReview === expected.canReview &&
    canPayment === expected.canPayment &&
    redirectRoute === expected.redirectRoute
  );

  console.log('✅ Test Result:', passed ? 'PASSED' : 'FAILED');
  
  if (!passed) {
    console.log('❌ Expected:', expected);
    console.log('❌ Actual:', { canConsent, canReview, canPayment, redirectRoute });
  }

  console.groupEnd();
  return passed;
};

/**
 * Run all booking flow tests
 */
export const runAllTests = () => {
  console.group('🧪 Running All Booking Flow Tests');
  
  const scenarios = ['new-user', 'returning-user', 'incomplete-booking'];
  const results = scenarios.map(scenario => ({
    scenario,
    passed: testBookingFlow(scenario)
  }));

  const allPassed = results.every(r => r.passed);
  const passedCount = results.filter(r => r.passed).length;

  console.log(`📊 Test Summary: ${passedCount}/${results.length} tests passed`);
  console.log('✅ Overall Result:', allPassed ? 'ALL TESTS PASSED' : 'SOME TESTS FAILED');

  console.groupEnd();
  return allPassed;
};

/**
 * Setup debugging environment
 */
export const setupDebugEnvironment = () => {
  console.log('🔧 Setting up debug environment...');
  
  // Enable debug mode
  setDebugMode(true);
  
  // Add global functions to window for easy access
  if (typeof window !== 'undefined') {
    window.validateBookingFlow = validateBookingFlow;
    window.testBookingFlow = testBookingFlow;
    window.runAllTests = runAllTests;
    window.debugBookingState = () => {
      const authState = useAuthStore.getState();
      const bookingState = useBookingStore.getState();
      debugBookingState(bookingState.bookingData, authState.user);
    };
    
    console.log('✅ Debug functions available:');
    console.log('  - validateBookingFlow()');
    console.log('  - testBookingFlow(scenario)');
    console.log('  - runAllTests()');
    console.log('  - debugBookingState()');
  }
};

export default {
  validateBookingFlow,
  testBookingFlow,
  runAllTests,
  setupDebugEnvironment
};
