import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { useAuthStore } from './authStore';

const initialBookingData = {
  selectedService: null,
  selectedEmployee: null,
  selectedAddOns: [],
  selectedDate: null,
  selectedTime: null,
  customerInfo: {
    name: '',
    email: '',
    phone: '',
  },
  paymentInfo: {
    cardNumber: '',
    expiryDate: '',
    cvv: '',
    cardholderName: '',
  },
  step: 'service',
  subtotal: 0,
  tax: 0,
  total: 0,
};

const initialState = {
  bookingData: initialBookingData,
  loading: false,
  error: null,
  consentStatus: 'not_signed',
};

export const useBookingStore = create(
  persist(
    (set, get) => ({
      ...initialState,

      // Data update actions
      updateBookingData: (updates) => {
        const currentData = get().bookingData;
        const newData = { ...currentData, ...updates };
        
        set({ bookingData: newData });
        
        // Recalculate totals when relevant data changes
        if (updates.selectedService || updates.selectedAddOns) {
          get().calculateTotals();
        }
      },

      setSelectedService: (service) => {
        get().updateBookingData({ selectedService: service });
      },

      setSelectedEmployee: (employee) => {
        get().updateBookingData({ selectedEmployee: employee });
      },

      setSelectedAddOns: (addOns) => {
        get().updateBookingData({ selectedAddOns: addOns });
      },

      setSelectedDateTime: (date, time) => {
        get().updateBookingData({ 
          selectedDate: date, 
          selectedTime: time 
        });
      },

      setCustomerInfo: (info) => {
        const currentInfo = get().bookingData.customerInfo;
        get().updateBookingData({ 
          customerInfo: { ...currentInfo, ...info } 
        });
      },

      // Consent data is now managed in auth store - this method is deprecated
      setConsentData: (consent) => {
        console.warn('⚠️ setConsentData is deprecated. Consent status is now managed in auth store.');
        // For backward compatibility during transition, we'll do nothing
      },

      setPaymentInfo: (payment) => {
        const currentPayment = get().bookingData.paymentInfo;
        get().updateBookingData({ 
          paymentInfo: { ...currentPayment, ...payment } 
        });
      },

      // Flow control
      setStep: (step) => {
        get().updateBookingData({ step });
      },

      nextStep: () => {
        const currentStep = get().bookingData.step;
        const stepOrder = [
          'service', 'employee', 'datetime', 'consent', 'review', 'payment', 'confirmation'
        ];
        
        const currentIndex = stepOrder.indexOf(currentStep);
        if (currentIndex < stepOrder.length - 1) {
          const nextStep = stepOrder[currentIndex + 1];
          if (get().canProceedToStep(nextStep)) {
            get().setStep(nextStep);
          }
        }
      },

      previousStep: () => {
        const currentStep = get().bookingData.step;
        const stepOrder = [
          'service', 'employee', 'datetime', 'consent', 'review', 'payment', 'confirmation'
        ];
        
        const currentIndex = stepOrder.indexOf(currentStep);
        if (currentIndex > 0) {
          get().setStep(stepOrder[currentIndex - 1]);
        }
      },

      // Validation
      canProceedToStep: (step) => {
        const { bookingData } = get();
        
        switch (step) {
          case 'service':
            return true;
          case 'employee':
            return !!bookingData.selectedService;
          case 'datetime':
            return !!bookingData.selectedService && !!bookingData.selectedEmployee;
          case 'consent':
            return !!bookingData.selectedService && 
                   !!bookingData.selectedEmployee && 
                   !!bookingData.selectedDate && 
                   !!bookingData.selectedTime;
          case 'review':
            // Use auth store for consent status (single source of truth)
            const authStore = useAuthStore.getState();
            return !!bookingData.selectedService &&
                   !!bookingData.selectedEmployee &&
                   !!bookingData.selectedDate &&
                   !!bookingData.selectedTime &&
                   authStore.hasCompletedAllForms();
          case 'payment':
            return get().canProceedToStep('review') && 
                   !!bookingData.customerInfo.name &&
                   !!bookingData.customerInfo.email &&
                   !!bookingData.customerInfo.phone;
          case 'confirmation':
            return get().canProceedToStep('payment') &&
                   !!bookingData.paymentInfo.cardNumber &&
                   !!bookingData.paymentInfo.expiryDate &&
                   !!bookingData.paymentInfo.cvv &&
                   !!bookingData.paymentInfo.cardholderName;
          default:
            return false;
        }
      },

      // State management
      setLoading: (loading) => {
        set({ loading });
      },

      setError: (error) => {
        set({ error });
      },

      clearError: () => {
        set({ error: null });
      },

      clearBookingData: () => {
        set({ 
          bookingData: initialBookingData,
          error: null,
          consentStatus: 'not_signed'
        });
      },

      // Consent management
      checkConsentStatus: async () => {
        const authStore = useAuthStore.getState();
        
        if (!authStore.isAuthenticated || !authStore.user) {
          set({ consentStatus: 'not_signed' });
          return 'not_signed';
        }

        try {
          set({ consentStatus: 'checking' });
          
          // Check stored consent status first
          const storedStatus = authStore.consentStatus;
          if (storedStatus !== null) {
            const status = storedStatus.all_forms_completed ? 'signed' : 'not_signed';
            set({ consentStatus: status });
            return status;
          }

          // If no stored status, would check with backend here
          // For now, default to not_signed
          set({ consentStatus: 'not_signed' });
          return 'not_signed';
        } catch (error) {
          console.error('Failed to check consent status:', error);
          set({ consentStatus: 'not_signed' });
          return 'not_signed';
        }
      },

      setConsentStatus: (status) => {
        set({ consentStatus: status });
      },

      // Calculations
      calculateTotals: () => {
        const { bookingData } = get();
        const { selectedService, selectedAddOns } = bookingData;
        
        let subtotal = 0;
        
        if (selectedService) {
          subtotal += parseFloat(selectedService.price);
        }
        
        selectedAddOns.forEach(addOn => {
          subtotal += parseFloat(addOn.price);
        });
        
        const tax = subtotal * 0.08; // 8% tax rate
        const total = subtotal + tax;
        
        get().updateBookingData({
          subtotal: Math.round(subtotal * 100) / 100,
          tax: Math.round(tax * 100) / 100,
          total: Math.round(total * 100) / 100,
        });
      },
    }),
    {
      name: 'booking-store',
      // Persist booking data but not loading/error states
      partialize: (state) => ({
        bookingData: state.bookingData,
        consentStatus: state.consentStatus,
      }),
    }
  )
);
