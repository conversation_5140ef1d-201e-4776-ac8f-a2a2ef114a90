from django import forms
from django.core.exceptions import ValidationError
from django.utils.translation import gettext_lazy as _
from phonenumber_field.formfields import PhoneNumberField

from .models import Business

class BusinessRegistrationForm(forms.Form):
    """
    Form for self-service business registration that creates both
    a business and the admin user in one step.
    """
    # Business fields
    business_name = forms.CharField(
        max_length=255,
        label=_('Business Name'),
        widget=forms.TextInput(attrs={'placeholder': 'Enter your business name'})
    )
    business_description = forms.CharField(
        widget=forms.Textarea(attrs={'rows': 3, 'placeholder': 'Describe your business'}),
        required=False,
        label=_('Business Description')
    )
    business_phone = forms.CharField(
        max_length=20,
        required=False,
        label=_('Business Phone'),
        widget=forms.TextInput(attrs={'placeholder': '+****************'})
    )
    business_email = forms.EmailField(
        required=False,
        label=_('Business Email'),
        widget=forms.EmailInput(attrs={'placeholder': '<EMAIL>'})
    )
    business_website = forms.URLField(
        required=False,
        label=_('Business Website'),
        widget=forms.URLInput(attrs={'placeholder': 'https://www.yourbusiness.com'})
    )
    
    # User fields
    first_name = forms.CharField(
        max_length=30,
        label=_('First Name'),
        widget=forms.TextInput(attrs={'placeholder': 'Your first name'})
    )
    last_name = forms.CharField(
        max_length=30,
        label=_('Last Name'),
        widget=forms.TextInput(attrs={'placeholder': 'Your last name'})
    )
    email = forms.EmailField(
        label=_('Email Address'),
        widget=forms.EmailInput(attrs={'placeholder': 'Your email address'})
    )
    phone_number = PhoneNumberField(
        label=_('Phone Number'),
        widget=forms.TextInput(attrs={'placeholder': '+****************'})
    )
    password = forms.CharField(
        label=_('Password'),
        widget=forms.PasswordInput(attrs={'placeholder': 'Create a password'})
    )
    password_confirm = forms.CharField(
        label=_('Confirm Password'),
        widget=forms.PasswordInput(attrs={'placeholder': 'Confirm your password'})
    )
    
    # Terms acceptance
    terms_accepted = forms.BooleanField(
        label=_('I accept the Terms of Service and Privacy Policy'),
        required=True
    )
    
    def clean(self):
        cleaned_data = super().clean()
        password = cleaned_data.get('password')
        password_confirm = cleaned_data.get('password_confirm')
        
        if password and password_confirm and password != password_confirm:
            raise ValidationError(_('Passwords do not match.'))
        
        return cleaned_data
    
    def get_business_data(self):
        """
        Return formatted business data for model creation
        """
        return {
            'name': self.cleaned_data['business_name'],
            'description': self.cleaned_data['business_description'],
            'phone': self.cleaned_data['business_phone'],
            'email': self.cleaned_data['business_email'],
            'website': self.cleaned_data['business_website'],
        }
    
    def get_user_data(self):
        """
        Return formatted user data for model creation
        """
        return {
            'email': self.cleaned_data['email'],
            'phone_number': self.cleaned_data['phone_number'],
            'password': self.cleaned_data['password'],
            'first_name': self.cleaned_data['first_name'],
            'last_name': self.cleaned_data['last_name'],
        }
    
    def save(self):
        """
        Create the business and admin user
        """
        business_data = self.get_business_data()
        user_data = self.get_user_data()
        
        # Use the email as the identifier
        identifier = user_data['email']
        
        # Use the Business model's create_with_admin method
        return Business.create_with_admin(
            business_data=business_data,
            user_data=user_data,
            identifier=identifier
        ) 