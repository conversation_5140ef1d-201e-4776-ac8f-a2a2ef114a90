[project]
name = "chatbook-backend"
version = "0.1.0"
description = "A Django-based chat application"
authors = [
    {name = "Your Name",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.10,<4.0"
dependencies = [
    "django (>=5.2,<6.0)",
    "djangorestframework (>=3.16.0,<4.0.0)",
    "psycopg2-binary (>=2.9.10,<3.0.0)",
    "django-allauth (>=65.7.0,<66.0.0)",
    "djangorestframework-simplejwt (>=5.5.0,<6.0.0)",
    "stripe (>=12.0.0,<13.0.0)",
    "django-environ (>=0.12.0,<0.13.0)",
    "celery (>=5.5.0,<6.0.0)",
    "twilio (>=9.5.1,<10.0.0)",
    "pillow (>=11.1.0,<12.0.0)",
    "django-cors-headers (>=4.7.0,<5.0.0)",
    "cryptography (>=44.0.2,<45.0.0)",
    "django-soft-delete (>=1.0.19,<2.0.0)",
    "python-dotenv (>=1.1.1,<2.0.0)",
    "django-jazzmin (>=3.0.1,<4.0.0)",
    "django-filter (>=25.1,<26.0)",
    "django-otp (>=1.6.0,<2.0.0)",
    "django-axes (>=8.0.0,<9.0.0)",
    "django-storages (>=1.14.6,<2.0.0)",
    "django-phonenumber-field (>=8.1.0,<9.0.0)",
    "phonenumbers (>=9.0.8,<10.0.0)",
    "django-timezone-field (>=7.1,<8.0)",
    "pyotp (>=2.9.0,<3.0.0)",
    "django-ipware (>=7.0.1,<8.0.0)",
    "boto3 (>=1.39.3,<2.0.0)",
    "pandas (>=2.3.0,<3.0.0)",
    "django-redis (>=6.0.0,<7.0.0)"
]


[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
