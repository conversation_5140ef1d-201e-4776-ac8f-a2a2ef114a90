import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { authService } from '../features/auth/services/authApi';
import { storage } from '../utils/storage';

const initialState = {
  user: null,
  isAuthenticated: false,
  loading: false,
  error: null,
  consentStatus: null, // { all_forms_completed: boolean, missing_forms: [], checked_at: timestamp }
};

export const useAuthStore = create(
  persist(
    (set, get) => ({
      ...initialState,

      // Initialize auth state on app startup
      initializeAuth: async () => {
        try {
          set({ loading: true, error: null });
          
          // Clear any expired tokens first
          storage.cleanup.clearExpiredData();

          const token = storage.auth.getToken();
          const storedUser = storage.auth.getUser();

          if (token && storedUser) {
            console.log('✅ Found stored auth data, setting user');
            set({ 
              user: storedUser, 
              isAuthenticated: true,
              loading: false 
            });

            // Validate token in background
            try {
              const currentUser = await authService.getCurrentUser();
              console.log('✅ Token validation successful, updating user data');
              set({ user: currentUser });
            } catch (error) {
              console.warn('⚠️ Token validation failed:', error.message);
              // Only clear if it's a definitive auth error
              if (error.response?.status === 401 || error.response?.status === 403) {
                console.log('🔒 Definitive auth error, clearing session');
                await get().logout();
              }
            }
          } else {
            console.log('🔍 No stored auth data found');
            set({ loading: false });
          }
        } catch (error) {
          console.error('Auth initialization error:', error);
          set({ error: error.message, loading: false });
        }
      },

      // Login action
      login: async (credentials) => {
        try {
          set({ loading: true, error: null });
          const response = await authService.login(credentials);

          // Handle both old format and new format with consent status
          const user = response.user || response;
          const consentStatus = response.consentStatus || null;

          set({
            user,
            isAuthenticated: true,
            loading: false,
            consentStatus: consentStatus ? {
              ...consentStatus,
              checked_at: new Date().toISOString()
            } : null
          });

          console.log('✅ Auth store updated with user and consent status:', {
            user: user?.email,
            consentStatus: consentStatus
          });

          return { success: true };
        } catch (error) {
          console.error('Login error:', error);
          set({
            error: error.message,
            loading: false,
            user: null,
            isAuthenticated: false,
            consentStatus: null
          });
          return { success: false, error: error.message };
        }
      },

      // Register action
      register: async (data) => {
        try {
          set({ loading: true, error: null });
          const { access, user } = await authService.register(data);
          
          set({ 
            user, 
            isAuthenticated: true, 
            loading: false 
          });
          
          return { success: true };
        } catch (error) {
          console.error('Register error:', error);
          set({ error: error.message, loading: false });
          return { success: false, error: error.message };
        }
      },

      // Social login action
      socialLogin: async (provider, token) => {
        try {
          set({ loading: true, error: null });
          const { access, user } = await authService.socialLogin(provider, token);
          
          set({ 
            user, 
            isAuthenticated: true, 
            loading: false 
          });
          
          return { success: true };
        } catch (error) {
          console.error('Social login error:', error);
          set({ error: error.message, loading: false });
          return { success: false, error: error.message };
        }
      },

      // Logout action
      logout: async () => {
        const currentUser = get().user;
        console.log('🚪 Starting logout process...');

        try {
          await authService.logout();
          console.log('✅ Logout API calls completed successfully');
        } catch (error) {
          console.error('❌ Logout API calls failed:', error);

          // Log detailed error information if available
          if (error.details && Array.isArray(error.details)) {
            error.details.forEach(({ type, error: detailError }) => {
              console.error(`❌ ${type} error:`, detailError.message || detailError);
            });
          }

          // Continue with local cleanup even if API calls failed
          console.log('🧹 Continuing with local cleanup despite API errors');
        } finally {
          // Clear user-specific data
          if (currentUser?.id) {
            console.log(`🧹 Clearing data for user ${currentUser.id}`);
            storage.cleanup.clearUserData(currentUser.id);
          } else {
            console.log('🧹 Clearing general auth data');
            storage.auth.clearAuth();
          }

          // Always clear local state
          set({
            ...initialState,
            loading: false
          });

          console.log('✅ Logout process completed - local state cleared');
        }
      },

      // Utility actions
      setUser: (user) => {
        set({ 
          user, 
          isAuthenticated: !!user 
        });
      },

      setLoading: (loading) => {
        set({ loading });
      },

      setError: (error) => {
        set({ error });
      },

      clearError: () => {
        set({ error: null });
      },

      // Update consent status (called after API check)
      updateConsentStatus: (status) => {
        set({
          consentStatus: {
            ...status,
            checked_at: new Date().toISOString()
          }
        });
      },

      // Get current consent status (single source of truth)
      getConsentStatus: () => {
        return get().consentStatus;
      },

      // Check if user has completed all required forms
      hasCompletedAllForms: () => {
        const status = get().consentStatus;
        return status?.all_forms_completed === true;
      },
    }),
    {
      name: 'auth-store',
      // Only persist essential auth state
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
        consentStatus: state.consentStatus,
      }),
    }
  )
);
