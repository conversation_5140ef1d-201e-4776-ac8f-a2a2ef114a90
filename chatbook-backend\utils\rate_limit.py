from django.conf import settings
from django.core.cache import cache
from django.core.exceptions import ValidationError
from datetime import datetime, timedelta
import hashlib

class RateLimiter:
    """
    Rate limiter class that uses Redis as a backend.
    Supports multiple rate limit windows and custom key generation.
    """
    def __init__(self, key_prefix, limit=100, window=3600):
        """
        Initialize rate limiter
        :param key_prefix: Prefix for the rate limit key
        :param limit: Number of requests allowed in the window
        :param window: Time window in seconds
        """
        self.key_prefix = key_prefix
        self.limit = limit
        self.window = window

    def _generate_key(self, identifier):
        """
        Generate a unique key for the rate limit
        :param identifier: Unique identifier (e.g. IP address, user ID)
        :return: Hashed key
        """
        key = f"{self.key_prefix}:{identifier}"
        return hashlib.sha256(key.encode()).hexdigest()

    def is_allowed(self, identifier):
        """
        Check if the request is allowed
        :param identifier: Unique identifier
        :return: (bool, dict) - (is_allowed, rate_limit_info)
        """
        key = self._generate_key(identifier)
        now = datetime.now()
        
        # Get current count and window start time
        pipe = cache.client.pipeline()
        pipe.get(f"{key}:count")
        pipe.get(f"{key}:window_start")
        count, window_start = pipe.execute()

        # Convert to proper types
        count = int(count) if count else 0
        window_start = float(window_start) if window_start else now.timestamp()

        # Check if window has expired
        window_end = datetime.fromtimestamp(window_start) + timedelta(seconds=self.window)
        if now > window_end:
            # Reset window
            window_start = now.timestamp()
            count = 0

        # Increment count
        count += 1
        
        # Update values in Redis
        pipe = cache.client.pipeline()
        pipe.setex(f"{key}:count", self.window, count)
        pipe.setex(f"{key}:window_start", self.window, window_start)
        pipe.execute()

        is_allowed = count <= self.limit
        reset_time = datetime.fromtimestamp(window_start) + timedelta(seconds=self.window)

        rate_limit_info = {
            'limit': self.limit,
            'remaining': max(0, self.limit - count),
            'reset': int(reset_time.timestamp()),
            'window': self.window
        }

        return is_allowed, rate_limit_info

    def get_rate_limit_response_headers(self, rate_limit_info):
        """
        Get rate limit headers for HTTP response
        :param rate_limit_info: Rate limit information dictionary
        :return: Dictionary of headers
        """
        return {
            'X-RateLimit-Limit': str(rate_limit_info['limit']),
            'X-RateLimit-Remaining': str(rate_limit_info['remaining']),
            'X-RateLimit-Reset': str(rate_limit_info['reset']),
            'X-RateLimit-Window': str(rate_limit_info['window'])
        }

class LoginRateLimiter(RateLimiter):
    """
    Rate limiter specifically for login attempts
    """
    def __init__(self):
        super().__init__(
            key_prefix='login',
            limit=getattr(settings, 'LOGIN_ATTEMPT_LIMIT', 5),
            window=getattr(settings, 'LOGIN_ATTEMPT_WINDOW', 300)  # 5 minutes
        )

    def check_login_attempt(self, identifier):
        """
        Check if login attempt is allowed
        :param identifier: IP address or user identifier
        :raises: ValidationError if rate limit exceeded
        """
        is_allowed, rate_limit_info = self.is_allowed(identifier)
        if not is_allowed:
            reset_time = datetime.fromtimestamp(rate_limit_info['reset'])
            wait_time = (reset_time - datetime.now()).total_seconds()
            raise ValidationError(
                f"Too many login attempts. Please try again in {int(wait_time)} seconds."
            )
        return rate_limit_info

class OTPRateLimiter(RateLimiter):
    """
    Rate limiter for OTP verification attempts
    """
    def __init__(self):
        super().__init__(
            key_prefix='otp',
            limit=getattr(settings, 'OTP_ATTEMPT_LIMIT', 3),
            window=getattr(settings, 'OTP_ATTEMPT_WINDOW', 300)  # 5 minutes
        )

    def check_otp_attempt(self, identifier):
        """
        Check if OTP verification attempt is allowed
        :param identifier: User identifier
        :raises: ValidationError if rate limit exceeded
        """
        is_allowed, rate_limit_info = self.is_allowed(identifier)
        if not is_allowed:
            reset_time = datetime.fromtimestamp(rate_limit_info['reset'])
            wait_time = (reset_time - datetime.now()).total_seconds()
            raise ValidationError(
                f"Too many OTP verification attempts. Please try again in {int(wait_time)} seconds."
            )
        return rate_limit_info 