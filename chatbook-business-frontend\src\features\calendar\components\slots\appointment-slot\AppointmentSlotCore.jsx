import React, { useState, useEffect } from 'react'
import { cn } from '../../../../../utils'

// Import all the modular components
import { useBufferTimeCalculations } from './BufferTimeCalculations'
import { useDragBehaviors } from './DragBehaviors'
import { useResizeBehaviors } from './ResizeBehaviors'
import { usePositionCalculations } from './PositionCalculations'
import { useOverlapCalculations } from './OverlapCalculations'
import { useStylingFunctions } from './StylingFunctions'
import { AppointmentZones } from './AppointmentZones'
import AppointmentClickOverlay from './AppointmentClickOverlay'

/**
 * AppointmentSlotCore - Main appointment display component
 * Enhanced for multi-employee support and drag movement with smart appointment border snapping
 */
const AppointmentSlotCore = ({
  appointment,
  config,
  slotHour,
  slotMinute,
  onClick,
  onDragStart,
  onDragMove,
  onDragEnd,
  onResizeStart,
  onResizeMove,
  onResizeEnd,
  onStatusUpdate,
  onEditAppointment,
  onMoveAppointment,
  scrollContainerRef,

  onMouseEnter,
  onMouseLeave,
  className = '',
  employeeId,
  displayMode,
  weekDays = [],
  selectedEmployees = [],
  allAppointments = [],
  currentDate = new Date(),
  isShowingConfirmation = false,
  draggedAppointmentId = null,
  globalDragOffset = { x: 0, y: 0 },
  isShowingResizeConfirmation = false,
  resizedAppointmentId = null,
  globalResizeOffset = { x: 0, y: 0 },
  isUpdatingStatus = false
}) => {
  // Early return if appointment is undefined
  if (!appointment) {
    console.warn('AppointmentSlotCore: appointment prop is undefined')
    return null
  }

  // Initialize hover state (still needed for resize handles)
  const [isHovered, setIsHovered] = useState(false)
  
  // Handle hover state changes (still needed for resize handles)
  const handleMouseEnter = (event) => {
    setIsHovered(true)
  }

  const handleMouseLeave = () => {
    setIsHovered(false)
  }

  // Initialize all the modular behaviors
  const { appointmentHeight, calculateBufferTimeLayout } = useBufferTimeCalculations(appointment, config)
  
  const { calculatePosition } = usePositionCalculations(appointment, config, slotHour, slotMinute)
  
  const { calculateOverlap } = useOverlapCalculations(appointment, allAppointments, employeeId, currentDate, displayMode)
  
  // CRITICAL: Calculate edge case information for this appointment
  const appointmentStartTime = new Date(appointment.start)
  const appointmentMinute = appointmentStartTime.getMinutes()
  const isExactlyOnGridLine = appointmentMinute % config.timeResolution === 0
  const isEdgeCaseMode = !isExactlyOnGridLine
  
  const edgeCaseInfo = isEdgeCaseMode ? {
    appointmentTime: `${appointmentStartTime.getHours()}:${appointmentMinute.toString().padStart(2, '0')}`,
    slotTime: `${appointmentStartTime.getHours()}:${Math.floor(appointmentMinute / config.timeResolution) * config.timeResolution}`,
    minuteOffset: appointmentMinute - (Math.floor(appointmentMinute / config.timeResolution) * config.timeResolution),
    timeResolution: config.timeResolution,
    detectedAt: 'AppointmentSlotCore',
    reason: 'Appointment starts between time grid lines - will bypass ALL snapping'
  } : null
  

  
  const {
    dragState,
    dragRef,
    isPressed: dragIsPressed,
    isGloballyDragged,
    handleMouseDown,
    setIsPressed: setDragIsPressed,
    showGhostCopy,
    originalSlotPosition
  } = useDragBehaviors(
    appointment,
    config,
    onDragStart,
    onDragMove,
    onDragEnd,
    allAppointments,
    currentDate,
    displayMode,
    weekDays,
    selectedEmployees,
    draggedAppointmentId,
    globalDragOffset,
    isShowingConfirmation,
    scrollContainerRef,
    isEdgeCaseMode, // Pass edge case mode
    edgeCaseInfo // Pass edge case details
  )

  const {
    resizeState,
    resizeTopRef,
    resizeBottomRef,
    isPressed: resizeIsPressed,
    isGloballyResized,
    handleResizeMouseDown,
    calculateDynamicHeight,
    setIsPressed: setResizeIsPressed
  } = useResizeBehaviors(
    appointment,
    config,
    onResizeStart,
    onResizeMove,
    onResizeEnd,
    appointmentHeight,
    resizedAppointmentId,
    globalResizeOffset,
    isShowingResizeConfirmation
  )

  const {
    getServiceZoneClasses,
    getBufferZoneClasses,
    getAppointmentStyling,
    getContentClasses,
    getIndicators
  } = useStylingFunctions(
    appointment,
    appointmentHeight,
    dragState,
    resizeState,
    isGloballyDragged,
    isGloballyResized,
    globalDragOffset,
    globalResizeOffset,
    isHovered
  )

  // Calculate dynamic height and buffer layout
  const dynamicHeight = calculateDynamicHeight(appointmentHeight)
  const isTransforming = resizeState.isResizing || resizeState.wasResized || isGloballyResized || 
                         dragState.isDragging || dragState.wasDragged || isGloballyDragged
  const bufferLayout = calculateBufferTimeLayout(dynamicHeight, isTransforming)

  // Note: No longer need to hide overlay on drag/resize since it's click-based now

  // Calculate overlap info
  const overlapInfo = calculateOverlap()
  
  // Get position - don't render if this isn't the starting slot
  const position = calculatePosition(overlapInfo)
  if (!position) {
    return null
  }

  // Don't render if this appointment doesn't belong to this employee column
  if (employeeId && appointment.employeeId !== employeeId) {
    return null
  }

  // Get styling and indicators
  const appointmentStyling = getAppointmentStyling()
  const contentClasses = getContentClasses()
  const { statusIndicator, dragIndicator } = getIndicators(employeeId)

  // Combined pressed state
  const isPressed = dragIsPressed || resizeIsPressed

  // Handle click events
  const handleClick = (e) => {
    if (!dragState.isDragging && !dragState.wasDragged && !resizeState.isResizing && !resizeState.wasResized) {
      // Don't stop propagation here - let the parent TimeSlot handle it
      onClick?.(appointment, e)
    }
  }



  // Render ghost copy at original position when dragging
  const renderGhostCopy = () => {
    if (!showGhostCopy || !originalSlotPosition) return null
    
    const ghostStyling = getAppointmentStyling(true) // true = isGhostCopy
    
    return (
      <div
        className={cn(
          ghostStyling.className,
          className,
          "appointment-slot",
          "appointment-ghost-copy"
        )}
        style={{ 
          ...originalSlotPosition, // Use original position
          height: `${dynamicHeight}px`,
          ...ghostStyling.style,
          pointerEvents: 'none', // Ghost copy should not intercept clicks
          zIndex: 5 // Lower z-index than dragging appointment
        }}
        data-appointment-id={`${appointment.id}-ghost`}
        data-is-ghost="true"
      >
        <AppointmentZones
          appointment={appointment}
          bufferLayout={bufferLayout}
          getServiceZoneClasses={getServiceZoneClasses}
          getBufferZoneClasses={getBufferZoneClasses}
          contentClasses={contentClasses}
          statusIndicator={statusIndicator}
          dragIndicator={dragIndicator}
          isHovered={false} // Ghost copy should not appear hovered
          dragState={{ ...dragState, isDragging: false }} // Ghost copy should not appear as dragging
          resizeState={{ ...resizeState, isResizing: false }}
          appointmentHeight={appointmentHeight}
          resizeTopRef={null} // Ghost copy should not have resize handles
          resizeBottomRef={null}
          handleResizeMouseDown={() => {}} // No-op for ghost copy
        />
      </div>
    )
  }

  return (
    <>
      {/* Render ghost copy first (lower z-index) */}
      {renderGhostCopy()}
      
      {/* Render main appointment */}
      <div
        ref={dragRef}
        className={cn(
          appointmentStyling.className,
          className,
          "appointment-slot"
        )}
        style={{ 
          ...position,
          height: `${dynamicHeight}px`,
          ...appointmentStyling.style
        }}
        onClick={handleClick}
        onMouseDown={handleMouseDown}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        data-appointment-id={appointment.id}
        data-employee-id={appointment.employeeId}
        data-service-category={appointment.serviceCategory}
        data-appointment-type={appointment.appointmentType}
        data-status={appointment.status}
        data-draggable="true"
        data-is-original="true"
      >
        <AppointmentZones
          appointment={appointment}
          bufferLayout={bufferLayout}
          getServiceZoneClasses={getServiceZoneClasses}
          getBufferZoneClasses={getBufferZoneClasses}
          contentClasses={contentClasses}
          statusIndicator={statusIndicator}
          dragIndicator={dragIndicator}
          isHovered={isHovered}
          dragState={dragState}
          resizeState={resizeState}
          appointmentHeight={appointmentHeight}
          resizeTopRef={resizeTopRef}
          resizeBottomRef={resizeBottomRef}
          handleResizeMouseDown={handleResizeMouseDown}
        />
      </div>
    </>
  )
}

export default AppointmentSlotCore 