from django.test import TestCase
from django.contrib.auth import get_user_model
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from unittest.mock import patch, MagicMock
from .models import DeviceToken
from .services import NotificationService, notify_user

User = get_user_model()


class DeviceTokenModelTest(TestCase):
    """Test cases for DeviceToken model"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            identifier='<EMAIL>',
            phone_number='+1234567890',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )
    
    def test_device_token_creation(self):
        """Test creating a device token"""
        token = DeviceToken.objects.create(
            user=self.user,
            token='test_device_token_123'
        )
        
        self.assertEqual(token.user, self.user)
        self.assertEqual(token.token, 'test_device_token_123')
        self.assertTrue(token.is_active)
        self.assertIsNotNone(token.created_at)
        self.assertIsNotNone(token.last_active)
    
    def test_device_token_str_representation(self):
        """Test string representation of device token"""
        token = DeviceToken.objects.create(
            user=self.user,
            token='test_device_token_123'
        )

        expected = f"{self.user.email} - test_device_token_12..."
        self.assertEqual(str(token), expected)
    
    def test_device_token_unique_constraint(self):
        """Test that device tokens must be unique"""
        DeviceToken.objects.create(
            user=self.user,
            token='unique_token'
        )
        
        # Create another user
        user2 = User.objects.create_user(
            identifier='<EMAIL>',
            phone_number='+1234567891',
            first_name='Test2',
            last_name='User2',
            password='testpass123'
        )
        
        # Should raise IntegrityError when trying to create duplicate token
        with self.assertRaises(Exception):
            DeviceToken.objects.create(
                user=user2,
                token='unique_token'
            )


class NotificationServiceTest(TestCase):
    """Test cases for NotificationService"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            identifier='<EMAIL>',
            phone_number='+1234567890',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )
        self.service = NotificationService()
    
    def test_service_initialization(self):
        """Test that notification service initializes"""
        self.assertIsNotNone(self.service)
    
    @patch.dict('os.environ', {'SNS_TOPIC_ARN': 'arn:aws:sns:us-west-2:123456789012:test-topic'})
    @patch('boto3.client')
    def test_service_availability_with_config(self, mock_boto_client):
        """Test service availability when properly configured"""
        mock_client = MagicMock()
        mock_boto_client.return_value = mock_client
        
        service = NotificationService()
        self.assertTrue(service.is_available())
    
    def test_service_availability_without_config(self):
        """Test service availability when not configured"""
        # Without SNS_TOPIC_ARN, service should not be available
        self.assertFalse(self.service.is_available())
    
    def test_notify_user_without_device_tokens(self):
        """Test notifying user with no device tokens"""
        result = notify_user(self.user, "Test message")
        self.assertFalse(result)
    
    @patch.dict('os.environ', {'SNS_TOPIC_ARN': 'arn:aws:sns:us-west-2:123456789012:test-topic'})
    @patch('boto3.client')
    def test_notify_user_with_device_tokens(self, mock_boto_client):
        """Test notifying user with device tokens"""
        # Create device token
        DeviceToken.objects.create(
            user=self.user,
            token='test_device_token'
        )
        
        # Mock SNS client
        mock_client = MagicMock()
        mock_client.publish.return_value = {'MessageId': 'test-message-id'}
        mock_boto_client.return_value = mock_client
        
        service = NotificationService()
        result = service.notify_user(self.user, "Test message")
        
        self.assertTrue(result)
        mock_client.publish.assert_called_once()


class DeviceRegistrationAPITest(APITestCase):
    """Test cases for device registration API endpoints"""
    
    def setUp(self):
        self.user = User.objects.create_user(
            identifier='<EMAIL>',
            phone_number='+1234567890',
            first_name='Test',
            last_name='User',
            password='testpass123'
        )
        self.register_url = reverse('v1:notifications:register_device')
        self.deactivate_url = reverse('v1:notifications:deactivate_device')
    
    def test_register_device_unauthenticated(self):
        """Test device registration without authentication"""
        response = self.client.post(self.register_url, {
            'device_token': 'test_token'
        })
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_register_device_success(self):
        """Test successful device registration"""
        self.client.force_authenticate(user=self.user)
        
        response = self.client.post(self.register_url, {
            'device_token': 'test_device_token_123'
        })
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('detail', response.data)
        self.assertTrue(response.data['created'])
        
        # Verify device token was created
        self.assertTrue(
            DeviceToken.objects.filter(
                user=self.user,
                token='test_device_token_123'
            ).exists()
        )
    
    def test_register_device_update_existing(self):
        """Test updating existing device token"""
        # Create existing token
        DeviceToken.objects.create(
            user=self.user,
            token='existing_token',
            is_active=False
        )
        
        self.client.force_authenticate(user=self.user)
        
        response = self.client.post(self.register_url, {
            'device_token': 'existing_token'
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertFalse(response.data['created'])
        
        # Verify token was reactivated
        token = DeviceToken.objects.get(user=self.user, token='existing_token')
        self.assertTrue(token.is_active)
    
    def test_register_device_missing_token(self):
        """Test device registration with missing token"""
        self.client.force_authenticate(user=self.user)
        
        response = self.client.post(self.register_url, {})
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    def test_register_device_invalid_token(self):
        """Test device registration with invalid token"""
        self.client.force_authenticate(user=self.user)
        
        response = self.client.post(self.register_url, {
            'device_token': 'short'  # Too short
        })
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    def test_deactivate_device_success(self):
        """Test successful device deactivation"""
        # Create device token
        DeviceToken.objects.create(
            user=self.user,
            token='test_token_to_deactivate'
        )
        
        self.client.force_authenticate(user=self.user)
        
        response = self.client.post(self.deactivate_url, {
            'device_token': 'test_token_to_deactivate'
        })
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify token was deactivated
        token = DeviceToken.objects.get(user=self.user, token='test_token_to_deactivate')
        self.assertFalse(token.is_active)
    
    def test_deactivate_device_not_found(self):
        """Test deactivating non-existent device token"""
        self.client.force_authenticate(user=self.user)
        
        response = self.client.post(self.deactivate_url, {
            'device_token': 'non_existent_token'
        })
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn('error', response.data)
