from rest_framework import serializers
from business.models import Business, BusinessCustomer, Location, OnlineBookingRules, StylistLevel, AccessLevel
from customers.models import CustomerProfile, CustomerTag
from django.contrib.auth import get_user_model
from django.utils.translation import gettext_lazy as _

User = get_user_model()

class CustomerTagSerializer(serializers.ModelSerializer):
    """Serializer for CustomerTag model"""
    class Meta:
        model = CustomerTag
        fields = ['id', 'name', 'description', 'color']


class UserBasicSerializer(serializers.ModelSerializer):
    """Basic serializer for User model to expose contact information"""
    class Meta:
        model = User
        fields = ['id', 'email', 'phone_number', 'first_name', 'last_name']
        read_only_fields = ['id', 'email', 'phone_number', 'first_name', 'last_name']


class CustomerProfileSerializer(serializers.ModelSerializer):
    """Serializer for CustomerProfile model with user details"""
    user = UserBasicSerializer(read_only=True)
    
    class Meta:
        model = CustomerProfile
        fields = [
            'id', 
            'user', 
            'card_on_file',
            'birthdate',
            'gender',
            'address_street',
            'address_apt_suite', 
            'address_city',
            'address_state',
            'address_zip',
            'created_at',
            'updated_at'
        ]
        read_only_fields = [
            'id', 
            'created_at',
            'updated_at'
        ]


class BusinessCustomerSerializer(serializers.ModelSerializer):
    """Serializer for BusinessCustomer model with customer details"""
    customer = CustomerProfileSerializer(read_only=True)
    customer_id = serializers.PrimaryKeyRelatedField(
        source='customer',
        queryset=CustomerProfile.objects.all(),
        write_only=True
    )
    tags = CustomerTagSerializer(many=True, read_only=True)
    tag_ids = serializers.PrimaryKeyRelatedField(
        source='tags',
        queryset=CustomerTag.objects.all(),
        many=True,
        write_only=True,
        required=False
    )
    
    class Meta:
        model = BusinessCustomer
        fields = [
            'id',
            'business',
            'customer',
            'customer_id',
            'notes',
            'loyalty_points',
            'opt_in_marketing',
            'email_reminders',
            'sms_reminders',
            'tags',
            'tag_ids',
            # Business-specific import fields
            'customer_since',
            'last_visited',
            'membership_type',
            'referred_by',
            'online_booking_allowed',
            'credit_card_info',
            'appointments_booked',
            'classes_booked',
            'amount_paid',
            'no_shows_cancellations',
            'employee_seen',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'business', 'created_at', 'updated_at']


class BusinessCustomerUpdateSerializer(serializers.ModelSerializer):
    """Serializer for updating BusinessCustomer model data"""
    tag_ids = serializers.PrimaryKeyRelatedField(
        source='tags',
        queryset=CustomerTag.objects.all(),
        many=True,
        required=False
    )
    
    class Meta:
        model = BusinessCustomer
        fields = [
            'notes',
            'loyalty_points',
            'opt_in_marketing',
            'email_reminders',
            'sms_reminders',
            'tag_ids'
        ]


class CustomerTagAssignmentSerializer(serializers.Serializer):
    """Serializer for assigning tags to a customer"""
    tag_ids = serializers.ListField(
        child=serializers.IntegerField(),
        required=True
    )


class BusinessSerializer(serializers.ModelSerializer):
    """Serializer for Business model"""
    class Meta:
        model = Business
        fields = [
            'id',
            'name',
            'description',
            'phone',
            'email',
            'website',
            'logo',
            'is_active',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class LocationSerializer(serializers.ModelSerializer):
    """Serializer for Location model"""
    class Meta:
        model = Location
        fields = [
            'id',
            'business',
            'name',
            'address_line1',
            'address_line2',
            'city',
            'state',
            'postal_code',
            'country',
            'phone',
            'email',
            'is_active',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class OnlineBookingRulesSerializer(serializers.ModelSerializer):
    """Serializer for OnlineBookingRules model"""
    class Meta:
        model = OnlineBookingRules
        fields = [
            'id',
            'business',
            'timezone',
            'currency',
            'max_days_in_advance',
            'min_hours_before',
            'appointment_interval',
            'allow_cancellation',
            'cancellation_hours_before',
            'cancellation_policy',
            'allow_rescheduling',
            'rescheduling_hours_before',
            'require_payment',
            'deposit_percentage',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class StylistLevelSerializer(serializers.ModelSerializer):
    """Serializer for StylistLevel model"""
    class Meta:
        model = StylistLevel
        fields = [
            'id',
            'business',
            'name',
            'level',
            'description',
            'is_active',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class BusinessCustomerFlatSerializer(serializers.ModelSerializer):
    """Flat serializer matching frontend expected structure with imported data support"""
    name = serializers.SerializerMethodField()
    email = serializers.SerializerMethodField()
    phone = serializers.SerializerMethodField()
    avatar = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    gender = serializers.SerializerMethodField()
    joinDate = serializers.SerializerMethodField()
    lastOrder = serializers.SerializerMethodField()
    totalOrders = serializers.SerializerMethodField()
    address = serializers.SerializerMethodField()
    
    # Additional fields from imported data
    customerSince = serializers.SerializerMethodField()
    lastVisited = serializers.SerializerMethodField()
    membership = serializers.SerializerMethodField()
    birthdate = serializers.SerializerMethodField()
    dayPhone = serializers.SerializerMethodField()
    nightPhone = serializers.SerializerMethodField()
    referredBy = serializers.SerializerMethodField()
    onlineBooking = serializers.SerializerMethodField()
    creditCard = serializers.SerializerMethodField()
    bank = serializers.SerializerMethodField()
    appointmentsBooked = serializers.SerializerMethodField()
    classesBooked = serializers.SerializerMethodField()
    pointsEarned = serializers.SerializerMethodField()
    amountPaid = serializers.SerializerMethodField()
    noShowsCancellations = serializers.SerializerMethodField()
    employeeSeen = serializers.SerializerMethodField()
    

    class Meta:
        model = BusinessCustomer
        fields = [
            'id',
            'name',
            'email',
            'phone',
            'avatar',
            'status',
            'gender',
            'joinDate',
            'lastOrder',
            'totalOrders',
            'address',
            # Additional imported data fields
            'customerSince',
            'lastVisited',
            'membership',
            'birthdate',
            'dayPhone',
            'nightPhone',
            'referredBy',
            'onlineBooking',
            'creditCard',
            'bank',
            'appointmentsBooked',
            'classesBooked',
            'pointsEarned',
            'amountPaid',
            'noShowsCancellations',
            'employeeSeen'
        ]



    # Existing helper getters
    def get_name(self, obj):
        first = obj.customer.user.first_name or ''
        last = obj.customer.user.last_name or ''
        return f"{first} {last}".strip()

    def get_email(self, obj):
        return obj.customer.user.email

    def get_phone(self, obj):
        return str(obj.customer.user.phone_number) if obj.customer.user.phone_number else ''

    def get_avatar(self, obj):
        # Placeholder avatar service using initials
        initials = ''.join(x[0] for x in [obj.customer.user.first_name, obj.customer.user.last_name] if x)
        return f"https://ui-avatars.com/api/?name={initials}&size=40&background=random"

    def get_status(self, obj):
        return 'active' if obj.customer.user.is_active else 'inactive'

    def get_gender(self, obj):
        return obj.customer.gender if obj.customer else None

    def get_joinDate(self, obj):
        return obj.created_at.date().isoformat() if obj.created_at else None

    def get_lastOrder(self, obj):
        # Placeholder until order history implemented
        return None

    def get_totalOrders(self, obj):
        # Placeholder until order model implemented
        return 0

    def get_address(self, obj):
        # Build address from customer profile fields
        if not obj.customer:
            return {'street': '', 'postalCode': '', 'city': '', 'state': '', 'country': ''}
            
        street = obj.customer.address_street or ''
        apt_suite = obj.customer.address_apt_suite or ''
        city = obj.customer.address_city or ''
        state = obj.customer.address_state or ''
        postal_code = obj.customer.address_zip or ''
        
        # Combine street and apt/suite if both exist
        full_street = street
        if apt_suite and street:
            full_street = f"{street}, {apt_suite}"
        elif apt_suite:
            full_street = apt_suite
            
        return {
            'street': full_street,
            'postalCode': postal_code,
            'city': city,
            'state': state,
            'country': ''  # Not stored in import data
        }

    # New helper getters for imported data fields - read from model fields instead of notes
    def get_customerSince(self, obj):
        return obj.customer_since

    def get_lastVisited(self, obj):
        return obj.last_visited

    def get_membership(self, obj):
        return obj.membership_type

    def get_birthdate(self, obj):
        return obj.customer.birthdate if obj.customer else None

    def get_dayPhone(self, obj):
        # Remove day phone - no longer stored
        return None

    def get_nightPhone(self, obj):
        # Remove night phone - no longer stored  
        return None

    def get_referredBy(self, obj):
        return obj.referred_by

    def get_onlineBooking(self, obj):
        return obj.online_booking_allowed

    def get_creditCard(self, obj):
        return obj.credit_card_info

    def get_bank(self, obj):
        # Remove bank info - no longer stored
        return None

    def get_appointmentsBooked(self, obj):
        return obj.appointments_booked or 0

    def get_classesBooked(self, obj):
        return obj.classes_booked or 0

    def get_pointsEarned(self, obj):
        return obj.loyalty_points or 0

    def get_amountPaid(self, obj):
        return float(obj.amount_paid) if obj.amount_paid else 0.0

    def get_noShowsCancellations(self, obj):
        return obj.no_shows_cancellations or 0

    def get_employeeSeen(self, obj):
        return obj.employee_seen 

# TODO: Implement FormTemplateHistory model and uncomment these serializers
# class FormTemplateHistorySerializer(serializers.ModelSerializer):
#     """Serializer for the FormTemplateHistory model"""
#     user_name = serializers.SerializerMethodField()
#     action_display = serializers.SerializerMethodField()
#
#     class Meta:
#         model = FormTemplateHistory
#         fields = [
#             'id', 'template', 'user', 'user_name', 'timestamp',
#             'action', 'action_display', 'content_snapshot', 'notes'
#         ]
#         read_only_fields = ['id', 'timestamp']
#
#     def get_user_name(self, obj):
#         if obj.user:
#             return obj.user.get_full_name() or obj.user.email
#         return "Unknown user"
#
#     def get_action_display(self, obj):
#         return dict(FormTemplateHistory.ACTION_CHOICES).get(obj.action, obj.action)


# class FormTemplateHistoryNestedSerializer(serializers.ModelSerializer):
#     """Simplified serializer for nested history in templates to avoid circular references"""
#     user_name = serializers.SerializerMethodField()
#     action_display = serializers.SerializerMethodField()
#
#     class Meta:
#         model = FormTemplateHistory
#         fields = [
#             'id', 'user_name', 'timestamp',
#             'action', 'action_display', 'notes'
#         ]
#
#     def get_user_name(self, obj):
#         if obj.user:
#             return obj.user.get_full_name() or obj.user.email
#         return "Unknown user"
#
#     def get_action_display(self, obj):
#         return dict(FormTemplateHistory.ACTION_CHOICES).get(obj.action, obj.action)


# Forms-related serializers have been moved to api/serializers/forms.py
# Import them from there if needed:
# from api.serializers.forms import FormTemplateSerializer, FormSubmissionSerializer, SignatureSerializer

class BusinessCustomerIOSSerializer(serializers.ModelSerializer):
    """iOS-compatible serializer with snake_case fields and sparse fieldsets support"""

    # Required fields (not optional)
    id = serializers.IntegerField(read_only=True)
    first_name = serializers.SerializerMethodField()
    last_name = serializers.SerializerMethodField()
    email = serializers.SerializerMethodField()
    phone = serializers.SerializerMethodField()
    has_upcoming_appointment = serializers.SerializerMethodField()

    # Optional fields
    address = serializers.SerializerMethodField()
    birthday = serializers.SerializerMethodField()
    gender = serializers.SerializerMethodField()
    tags = serializers.SerializerMethodField()
    pronounced_as = serializers.SerializerMethodField()
    referred_by = serializers.SerializerMethodField()
    general_tag = serializers.SerializerMethodField()

    point_balance = serializers.SerializerMethodField()
    notes = serializers.CharField(read_only=True)
    opt_in_marketing = serializers.BooleanField(read_only=True)

    email_reminders = serializers.BooleanField(read_only=True)
    sms_reminders = serializers.BooleanField(read_only=True)
    card_on_file = serializers.SerializerMethodField()
    payment_card_type = serializers.SerializerMethodField()
    payment_last_4_digits = serializers.SerializerMethodField()
    last_updated = serializers.SerializerMethodField()
    created_at = serializers.DateTimeField(read_only=True)
    avatar = serializers.SerializerMethodField()
    last_visited = serializers.SerializerMethodField()
    next_visit = serializers.SerializerMethodField()
    online_booking = serializers.SerializerMethodField()
    employee_seen = serializers.SerializerMethodField()

    class Meta:
        model = BusinessCustomer
        fields = [
            'id', 'first_name', 'last_name', 'email', 'phone',
            'has_upcoming_appointment', 'address', 'birthday', 'gender', 'tags',
            'pronounced_as', 'referred_by', 'general_tag', 'point_balance', 'notes',
            'opt_in_marketing', 'email_reminders', 'sms_reminders', 'card_on_file',
            'payment_card_type', 'payment_last_4_digits', 'last_updated', 'created_at',
            'avatar', 'last_visited', 'next_visit', 'online_booking', 'employee_seen'
        ]

    def __init__(self, *args, **kwargs):
        # Handle sparse fieldsets
        fields = kwargs.pop('fields', None)
        super().__init__(*args, **kwargs)

        if fields is not None:
            # Drop any fields that are not specified in the `fields` argument
            allowed = set(fields)
            existing = set(self.fields)
            for field_name in existing - allowed:
                self.fields.pop(field_name)

    # Required field methods
    def get_first_name(self, obj):
        return obj.customer.user.first_name or ''

    def get_last_name(self, obj):
        return obj.customer.user.last_name or ''

    def get_email(self, obj):
        return obj.customer.user.email

    def get_phone(self, obj):
        return str(obj.customer.user.phone_number) if obj.customer.user.phone_number else ''

    def get_has_upcoming_appointment(self, obj):
        """Check if customer has any upcoming appointments"""
        from django.utils import timezone
        from appointments.models import Appointment

        now = timezone.now()
        upcoming_appointments = Appointment.objects.filter(
            customer=obj,
            start_time__gt=now,
            status__in=['pending', 'confirmed', 'accepted']
        ).exists()

        return upcoming_appointments

    # Optional field methods
    def get_address(self, obj):
        if not obj.customer:
            return None

        street = obj.customer.address_street or ''
        apt_suite = obj.customer.address_apt_suite or ''
        city = obj.customer.address_city or ''
        state = obj.customer.address_state or ''
        postal_code = obj.customer.address_zip or ''

        # Filter out empty strings and "---" values
        street = street if street and street != '---' else ''
        apt_suite = apt_suite if apt_suite and apt_suite != '---' else ''
        city = city if city and city != '---' else ''
        state = state if state and state != '---' else ''
        postal_code = postal_code if postal_code and postal_code != '---' else ''

        # Combine street and apt/suite if both exist
        full_street = street
        if apt_suite and street:
            full_street = f"{street}, {apt_suite}"
        elif apt_suite:
            full_street = apt_suite

        # Return full address string or None if empty
        address_parts = [full_street, city, state, postal_code]
        address_parts = [part for part in address_parts if part]
        return ', '.join(address_parts) if address_parts else None

    def get_birthday(self, obj):
        return obj.customer.birthdate if obj.customer and obj.customer.birthdate else None

    def get_gender(self, obj):
        if not obj.customer or not obj.customer.gender:
            return None
        gender = obj.customer.gender
        return gender if gender != '---' else None

    def get_tags(self, obj):
        # Return list of tag names
        return [tag.name for tag in obj.tags.all()]

    def get_pronounced_as(self, obj):
        # This field doesn't exist in current model, return None
        return None

    def get_referred_by(self, obj):
        return obj.referred_by

    def get_general_tag(self, obj):
        # This field doesn't exist in current model, return None
        return None

    def get_point_balance(self, obj):
        return obj.loyalty_points

    def get_card_on_file(self, obj):
        return obj.customer.card_on_file if obj.customer else False

    def get_payment_card_type(self, obj):
        # This field doesn't exist in current model, return None
        return None

    def get_payment_last_4_digits(self, obj):
        # This field doesn't exist in current model, return None
        return None

    def get_last_updated(self, obj):
        return obj.updated_at

    def get_avatar(self, obj):
        # Generate avatar using initials
        initials = ''.join(x[0] for x in [obj.customer.user.first_name, obj.customer.user.last_name] if x)
        return f"https://ui-avatars.com/api/?name={initials}&size=40&background=random"

    def get_last_visited(self, obj):
        return obj.last_visited

    def get_next_visit(self, obj):
        # This field doesn't exist in current model, return None
        return None

    def get_online_booking(self, obj):
        return obj.online_booking_allowed

    def get_employee_seen(self, obj):
        return obj.employee_seen
