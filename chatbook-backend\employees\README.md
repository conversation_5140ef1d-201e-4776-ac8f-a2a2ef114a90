# Employee ICS Calendar Feed Testing Guide

This guide walks you through testing the ICS calendar feed functionality that allows employees to subscribe to their work schedules in third-party calendar applications (Apple Calendar, Google Calendar, Outlook, etc.).

## 📋 Prerequisites

- Django development server running
- Employee records with appointments in the database
- Internet connection for ngrok tunneling

## 🛠️ Setup Instructions

### 1. Install and Setup ngrok

**Option A: Download from Website**
1. Go to [https://ngrok.com/download](https://ngrok.com/download)
2. Download the appropriate version for your OS
3. Extract the executable to a directory in your PATH

**Option B: Install via Package Manager**

On Ubuntu/WSL:
```bash
curl -s https://ngrok-agent.s3.amazonaws.com/ngrok.asc | sudo tee /etc/apt/trusted.gpg.d/ngrok.asc >/dev/null
echo "deb https://ngrok-agent.s3.amazonaws.com buster main" | sudo tee /etc/apt/sources.list.d/ngrok.list
sudo apt update && sudo apt install ngrok
```

On macOS:
```bash
brew install ngrok
```

### 2. Configure Django Settings

Ensure your Django settings allow ngrok hosts. Edit `settings_dev.py`:

```python
# Line 21 in settings_dev.py
ALLOWED_HOSTS = ['*']  # Allow all hosts for testing
```

## 🚀 Testing Process

### Step 1: Start Django Development Server

```bash
cd chatbook-backend
source venv/bin/activate
python manage.py runserver 0.0.0.0:8000
```

### Step 2: Start ngrok Tunnel

In a **new terminal window**:

```bash
ngrok http 8000
```

You'll see output like:
```
ngrok                                                          

Session Status                online                                            
Account                       your-account                               
Version                       3.0.0                                             
Region                        United States (us)                                
Latency                       45ms                                              
Web Interface                 http://127.0.0.1:4040                            
Forwarding                    https://abc123.ngrok.io -> http://localhost:8000  

Connections                   ttl     opn     rt1     rt5     p50     p90       
                              0       0       0.00    0.00    0.00    0.00   
```

**📝 Copy the HTTPS forwarding URL** (e.g., `https://abc123.ngrok.io`)

### Step 3: Get Employee ICS URLs

In a **third terminal window**:

```bash
cd chatbook-backend
source venv/bin/activate

python manage.py shell -c "
from employees.models import Employee

# Replace with your actual ngrok URL
NGROK_URL = 'https://abc123.ngrok.io'  # <-- UPDATE THIS

print('🗓️  ICS Calendar Feed URLs (ngrok)')
print('=' * 50)

for emp in Employee.objects.all():
    token = emp.ics_feed_token
    url = f'{NGROK_URL}/employees/calendar/{token}.ics'
    print(f'👤 {emp.user.first_name} {emp.user.last_name}:')
    print(f'   🔗 {url}')
    print()
"
```

**Example Output:**
```
🗓️  ICS Calendar Feed URLs (ngrok)
==================================================
👤 Carol Fan:
   🔗 https://abc123.ngrok.io/employees/calendar/12345678-1234-1234-1234-123456789abc.ics

👤 Serena Zhou:
   🔗 https://abc123.ngrok.io/employees/calendar/87654321-4321-4321-4321-cba987654321.ics
```

### Step 4: Test ICS URL in Browser

1. Copy one of the ICS URLs from the output above
2. Paste it into your web browser
3. **Expected Result**: The browser should download an `.ics` file or display calendar data starting with `BEGIN:VCALENDAR`

**✅ Success indicators:**
- Browser downloads `.ics` file
- File content starts with `BEGIN:VCALENDAR`
- File contains `BEGIN:VEVENT` entries for appointments

**❌ Common Issues:**
- **404 Error**: Check that Django server is running and URL is correct
- **500 Error**: Check Django logs for detailed error messages
- **DisallowedHost Error**: Ensure `ALLOWED_HOSTS = ['*']` in `settings_dev.py`

## 📱 Adding to Calendar Applications

### Apple Calendar (macOS)

1. Open **Calendar** app
2. Go to **File** → **New Calendar Subscription**
3. Paste the ICS URL: `https://abc123.ngrok.io/employees/calendar/{token}.ics`
4. Click **Subscribe**
5. Configure subscription settings:
   - **Name**: "Employee Name - Work Schedule"
   - **Color**: Choose a color
   - **Refresh**: Every hour (recommended)
6. Click **OK**

### Apple Calendar (iOS/iPadOS)

1. Open **Settings** app
2. Go to **Calendar** → **Accounts** → **Add Account**
3. Select **Other** → **Add Calendar Subscription**
4. Paste the ICS URL
5. Tap **Next** and configure as desired
6. Tap **Save**

### Google Calendar (Web)

1. Open [Google Calendar](https://calendar.google.com)
2. In the left sidebar, click the **+** next to "Other calendars"
3. Select **From URL**
4. Paste the ICS URL
5. Click **Add Calendar**

### Outlook

1. Open Outlook (desktop or web)
2. Go to **File** → **Account Settings** → **Internet Calendars**
3. Click **New**
4. Paste the ICS URL
5. Click **Add** and configure subscription settings

## 🔧 Troubleshooting

### Problem: No Events Showing in Calendar

**Check appointment data:**
```bash
python manage.py shell -c "
from employees.models import Employee
from appointments.models import Appointment
from django.utils import timezone
from datetime import timedelta

emp = Employee.objects.first()
if emp:
    # Check total appointments
    total = Appointment.objects.filter(employee=emp).count()
    print(f'📊 Total appointments for {emp.user.first_name}: {total}')
    
    # Check appointments in date range
    start_date = timezone.now().date() - timedelta(days=30)  # Past month
    end_date = timezone.now().date() + timedelta(days=90)    # Next 3 months
    
    in_range = Appointment.objects.filter(
        employee=emp,
        start_time__date__gte=start_date,
        start_time__date__lte=end_date,
        status__in=['confirmed', 'accepted', 'checked_in', 'service_started']
    ).count()
    print(f'📊 Confirmed appointments in range: {in_range}')
    
    if in_range == 0:
        print('❌ No confirmed appointments found in date range')
        print('💡 Create test appointments or check appointment statuses')
"
```

### Problem: Calendar App Won't Subscribe

1. **Check URL format**: Ensure it starts with `https://` and ends with `.ics`
2. **Test in browser first**: The URL should work in a web browser
3. **Try different calendar app**: Test with another calendar application
4. **Check refresh settings**: Set calendar refresh to "Every hour" or "Push"

### Problem: ngrok URL Changes

ngrok URLs change every time you restart ngrok. For persistent testing:

1. **Get a static ngrok domain** (ngrok paid plan)
2. **Use your local network IP** instead of ngrok for same-network testing
3. **Update calendar subscriptions** when ngrok URL changes

### Problem: Django DisallowedHost Error

Edit `chatbook-backend/settings_dev.py` line 21:
```python
ALLOWED_HOSTS = ['*']  # Allow all hosts
```

Then restart Django server.

## 📊 ICS Feed Features

### Date Range
- **Past appointments**: 30 days back
- **Future appointments**: 90 days forward
- **Total range**: ~4 months of data

### Appointment Status Filter
Only includes appointments with status:
- `confirmed`
- `accepted` 
- `checked_in`
- `service_started`

### Event Details
Each calendar event includes:
- **Summary**: Customer name and services
- **Description**: Customer contact info and notes
- **Location**: Business address
- **Time**: Appointment start/end times in business timezone
- **Status**: Calendar status (confirmed/tentative/cancelled)

## 🔄 Refreshing Calendar Data

- **Automatic**: Calendar apps refresh based on subscription settings
- **Manual**: Force refresh in calendar app settings
- **Real-time**: Not real-time; updates based on refresh interval
- **Typical delay**: 5 minutes to 1 hour depending on calendar app

## 🛡️ Security Notes

- **UUID Tokens**: Each employee has a unique, secure UUID token
- **Token Regeneration**: Tokens can be regenerated to invalidate old URLs
- **No Authentication**: URLs are public but obscured by UUID
- **HTTPS**: Use HTTPS URLs in production for security

## 📝 Development Notes

- **Database Model**: `Employee.ics_feed_token` stores the unique identifier
- **View Function**: `employees.views.employee_ics_feed` generates ICS content
- **URL Pattern**: `/employees/calendar/{uuid}.ics`
- **Dependencies**: `icalendar` library for ICS generation

## 🤝 Support

For issues or questions about the ICS calendar feed functionality:

1. Check Django server logs for detailed error messages
2. Verify appointment data exists in the database
3. Test the ICS URL in a web browser first
4. Ensure ngrok tunnel is active and accessible

---

**Last Updated**: January 2025  
**Django Version**: 4.2.22  
**icalendar Version**: 6.0.1