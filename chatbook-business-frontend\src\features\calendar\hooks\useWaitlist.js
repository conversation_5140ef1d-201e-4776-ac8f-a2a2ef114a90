import { useState, useCallback, useReducer, useMemo } from 'react'
import { waitlistService } from '../services/waitlistService'
import { servicesApiService } from '../services/servicesApiService'
import { employeeApiService } from '../../employees/services'

/**
 * Initial state for waitlist creation form
 */
const initialState = {
  customer_name: '',
  phone_number: '',
  email: '',
  notes: '',
  service_ids: [],
  employee_ids: [],
  business: null, // This should come from app context
  // UI state
  customerSearch: '',
  searchResults: [],
  showSearchResults: false,
  // Availability windows
  availabilityWindows: [{ date: '', time: 'Anytime' }],
  // Customer selection state
  selectedExistingCustomer: null,
  hasSelectedExistingCustomer: false,
  // Validation
  errors: {},
  // Loading states
  isLoading: false,
  isSearching: false
}

/**
 * Waitlist creation steps
 */
export const WAITLIST_STEPS = ['service', 'availability', 'customer', 'confirmation']

/**
 * Waitlist form reducer for managing complex form state
 */
export const waitlistFormReducer = (state, action) => {
  switch (action.type) {
    case 'INITIALIZE':
      return { 
        ...initialState, 
        ...action.payload,
        availabilityWindows: action.payload.initialDate 
          ? [{ date: action.payload.initialDate.toISOString().split('T')[0], time: 'Anytime' }]
          : [{ date: '', time: 'Anytime' }],
        employee_ids: action.payload.initialEmployee ? [action.payload.initialEmployee.id] : []
      }
      
    case 'UPDATE_FIELD':
      return { 
        ...state, 
        [action.field]: action.value,
        errors: { ...state.errors, [action.field]: null } // Clear field error
      }
      
    case 'SELECT_SERVICE':
      return {
        ...state,
        service_ids: action.payload ? [action.payload] : [],
        errors: { ...state.errors, service_ids: null }
      }
      
    case 'SELECT_EMPLOYEE':
      return {
        ...state,
        employee_ids: action.payload ? [action.payload] : [],
        errors: { ...state.errors, employee_ids: null }
      }
      
    case 'UPDATE_CUSTOMER_SEARCH':
      return {
        ...state,
        customerSearch: action.payload,
        showSearchResults: action.payload.length > 2
      }
      
    case 'SET_SEARCH_RESULTS':
      return {
        ...state,
        searchResults: action.payload,
        isSearching: false
      }
      
    case 'SELECT_CUSTOMER':
      return {
        ...state,
        customer_name: action.payload.name,
        phone_number: action.payload.phone_number || '',
        email: action.payload.email || '',
        customerSearch: action.payload.name,
        showSearchResults: false,
        errors: { ...state.errors, customer_name: null, phone_number: null, email: null }
      }
      
    case 'SELECT_EXISTING_CUSTOMER':
      return {
        ...state,
        selectedExistingCustomer: action.payload,
        hasSelectedExistingCustomer: true,
        customer_name: (() => {
          // Try different possible name structures
          const firstName = action.payload.customer?.user?.first_name || action.payload.first_name || ''
          const lastName = action.payload.customer?.user?.last_name || action.payload.last_name || ''
          const email = action.payload.customer?.user?.email || action.payload.email || ''
          const fullName = `${firstName} ${lastName}`.trim()
          return fullName || email || `Customer ${action.payload.id}`
        })(),
        phone_number: action.payload.customer?.user?.phone || action.payload.phone || '',
        email: action.payload.customer?.user?.email || action.payload.email || '',
        errors: { ...state.errors, customer_name: null, phone_number: null, email: null }
      }
      
    case 'CLEAR_CUSTOMER_SELECTION':
      return {
        ...state,
        selectedExistingCustomer: null,
        hasSelectedExistingCustomer: false,
        customer_name: '',
        phone_number: '',
        email: '',
        errors: { ...state.errors, customer_name: null, phone_number: null, email: null }
      }
      
    case 'ADD_AVAILABILITY_WINDOW':
      if (state.availabilityWindows.length >= 5) return state
      return {
        ...state,
        availabilityWindows: [...state.availabilityWindows, { date: '', time: 'Anytime' }]
      }
      
    case 'REMOVE_AVAILABILITY_WINDOW':
      if (state.availabilityWindows.length <= 1) return state
      return {
        ...state,
        availabilityWindows: state.availabilityWindows.filter((_, index) => index !== action.index)
      }
      
    case 'UPDATE_AVAILABILITY_WINDOW':
      return {
        ...state,
        availabilityWindows: state.availabilityWindows.map((window, index) => 
          index === action.index ? { ...window, [action.field]: action.value } : window
        )
      }
      
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload }
      
    case 'SET_SEARCHING':
      return { ...state, isSearching: action.payload }
      
    case 'SET_ERRORS':
      return { ...state, errors: action.payload }
      
    case 'CLEAR_ERRORS':
      return { ...state, errors: {} }
      
    case 'RESET':
      return initialState
      
    default:
      return state
  }
}

/**
 * Validation function for waitlist form
 */
export const validateWaitlistForm = (formData, hasSelectedExistingCustomer = false) => {
  const errors = {}
  
  // Customer validation - only required if no existing customer is selected
  if (!hasSelectedExistingCustomer) {
    if (!formData.customer_name?.trim()) {
      errors.customer_name = 'Customer name is required'
    }
    
    if (!formData.phone_number?.trim()) {
      errors.phone_number = 'Phone number is required'
    }
    
    if (!formData.email?.trim()) {
      errors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      errors.email = 'Please enter a valid email address'
    }
  }
  
  if (!formData.service_ids || formData.service_ids.length === 0) {
    errors.service_ids = 'Please select a service'
  }
  
  // Validate availability windows
  const validWindows = formData.availabilityWindows?.filter(window => window.date) || []
  if (validWindows.length === 0) {
    errors.availability = 'Please select at least one available date'
  }
  
  return errors
}

/**
 * Custom hook for managing waitlist creation
 */
export const useWaitlist = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [currentStep, setCurrentStep] = useState('service')
  const [formData, dispatch] = useReducer(waitlistFormReducer, initialState)
  const [services, setServices] = useState([])
  const [employees, setEmployees] = useState([])
  const [isInitialized, setIsInitialized] = useState(false)

  /**
   * Initialize waitlist creation
   */
  const startWaitlistCreation = useCallback(({ date, employee }) => {
    console.log('📋 Starting waitlist creation for:', { date, employee })
    
    dispatch({
      type: 'INITIALIZE',
      payload: {
        initialDate: date,
        initialEmployee: employee
      }
    })
    
    setCurrentStep('service')
    setIsOpen(true)
    
    // Load initial data if not already loaded
    if (!isInitialized) {
      loadInitialData()
    }
  }, [isInitialized])

  /**
   * Load services and employees
   */
  const loadInitialData = useCallback(async () => {
    try {
      console.log('📋 Loading initial data for waitlist...')
      
      const [servicesData, employeesData] = await Promise.all([
        servicesApiService.fetchServices(),
        employeeApiService.getAllEmployees()
      ])
      
      setServices(servicesData)
      setEmployees(employeesData)
      setIsInitialized(true)
      
      console.log('✅ Initial data loaded:', { 
        services: servicesData.length, 
        employees: employeesData.length 
      })
    } catch (error) {
      console.error('❌ Failed to load initial data:', error)
      dispatch({ type: 'SET_ERRORS', payload: { general: 'Failed to load data' } })
    }
  }, [])

  /**
   * Search customers
   */
  const searchCustomers = useCallback(async (searchTerm) => {
    if (!searchTerm || searchTerm.length < 2) {
      dispatch({ type: 'SET_SEARCH_RESULTS', payload: [] })
      return
    }

    dispatch({ type: 'SET_SEARCHING', payload: true })
    
    try {
      const results = await waitlistService.searchCustomers(searchTerm)
      dispatch({ type: 'SET_SEARCH_RESULTS', payload: results })
    } catch (error) {
      console.error('❌ Customer search failed:', error)
      dispatch({ type: 'SET_SEARCH_RESULTS', payload: [] })
    }
  }, [])

  /**
   * Update form field
   */
  const updateField = useCallback((field, value) => {
    dispatch({ type: 'UPDATE_FIELD', field, value })
    
    // Trigger customer search if it's the search field
    if (field === 'customerSearch') {
      dispatch({ type: 'UPDATE_CUSTOMER_SEARCH', payload: value })
      searchCustomers(value)
    }
  }, [searchCustomers])

  /**
   * Select service
   */
  const selectService = useCallback((serviceId) => {
    dispatch({ type: 'SELECT_SERVICE', payload: serviceId })
  }, [])

  /**
   * Select employee
   */
  const selectEmployee = useCallback((employeeId) => {
    dispatch({ type: 'SELECT_EMPLOYEE', payload: employeeId })
  }, [])

  /**
   * Select customer from search results
   */
  const selectCustomer = useCallback((customer) => {
    dispatch({ type: 'SELECT_CUSTOMER', payload: customer })
  }, [])

  /**
   * Select existing customer
   */
  const selectExistingCustomer = useCallback((customer) => {
    dispatch({ type: 'SELECT_EXISTING_CUSTOMER', payload: customer })
  }, [])

  /**
   * Clear customer selection (for creating new customer)
   */
  const clearCustomerSelection = useCallback(() => {
    dispatch({ type: 'CLEAR_CUSTOMER_SELECTION' })
  }, [])

  /**
   * Add availability window
   */
  const addAvailabilityWindow = useCallback(() => {
    dispatch({ type: 'ADD_AVAILABILITY_WINDOW' })
  }, [])

  /**
   * Remove availability window
   */
  const removeAvailabilityWindow = useCallback((index) => {
    dispatch({ type: 'REMOVE_AVAILABILITY_WINDOW', index })
  }, [])

  /**
   * Update availability window
   */
  const updateAvailabilityWindow = useCallback((index, field, value) => {
    dispatch({ type: 'UPDATE_AVAILABILITY_WINDOW', index, field, value })
  }, [])

  /**
   * Validate current form
   */
  const validateForm = useCallback(() => {
    const errors = validateWaitlistForm(formData, formData.hasSelectedExistingCustomer)
    dispatch({ type: 'SET_ERRORS', payload: errors })
    return Object.keys(errors).length === 0
  }, [formData])

  /**
   * Submit waitlist entry
   */
  const submitWaitlist = useCallback(async () => {
    if (!validateForm()) {
      return { success: false, errors: formData.errors }
    }

    dispatch({ type: 'SET_LOADING', payload: true })
    
    try {
      // Get business ID from user data or use default
      const userDataString = localStorage.getItem('user_data')
      let businessId = formData.business
      
      if (!businessId && userDataString) {
        try {
          const userData = JSON.parse(userDataString)
          businessId = userData.business_id || userData.business || 1
        } catch (error) {
          console.warn('Failed to parse user data for business ID:', error)
          businessId = 1 // fallback
        }
      }
      
      if (!businessId) {
        businessId = 1 // default fallback
      }
      
      // Prepare waitlist data
      const waitlistData = {
        customer_name: formData.customer_name,
        phone_number: formData.phone_number,
        email: formData.email,
        notes: formData.notes,
        service_ids: formData.service_ids,
        // Handle "Any Available Staff" case - send all employee IDs when none selected
        employee_ids: formData.employee_ids.length === 0 ? 
          (employees && employees.length > 0 ? employees.map(emp => emp.id) : []) : 
          formData.employee_ids,
        business: businessId,
        // Convert availability windows to API format with proper timezone
        availability: formData.availabilityWindows
          .filter(window => window.date)
          .map(window => {
            const startTime = window.time === 'Anytime' ? '09:00:00' : `${window.time}:00`
            const endTime = window.time === 'Anytime' ? '17:00:00' : `${window.time}:00`
            
            // Format datetime as backend expects: YYYY-MM-DDThh:mm:ss+HHMM
            const formatDateTimeForBackend = (dateString, timeString) => {
              // Get timezone offset in the format +HHMM or -HHMM
              const now = new Date()
              const timezoneOffset = now.getTimezoneOffset()
              const offsetHours = Math.floor(Math.abs(timezoneOffset) / 60)
              const offsetMinutes = Math.abs(timezoneOffset) % 60
              const offsetSign = timezoneOffset <= 0 ? '+' : '-'
              const offsetString = `${offsetSign}${offsetHours.toString().padStart(2, '0')}${offsetMinutes.toString().padStart(2, '0')}`
              
              return `${dateString}T${timeString}${offsetString}`
            }
            
            const startDateTime = formatDateTimeForBackend(window.date, startTime)
            const endDateTime = formatDateTimeForBackend(window.date, endTime)
            
            return {
              start_datetime: startDateTime,
              end_datetime: endDateTime
            }
          })
      }

      console.log('📋 Submitting waitlist data:', waitlistData)
      
      const result = await waitlistService.createWaitlistEntry(waitlistData)
      
      console.log('✅ Waitlist entry created successfully:', result)
      
      return { success: true, data: result }
    } catch (error) {
      console.error('❌ Failed to create waitlist entry:', error)
      
      const errorMessage = error.message || 'Failed to create waitlist entry'
      dispatch({ type: 'SET_ERRORS', payload: { general: errorMessage } })
      
      return { success: false, error: errorMessage }
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false })
    }
  }, [formData, validateForm])

  /**
   * Close modal and reset
   */
  const closeWaitlist = useCallback(() => {
    setIsOpen(false)
    setCurrentStep('service')
    dispatch({ type: 'RESET' })
  }, [])

  /**
   * Navigate to next step
   */
  const nextStep = useCallback(() => {
    const currentIndex = WAITLIST_STEPS.indexOf(currentStep)
    if (currentIndex < WAITLIST_STEPS.length - 1) {
      setCurrentStep(WAITLIST_STEPS[currentIndex + 1])
    }
  }, [currentStep])

  /**
   * Navigate to previous step
   */
  const previousStep = useCallback(() => {
    const currentIndex = WAITLIST_STEPS.indexOf(currentStep)
    if (currentIndex > 0) {
      setCurrentStep(WAITLIST_STEPS[currentIndex - 1])
    }
  }, [currentStep])

  // Computed values
  const selectedService = useMemo(() => {
    return services.find(service => service.id === formData.service_ids[0])
  }, [services, formData.service_ids])

  const selectedEmployee = useMemo(() => {
    return employees.find(employee => employee.id === formData.employee_ids[0])
  }, [employees, formData.employee_ids])

  const canAddMoreWindows = useMemo(() => {
    return formData.availabilityWindows.length < 5
  }, [formData.availabilityWindows])

  return {
    // State
    isOpen,
    currentStep,
    formData,
    services,
    employees,
    selectedService,
    selectedEmployee,
    canAddMoreWindows,
    
    // Actions
    startWaitlistCreation,
    closeWaitlist,
    updateField,
    selectService,
    selectEmployee,
    selectCustomer,
    selectExistingCustomer,
    clearCustomerSelection,
    addAvailabilityWindow,
    removeAvailabilityWindow,
    updateAvailabilityWindow,
    searchCustomers,
    validateForm,
    submitWaitlist,
    nextStep,
    previousStep
  }
}

export default useWaitlist 