import React, { useState, useRef, useEffect } from 'react'
import { ChevronDownIcon, FolderPlusIcon, DocumentPlusIcon } from '@heroicons/react/24/outline'

const AddItemDropdown = ({ onAddCategory, onAddService, onClose }) => {
  const [isOpen, setIsOpen] = useState(true)
  const dropdownRef = useRef(null)

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false)
        onClose()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [onClose])

  const handleOptionClick = (action) => {
    action()
    setIsOpen(false)
    onClose()
  }

  if (!isOpen) return null

  return (
    <div 
      ref={dropdownRef}
      className="absolute top-full right-0 mt-1 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-50 py-1"
    >
      <button
        onClick={() => handleOptionClick(onAddCategory)}
        className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center"
      >
        <FolderPlusIcon className="w-4 h-4 mr-3 text-blue-500" />
        Add Category
      </button>
      <button
        onClick={() => handleOptionClick(onAddService)}
        className="w-full px-4 py-2 text-left text-sm text-gray-700 hover:bg-gray-50 flex items-center"
      >
        <DocumentPlusIcon className="w-4 h-4 mr-3 text-green-500" />
        Add Service
      </button>
    </div>
  )
}

export default AddItemDropdown 