"""
Timezone utilities for consistent timezone handling across the application.

This module provides centralized timezone functions following the TIMEZONE_RULE.md guidelines:
- Store in UTC, convert at the boundary
- Use zoneinfo for modern timezone handling
- Validate timezone identifiers
- Handle DST transitions gracefully
"""

import logging
from datetime import datetime, time, date, timedelta
from typing import Optional, Union, List
from zoneinfo import ZoneInfo, ZoneInfoNotFoundError
from django.utils import timezone
from django.conf import settings

logger = logging.getLogger(__name__)

# Default timezone fallback
DEFAULT_TIMEZONE = 'UTC'
BUSINESS_DEFAULT_TIMEZONE = getattr(settings, 'TIME_ZONE', 'America/Los_Angeles')


class TimezoneError(Exception):
    """Custom exception for timezone-related errors"""
    pass


def validate_timezone(timezone_str: str) -> bool:
    """
    Validate that a timezone string is a valid IANA timezone identifier.
    
    Args:
        timezone_str: IANA timezone identifier (e.g., 'America/Los_Angeles')
        
    Returns:
        bool: True if valid, False otherwise
    """
    try:
        ZoneInfo(timezone_str)
        return True
    except ZoneInfoNotFoundError:
        logger.warning(f"Invalid timezone identifier: {timezone_str}")
        return False


def get_timezone(timezone_str: str) -> ZoneInfo:
    """
    Get a ZoneInfo object for the given timezone string with fallback.
    
    Args:
        timezone_str: IANA timezone identifier
        
    Returns:
        ZoneInfo: Timezone object, defaults to UTC if invalid
    """
    try:
        return ZoneInfo(timezone_str)
    except ZoneInfoNotFoundError:
        logger.warning(f"Unknown timezone '{timezone_str}', falling back to UTC")
        return ZoneInfo(DEFAULT_TIMEZONE)


def get_business_timezone(business) -> ZoneInfo:
    """
    Get the timezone for a business from its booking rules.
    
    Args:
        business: Business model instance
        
    Returns:
        ZoneInfo: Business timezone, defaults to UTC if not configured
    """
    try:
        if hasattr(business, 'booking_rules') and business.booking_rules:
            timezone_str = business.booking_rules.timezone
        elif hasattr(business, 'timezone'):
            timezone_str = business.timezone
        else:
            timezone_str = DEFAULT_TIMEZONE
            
        return get_timezone(timezone_str)
    except Exception as e:
        logger.error(f"Error getting business timezone: {e}")
        return ZoneInfo(DEFAULT_TIMEZONE)


def now_in_timezone(tz: Union[str, ZoneInfo]) -> datetime:
    """
    Get current datetime in the specified timezone.
    
    Args:
        tz: Timezone string or ZoneInfo object
        
    Returns:
        datetime: Current time in the specified timezone
    """
    if isinstance(tz, str):
        tz = get_timezone(tz)
    
    return timezone.now().astimezone(tz)


def convert_to_business_timezone(dt: datetime, business_tz: Union[str, ZoneInfo]) -> datetime:
    """
    Convert a UTC datetime to business timezone.
    
    Args:
        dt: UTC datetime (timezone-aware)
        business_tz: Business timezone string or ZoneInfo object
        
    Returns:
        datetime: Datetime converted to business timezone
    """
    if isinstance(business_tz, str):
        business_tz = get_timezone(business_tz)
    
    if dt.tzinfo is None:
        logger.warning("Converting naive datetime - assuming UTC")
        dt = dt.replace(tzinfo=ZoneInfo('UTC'))
    
    return dt.astimezone(business_tz)


def convert_to_utc(dt: datetime, source_tz: Union[str, ZoneInfo]) -> datetime:
    """
    Convert a datetime from source timezone to UTC.
    
    Args:
        dt: Datetime in source timezone (can be naive or aware)
        source_tz: Source timezone string or ZoneInfo object
        
    Returns:
        datetime: UTC datetime
    """
    if isinstance(source_tz, str):
        source_tz = get_timezone(source_tz)
    
    if dt.tzinfo is None:
        # Naive datetime - attach source timezone
        dt = dt.replace(tzinfo=source_tz)
    
    return dt.astimezone(ZoneInfo('UTC'))


def localize_time_to_date(local_time: time, target_date: date, 
                         business_tz: Union[str, ZoneInfo]) -> datetime:
    """
    Convert a local time to a timezone-aware datetime for a specific date.
    
    This is useful for working hours which are stored as TimeField but need
    to be converted to specific datetime for availability calculations.
    
    Args:
        local_time: Time in business timezone (e.g., 9:00 AM)
        target_date: Date to apply the time to
        business_tz: Business timezone
        
    Returns:
        datetime: Timezone-aware datetime in business timezone
    """
    if isinstance(business_tz, str):
        business_tz = get_timezone(business_tz)
    
    # Combine date and time, then attach timezone
    naive_dt = datetime.combine(target_date, local_time)
    return naive_dt.replace(tzinfo=business_tz)


def format_time_for_display(dt: datetime, business_tz: Union[str, ZoneInfo], 
                           format_str: str = '%H:%M') -> str:
    """
    Format a UTC datetime for display in business timezone.
    
    Args:
        dt: UTC datetime
        business_tz: Business timezone for display
        format_str: Format string for time display
        
    Returns:
        str: Formatted time string in business timezone
    """
    business_dt = convert_to_business_timezone(dt, business_tz)
    return business_dt.strftime(format_str)


def is_dst_transition_date(target_date: date, tz: Union[str, ZoneInfo]) -> bool:
    """
    Check if a date has a DST transition in the given timezone.
    
    Args:
        target_date: Date to check
        tz: Timezone to check for DST transition
        
    Returns:
        bool: True if DST transition occurs on this date
    """
    if isinstance(tz, str):
        tz = get_timezone(tz)
    
    # Check start and end of day
    start_of_day = datetime.combine(target_date, time.min).replace(tzinfo=tz)
    end_of_day = datetime.combine(target_date, time.max).replace(tzinfo=tz)
    
    # If UTC offsets are different, there's a DST transition
    return start_of_day.utcoffset() != end_of_day.utcoffset()


def get_safe_time_slots(start_time: time, end_time: time, target_date: date,
                       business_tz: Union[str, ZoneInfo], 
                       interval_minutes: int = 15) -> List[datetime]:
    """
    Generate time slots for a date, handling DST transitions safely.
    
    Args:
        start_time: Start time (e.g., 9:00 AM)
        end_time: End time (e.g., 5:00 PM)
        target_date: Date to generate slots for
        business_tz: Business timezone
        interval_minutes: Interval between slots in minutes
        
    Returns:
        List[datetime]: List of timezone-aware datetime slots
    """
    if isinstance(business_tz, str):
        business_tz = get_timezone(business_tz)
    
    slots = []
    
    # Create start datetime
    current_dt = localize_time_to_date(start_time, target_date, business_tz)
    end_dt = localize_time_to_date(end_time, target_date, business_tz)
    
    # Handle DST transitions
    if is_dst_transition_date(target_date, business_tz):
        logger.info(f"DST transition detected on {target_date} in {business_tz}")
    
    # Generate slots
    while current_dt < end_dt:
        slots.append(current_dt)
        try:
            # Add interval, handling DST transitions
            current_dt += timedelta(minutes=interval_minutes)
        except Exception as e:
            logger.error(f"Error generating time slot: {e}")
            break
    
    return slots


def validate_appointment_time(appointment_dt: datetime, business_tz: Union[str, ZoneInfo],
                            min_hours_before: int = 24) -> tuple[bool, str]:
    """
    Validate that an appointment time is valid for booking.

    Args:
        appointment_dt: Proposed appointment datetime (UTC)
        business_tz: Business timezone
        min_hours_before: Minimum hours before appointment can be booked

    Returns:
        tuple: (is_valid, error_message)
    """
    if isinstance(business_tz, str):
        business_tz = get_timezone(business_tz)

    # Convert to business timezone for validation
    business_dt = convert_to_business_timezone(appointment_dt, business_tz)
    now_business = now_in_timezone(business_tz)

    # Check if in the past
    if business_dt <= now_business:
        return False, "Appointment time cannot be in the past"

    # Check minimum lead time
    min_time = now_business + timedelta(hours=min_hours_before)
    if business_dt < min_time:
        return False, f"Appointment must be at least {min_hours_before} hours in advance"

    return True, ""


def calculate_employee_availability(employee, target_date: date, service,
                                  existing_appointments, interval_minutes: int = 15,
                                  min_hours_before: int = 24) -> List[dict]:
    """
    Calculate availability slots for an employee on a specific date.

    This is a simplified, bulletproof version of availability calculation
    that handles all timezone edge cases properly.

    Args:
        employee: Employee model instance
        target_date: Date to calculate availability for
        service: Service model instance
        existing_appointments: QuerySet of existing appointments
        interval_minutes: Interval between slots in minutes
        min_hours_before: Minimum hours before booking allowed

    Returns:
        List[dict]: List of availability slots with time and availability status
    """
    from employees.models import EmployeeWorkingHours

    # Get business timezone
    business_tz = get_business_timezone(employee.business)

    # Get day of week for working hours lookup
    day_name = target_date.strftime('%A').lower()

    # Get employee working hours for this day
    try:
        working_hours = EmployeeWorkingHours.objects.get(
            employee=employee,
            day=day_name,
            is_active=True
        )

        # Use employee-specific timezone if set, otherwise business timezone
        effective_tz = get_timezone(working_hours.get_effective_timezone())
        start_time = working_hours.start_time
        end_time = working_hours.end_time

    except EmployeeWorkingHours.DoesNotExist:
        # Default working hours (9 AM - 5 PM)
        effective_tz = business_tz
        start_time = time(9, 0)
        end_time = time(17, 0)

    # Generate time slots using utility function
    time_slots = get_safe_time_slots(
        start_time=start_time,
        end_time=end_time,
        target_date=target_date,
        business_tz=effective_tz,
        interval_minutes=interval_minutes
    )

    # Get current time for filtering past slots
    now_business = now_in_timezone(business_tz)
    min_booking_time = now_business + timedelta(hours=min_hours_before)

    # Filter existing appointments for this employee and date
    employee_appointments = existing_appointments.filter(
        employee=employee,
        start_time__date=target_date,
        status__in=['pending', 'confirmed', 'accepted']
    )

    # Calculate service duration
    service_duration = timedelta(minutes=int(service.base_duration.total_seconds() / 60))
    buffer_duration = service.buffer_time
    total_duration = service_duration + buffer_duration

    # Check availability for each slot and return only available ones in ISO format
    available_slots = []

    for slot_dt in time_slots:
        # Check if slot is in the future with minimum lead time
        if slot_dt <= min_booking_time:
            continue  # Skip past times

        # Calculate service end time
        service_end_dt = slot_dt + total_duration

        # Check for conflicts with existing appointments
        is_available = True

        for appt in employee_appointments:
            appt_start = convert_to_business_timezone(appt.start_time, effective_tz)
            appt_end = convert_to_business_timezone(appt.end_time, effective_tz)

            # Check for overlap
            if (slot_dt < appt_end and service_end_dt > appt_start):
                is_available = False
                break

        # Only include available slots, return in ISO 8601 format with timezone offset
        if is_available:
            # Convert to UTC for consistent API response
            slot_utc = convert_to_utc(slot_dt, effective_tz)
            available_slots.append(slot_utc.isoformat())

    return available_slots
