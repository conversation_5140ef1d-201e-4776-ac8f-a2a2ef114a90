# Accounts App

## Overview
The accounts app handles user authentication, authorization, and user management in the Chatbook application. It has been refactored to use a modular architecture with improved security features.

## Special Architecture Note
Unlike other apps in this project, the accounts app uses a modular structure with models split into separate files under the `models/` directory due to its complexity. A compatibility layer (`accounts/models.py`) is provided to maintain traditional import patterns like `from accounts.models import User`. This allows for better organization of complex authentication-related models while maintaining backward compatibility with the rest of the codebase.

## Key Features

### Authentication
- Email or phone number-based authentication using `EmailOrPhoneBackend`
- Social authentication integration
- Multi-factor authentication (MFA) support via django-otp
- Account lockout and rate-limiting protection via django-axes
- Secure password management

### User Model
- Custom User model with email as primary identifier (no username field)
- Properly validated phone number using `PhoneNumberField`
- Role-based permissions system
- Profile data and preferences management

### Security
- Brute-force protection with automatic account lockout via django-axes
- Rate limiting for sensitive operations
- Secure token generation for account recovery
- Session management and device tracking

## Directory Structure

### Models
The models are now modular, organized in separate files under the `models/` directory:
- `user.py`: Core User model with CustomUserManager
- `role.py`: Role model for permission management
- `user_settings.py`: UserSettings for user preferences
- `recovery_token.py`: RecoveryToken for password recovery
- `user_device.py`: UserDevice for device management
- `social_account.py`: SocialAccount for social auth connections

### Authentication
- `backends.py`: Custom authentication backends
  - `EmailOrPhoneBackend`: Authenticate with email or phone
  - `SocialAuthBackend`: Handle social authentication

### Security
- `recovery.py`: Secure token-based account recovery

## Usage Examples

### Authenticating Users
```python
# Authenticate with email
user = authenticate(request, username='<EMAIL>', password='secure_password')

# Authenticate with phone number
user = authenticate(request, username='+***********', password='secure_password')
```

### User Roles
```python
# Check if user has a specific role
if user.has_role('admin'):
    # Allow admin operations

# Get user's primary type based on roles
user_type = user.user_type  # Returns 'admin', 'employee', or 'customer'
```

### Multi-Factor Authentication with django-otp
```python
# Setup TOTP device
from django_otp.plugins.otp_totp.models import TOTPDevice

# Create a TOTP device for the user
device = TOTPDevice.objects.create(user=user, name="Default")
device.confirmed = True
device.save()

# Get the provisioning URI for QR code
totp_url = device.config_url

# Verify a token
from django_otp import match_token
if match_token(user, otp_token):
    # Token is valid
    pass
```

### Account Recovery
```python
from accounts.recovery import generate_recovery_token, verify_recovery_token
# Generate a recovery token
token = generate_recovery_token(user)

# Verify a recovery token
user = verify_recovery_token(token)
```

## Changes from Previous Version

### Improvements
1. **Modular Architecture**: Models are now organized in separate files for better maintainability
2. **Enhanced Security**: 
   - Integrated django-axes for brute-force protection
   - Integrated django-otp for standardized MFA
   - Improved token generation for recovery
   - Better phone number validation
3. **Role-Based System**: Simplified role management with the new Role model
4. **Better Constraints**: Database-level uniqueness constraints for email and phone

### Removed Features
1. **Custom password handling**: Now using Django's built-in secure password functions
2. **Custom account lockout**: Replaced with django-axes for improved security
3. **Custom OTP implementation**: Replaced with django-otp for standardized implementation
4. **Custom middleware**: Removed in favor of django-otp and django-axes middleware

## Dependencies
- django-phonenumber-field: For phone number validation and formatting
- django-otp: For one-time password functionality
- django-axes: For login security and rate limiting

## Database Structure
- User: Core user model with authentication details
- Role: User roles (admin, employee, customer)
- UserSettings: User preferences and settings
- RecoveryToken: Secure password recovery tokens
- UserDevice: Tracks user devices for enhanced security
- SocialAccount: Links to external authentication providers 