import { useState, useCallback, useRef } from 'react'
import { appointmentService, validateAppointmentMove, checkAppointmentConflicts } from '../services/appointmentService'
import { validateTimePosition, alignTimeToResolution } from '../utils/timeGridCalculations'

/**
 * Custom hook for managing appointment update slot resize operations
 * Follows the same confirmation pattern as drag but for duration changes
 */
export const useAppointmentUpdateSlotResize = (workingHoursValidation = {}) => {
  const { isWorkingHourSlot, getWorkingHoursForEmployeeSync } = workingHoursValidation

  // Resize state management
  const [resizeState, setResizeState] = useState({
    isResizing: false,
    resizedAppointment: null,
    originalDuration: null,
    newDuration: null,
    originalStartTime: null,
    originalEndTime: null,
    newStartTime: null,
    newEndTime: null,
    showConfirmation: false,
    resizeDirection: 'end', // 'start' or 'end'
    lastSnappedPosition: null,
    finalResizeOffset: { x: 0, y: 0 }
  })

  const [isUpdating, setIsUpdating] = useState(false)
  const [error, setError] = useState(null)
  
  // Refs for tracking resize state
  const resizeTimeoutRef = useRef(null)
  const lastValidationRef = useRef(null)

  // Handle resize start
  const handleResizeStart = useCallback((appointment, direction = 'end') => {
    console.log('🎯 UpdateSlotResize started:', { appointmentId: appointment.id, direction })
    
    const originalStart = new Date(appointment.start)
    const originalEnd = new Date(appointment.end || appointment.endTime)
    const originalDuration = originalEnd - originalStart
    
    setResizeState(prev => ({
      ...prev,
      isResizing: true,
      resizedAppointment: appointment,
      originalDuration: originalDuration,
      originalStartTime: originalStart,
      originalEndTime: originalEnd,
      resizeDirection: direction,
      showConfirmation: false
    }))
    
    setError(null)
  }, [])

  // Handle resize movement (real-time updates during resize)
  const handleResizeMove = useCallback((appointment, newEndTime, resizeInfo) => {
    if (!resizeState.isResizing || appointment.id !== resizeState.resizedAppointment?.id) {
      return
    }

    // Align time to resolution for precise snapping
    const alignedEndTime = alignTimeToResolution(newEndTime, resizeInfo.timeResolution || 15)
    
    // Handle both start and end time changes based on resize direction
    let effectiveStartTime = new Date(appointment.start)
    let effectiveEndTime = alignedEndTime
    
    if (resizeInfo.direction === 'start' && resizeInfo.newStartTime) {
      // Top resize - align the new start time and keep original end time
      effectiveStartTime = alignTimeToResolution(resizeInfo.newStartTime, resizeInfo.timeResolution || 15)
      effectiveEndTime = new Date(appointment.end || appointment.endTime)
    }
    
    // Calculate new duration
    const newDuration = effectiveEndTime - effectiveStartTime

    // Update resize state with aligned time and movement info
    setResizeState(prev => ({
      ...prev,
      newEndTime: effectiveEndTime,
      newStartTime: effectiveStartTime,
      newDuration: newDuration,
      lastSnappedPosition: resizeInfo.offset
    }))

    // Debounced validation to avoid excessive API calls
    if (resizeTimeoutRef.current) {
      clearTimeout(resizeTimeoutRef.current)
    }

    resizeTimeoutRef.current = setTimeout(() => {
      validateResizePosition(appointment, effectiveEndTime, resizeInfo, effectiveStartTime)
    }, 300) // 300ms debounce

  }, [resizeState.isResizing, resizeState.resizedAppointment])

  // Handle resize completion
  const handleResizeEnd = useCallback((appointment, newEndTime, resizeInfo) => {
    console.log('🎯 UpdateSlotResize ended:', { 
      appointmentId: appointment.id, 
      originalEndTime: resizeState.originalEndTime,
      newEndTime: newEndTime,
      completed: resizeInfo.completed 
    })

    if (!resizeInfo.completed) {
      // Resize was cancelled - reset state
      resetResizeState()
      return
    }

    // Calculate new duration and check for significant changes
    const effectiveStartTime = resizeInfo.newStartTime ? new Date(resizeInfo.newStartTime) : new Date(appointment.start)
    const newEndTimeDate = new Date(newEndTime)
    const newDuration = newEndTimeDate - effectiveStartTime
    const originalDuration = resizeState.originalDuration || 0
    
    const durationDifference = Math.abs(newDuration - originalDuration)
    const minChangeThreshold = 5 * 60 * 1000 // 5 minutes in milliseconds

    console.log('🎯 Duration difference check:', {
      originalDuration: originalDuration,
      newDuration: newDuration,
      durationDifference: durationDifference,
      minChangeThreshold: minChangeThreshold,
      shouldShowConfirmation: durationDifference >= minChangeThreshold
    })

    if (durationDifference < minChangeThreshold) {
      console.log('🎯 No significant duration change, cancelling resize')
      resetResizeState()
      return
    }

    // Show confirmation dialog for significant changes
    console.log('🎯 Showing update slot resize confirmation dialog')
    setResizeState(prev => ({
      ...prev,
      resizedAppointment: appointment,
      newStartTime: effectiveStartTime,
      newEndTime: newEndTimeDate,
      newDuration: newDuration,
      showConfirmation: true,
      finalResizeOffset: resizeInfo.offset || { x: 0, y: 0 }
    }))

  }, [resizeState.originalDuration, resizeState.originalEndTime])

  // Validate resize position (working hours, conflicts, etc.)
  const validateResizePosition = useCallback(async (appointment, newEndTime, resizeInfo = {}, newStartTime = null) => {
    try {
      const startTime = newStartTime ? new Date(newStartTime) : new Date(appointment.start)
      const endTime = new Date(newEndTime)
      const duration = endTime - startTime

      // Check minimum duration (15 minutes)
      if (duration < 15 * 60 * 1000) {
        setError('Minimum appointment duration is 15 minutes')
        return false
      }

      // Check maximum duration (8 hours)
      if (duration > 8 * 60 * 60 * 1000) {
        setError('Maximum appointment duration is 8 hours')
        return false
      }

      // First validate time position against resolution and business rules
      const timeValidation = validateTimePosition(endTime, {
        timeResolution: resizeInfo.timeResolution || 15,
        displayHourStart: resizeInfo.displayHourStart || 0,
        displayHourEnd: resizeInfo.displayHourEnd || 24,
        showWorkingHours: resizeInfo.showWorkingHours || true
      })

      if (!timeValidation.isValid) {
        setError(timeValidation.message)
        return false
      }

      // Enhanced working hours validation using provided functions
      if (isWorkingHourSlot && getWorkingHoursForEmployeeSync) {
        const employeeWorkingHours = getWorkingHoursForEmployeeSync(appointment.employeeId)
        
        if (employeeWorkingHours) {
          const endHour = endTime.getHours()
          const endMinute = endTime.getMinutes()
          const endTotalMinutes = endHour * 60 + endMinute
          
          // Check if new end time is within working hours
          if (endTotalMinutes < employeeWorkingHours.startMinutes || 
              endTotalMinutes > employeeWorkingHours.endMinutes) {
            const employeeName = employeeWorkingHours.employee?.name || 'Employee'
            const startTime = `${Math.floor(employeeWorkingHours.startMinutes/60)}:${String(employeeWorkingHours.startMinutes%60).padStart(2, '0')}`
            const endTime = `${Math.floor(employeeWorkingHours.endMinutes/60)}:${String(employeeWorkingHours.endMinutes%60).padStart(2, '0')}`
            
            setError(`${employeeName} is only available from ${startTime} to ${endTime}`)
            return false
          }
        }
      }

      // Validate business rules using existing service
      const validation = validateAppointmentMove(
        appointment,
        startTime.toISOString(),
        endTime.toISOString(),
        appointment.employeeId
      )

      if (!validation.isValid) {
        setError(validation.message)
        return false
      }

      // Check for conflicts with other appointments
      const conflictCheck = await checkAppointmentConflicts(
        appointment,
        startTime.toISOString(),
        endTime.toISOString(),
        appointment.employeeId
      )

      if (conflictCheck.hasConflict) {
        setError('Appointment conflicts with existing booking')
        return false
      }

      // Clear any previous errors
      setError(null)
      return true

    } catch (err) {
      console.error('❌ UpdateSlotResize validation error:', err)
      setError('Failed to validate appointment resize')
      return false
    }
  }, [isWorkingHourSlot, getWorkingHoursForEmployeeSync])

  // Confirm appointment resize
  const confirmResize = useCallback(async (notifyCustomer = true) => {
    if (!resizeState.resizedAppointment || !resizeState.newEndTime) {
      console.error('❌ No appointment or new end time to confirm')
      return
    }

    setIsUpdating(true)
    setError(null)

    try {
      const appointment = resizeState.resizedAppointment
      const newEndTime = new Date(resizeState.newEndTime)
      const effectiveStartTime = resizeState.newStartTime ? new Date(resizeState.newStartTime) : new Date(appointment.start)



      // Calculate the new total duration in minutes for logging
      const newTotalDurationMinutes = Math.round((newEndTime - effectiveStartTime) / (1000 * 60))

      // Update appointment via API - send only the fields that need updating
      // IMPORTANT: We need to update the service duration in appointment_services
      // because total_duration is calculated from services, not from start_time/end_time difference
      const appointmentUpdateData = {
        // Update start time if it changed (top resize)
        start_time: effectiveStartTime.toISOString().slice(0, 19) + '+0000',
        // Update end time - use Django format YYYY-MM-DDThh:mm:ss+HHMM
        end_time: newEndTime.toISOString().slice(0, 19) + '+0000',
        // Keep existing status and notification preferences
        status: appointment.status || 'confirmed',
        // Keep existing notes
        notes_from_customer: appointment.notes || appointment.description || ''
      }

      // Get services from either appointment_services or services field
      const appointmentServices = appointment.appointment_services || appointment.services || []
      
      // Update the appointment services duration to match the new time range
      if (appointmentServices && appointmentServices.length > 0) {
        appointmentUpdateData.appointment_services = appointmentServices.map((service, index) => {
          // Calculate new service duration (excluding buffer time)
          const currentBufferTime = service.buffer_time || 0
          const newServiceDuration = Math.max(15, newTotalDurationMinutes - currentBufferTime) // Minimum 15 minutes
          
          const updatedService = {
            // Essential fields for backend update
            service: parseInt(service.service), // Ensure service ID is a number
            duration: parseInt(newServiceDuration), // Ensure duration is a number
            buffer_time: parseInt(currentBufferTime),
            base_price: service.base_price, // Keep as string for decimal precision
            price_override: service.price_override,
            quantity: parseInt(service.quantity || 1),
            notes: service.notes || ''
            // Note: Don't include the AppointmentService 'id' field as it confuses the backend update logic
          }
          
          return updatedService
        })
      }
      
      // If notifyCustomer is provided, include it
      if (notifyCustomer !== undefined) {
        appointmentUpdateData.notify_customer = notifyCustomer
      }

      // Use patch method for updates
      const updatedAppointment = await appointmentService.patchAppointment(
        appointment.id,
        appointmentUpdateData
      )



      // Reset resize state
      resetResizeState()

      // Return updated appointment for parent component to handle
      return updatedAppointment

    } catch (err) {
      console.error('❌ Failed to update slot resize appointment:', err)
      setError(err.message || 'Failed to resize appointment')
      throw err
    } finally {
      setIsUpdating(false)
    }
  }, [resizeState.resizedAppointment, resizeState.newEndTime, resizeState.originalEndTime, resizeState.newDuration])

  // Cancel appointment resize
  const cancelResize = useCallback(() => {
    resetResizeState()
  }, [])

  // Reset resize state
  const resetResizeState = useCallback(() => {
    if (resizeTimeoutRef.current) {
      clearTimeout(resizeTimeoutRef.current)
    }

    setResizeState({
      isResizing: false,
      resizedAppointment: null,
      originalDuration: null,
      newDuration: null,
      originalStartTime: null,
      originalEndTime: null,
      newStartTime: null,
      newEndTime: null,
      showConfirmation: false,
      resizeDirection: 'end',
      lastSnappedPosition: null,
      finalResizeOffset: { x: 0, y: 0 }
    })
    
    setError(null)
  }, [])

  // Calculate duration difference for display
  const getDurationDifference = useCallback(() => {
    if (!resizeState.originalDuration || !resizeState.newDuration) return null

    const diff = resizeState.newDuration - resizeState.originalDuration
    const diffMinutes = Math.round(diff / (1000 * 60))
    const hours = Math.floor(Math.abs(diffMinutes) / 60)
    const remainingMinutes = Math.abs(diffMinutes) % 60

    return {
      minutes: diffMinutes,
      hours,
      remainingMinutes,
      isLonger: diffMinutes > 0,
      formatted: hours > 0 
        ? `${hours}h ${remainingMinutes}m` 
        : `${Math.abs(diffMinutes)}m`
    }
  }, [resizeState.originalDuration, resizeState.newDuration])

  // Check if appointment has actually been resized
  const hasAppointmentResized = useCallback(() => {
    if (!resizeState.originalDuration || !resizeState.newDuration) return false
    
    const durationDifference = Math.abs(resizeState.newDuration - resizeState.originalDuration)
    const minChangeThreshold = 5 * 60 * 1000 // 5 minutes
    
    return durationDifference >= minChangeThreshold
  }, [resizeState.originalDuration, resizeState.newDuration])

  // Get current resize validation status
  const getResizeValidation = useCallback(() => {
    return {
      isValid: !error,
      error: error,
      hasConflict: error?.includes('conflict'),
      isOutsideHours: error?.includes('available') || error?.includes('working hours'),
      isDurationInvalid: error?.includes('duration')
    }
  }, [error])

  return {
    // State
    resizeState,
    isUpdating,
    error,
    
    // Actions
    handleResizeStart,
    handleResizeMove,
    handleResizeEnd,
    confirmResize,
    cancelResize,
    resetResizeState,
    
    // Computed values
    getDurationDifference,
    hasAppointmentResized,
    getResizeValidation,
    
    // Helper flags
    isResizing: resizeState.isResizing,
    showConfirmation: resizeState.showConfirmation,
    resizedAppointment: resizeState.resizedAppointment,
    originalDuration: resizeState.originalDuration,
    newDuration: resizeState.newDuration,
    originalStartTime: resizeState.originalStartTime,
    originalEndTime: resizeState.originalEndTime,
    newStartTime: resizeState.newStartTime,
    newEndTime: resizeState.newEndTime,
    resizeDirection: resizeState.resizeDirection,
    finalResizeOffset: resizeState.finalResizeOffset
  }
} 