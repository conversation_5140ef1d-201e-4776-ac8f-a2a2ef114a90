import { useState, useCallback } from 'react'

export const useAddCustomer = (onAddCustomer) => {
  const [showModal, setShowModal] = useState(false)
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    gender: '',
    birthday: '',
    address: {
      street: '',
      city: '',
      postalCode: '',
      country: ''
    }
  })
  const [errors, setErrors] = useState({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const openModal = () => {
    setShowModal(true)
    resetForm()
  }

  const closeModal = () => {
    setShowModal(false)
    resetForm()
  }

  const resetForm = useCallback(() => {
    setFormData({
      name: '',
      email: '',
      phone: '',
      gender: '',
      birthday: '',
      address: {
        street: '',
        city: '',
        postalCode: '',
        country: ''
      }
    })
    setErrors({})
    setIsSubmitting(false)
  }, [])

  const updateFormData = (field, value) => {
    if (field.includes('.')) {
      const [parent, child] = field.split('.')
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: value
        }
      }))
    } else {
      setFormData(prev => ({
        ...prev,
        [field]: value
      }))
    }
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }))
    }
  }

  const validateForm = () => {
    const newErrors = {}

    if (!formData.name.trim()) {
      newErrors.name = 'Name is required'
    }

    if (!formData.email.trim()) {
      newErrors.email = 'Email is required'
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Please enter a valid email address'
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required'
    }

    // Gender and address fields are now optional - no validation required

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      // Create new customer object
      const newCustomer = {
        id: Date.now(), // In real app, this would come from backend
        ...formData,
        avatar: 'https://via.placeholder.com/40',
        status: 'active',
        joinDate: new Date().toISOString().split('T')[0],
        lastOrder: null,
        totalOrders: 0
      }

      onAddCustomer(newCustomer)
      closeModal()
    } catch (error) {
      console.error('Error adding customer:', error)
      setErrors({ submit: 'Failed to add customer. Please try again.' })
    } finally {
      setIsSubmitting(false)
    }
  }

  return {
    showModal,
    formData,
    errors,
    isSubmitting,
    openModal,
    closeModal,
    resetForm,
    updateFormData,
    handleSubmit
  }
} 