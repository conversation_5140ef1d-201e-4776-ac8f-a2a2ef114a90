import { useState, useEffect } from 'react'
import apiClient from '../../../employees/services/apiClient'
import { API_ENDPOINTS } from '../constants'

export const useServiceData = () => {
  const [categories, setCategories] = useState([])
  const [services, setServices] = useState([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)

  const fetchData = async () => {
    try {
      setIsLoading(true)
      setError(null)
      
      const [categoriesResponse, servicesResponse] = await Promise.all([
        apiClient.get(API_ENDPOINTS.CATEGORIES),
        apiClient.get(API_ENDPOINTS.SERVICES)
      ])
      
      const categoriesData = categoriesResponse.data.results || categoriesResponse.data || []
      const servicesData = servicesResponse.data.results || servicesResponse.data || []
      
      setCategories(categoriesData)
      setServices(servicesData)
      
    } catch (error) {
      console.error('Failed to fetch data:', error)
      setError('Failed to load services and categories')
    } finally {
      setIsLoading(false)
    }
  }

  const deleteCategory = async (categoryId) => {
    if (window.confirm('Are you sure you want to delete this category? This will also affect all services in this category.')) {
      try {
        await apiClient.delete(`${API_ENDPOINTS.CATEGORIES}${categoryId}/`)
        await fetchData()
      } catch (error) {
        console.error('Failed to delete category:', error)
        alert('Failed to delete category')
      }
    }
  }

  const deleteService = async (serviceId) => {
    if (window.confirm('Are you sure you want to delete this service?')) {
      try {
        await apiClient.delete(`${API_ENDPOINTS.SERVICES}${serviceId}/`)
        await fetchData()
      } catch (error) {
        console.error('Failed to delete service:', error)
        alert('Failed to delete service')
      }
    }
  }

  const getCategoryServices = (categoryId) => {
    return services.filter(service => service.category === categoryId)
  }

  useEffect(() => {
    fetchData()
  }, [])

  return {
    categories,
    services,
    isLoading,
    error,
    fetchData,
    deleteCategory,
    deleteService,
    getCategoryServices
  }
} 