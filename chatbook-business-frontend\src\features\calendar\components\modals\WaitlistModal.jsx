import React, { useState, useEffect } from 'react'
import { 
  XMarkIcon, 
  ChevronLeftIcon, 
  ChevronRightIcon,
  CheckIcon,
  PlusIcon,
  MinusIcon,
  MagnifyingGlassIcon,
  CalendarDaysIcon,
  ClockIcon,
  UserIcon,
  PhoneIcon,
  EnvelopeIcon,
  ChatBubbleLeftRightIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ExclamationCircleIcon
} from '@heroicons/react/24/outline'
import { waitlistService } from '../../services/waitlistService'

/**
 * WaitlistModal - Desktop-optimized waitlist creation modal
 * Multi-step modal with progress indicator and desktop-friendly navigation
 */
const WaitlistModal = ({
  waitlistHook,
  onSuccess,
  onCancel,
  allowCustomerWaitlistAddition = true // Default to enabled for backwards compatibility
}) => {
  const {
    isOpen,
    currentStep,
    formData,
    services,
    employees,
    selectedService,
    selectedEmployee,
    canAddMoreWindows,
    closeWaitlist,
    updateField,
    selectService,
    selectEmployee,
    selectCustomer,
    selectExistingCustomer,
    clearCustomerSelection,
    addAvailabilityWindow,
    removeAvailabilityWindow,
    updateAvailabilityWindow,
    submitWaitlist,
    nextStep,
    previousStep
  } = waitlistHook

  // Local state for customer selection
  const [allCustomers, setAllCustomers] = useState([])
  const [loadingCustomers, setLoadingCustomers] = useState(false)
  const [showCreateNew, setShowCreateNew] = useState(false)
  
  // Local state for collapsible categories
  const [expandedCategories, setExpandedCategories] = useState({})

  // Load all customers when modal opens
  useEffect(() => {
    if (isOpen && allCustomers.length === 0) {
      // Check authentication status first
      const authToken = localStorage.getItem('auth_token')
      
      if (!authToken) {
        console.error('❌ No auth token found - customers cannot be loaded')
        setAllCustomers([])
        return
      }
      
      loadAllCustomers()
    }
  }, [isOpen])

  const loadAllCustomers = async () => {
    setLoadingCustomers(true)
    try {
      const customers = await waitlistService.getAllCustomers()
      setAllCustomers(customers)
    } catch (error) {
      console.error('❌ WaitlistModal: Failed to load customers:', error)
      setAllCustomers([])
    } finally {
      setLoadingCustomers(false)
    }
  }

  const handleSelectExistingCustomer = (customer) => {
    setShowCreateNew(false)
    selectExistingCustomer(customer)
    // Clear any customer error when a valid selection is made
    updateField('customer_error', '')
  }

  const handleCreateNew = () => {
    setShowCreateNew(true)
    clearCustomerSelection()
    // Clear any customer error when expanding create new form
    updateField('customer_error', '')
  }

  // Toggle category expansion
  const toggleCategory = (categoryName) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryName]: !prev[categoryName]
    }))
  }

  // Initialize expanded categories when services load
  useEffect(() => {
    if (services.length > 0) {
      const servicesByCategory = services.reduce((acc, service) => {
        const categoryName = service.category_name || service.category?.name || 'General Services'
        if (!acc[categoryName]) {
          acc[categoryName] = []
        }
        acc[categoryName].push(service)
        return acc
      }, {})
      
      // Initialize all categories as expanded by default
      const initialExpanded = {}
      Object.keys(servicesByCategory).forEach(categoryName => {
        initialExpanded[categoryName] = true
      })
      setExpandedCategories(initialExpanded)
    }
  }, [services])

  // Clear customer error when create new customer form is properly filled
  useEffect(() => {
    if (showCreateNew && formData.customer_name.trim() && formData.phone_number.trim() && formData.email.trim()) {
      updateField('customer_error', '')
    }
  }, [showCreateNew, formData.customer_name, formData.phone_number, formData.email, updateField])

  if (!isOpen) return null

  // Show disabled state when waitlist addition is not allowed
  if (!allowCustomerWaitlistAddition) {
    return (
      <div 
        className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
        onClick={(e) => e.target === e.currentTarget && onCancel?.()}
      >
        <div className="bg-white rounded-lg shadow-2xl w-full max-w-md">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-xl font-semibold text-gray-900">Waitlist Unavailable</h2>
            <button
              onClick={onCancel}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors"
            >
              <XMarkIcon className="h-5 w-5" />
            </button>
          </div>
          
          {/* Content */}
          <div className="p-6 text-center">
            <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-gray-100 mb-4">
              <ExclamationCircleIcon className="h-6 w-6 text-gray-600" />
            </div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Customer Waitlist Addition Disabled
            </h3>
            <p className="text-sm text-gray-600 mb-6">
              The ability for customers to add themselves to the waitlist has been disabled in your booking settings. 
              Contact your administrator to enable this feature.
            </p>
            <button
              onClick={onCancel}
              className="w-full bg-blue-600 text-white py-2 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors"
            >
              Close
            </button>
          </div>
        </div>
      </div>
    )
  }

  // Handle modal close
  const handleClose = () => {
    if (formData.isLoading) return
    closeWaitlist()
    onCancel?.()
  }

  // Handle backdrop click
  const handleBackdropClick = (e) => {
    if (e.target === e.currentTarget) {
      handleClose()
    }
  }

  // Handle form submission
  const handleSubmit = async () => {
    const result = await submitWaitlist()
    if (result.success) {
      onSuccess?.(result.data)
      closeWaitlist()
    }
  }

  // Handle step navigation
  const handleNext = () => {
    // Validate service step before proceeding
    if (currentStep === 'service') {
      if (!formData.service_ids || formData.service_ids.length === 0) {
        updateField('service_error', 'Please select at least one service')
        return
      } else {
        updateField('service_error', '')
      }
      
      // Check if staff selection has been made
      // Note: employee_ids.length === 0 means "Any Available Staff" is selected
      // We need to ensure that a staff selection choice has been explicitly made
      const hasStaffSelection = formData.hasStaffSelection || formData.employee_ids.length > 0
      if (!hasStaffSelection) {
        updateField('staff_error', 'Please select a preferred staff member or choose "Any Available Staff"')
        return
      } else {
        updateField('staff_error', '')
      }
    }
    
    // Validate customer step before proceeding
    if (currentStep === 'customer') {
      const hasExistingCustomer = formData.hasSelectedExistingCustomer
      const isCreatingNew = showCreateNew && formData.customer_name.trim() && formData.phone_number.trim() && formData.email.trim()
      
      if (!hasExistingCustomer && !isCreatingNew) {
        // Show error or prevent navigation
        updateField('customer_error', 'Please select an existing customer or create a new one')
        return
      } else {
        // Clear any previous error
        updateField('customer_error', '')
      }
    }
    
    if (isLastStep()) {
      handleSubmit()
    } else {
      nextStep()
    }
  }

  const handlePrevious = () => {
    if (!isFirstStep()) {
      previousStep()
    }
  }

  // Step utilities
  const steps = ['service', 'availability', 'customer', 'confirmation']
  const currentStepIndex = steps.indexOf(currentStep)
  const isFirstStep = () => currentStepIndex === 0
  const isLastStep = () => currentStepIndex === steps.length - 1

  // Step titles for UI
  const stepTitles = {
    service: 'Select Service',
    availability: 'Set Availability',
    customer: 'Customer Info',
    confirmation: 'Review & Submit'
  }

  // Group services by category
  const servicesByCategory = services.reduce((acc, service) => {
    const categoryName = service.category_name || service.category?.name || 'General Services'
    if (!acc[categoryName]) {
      acc[categoryName] = []
    }
    acc[categoryName].push(service)
    return acc
  }, {})

  // Render Service Selection Step
  const renderServiceStep = () => (
    <div className="space-y-6">
      <div>
        <div className="flex items-center justify-between mb-2">
          <h3 className="text-xl font-semibold text-gray-900">Choose Service</h3>
          <button
            onClick={() => {
              const allExpanded = Object.values(expandedCategories).every(expanded => expanded)
              const newState = {}
              Object.keys(servicesByCategory).forEach(categoryName => {
                newState[categoryName] = !allExpanded
              })
              setExpandedCategories(newState)
            }}
            className="text-sm text-blue-600 hover:text-blue-800 font-medium transition-colors"
          >
            {Object.values(expandedCategories).every(expanded => expanded) ? 'Collapse All' : 'Expand All'}
          </button>
        </div>
        <p className="text-gray-600 mb-6">Select the service you'd like to be added to the waitlist for</p>
        
        <div className="space-y-4">
          {Object.entries(servicesByCategory).map(([categoryName, categoryServices]) => {
            const isExpanded = expandedCategories[categoryName]
            const selectedInCategory = categoryServices.some(service => 
              formData.service_ids.includes(service.id)
            )
            
            return (
              <div key={categoryName} className="border border-gray-200 rounded-xl overflow-hidden">
                <button
                  onClick={() => toggleCategory(categoryName)}
                  className={`
                    w-full px-6 py-4 text-left bg-gray-50 hover:bg-gray-100 transition-all duration-200 border-b border-gray-200
                    ${selectedInCategory ? 'bg-blue-50 border-blue-200' : ''}
                  `}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex items-center">
                      <h4 className={`text-lg font-semibold ${
                        selectedInCategory ? 'text-blue-900' : 'text-gray-800'
                      }`}>
                        {categoryName}
                      </h4>
                      {selectedInCategory && (
                        <div className="ml-3 flex items-center px-2 py-1 bg-blue-100 text-blue-800 rounded-full text-xs font-medium">
                          <CheckIcon className="h-3 w-3 mr-1" />
                          Selected
                        </div>
                      )}
                    </div>
                    <div className="flex items-center space-x-2">
                      <span className="text-sm text-gray-500">
                        {categoryServices.length} service{categoryServices.length !== 1 ? 's' : ''}
                      </span>
                      {isExpanded ? (
                        <ChevronUpIcon className="h-5 w-5 text-gray-500" />
                      ) : (
                        <ChevronDownIcon className="h-5 w-5 text-gray-500" />
                      )}
                    </div>
                  </div>
                </button>
                
                <div className={`
                  transition-all duration-300 ease-in-out overflow-hidden
                  ${isExpanded ? 'max-h-[2000px] opacity-100' : 'max-h-0 opacity-0'}
                `}>
                  <div className="p-4 bg-white">
                    <div className="grid gap-3">
                      {categoryServices.map((service) => (
                        <button
                          key={service.id}
                          onClick={() => selectService(service.id)}
                          className={`
                            w-full p-5 text-left rounded-xl border-2 transition-all duration-200 group
                            ${formData.service_ids.includes(service.id)
                              ? 'border-blue-500 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-md ring-1 ring-blue-200'
                              : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50 hover:shadow-sm'
                            }
                          `}
                        >
                          <div className="flex justify-between items-start">
                            <div className="flex-1">
                              <div className="flex items-start justify-between">
                                <div>
                                  <div className={`font-semibold text-lg ${
                                    formData.service_ids.includes(service.id) ? 'text-blue-900' : 'text-gray-900'
                                  }`}>
                                    {service.name}
                                  </div>
                                  {service.description && (
                                    <p className={`text-sm mt-1 ${
                                      formData.service_ids.includes(service.id) ? 'text-blue-700' : 'text-gray-600'
                                    }`}>
                                      {service.description}
                                    </p>
                                  )}
                                </div>
                                {formData.service_ids.includes(service.id) && (
                                  <div className="ml-4 flex-shrink-0">
                                    <CheckIcon className="h-6 w-6 text-blue-600" />
                                  </div>
                                )}
                              </div>
                              <div className="mt-3 flex items-center space-x-4">
                                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                                  formData.service_ids.includes(service.id) 
                                    ? 'bg-blue-100 text-blue-800' 
                                    : 'bg-gray-100 text-gray-700 group-hover:bg-blue-100 group-hover:text-blue-800'
                                }`}>
                                  <ClockIcon className="h-4 w-4 mr-1" />
                                  {service.duration} mins
                                </div>
                                <div className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${
                                  formData.service_ids.includes(service.id) 
                                    ? 'bg-green-100 text-green-800' 
                                    : 'bg-gray-100 text-gray-700 group-hover:bg-green-100 group-hover:text-green-800'
                                }`}>
                                  ${service.price}
                                </div>
                              </div>
                            </div>
                          </div>
                        </button>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>
        
        {formData.errors.service_ids && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600 flex items-center">
              <ExclamationCircleIcon className="h-4 w-4 mr-2" />
              {formData.errors.service_ids}
            </p>
          </div>
        )}
      </div>

      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Preferred Staff</h3>
        <p className="text-gray-600 mb-6">Choose a specific staff member or let us assign the next available</p>
        
        <div className="space-y-4">
          <button
            onClick={() => {
              selectEmployee(null)
              updateField('hasStaffSelection', true)
              updateField('staff_error', '')
            }}
            className={`
              w-full p-5 text-left rounded-xl border-2 transition-all duration-200 group
              ${formData.employee_ids.length === 0 && formData.hasStaffSelection
                ? 'border-blue-500 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-md ring-1 ring-blue-200'
                : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50 hover:shadow-sm'
              }
            `}
          >
            <div className="flex justify-between items-center">
              <div className="flex items-center">
                <div className={`w-12 h-12 rounded-full flex items-center justify-center mr-4 ${
                  formData.employee_ids.length === 0 && formData.hasStaffSelection
                    ? 'bg-blue-100 text-blue-600' 
                    : 'bg-gray-100 text-gray-600 group-hover:bg-blue-100 group-hover:text-blue-600'
                }`}>
                  <UserIcon className="h-6 w-6" />
                </div>
                <div>
                  <div className={`font-semibold text-lg ${
                    formData.employee_ids.length === 0 && formData.hasStaffSelection ? 'text-blue-900' : 'text-gray-900'
                  }`}>
                    Any Available Staff
                  </div>
                  <div className={`text-sm ${
                    formData.employee_ids.length === 0 && formData.hasStaffSelection ? 'text-blue-700' : 'text-gray-500'
                  }`}>
                    First available stylist will be assigned
                  </div>
                </div>
              </div>
              {formData.employee_ids.length === 0 && formData.hasStaffSelection && (
                <CheckIcon className="h-6 w-6 text-blue-600" />
              )}
            </div>
          </button>
          
          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <div className="w-full border-t border-gray-200" />
            </div>
            <div className="relative flex justify-center text-sm">
              <span className="px-4 bg-white text-gray-500">Or choose a specific stylist</span>
            </div>
          </div>
          
          <div className="grid gap-3">
            {employees.map((employee) => (
              <button
                key={employee.id}
                onClick={() => {
                  selectEmployee(employee.id)
                  updateField('hasStaffSelection', true)
                  updateField('staff_error', '')
                }}
                className={`
                  w-full p-5 text-left rounded-xl border-2 transition-all duration-200 group
                  ${formData.employee_ids.includes(employee.id)
                    ? 'border-blue-500 bg-gradient-to-r from-blue-50 to-indigo-50 shadow-md ring-1 ring-blue-200'
                    : 'border-gray-200 hover:border-blue-300 hover:bg-gray-50 hover:shadow-sm'
                  }
                `}
              >
                <div className="flex justify-between items-center">
                  <div className="flex items-center">
                    <div className={`w-12 h-12 rounded-full flex items-center justify-center mr-4 text-white font-semibold ${
                      formData.employee_ids.includes(employee.id) 
                        ? 'ring-2 ring-blue-300' 
                        : 'group-hover:ring-2 group-hover:ring-blue-200'
                    }`}
                    style={{ backgroundColor: employee.color || '#6b7280' }}>
                      {employee.avatar || employee.name?.charAt(0) || 'S'}
                    </div>
                    <div>
                      <div className={`font-semibold text-lg ${
                        formData.employee_ids.includes(employee.id) ? 'text-blue-900' : 'text-gray-900'
                      }`}>
                        {employee.name || employee.full_name}
                      </div>
                      <div className={`text-sm ${
                        formData.employee_ids.includes(employee.id) ? 'text-blue-700' : 'text-gray-500'
                      }`}>
                        {employee.title || employee.stylist_level_display || 'Stylist'}
                      </div>
                    </div>
                  </div>
                  {formData.employee_ids.includes(employee.id) && (
                    <CheckIcon className="h-6 w-6 text-blue-600" />
                  )}
                </div>
              </button>
            ))}
          </div>
        </div>
        
        {formData.errors.staff_error && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600 flex items-center">
              <ExclamationCircleIcon className="h-4 w-4 mr-2" />
              {formData.errors.staff_error}
            </p>
          </div>
        )}
      </div>
    </div>
  )

  // Render Availability Step
  const renderAvailabilityStep = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Set Your Availability</h3>
        <p className="text-gray-600 mb-6">
          Tell us when you're available and we'll notify you when a spot opens up during these times.
        </p>
        
        <div className="space-y-5">
          {formData.availabilityWindows.map((window, index) => (
            <div key={index} className="p-6 border-2 border-gray-200 rounded-xl bg-gradient-to-r from-gray-50 to-white">
              <div className="flex items-center justify-between mb-4">
                <h4 className="font-semibold text-gray-900 flex items-center">
                  <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-bold mr-3">
                    {index + 1}
                  </div>
                  Available Time Window
                </h4>
                {formData.availabilityWindows.length > 1 && (
                  <button
                    onClick={() => removeAvailabilityWindow(index)}
                    className="p-2 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-lg transition-all"
                  >
                    <MinusIcon className="h-5 w-5" />
                  </button>
                )}
              </div>
              
              <div className="grid grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <CalendarDaysIcon className="h-4 w-4 inline mr-2" />
                    Date
                  </label>
                  <input
                    type="date"
                    value={window.date}
                    onChange={(e) => updateAvailabilityWindow(index, 'date', e.target.value)}
                    className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                  />
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    <ClockIcon className="h-4 w-4 inline mr-2" />
                    Preferred Time
                  </label>
                  <select
                    value={window.time}
                    onChange={(e) => updateAvailabilityWindow(index, 'time', e.target.value)}
                    className="w-full px-4 py-3 border-2 border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all"
                  >
                    <option value="Anytime">Anytime during business hours</option>
                    <option value="09:00">9:00 AM</option>
                    <option value="10:00">10:00 AM</option>
                    <option value="11:00">11:00 AM</option>
                    <option value="12:00">12:00 PM</option>
                    <option value="13:00">1:00 PM</option>
                    <option value="14:00">2:00 PM</option>
                    <option value="15:00">3:00 PM</option>
                    <option value="16:00">4:00 PM</option>
                    <option value="17:00">5:00 PM</option>
                  </select>
                </div>
              </div>
            </div>
          ))}
          
          {canAddMoreWindows && (
            <button
              onClick={addAvailabilityWindow}
              className="w-full p-6 border-2 border-dashed border-gray-300 rounded-xl text-gray-600 hover:border-blue-400 hover:text-blue-600 hover:bg-blue-50 transition-all flex items-center justify-center group"
            >
              <div className="flex items-center">
                <div className="w-10 h-10 bg-gray-200 group-hover:bg-blue-100 rounded-full flex items-center justify-center mr-3 transition-all">
                  <PlusIcon className="h-5 w-5 group-hover:text-blue-600" />
                </div>
                <span className="font-medium">Add Another Available Time</span>
              </div>
            </button>
          )}
        </div>
        
        {formData.errors.availability && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600 flex items-center">
              <ExclamationCircleIcon className="h-4 w-4 mr-2" />
              {formData.errors.availability}
            </p>
          </div>
        )}
      </div>
    </div>
  )

  // Render Customer Step
  const renderCustomerStep = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Customer Information</h3>
        <p className="text-gray-600 mb-6">Select an existing customer or create a new one for the waitlist</p>
        
        <div className="space-y-4">
          {/* Existing Customers Section */}
          <div>
            <h4 className="font-medium text-gray-900 mb-3">Select Existing Customer</h4>
            {loadingCustomers ? (
              <div className="flex items-center justify-center py-8">
                <div className="animate-spin rounded-full h-6 w-6 border-2 border-blue-600 border-t-transparent"></div>
                <span className="ml-2 text-gray-600">Loading customers...</span>
              </div>
            ) : (
              <div className="max-h-60 overflow-y-auto border border-gray-200 rounded-md">
                {allCustomers.length === 0 ? (
                  <div className="p-4 text-center text-gray-500">
                    <UserIcon className="h-12 w-12 mx-auto mb-2 text-gray-300" />
                    <p>No existing customers found</p>
                  </div>
                ) : (
                  <div className="space-y-0">
                    {allCustomers.map((customer) => (
                      <button
                        key={customer.id}
                        onClick={() => handleSelectExistingCustomer(customer)}
                        className={`
                          w-full px-4 py-3 text-left border-b border-gray-100 hover:bg-gray-50 transition-colors
                          ${formData.selectedExistingCustomer?.id === customer.id ? 'bg-blue-50 border-blue-200' : ''}
                        `}
                      >
                        <div className="flex items-center justify-between">
                          <div className="flex items-center">
                            <UserIcon className="h-5 w-5 mr-3 text-gray-400" />
                            <div>
                              <div className="font-medium text-gray-900">
                                {(() => {
                                  // Try different possible name structures
                                  const firstName = customer.customer?.user?.first_name || customer.first_name || ''
                                  const lastName = customer.customer?.user?.last_name || customer.last_name || ''
                                  const email = customer.customer?.user?.email || customer.email || ''
                                  const fullName = `${firstName} ${lastName}`.trim()
                                  
                                  if (fullName) {
                                    return fullName
                                  } else if (email) {
                                    return email
                                  } else {
                                    return `Customer ${customer.id}`
                                  }
                                })()}
                              </div>
                              <div className="text-sm text-gray-500 space-y-1">
                                {(() => {
                                  const phone = customer.customer?.user?.phone || customer.phone || ''
                                  const email = customer.customer?.user?.email || customer.email || ''
                                  
                                  return (
                                    <div className="space-y-1">
                                      {phone && (
                                        <div className="flex items-center">
                                          <PhoneIcon className="h-3 w-3 mr-1" />
                                          {phone}
                                        </div>
                                      )}
                                      {email && (
                                        <div className="flex items-center">
                                          <EnvelopeIcon className="h-3 w-3 mr-1" />
                                          {email}
                                        </div>
                                      )}
                                      {!phone && !email && (
                                        <div className="text-gray-400 italic">No contact info</div>
                                      )}
                                    </div>
                                  )
                                })()}
                              </div>
                            </div>
                          </div>
                          {formData.selectedExistingCustomer?.id === customer.id && (
                            <CheckIcon className="h-5 w-5 text-blue-600" />
                          )}
                        </div>
                      </button>
                    ))}
                  </div>
                )}
              </div>
            )}
          </div>
          
          {/* Create New Customer Section */}
          <div className="border-t pt-4">
            <button
              onClick={() => setShowCreateNew(!showCreateNew)}
              className="flex items-center justify-between w-full p-3 bg-gray-50 rounded-md hover:bg-gray-100 transition-colors"
            >
              <div className="flex items-center">
                <PlusIcon className="h-5 w-5 mr-2 text-gray-600" />
                <span className="font-medium text-gray-900">Create New Customer</span>
              </div>
              {showCreateNew ? (
                <ChevronUpIcon className="h-5 w-5 text-gray-400" />
              ) : (
                <ChevronDownIcon className="h-5 w-5 text-gray-400" />
              )}
            </button>
            
            {showCreateNew && (
              <div className="mt-4 p-4 bg-gray-50 rounded-md">
                <div className="grid grid-cols-1 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      <UserIcon className="h-4 w-4 inline mr-1" />
                      Full Name *
                    </label>
                    <input
                      type="text"
                      value={formData.customer_name}
                      onChange={(e) => updateField('customer_name', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter customer's full name"
                    />
                    {formData.errors.customer_name && (
                      <p className="mt-1 text-sm text-red-600">{formData.errors.customer_name}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      <PhoneIcon className="h-4 w-4 inline mr-1" />
                      Phone Number *
                    </label>
                    <input
                      type="tel"
                      value={formData.phone_number}
                      onChange={(e) => updateField('phone_number', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter phone number"
                    />
                    {formData.errors.phone_number && (
                      <p className="mt-1 text-sm text-red-600">{formData.errors.phone_number}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      <EnvelopeIcon className="h-4 w-4 inline mr-1" />
                      Email *
                    </label>
                    <input
                      type="email"
                      value={formData.email}
                      onChange={(e) => updateField('email', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter email address"
                    />
                    {formData.errors.email && (
                      <p className="mt-1 text-sm text-red-600">{formData.errors.email}</p>
                    )}
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      <ChatBubbleLeftRightIcon className="h-4 w-4 inline mr-1" />
                      Notes (Optional)
                    </label>
                    <textarea
                      value={formData.notes}
                      onChange={(e) => updateField('notes', e.target.value)}
                      rows={3}
                      placeholder="Any special requests or notes..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                    />
                  </div>
                </div>
              </div>
            )}
          </div>
          
          {/* Customer Selection Error */}
          {formData.customer_error && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-sm text-red-600 flex items-center">
                <ExclamationCircleIcon className="h-4 w-4 mr-2" />
                {formData.customer_error}
              </p>
            </div>
          )}
        </div>
      </div>
    </div>
  )

  // Render Confirmation Step
  const renderConfirmationStep = () => (
    <div className="space-y-6">
      <div>
        <h3 className="text-xl font-semibold text-gray-900 mb-2">Review Waitlist Entry</h3>
        <p className="text-gray-600 mb-6">Please review all details before adding to the waitlist</p>
        
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 border border-blue-100">
          <div className="space-y-5">
            <div className="flex justify-between items-start py-3 border-b border-blue-200">
              <span className="font-semibold text-gray-700 flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                Service:
              </span>
              <span className="text-gray-900 font-medium">{selectedService?.name}</span>
            </div>
            
            <div className="flex justify-between items-start py-3 border-b border-blue-200">
              <span className="font-semibold text-gray-700 flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                Staff:
              </span>
              <span className="text-gray-900 font-medium">
                {formData.employee_ids.length === 0 ? 'Any Available Staff' : selectedEmployee?.name || 'Selected Staff'}
              </span>
            </div>
            
            <div className="flex justify-between items-start py-3 border-b border-blue-200">
              <span className="font-semibold text-gray-700 flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                Customer:
              </span>
              <span className="text-gray-900 font-medium">
                {formData.hasSelectedExistingCustomer ? 
                  (() => {
                    const customer = formData.selectedExistingCustomer
                    const firstName = customer?.customer?.user?.first_name || customer?.first_name || ''
                    const lastName = customer?.customer?.user?.last_name || customer?.last_name || ''
                    const email = customer?.customer?.user?.email || customer?.email || ''
                    const fullName = `${firstName} ${lastName}`.trim()
                    return fullName || email || `Customer ${customer?.id}`
                  })() :
                  formData.customer_name
                }
              </span>
            </div>
            
            <div className="flex justify-between items-start py-3 border-b border-blue-200">
              <span className="font-semibold text-gray-700 flex items-center">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                Contact:
              </span>
              <span className="text-gray-900 font-medium">{formData.phone_number}</span>
            </div>
            
            <div className="py-3">
              <span className="font-semibold text-gray-700 flex items-center mb-3">
                <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                Available Times:
              </span>
              <div className="ml-5 space-y-2">
                {formData.availabilityWindows
                  .filter(window => window.date)
                  .map((window, index) => (
                    <div key={index} className="flex items-center bg-white rounded-lg p-3 border border-blue-200">
                      <CalendarDaysIcon className="h-4 w-4 text-blue-600 mr-2" />
                      <span className="text-sm font-medium text-gray-700">
                        {new Date(window.date).toLocaleDateString('en-US', { 
                          weekday: 'long', 
                          year: 'numeric', 
                          month: 'long', 
                          day: 'numeric' 
                        })}
                      </span>
                      <span className="ml-2 text-sm text-gray-500">at</span>
                      <ClockIcon className="h-4 w-4 text-blue-600 ml-2 mr-1" />
                      <span className="text-sm font-medium text-gray-700">
                        {window.time === 'Anytime' ? 'Anytime during business hours' : window.time}
                      </span>
                    </div>
                  ))}
              </div>
            </div>
            
            {formData.notes && (
              <div className="py-3 border-t border-blue-200">
                <span className="font-semibold text-gray-700 flex items-center mb-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full mr-3"></div>
                  Notes:
                </span>
                <div className="ml-5 bg-white rounded-lg p-3 border border-blue-200">
                  <p className="text-sm text-gray-700">{formData.notes}</p>
                </div>
              </div>
            )}
          </div>
        </div>
        
        {formData.errors.general && (
          <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
            <p className="text-sm text-red-600 flex items-center">
              <ExclamationCircleIcon className="h-4 w-4 mr-2" />
              {formData.errors.general}
            </p>
          </div>
        )}
      </div>
    </div>
  )

  // Render current step
  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'service':
        return renderServiceStep()
      case 'availability':
        return renderAvailabilityStep()
      case 'customer':
        return renderCustomerStep()
      case 'confirmation':
        return renderConfirmationStep()
      default:
        return null
    }
  }

  return (
    <div 
      className="fixed inset-0 z-50 flex items-center justify-center p-4 bg-black bg-opacity-50"
      onClick={handleBackdropClick}
    >
      <div className="bg-white rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <h2 className="text-xl font-semibold text-gray-900">Add to Waitlist</h2>
            <div className="text-sm text-gray-600">
              Step {currentStepIndex + 1} of {steps.length}
            </div>
          </div>
          <button
            onClick={handleClose}
            disabled={formData.isLoading}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-100 transition-colors disabled:opacity-50"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Progress Indicator */}
        <div className="px-6 py-6 bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-100">
          <div className="flex items-center justify-between relative">
            {/* Background progress line */}
            <div className="absolute top-6 left-0 right-0 h-0.5 bg-gray-200" />
            <div 
              className="absolute top-6 left-0 h-0.5 bg-gradient-to-r from-blue-500 to-indigo-500 transition-all duration-500"
              style={{ width: `${(currentStepIndex / (steps.length - 1)) * 100}%` }}
            />
            
            {steps.map((step, index) => {
              const isActive = step === currentStep
              const isCompleted = currentStepIndex > index
              const isClickable = currentStepIndex >= index

              return (
                <div key={step} className="flex flex-col items-center relative z-10">
                  <div
                    className={`
                      flex items-center justify-center w-12 h-12 rounded-full text-sm font-bold shadow-lg transition-all duration-300
                      ${isActive ? 'bg-gradient-to-r from-blue-500 to-indigo-600 text-white ring-4 ring-blue-200 scale-110' : 
                        isCompleted ? 'bg-gradient-to-r from-green-500 to-emerald-600 text-white' : 
                        'bg-white text-gray-400 border-2 border-gray-200'}
                    `}
                  >
                    {isCompleted ? (
                      <CheckIcon className="h-5 w-5" />
                    ) : (
                      index + 1
                    )}
                  </div>
                  <div className="mt-3 text-center">
                    <div className={`
                      text-sm font-semibold transition-colors duration-300
                      ${isActive ? 'text-blue-700' : 
                        isCompleted ? 'text-green-700' : 
                        'text-gray-500'}
                    `}>
                      {stepTitles[step]}
                    </div>
                  </div>
                </div>
              )
            })}
          </div>
        </div>

        {/* Modal Content */}
        <div className="flex-1 overflow-y-auto p-6">
          {renderCurrentStep()}
        </div>

        {/* Modal Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <button
            onClick={handlePrevious}
            disabled={isFirstStep() || formData.isLoading}
            className="flex items-center px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <ChevronLeftIcon className="h-4 w-4 mr-2" />
            Previous
          </button>
          
          <button
            onClick={handleNext}
            disabled={formData.isLoading || (currentStep === 'service' && (!formData.service_ids?.length || !formData.hasStaffSelection))}
            className="flex items-center px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {formData.isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent mr-2" />
                {isLastStep() ? 'Adding...' : 'Processing...'}
              </>
            ) : (
              <>
                {isLastStep() ? 'Add to Waitlist' : 'Next'}
                {!isLastStep() && <ChevronRightIcon className="h-4 w-4 ml-2" />}
              </>
            )}
          </button>
        </div>
      </div>
    </div>
  )
}

export default WaitlistModal 