#!/usr/bin/env python3
"""
Script to import real transaction records from Excel file for the chatbook backend.
Creates employees, services, and customers as needed based on the Excel data.
"""
import os
import sys
import django
import pandas as pd
import re
from datetime import datetime, timedelta
from decimal import Decimal

# Setup Django environment
current_dir = os.path.dirname(os.path.abspath(__file__))
parent_dir = os.path.dirname(os.path.dirname(current_dir))
sys.path.append(parent_dir)

os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'scripts.settings_for_scripts')
django.setup()

from django.utils import timezone
import django.db.models
from django.contrib.auth import get_user_model
from business.models import BusinessCustomer, Business
from employees.models import Employee
from services.models import Service, ServiceCategory
from appointments.models import Appointment, AppointmentService
from reports.models import Transaction
from accounts.models import Role

User = get_user_model()

def clean_name(name_str):
    """Extract first and last name from string, removing descriptions in parentheses"""
    if not name_str or pd.isna(name_str):
        return None, None
    
    # Remove content in parentheses
    cleaned = re.sub(r'\([^)]*\)', '', str(name_str)).strip()
    
    # Split into first and last name
    parts = cleaned.split()
    if len(parts) >= 2:
        first_name = parts[0]
        last_name = ' '.join(parts[1:])
        return first_name, last_name
    elif len(parts) == 1:
        return parts[0], ''
    else:
        return None, None

def get_or_create_employee(name_str, business):
    """Get or create employee based on name string"""
    if not name_str or pd.isna(name_str):
        return None
    
    # Handle special case for "Front Desk" - create a generic front desk employee
    if str(name_str).strip().lower() in ['front desk', 'front-desk', 'frontdesk']:
        try:
            user = User.objects.get(email=f"frontdesk@{business.name.lower().replace(' ', '')}.com")
            employee = Employee.objects.get(user=user, business=business)
            return employee
        except (User.DoesNotExist, Employee.DoesNotExist):
            # Create front desk employee
            try:
                email = f"frontdesk@{business.name.lower().replace(' ', '')}.com"
                phone_number = "+***********"
                
                user = User.objects.create_user(
                    identifier=email,
                    password='defaultpassword123',
                    first_name='Front',
                    last_name='Desk',
                    phone_number=phone_number
                )
                
                employee_role, _ = Role.objects.get_or_create(name='employee')
                user.roles.add(employee_role)
                
                employee = Employee.objects.create(
                    user=user,
                    business=business,
                    employee_type='admin'
                )
                
                print(f"✅ Created Front Desk employee")
                return employee
            except Exception as e:
                print(f"❌ Error creating Front Desk employee: {e}")
                return None
    
    first_name, last_name = clean_name(name_str)
    
    if not first_name:
        print(f"⚠️  Warning: Could not parse employee name '{name_str}'")
        return None
    
    # Try to find existing employee by name
    try:
        user = User.objects.get(
            first_name__iexact=first_name,
            last_name__iexact=last_name or ''
        )
        employee = Employee.objects.get(user=user, business=business)
        return employee
    except (User.DoesNotExist, Employee.DoesNotExist):
        pass
    
    # Create new user and employee
    try:
        # Generate unique email
        base_email = f"{first_name.lower()}.{last_name.lower()}@{business.name.lower().replace(' ', '')}.com"
        email = base_email
        counter = 1
        while User.objects.filter(email=email).exists():
            email = f"{first_name.lower()}.{last_name.lower()}{counter}@{business.name.lower().replace(' ', '')}.com"
            counter += 1
        
        # Generate unique phone number
        phone_base = "+1555000"
        phone_number = phone_base + str(1000 + User.objects.count())
        
        # Create user
        user = User.objects.create_user(
            identifier=email,
            password='defaultpassword123',
            first_name=first_name,
            last_name=last_name or '',
            phone_number=phone_number
        )
        
        # Assign employee role
        employee_role, _ = Role.objects.get_or_create(name='employee')
        user.roles.add(employee_role)
        
        # Create employee
        employee = Employee.objects.create(
            user=user,
            business=business,
            employee_type='service_provider'
        )
        
        print(f"✅ Created new employee: {first_name} {last_name}")
        return employee
                
    except Exception as e:
        print(f"❌ Error creating employee '{name_str}': {e}")
        return None

def get_or_create_customer(name_str, business):
    """Get or create customer based on name string"""
    from customers.models import CustomerProfile
    
    first_name, last_name = clean_name(name_str)
    
    if not first_name:
        print(f"⚠️  Warning: Could not parse customer name '{name_str}'")
        return None
    
    # Try to find existing customer by name
    try:
        user = User.objects.get(
            first_name__iexact=first_name,
            last_name__iexact=last_name or ''
        )
        # Get the customer profile for this user
        customer_profile = CustomerProfile.objects.get(user=user)
        # Get the business customer relationship
        business_customer = BusinessCustomer.objects.get(customer=customer_profile, business=business)
        return business_customer
    except (User.DoesNotExist, CustomerProfile.DoesNotExist, BusinessCustomer.DoesNotExist):
        pass
    
    # Create new customer
    try:
        # Generate unique email
        base_email = f"{first_name.lower()}.{last_name.lower()}@customer.com"
        email = base_email
        counter = 1
        while User.objects.filter(email=email).exists():
            email = f"{first_name.lower()}.{last_name.lower()}{counter}@customer.com"
            counter += 1
        
        # Generate unique phone number
        phone_base = "+1555001"
        phone_number = phone_base + str(1000 + User.objects.count())
        
        # Create user
        user = User.objects.create_user(
            identifier=email,
            password='defaultpassword123',
            first_name=first_name,
            last_name=last_name or '',
            phone_number=phone_number
        )
        
        # Assign customer role
        customer_role, _ = Role.objects.get_or_create(name='customer')
        user.roles.add(customer_role)
        
        # Create customer profile
        customer_profile = CustomerProfile.objects.create(user=user)
        
        # Create business customer relationship
        business_customer = BusinessCustomer.objects.create(
            business=business,
            customer=customer_profile
        )
        
        print(f"✅ Created new customer: {first_name} {last_name}")
        return business_customer
        
    except Exception as e:
        print(f"❌ Error creating customer '{name_str}': {e}")
        return None

def get_or_create_service(service_name, business):
    """Get or create service based on service name"""
    if not service_name or pd.isna(service_name):
        return None
    
    service_name = str(service_name).strip()
    
    # Try to find existing service
    try:
        service = Service.objects.get(name__iexact=service_name, business=business)
        return service
    except Service.DoesNotExist:
        pass
    
    # Create new service
    try:
        # Get or create default category
        category, _ = ServiceCategory.objects.get_or_create(
        business=business,
            name="General Services",
            defaults={'description': 'General services category'}
        )
        
        # Create service with default values
        service = Service.objects.create(
            business=business,
            category=category,
            name=service_name,
            description=f"Imported service: {service_name}",
            base_price=Decimal('00.00'),  # Default price
            base_duration=timedelta(hours=1),  # Default duration
            buffer_time=timedelta(minutes=15)  # Default buffer
        )
        
        print(f"✅ Created new service: {service_name}")
        return service
        
    except Exception as e:
        print(f"❌ Error creating service '{service_name}': {e}")
        return None

def determine_payment_method(row):
    """Determine payment method from payment columns"""
    payment_fields = {
        'Cash': 'cash',
        'Check': 'check',
        'CC': 'credit_card',
        'BankAccount': 'debit_card',
        'Vagaro Pay Later': 'other'
    }
    
    for field, method in payment_fields.items():
        if field in row and pd.notna(row[field]) and float(row[field] or 0) > 0:
            return method
    
    # Default to credit card if amount paid but no specific method
        return 'credit_card'

def parse_date(date_str):
    """Parse date string to datetime object"""
    if pd.isna(date_str):
        return None
    
    try:
        # Handle various date formats
        if isinstance(date_str, datetime):
            return date_str
        
        date_str = str(date_str).strip()
        
        # Try different date formats
        formats = [
            '%b %d, %Y - %I:%M %p',  # Jun 30, 2025 - 6:27 PM
            '%m/%d/%Y %H:%M:%S',
            '%m/%d/%Y %H:%M',
            '%m/%d/%Y',
            '%Y-%m-%d %H:%M:%S',
            '%Y-%m-%d %H:%M',
            '%Y-%m-%d'
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(date_str, fmt)
            except ValueError:
                continue
        
        print(f"⚠️  Warning: Could not parse date '{date_str}'")
        return None
        
    except Exception as e:
        print(f"⚠️  Warning: Date parsing error for '{date_str}': {e}")
        return None

def import_transactions_from_excel():
    """Import transactions from Excel file"""
    
    excel_file = os.path.join(os.path.dirname(__file__), '..', 'sample-import-data', 'transaction-list-0625.xlsx')
    
    if not os.path.exists(excel_file):
        print(f"❌ Excel file not found: {excel_file}")
        return
    
    print(f"📊 Reading Excel file: {excel_file}")
    
    try:
        # Read Excel file starting from row 22 (0-indexed row 21)
        df = pd.read_excel(excel_file, skiprows=22)
    
        print(f"📋 Found {len(df)} rows in Excel file")
        print(f"🗂️  Columns: {list(df.columns)}")
    
        # Get or create business
        business = Business.objects.first()
        if not business:
            print("❌ No business found. Please create a business first.")
            return
        
        print(f"🏢 Using business: {business.name}")
        
        # Track statistics
        transactions_created = 0
        transactions_skipped = 0
        appointments_created = 0
        
        # Group rows by original Transaction ID for appointment creation
        print("📊 Grouping rows by original Transaction ID for appointments...")
        grouped_for_appointments = {}
        original_transaction_sequences = {}  # Track sequence numbers per original transaction
    
        for index, row in df.iterrows():
            # Skip rows with missing essential data
            if pd.isna(row.get('Transaction ID')) or pd.isna(row.get('Customer')):
                transactions_skipped += 1
                continue
            
            original_transaction_id = str(row['Transaction ID']).strip()
            
            if original_transaction_id not in grouped_for_appointments:
                grouped_for_appointments[original_transaction_id] = []
                original_transaction_sequences[original_transaction_id] = 0
            
            grouped_for_appointments[original_transaction_id].append(row)
        
        print(f"📋 Found {len(grouped_for_appointments)} unique original transactions")
        
        # Create appointments first (one per original transaction ID)
        created_appointments = {}
        
        for original_transaction_id, transaction_rows in grouped_for_appointments.items():
            main_row = transaction_rows[0]  # Use first row for appointment info
            
            # Parse appointment date
            appointment_date = parse_date(main_row.get('Appointment Date'))
            
            if appointment_date:
                try:
                    # Make timezone-aware if naive
                    if appointment_date.tzinfo is None:
                        appointment_date = timezone.make_aware(appointment_date)
                    
                    # Get customer for appointment
                    customer = get_or_create_customer(main_row['Customer'], business)
                    if not customer:
                        print(f"❌ Could not create customer for appointment {original_transaction_id}")
                        continue
                    
                    # Get main service provider
                    main_employee = get_or_create_employee(main_row.get('Service Provider'), business)
                    if not main_employee:
                        print(f"❌ Could not create service provider for appointment {original_transaction_id}")
                        continue
            
                    # Create appointment
                    appointment = Appointment.objects.create(
                        customer=customer,
                        employee=main_employee,
                        start_time=appointment_date,
                        status='completed',  # Historical appointment, already completed
                        payment_status='paid',  # Will be linked to transactions
                        source='admin',
                        notes_from_customer=f"Imported appointment with {len(transaction_rows)} line items"
                    )
                    
                    created_appointments[original_transaction_id] = appointment
                    appointments_created += 1
                    print(f"✅ Created appointment for {customer.customer.user.get_full_name()} ({original_transaction_id})")
                    
                except Exception as e:
                    print(f"⚠️  Warning: Could not create appointment for {original_transaction_id}: {e}")
        
        # Process each row as individual transaction
        print("📊 Creating individual transaction records...")
        
        for index, row in df.iterrows():
            try:
                # Skip rows with missing essential data
                if pd.isna(row.get('Transaction ID')) or pd.isna(row.get('Customer')):
                    transactions_skipped += 1
                    continue
                
                original_transaction_id = str(row['Transaction ID']).strip()
                
                # Create unique transaction ID with sequence number
                original_transaction_sequences[original_transaction_id] += 1
                sequence_num = original_transaction_sequences[original_transaction_id]
                unique_transaction_id = f"{original_transaction_id}-{sequence_num}"
                
                # Check if this unique transaction already exists
                if Transaction.objects.filter(id=unique_transaction_id).exists():
                    print(f"⚠️  Transaction {unique_transaction_id} already exists, skipping")
                    transactions_skipped += 1
                    continue
                
                # Get or create customer
                customer = get_or_create_customer(row['Customer'], business)
                if not customer:
                    print(f"❌ Could not create customer for transaction {unique_transaction_id}")
                    transactions_skipped += 1
                    continue
                
                # Get or create service provider (sold by)
                sold_by = get_or_create_employee(row.get('Service Provider'), business)
                if not sold_by:
                    print(f"❌ Could not create service provider for transaction {unique_transaction_id}")
                    transactions_skipped += 1
                    continue
                
                # Get or create checkout employee
                checkout_by = get_or_create_employee(row.get('CheckedOut By'), business)
                if not checkout_by:
                    # Use sold_by as fallback
                    checkout_by = sold_by
                
                # Get or create service
                service = get_or_create_service(row.get('Service/Product/GC/Package/Membership/Class'), business)
                if not service:
                    print(f"❌ Could not create service for transaction {unique_transaction_id}")
                    transactions_skipped += 1
                    continue
                
                # Parse dates
                checkout_date = parse_date(row.get('Checkout Date'))
                if not checkout_date:
                    checkout_date = timezone.now()
                else:
                    # Make timezone-aware if naive
                    if checkout_date.tzinfo is None:
                        checkout_date = timezone.make_aware(checkout_date)
                
                appointment_date = parse_date(row.get('Appointment Date'))
                if appointment_date and appointment_date.tzinfo is None:
                    appointment_date = timezone.make_aware(appointment_date)
                
                # Get payment method
                payment_method = determine_payment_method(row)
                
                # Get financial data for this individual line item
                quantity = int(row.get('Qty', 1))
                price = Decimal(str(row.get('Price', 0)))
                tax = Decimal(str(row.get('Tax', 0)))
                tip = Decimal(str(row.get('Tip', 0)))
                discount = Decimal(str(row.get('Disc', 0)))
                amount_paid = Decimal(str(row.get('Amt paid', 0)))
                
                # Get linked appointment if it exists
                linked_appointment = created_appointments.get(original_transaction_id)
                
                # Create AppointmentService record if linked to appointment
                appointment_service = None
                if linked_appointment:
                    try:
                        appointment_service = AppointmentService.objects.create(
                            appointment=linked_appointment,
                            service=service,
                            quantity=quantity,
                            base_price=price,
                            # price_override=price,  # Use the actual transaction price
                            duration=int(service.base_duration.total_seconds() / 60),  # Convert to minutes
                            buffer_time=int(service.buffer_time.total_seconds() / 60)
                        )
                    except Exception as e:
                        print(f"⚠️  Warning: Could not create AppointmentService for {unique_transaction_id}: {e}")
                
                # Create individual transaction record (skip validation for historical data)
                transaction = Transaction(
                    id=unique_transaction_id,  # Unique ID with sequence number
                    customer=customer,
                    appointment=linked_appointment,  # Link to appointment if created
                    appointment_service=appointment_service,  # Link to specific service
                    checkout_date=checkout_date,
                    checkout_by=checkout_by,
                    sold_by=sold_by,
                    service=service,
                    item_sold=str(row.get('Service/Product/GC/Package/Membership/Class', '')),
                    quantity=quantity,
                    payment_method=payment_method,
                    price=price,
                    tax=tax,
                    tip=tip,
                    discount=discount,
                    amount_paid=amount_paid
                )
                
                # Temporarily disable clean method for historical import to skip validation
                original_clean = transaction.clean
                transaction.clean = lambda: None
                
                try:
                    transaction.save()
                    print(f"✅ Created transaction {unique_transaction_id}: {transaction.item_sold} (${amount_paid})")
                    transactions_created += 1
                finally:
                    # Restore original clean method
                    transaction.clean = original_clean
                
                if transactions_created % 10 == 0:
                    print(f"✅ Imported {transactions_created} individual transactions...")
            
            except Exception as e:
                print(f"❌ Error processing row {index}: {e}")
                transactions_skipped += 1
                continue
    
        # Print summary
        print(f"\n🎉 Import completed!")
        print(f"✅ Individual transactions created: {transactions_created}")
        print(f"📅 Appointments created: {appointments_created}")
        print(f"⚠️  Transactions skipped: {transactions_skipped}")
        
        # Print totals
        total_revenue = Transaction.objects.aggregate(
            total=django.db.models.Sum('amount_paid')
        )['total'] or 0
        
        total_count = Transaction.objects.count()
        total_appointments = Appointment.objects.count()
        
        # Calculate breakdown
        appointment_based_transactions = Transaction.objects.filter(appointment__isnull=False).count()
        direct_sale_transactions = Transaction.objects.filter(appointment__isnull=True).count()
        
        print(f"\n📊 Final Statistics:")
        print(f"💰 Total Revenue: ${total_revenue:,.2f}")
        print(f"🔢 Total Transactions: {total_count}")
        print(f"📅 Total Appointments: {total_appointments}")
        print(f"👥 Total Employees: {Employee.objects.count()}")
        print(f"🙋 Total Customers: {BusinessCustomer.objects.count()}")
        print(f"🛍️  Total Services: {Service.objects.count()}")
        
        print(f"\n📋 Transaction Source Breakdown:")
        print(f"👥 From Appointments: {appointment_based_transactions}")
        print(f"💰 Direct Sales: {direct_sale_transactions}")
        
        print(f"\n🔗 Sample Transaction IDs created:")
        sample_transactions = Transaction.objects.all()[:5]
        for t in sample_transactions:
            print(f"  • {t.id} - {t.item_sold} (${t.amount_paid})")
        
    except Exception as e:
        print(f"❌ Error reading Excel file: {e}")
        import traceback
        traceback.print_exc()

def main():
    """Main function"""
    print("🚀 Starting transaction import from Excel file...")
    print("=" * 50)
    
    try:
        import_transactions_from_excel()
        print("\n" + "=" * 50)
        print("✅ Transaction import completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Error during execution: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)

if __name__ == '__main__':
    main() 