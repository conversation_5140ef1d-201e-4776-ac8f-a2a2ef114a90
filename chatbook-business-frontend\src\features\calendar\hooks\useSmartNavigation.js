import { useState, useEffect, useRef } from 'react'

/**
 * Custom hook for handling smart date navigation in custom calendar
 * @param {Object} calendarControls - Custom calendar controls instance
 * @param {Object} options - Configuration options for smart navigation
 * @returns {Object} Navigation utilities and handlers
 */
export function useSmartNavigation(calendarControls, options = {}) {
  const [currentDate, setCurrentDate] = useState(new Date())
  const [isNavigating, setIsNavigating] = useState(false)
  const [navigationHistory, setNavigationHistory] = useState([])
  const [canGoBack, setCanGoBack] = useState(false)
  const [canGoForward, setCanGoForward] = useState(false)
  
  const navigationTimeoutRef = useRef(null)
  const historyIndexRef = useRef(0)

  const {
    enableHistory = true,
    maxHistoryLength = 20,
    navigationDelay = 300,
    enableSmartJump = true,
    enableTodayShortcut = true,
    enableWeekendSkip = false
  } = options

  /**
   * Navigate to a specific date
   * @param {Date|string} targetDate - The target date
   * @param {Object} options - Navigation options
   */
  const navigateToDate = (targetDate, navOptions = {}) => {
    if (!calendarControls) return

    const {
      updateHistory = true,
      animate = true,
      immediate = false
    } = navOptions

    const date = typeof targetDate === 'string' ? new Date(targetDate) : targetDate
    
    if (isNaN(date.getTime())) {
      console.error('Invalid date provided to navigateToDate:', targetDate)
      return
    }

    // Clear any existing navigation timeout
    if (navigationTimeoutRef.current) {
      clearTimeout(navigationTimeoutRef.current)
    }

    const performNavigation = () => {
      setIsNavigating(true)
      
      try {
        // Update calendar controls
        if (calendarControls.setDate) {
          calendarControls.setDate(date)
        }
        
        // Update local state
        setCurrentDate(date)
        
        // Update navigation history
        if (enableHistory && updateHistory) {
          addToHistory(date)
        }
        
      } catch (error) {
        console.error('Error during navigation:', error)
      } finally {
        // Reset navigation state
        setTimeout(() => {
          setIsNavigating(false)
        }, navigationDelay)
      }
    }

    if (immediate) {
      performNavigation()
    } else {
      navigationTimeoutRef.current = setTimeout(performNavigation, animate ? 150 : 0)
    }
  }

  /**
   * Navigate to today
   */
  const navigateToToday = () => {
    if (!enableTodayShortcut) return
    navigateToDate(new Date(), { updateHistory: true, animate: true })
  }

  /**
   * Navigate to next period (day, week, month based on current view)
   */
  const navigateNext = () => {
    const nextDate = getNextDate(currentDate)
    navigateToDate(nextDate, { updateHistory: true, animate: true })
  }

  /**
   * Navigate to previous period (day, week, month based on current view)
   */
  const navigatePrevious = () => {
    const prevDate = getPreviousDate(currentDate)
    navigateToDate(prevDate, { updateHistory: true, animate: true })
  }

  /**
   * Go back in navigation history
   */
  const goBack = () => {
    if (!canGoBack || !enableHistory) return
    
    const historyIndex = historyIndexRef.current
    if (historyIndex > 0) {
      historyIndexRef.current = historyIndex - 1
      const targetDate = navigationHistory[historyIndexRef.current]
      navigateToDate(targetDate, { updateHistory: false, animate: true })
      updateHistoryState()
    }
  }

  /**
   * Go forward in navigation history
   */
  const goForward = () => {
    if (!canGoForward || !enableHistory) return
    
    const historyIndex = historyIndexRef.current
    if (historyIndex < navigationHistory.length - 1) {
      historyIndexRef.current = historyIndex + 1
      const targetDate = navigationHistory[historyIndexRef.current]
      navigateToDate(targetDate, { updateHistory: false, animate: true })
      updateHistoryState()
    }
  }

  /**
   * Smart jump to a date (skips weekends if enabled)
   * @param {Date} targetDate - The target date
   */
  const smartJumpToDate = (targetDate) => {
    if (!enableSmartJump) {
      navigateToDate(targetDate)
      return
    }

    let adjustedDate = new Date(targetDate)
    
    // Skip weekends if enabled
    if (enableWeekendSkip) {
      const dayOfWeek = adjustedDate.getDay()
      if (dayOfWeek === 0) { // Sunday
        adjustedDate.setDate(adjustedDate.getDate() + 1)
      } else if (dayOfWeek === 6) { // Saturday
        adjustedDate.setDate(adjustedDate.getDate() + 2)
      }
    }

    navigateToDate(adjustedDate)
  }

  /**
   * Add a date to navigation history
   * @param {Date} date - The date to add
   */
  const addToHistory = (date) => {
    if (!enableHistory) return

    const newHistory = [...navigationHistory]
    
    // Remove any entries after current index
    newHistory.splice(historyIndexRef.current + 1)
    
    // Add new date
    newHistory.push(new Date(date))
    
    // Limit history length
    if (newHistory.length > maxHistoryLength) {
      newHistory.shift()
    } else {
      historyIndexRef.current = newHistory.length - 1
    }
    
    setNavigationHistory(newHistory)
    updateHistoryState()
  }

  /**
   * Update history navigation state
   */
  const updateHistoryState = () => {
    setCanGoBack(historyIndexRef.current > 0)
    setCanGoForward(historyIndexRef.current < navigationHistory.length - 1)
  }

  /**
   * Get the next date based on current view
   * @param {Date} date - Current date
   * @returns {Date} Next date
   */
  const getNextDate = (date) => {
    const nextDate = new Date(date)
    
    // This would need to be adapted based on your calendar view logic
    // For now, assuming day view (add 1 day)
    nextDate.setDate(nextDate.getDate() + 1)
    
    return nextDate
  }

  /**
   * Get the previous date based on current view
   * @param {Date} date - Current date
   * @returns {Date} Previous date
   */
  const getPreviousDate = (date) => {
    const prevDate = new Date(date)
    
    // This would need to be adapted based on your calendar view logic
    // For now, assuming day view (subtract 1 day)
    prevDate.setDate(prevDate.getDate() - 1)
    
    return prevDate
  }

  /**
   * Clear navigation history
   */
  const clearHistory = () => {
    setNavigationHistory([])
    historyIndexRef.current = 0
    updateHistoryState()
  }

  // Effect to sync current date with calendar controls
  useEffect(() => {
    if (calendarControls && calendarControls.currentDate) {
      setCurrentDate(new Date(calendarControls.currentDate))
    }
  }, [calendarControls])

  // Cleanup effect
  useEffect(() => {
    return () => {
      if (navigationTimeoutRef.current) {
        clearTimeout(navigationTimeoutRef.current)
      }
    }
  }, [])

  return {
    currentDate,
    isNavigating,
    navigationHistory,
    canGoBack,
    canGoForward,
    navigateToDate,
    navigateToToday,
    navigateNext,
    navigatePrevious,
    goBack,
    goForward,
    smartJumpToDate,
    clearHistory
  }
} 