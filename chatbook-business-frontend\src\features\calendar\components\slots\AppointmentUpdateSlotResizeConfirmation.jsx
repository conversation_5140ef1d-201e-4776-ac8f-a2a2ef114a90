import React, { useState } from 'react'
import { 
  ClockI<PERSON>, 
  ExclamationTriangleIcon,
  UserIcon,
  CalendarIcon,
  CheckIcon,
  XMarkIcon,
  BellIcon,
  BellSlashIcon
} from '@heroicons/react/24/outline'
import Toggle from '../ui/Toggle'

/**
 * AppointmentUpdateSlotResizeConfirmation - Confirmation modal for appointment update slot resize operations
 * Similar to AppointmentMoveConfirmation but for duration changes
 */
const AppointmentUpdateSlotResizeConfirmation = ({
  isOpen,
  onClose,
  appointment,
  originalDuration,
  newDuration,
  originalStartTime,
  originalEndTime,
  newStartTime,
  newEndTime,
  onConfirm,
  resizeDirection = 'end',
  error = null,
  isUpdating = false
}) => {
  const [notifyCustomer, setNotifyCustomer] = useState(true)

  if (!isOpen || !appointment) return null

  // Calculate duration difference for display
  const getDurationDifference = () => {
    if (!originalDuration || !newDuration) return null

    const diff = newDuration - originalDuration
    const diffMinutes = Math.round(diff / (1000 * 60))
    const hours = Math.floor(Math.abs(diffMinutes) / 60)
    const remainingMinutes = Math.abs(diffMinutes) % 60

    return {
      minutes: diffMinutes,
      hours,
      remainingMinutes,
      isLonger: diffMinutes > 0,
      formatted: hours > 0 
        ? `${hours}h ${remainingMinutes}m` 
        : `${Math.abs(diffMinutes)}m`
    }
  }

  const durationDiff = getDurationDifference()

  const handleConfirm = () => {
    onConfirm(notifyCustomer)
  }

  const formatTime = (date) => {
    if (!date) return ''
    return new Date(date).toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    })
  }

  const formatDate = (date) => {
    if (!date) return ''
    return new Date(date).toLocaleDateString('en-US', { 
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 p-6">
        <div className="flex items-center justify-between mb-4">
          <h2 className="text-xl font-semibold text-gray-900">
            Confirm Appointment Update Slot Resize
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={isUpdating}
          >
            <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        {/* Error Display */}
        {error && (
          <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-md flex items-center">
            <ExclamationTriangleIcon className="w-5 h-5 text-red-500 mr-2" />
            <span className="text-red-700 text-sm">{error}</span>
          </div>
        )}

        {/* Appointment Details */}
        <div className="bg-gray-50 rounded-md p-4 mb-4">
          <div className="flex items-center mb-2">
            <UserIcon className="w-4 h-4 text-gray-500 mr-2" />
            <span className="font-medium text-gray-900">
              {appointment.clientName || 'Customer'}
            </span>
          </div>
          <div className="flex items-center mb-2">
            <CalendarIcon className="w-4 h-4 text-gray-500 mr-2" />
            <span className="text-gray-700">
              {formatDate(appointment.start)}
            </span>
          </div>
          <div className="flex items-center">
            <ClockIcon className="w-4 h-4 text-gray-500 mr-2" />
            <span className="text-gray-700">
              {appointment.serviceShortName || appointment.serviceName || 'Service'}
            </span>
          </div>
        </div>

        {/* Duration Change Details */}
        <div className="bg-blue-50 rounded-md p-4 mb-4">
          <h3 className="font-medium text-blue-900 mb-3">Duration Change</h3>
          
          <div className="space-y-2">
            <div className="flex justify-between">
              <span className="text-blue-700">Original Duration:</span>
              <span className="font-medium text-blue-900">
                {originalDuration ? `${Math.round(originalDuration / (1000 * 60))} min` : 'N/A'}
              </span>
            </div>
            
            <div className="flex justify-between">
              <span className="text-blue-700">New Duration:</span>
              <span className="font-medium text-blue-900">
                {newDuration ? `${Math.round(newDuration / (1000 * 60))} min` : 'N/A'}
              </span>
            </div>
            
            {durationDiff && (
              <div className="flex justify-between pt-2 border-t border-blue-200">
                <span className="text-blue-700">Change:</span>
                <span className={`font-medium ${durationDiff.isLonger ? 'text-green-600' : 'text-red-600'}`}>
                  {durationDiff.isLonger ? '+' : '-'}{durationDiff.formatted}
                </span>
              </div>
            )}
          </div>
        </div>

        {/* Time Change Details */}
        <div className="bg-gray-50 rounded-md p-4 mb-4">
          <h3 className="font-medium text-gray-900 mb-3">Time Details</h3>
          
          <div className="space-y-2">
            {resizeDirection === 'start' ? (
              // Top resize - start time changes
              <>
                <div className="flex justify-between">
                  <span className="text-gray-700">Original Start:</span>
                  <span className="font-medium text-gray-900">
                    {formatTime(originalStartTime || appointment.start)}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-700">New Start:</span>
                  <span className="font-medium text-blue-600">
                    {formatTime(newStartTime || appointment.start)}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-700">End Time:</span>
                  <span className="font-medium text-gray-900">
                    {formatTime(originalEndTime || appointment.end)}
                  </span>
                </div>
              </>
            ) : (
              // Bottom resize - end time changes
              <>
                <div className="flex justify-between">
                  <span className="text-gray-700">Start Time:</span>
                  <span className="font-medium text-gray-900">
                    {formatTime(appointment.start)}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-700">Original End:</span>
                  <span className="font-medium text-gray-900">
                    {formatTime(originalEndTime)}
                  </span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-700">New End:</span>
                  <span className="font-medium text-green-600">
                    {formatTime(newEndTime)}
                  </span>
                </div>
              </>
            )}
          </div>
        </div>

        {/* Customer Notification Toggle */}
        <div className="bg-gradient-to-r from-gray-50 to-blue-50/30 p-4 rounded-xl border border-gray-100 mb-4">
          <div className="flex items-center justify-between">
            <div className="flex items-start space-x-3 flex-1">
              <div className="flex-shrink-0 mt-0.5">
                {notifyCustomer ? (
                  <BellIcon className="w-5 h-5 text-blue-600" />
                ) : (
                  <BellSlashIcon className="w-5 h-5 text-gray-400" />
                )}
              </div>
              <div className="flex-1">
                <div className="text-sm font-medium text-gray-900">
                  Notify Customer
                </div>
                <div className="text-xs text-gray-600 mt-0.5">
                  Send update about the appointment duration change
                </div>
              </div>
            </div>
            <div className="ml-4">
              <Toggle
                checked={notifyCustomer}
                onChange={setNotifyCustomer}
                disabled={isUpdating}
              />
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="border-t border-gray-200 pt-4 space-y-2">
          {/* Confirm Button */}
          <button
            onClick={handleConfirm}
            disabled={isUpdating}
            className="w-full bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
          >
            {isUpdating ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Updating...
              </>
            ) : (
              <>
                <CheckIcon className="w-5 h-5 mr-2" />
                Confirm Update Slot Resize
                {notifyCustomer && (
                  <span className="ml-2 text-xs opacity-80">& Notify</span>
                )}
              </>
            )}
          </button>
          
          {/* Cancel Button */}
          <button
            onClick={onClose}
            disabled={isUpdating}
            className="w-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50 text-gray-700 font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
          >
            <XMarkIcon className="w-5 h-5 mr-2" />
            Cancel
          </button>
        </div>
      </div>
    </div>
  )
}

export default AppointmentUpdateSlotResizeConfirmation 