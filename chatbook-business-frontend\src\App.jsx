import { Routes, Route, Navigate } from 'react-router-dom';
import Layout from './components/Layout';
import RequireAuth from './components/RequireAuth';
import { FormsProvider } from './features/forms/FormsContext';

// Pages
import Dashboard from './pages/Dashboard';
import Calendar from './pages/Calendar';

// Checkout
import Checkout from './pages/Checkout';
import CheckoutSettings from './pages/Checkout/Settings';
import RefundCustomer from './pages/Checkout/Refunds';
import Invoices from './pages/Checkout/Invoices';

// Customers
import CustomerManagement from './pages/Customers/Management';
import CustomerList from './pages/Customers/List';
import ImportManagement from './pages/Customers/Import';
import CustomerDetail from './pages/CustomerDetail';

// Marketing & Forms
import Marketing from './pages/Marketing';
import Forms from './pages/Forms';
import FormsEdit from './pages/FormsEdit';
import TemplateDetails from './features/forms/components/TemplateDetails';

// Reports
import Reports from './pages/Reports';
import AllReports from './pages/Reports/All';
import TransactionList from './pages/Reports/Transactions';

// Settings
import Settings from './pages/Settings';

// Auth
import Login from './pages/Auth/Login';
import Signup from './pages/Auth/Signup';

// Business registration
import BusinessRegister from './pages/Business/Register';
import RegisterSuccess from './pages/Business/RegisterSuccess';

// 404
import NotFound from './pages/NotFound';

function App() {
  return (
    <FormsProvider>
      <Routes>
        {/* Main app routes (require auth + common layout) */}
        <Route path="/" element={<RequireAuth><Layout /></RequireAuth>}>
          {/* Dashboard */}
          <Route index element={<Dashboard />} />
          <Route path="dashboard" element={<Dashboard />} />

          {/* Calendar */}
          <Route path="calendar" element={<Calendar />} />

          {/* Checkout */}
          <Route path="checkout" element={<Checkout />} />
          <Route path="checkout/settings" element={<CheckoutSettings />} />
          <Route path="checkout/refunds" element={<RefundCustomer />} />
          <Route path="checkout/invoices" element={<Invoices />} />

          {/* Customers */}
          <Route path="customers" element={<Navigate to="/customers/management" replace />} />
          <Route path="customers/management" element={<CustomerManagement />} />
          <Route path="customers/list" element={<CustomerList />} />
          <Route path="customers/import" element={<ImportManagement />} />
          <Route path="customers/:id" element={<CustomerDetail />} />

          {/* Marketing */}
          <Route path="marketing" element={<Marketing />} />

          {/* Forms */}
          <Route path="forms" element={<Forms />} />
          <Route path="forms/edit/:id" element={<FormsEdit />} />
          <Route path="forms/details/:id" element={<TemplateDetails />} />

          {/* Reports */}
          <Route path="reports" element={<Reports />} />
          <Route path="reports/all" element={<AllReports />} />
          <Route path="reports/transactions" element={<TransactionList />} />

          {/* Settings - using wildcard to support nested routing */}
          <Route path="settings/*" element={<Settings />} />

          {/* In-app 404 */}
          <Route path="*" element={<NotFound />} />
        </Route>

        {/* Auth (outside RequireAuth / Layout) */}
        <Route path="auth/login" element={<Login />} />
        <Route path="auth/signup" element={<Signup />} />

        {/* Business registration (public) */}
        <Route path="business/register" element={<BusinessRegister />} />
        <Route path="business/register/success" element={<RegisterSuccess />} />
      </Routes>
    </FormsProvider>
  );
}

export default App;
