/**
 * Calendar Components Barrel File
 * 
 * This file exports all calendar components for internal use.
 * External code should import from the main calendar index:
 * import { Calendar } from '@/features/calendar'
 * 
 * This file is organized by component type:
 * - Core: Main calendar functionality
 * - Views: Different calendar views (day/week/multi-staff)
 * - Slots: Time slot and appointment rendering
 * - Settings: Calendar configuration UI
 * - UI: Supporting UI components
 * - Modals: Modal dialogs
 * - Behaviors: Behavior management components
 * - Connectors: Integration components
 * - Layouts: Layout and container components
 */

// Core Calendar Components
export { default as Calendar } from './core/Calendar'
export { default as CalendarHeader } from './core/CalendarHeader'
export { default as EmployeeHeader } from './core/EmployeeHeader'
export { default as TimeGrid } from './core/TimeGrid'
export { default as WorkingHoursOverlay } from './core/WorkingHoursOverlay'
export { default as CurrentTimeIndicator } from './core/CurrentTimeIndicator'

// View Components
export { default as WeekView } from './views/WeekView'
export { default as DayView } from './views/DayView'
export { default as MultiStaffDayView } from './views/MultiStaffDayView'

// Slot Components
export { default as AppointmentSlot } from './slots/AppointmentSlot'
export { default as TimeSlot } from './slots/TimeSlot'
export { default as AppointmentMoveConfirmation } from './slots/AppointmentMoveConfirmation'
export { default as AppointmentUpdateSlotResizeConfirmation } from './slots/AppointmentUpdateSlotResizeConfirmation'

// Settings Components
export { default as CalendarSettingsPanel } from './settings/CalendarSettingsPanel'
export { default as GeneralSettings } from './settings/GeneralSettings'
export { default as TimeDisplaySettings } from './settings/TimeDisplaySettings'
export { default as ToggleSettings } from './settings/ToggleSettings'

// UI Components
export { default as CalendarSidebar } from './ui/CalendarSidebar'
export { default as CalendarActionSheet } from './ui/CalendarActionSheet'
export { default as MiniCalendar } from './ui/MiniCalendar'
export { default as NavigationFeedback } from './ui/NavigationFeedback'
export { default as StatusLegend } from './ui/StatusLegend'
export { default as StaffHeader } from './ui/StaffHeader'

// Modal Components
export { default as AppointmentCreationModal } from './modals/AppointmentCreationModal'
export { default as AppointmentModal } from './modals/AppointmentModal'
export { default as WaitlistModal } from './modals/WaitlistModal'

// Behavior Components
export { default as DragBehaviorManager } from './behaviors/DragBehaviorManager'

// Connector Components
export { default as EmployeeCalendarConnector } from './connectors/EmployeeCalendarConnector'

// Layout Components
export { default as CalendarContainer } from './layouts/CalendarContainer' 