"""
Management command to set up automatic cleanup of expired temporary signatures.

This command provides instructions and sample cron job configurations for
automatically cleaning up expired temporary signatures.
"""
from django.core.management.base import BaseCommand
from django.conf import settings
import os


class Command(BaseCommand):
    help = 'Set up automatic cleanup of expired temporary signatures'

    def add_arguments(self, parser):
        parser.add_argument(
            '--show-cron-examples',
            action='store_true',
            help='Show example cron job configurations',
        )
        parser.add_argument(
            '--create-script',
            action='store_true',
            help='Create a shell script for cron job execution',
        )

    def handle(self, *args, **options):
        if options['show_cron_examples']:
            self.show_cron_examples()
        
        if options['create_script']:
            self.create_cleanup_script()
        
        if not options['show_cron_examples'] and not options['create_script']:
            self.stdout.write(
                self.style.SUCCESS('Temporary Signature Cleanup Setup')
            )
            self.stdout.write(
                'Use --show-cron-examples to see cron job configurations'
            )
            self.stdout.write(
                'Use --create-script to create a cleanup script'
            )

    def show_cron_examples(self):
        """Show example cron job configurations"""
        self.stdout.write(
            self.style.SUCCESS('\n=== CRON JOB EXAMPLES ===\n')
        )
        
        # Get the current project path
        project_path = settings.BASE_DIR
        python_path = 'python'  # Adjust if using virtual environment
        
        self.stdout.write('1. Clean up expired signatures every hour:')
        self.stdout.write(
            f'0 * * * * cd {project_path} && {python_path} manage.py cleanup_temp_signatures'
        )
        
        self.stdout.write('\n2. Clean up expired signatures every 6 hours:')
        self.stdout.write(
            f'0 */6 * * * cd {project_path} && {python_path} manage.py cleanup_temp_signatures'
        )
        
        self.stdout.write('\n3. Clean up signatures older than 48 hours (daily at 2 AM):')
        self.stdout.write(
            f'0 2 * * * cd {project_path} && {python_path} manage.py cleanup_temp_signatures --older-than-hours=48'
        )
        
        self.stdout.write('\n4. With virtual environment:')
        self.stdout.write(
            f'0 * * * * cd {project_path} && /path/to/venv/bin/python manage.py cleanup_temp_signatures'
        )
        
        self.stdout.write('\n5. With logging:')
        self.stdout.write(
            f'0 * * * * cd {project_path} && {python_path} manage.py cleanup_temp_signatures >> /var/log/signature_cleanup.log 2>&1'
        )
        
        self.stdout.write('\n=== SETUP INSTRUCTIONS ===\n')
        self.stdout.write('1. Choose one of the above cron job configurations')
        self.stdout.write('2. Edit your crontab: crontab -e')
        self.stdout.write('3. Add the chosen line to your crontab')
        self.stdout.write('4. Save and exit')
        self.stdout.write('5. Verify with: crontab -l')

    def create_cleanup_script(self):
        """Create a shell script for cron job execution"""
        project_path = settings.BASE_DIR
        script_path = os.path.join(project_path, 'cleanup_temp_signatures.sh')
        
        script_content = f"""#!/bin/bash
# Temporary Signature Cleanup Script
# Generated by Django management command

# Change to project directory
cd {project_path}

# Activate virtual environment if needed
# source /path/to/venv/bin/activate

# Run cleanup command
python manage.py cleanup_temp_signatures

# Log the result
echo "$(date): Temporary signature cleanup completed" >> /var/log/signature_cleanup.log
"""
        
        try:
            with open(script_path, 'w') as f:
                f.write(script_content)
            
            # Make script executable
            os.chmod(script_path, 0o755)
            
            self.stdout.write(
                self.style.SUCCESS(f'Created cleanup script: {script_path}')
            )
            self.stdout.write(
                'You can now use this script in your cron job:'
            )
            self.stdout.write(
                f'0 * * * * {script_path}'
            )
            
        except Exception as e:
            self.stdout.write(
                self.style.ERROR(f'Failed to create script: {e}')
            )
