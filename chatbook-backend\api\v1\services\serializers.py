from rest_framework import serializers
from services.models import Service, ServiceCategory, AddOn, ServiceAddOn, EmployeeService, StylistLevelService, EmployeeAddOn
from employees.models import Employee
from django.contrib.auth import get_user_model
from appointments.models import Appointment, AppointmentAddOn, AppointmentService, AppointmentHistory
from business.models import BusinessCustomer
from datetime import timedelta
from django.contrib import admin

User = get_user_model()

class JSONCompatibleField(serializers.Field):
    """
    Field that ensures values are JSON serializable by
    converting problematic types to strings
    """
    def to_representation(self, value):
        """Convert value to JSON-compatible representation"""
        # If value is a built-in JSON-serializable type, return it
        if value is None or isinstance(value, (str, int, float, bool, list, dict)):
            return value
        # Otherwise, convert to string
        return str(value)

class AddOnSerializer(serializers.ModelSerializer):
    """Serializer for AddOn model"""
    duration = serializers.SerializerMethodField()
    color = serializers.SerializerMethodField()

    class Meta:
        model = AddOn
        fields = [
            'id',
            'name',
            'description',
            'base_price',
            'duration',
            'color',
            'is_active',
            'image',
            'display_order'
        ]
        extra_kwargs = {
            'name': {'required': False},
            'description': {'required': False},
            'base_price': {'required': False},
            'is_active': {'required': False},
            'display_order': {'required': False}
        }
    
    def get_duration(self, obj):
        """Convert DurationField to minutes for API compatibility"""
        return int(obj.base_duration.total_seconds() / 60)

    def get_color(self, obj):
        """Get the color inherited from the add-on category"""
        return obj.color

class ServiceAddOnSerializer(serializers.ModelSerializer):
    """Serializer for ServiceAddOn model"""
    addon = AddOnSerializer(read_only=True)
    price = serializers.SerializerMethodField()
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = ServiceAddOn
        fields = [
            'id',
            'service',
            'addon',
            'price',
            'duration',
            'is_active',
            'is_required',
            'display_order'
        ]
        extra_kwargs = {
            'is_active': {'required': False},
            'is_required': {'required': False},
            'display_order': {'required': False}
        }
    
    def get_price(self, obj):
        """Get price with override logic"""
        if obj.price_override is not None:
            return obj.price_override
        return obj.addon.base_price
    
    def get_duration(self, obj):
        """Get duration in minutes with override logic"""
        if obj.duration_override is not None:
            return int(obj.duration_override.total_seconds() / 60)
        return int(obj.addon.base_duration.total_seconds() / 60)

class ServiceSerializer(serializers.ModelSerializer):
    """Serializer for Service model"""
    price = serializers.DecimalField(source='base_price', max_digits=10, decimal_places=2, coerce_to_string=True)
    duration = serializers.SerializerMethodField()
    buffer_time = serializers.SerializerMethodField()
    category_name = serializers.SerializerMethodField()
    color = serializers.SerializerMethodField()

    class Meta:
        model = Service
        fields = [
            'id',
            'business',
            'category',
            'category_name',
            'name',
            'description',
            'price',
            'duration',
            'buffer_time',
            'color',
            'is_active',
            'image',
            'display_order'
        ]
        extra_kwargs = {
            'display_order': {'required': False}
        }
    
    def get_duration(self, obj):
        """Convert DurationField to minutes for API compatibility"""
        return int(obj.base_duration.total_seconds() / 60)
    
    def get_buffer_time(self, obj):
        """Convert buffer_time DurationField to minutes"""
        return int(obj.buffer_time.total_seconds() / 60)

    def get_category_name(self, obj):
        """Get the category name for the service"""
        return obj.category.name if obj.category else None

    def get_color(self, obj):
        """Get the color inherited from the service category"""
        return obj.color

class ServiceDetailSerializer(serializers.ModelSerializer):
    """Detailed serializer for Service model including addons and employee services"""
    addons = ServiceAddOnSerializer(source='service_addons', many=True, read_only=True)
    employee_services = serializers.SerializerMethodField()
    price = serializers.DecimalField(source='base_price', max_digits=10, decimal_places=2, coerce_to_string=True)
    duration = serializers.SerializerMethodField()
    buffer_time = serializers.SerializerMethodField()
    total_duration = serializers.SerializerMethodField()
    category_name = serializers.SerializerMethodField()
    color = serializers.SerializerMethodField()
    
    class Meta:
        model = Service
        fields = [
            'id',
            'business',
            'category',
            'category_name',
            'name',
            'description',
            'price',
            'duration',
            'total_duration',
            'buffer_time',
            'color',
            'is_active',
            'image',
            'display_order',
            'addons',
            'employee_services'
        ]
        extra_kwargs = {
            'display_order': {'required': False}
        }
    
    def get_employee_services(self, obj):
        employee_services = EmployeeService.objects.filter(service=obj, is_active=True)
        return EmployeeServiceSerializer(employee_services, many=True).data
    
    def get_duration(self, obj):
        """Convert DurationField to minutes for API compatibility"""
        return int(obj.base_duration.total_seconds() / 60)
    
    def get_buffer_time(self, obj):
        """Convert buffer_time DurationField to minutes"""
        return int(obj.buffer_time.total_seconds() / 60)
    
    def get_total_duration(self, obj):
        """Calculate total duration in minutes"""
        total_seconds = obj.total_duration.total_seconds()
        return int(total_seconds / 60)

    def get_category_name(self, obj):
        """Get the category name for the service"""
        return obj.category.name if obj.category else None

    def get_color(self, obj):
        """Get the color inherited from the service category"""
        return obj.color

class ServiceCategorySerializer(serializers.ModelSerializer):
    """Serializer for service categories"""
    class Meta:
        model = ServiceCategory
        fields = ['id', 'name', 'description', 'color', 'order', 'is_active']
        extra_kwargs = {
            'name': {'required': False},  # For PATCH requests
            'description': {'required': False},
            'color': {'required': False},
            'order': {'required': False},
            'is_active': {'required': False}
        }

    def validate_color(self, value):
        """Validate that color is one of the predefined choices"""
        if value:
            from services.models import ServiceCategory
            valid_colors = [choice[0] for choice in ServiceCategory.COLOR_CHOICES]
            if value not in valid_colors:
                raise serializers.ValidationError(
                    'Please select a color from the available options.'
                )
        return value

class EmployeeBasicSerializer(serializers.ModelSerializer):
    """Basic serializer for Employee model"""
    full_name = serializers.CharField(read_only=True)
    user_details = serializers.SerializerMethodField()
    stylist_level_display = serializers.SerializerMethodField()
    
    class Meta:
        model = Employee
        fields = [
            'id',
            'user',
            'business',
            'is_active',
            'full_name',
            'stylist_level',
            'stylist_level_display',
            'user_details'
        ]
    
    def get_user_details(self, obj):
        """Get basic user details"""
        if obj.user:
            return {
                'id': obj.user.id,
                'email': obj.user.email,
                'first_name': obj.user.first_name,
                'last_name': obj.user.last_name
            }
        return None
    
    def get_stylist_level_display(self, obj):
        """Get the stylist level name"""
        if obj.stylist_level:
            return obj.stylist_level.name
        return "No Level"

class EmployeeServiceSerializer(serializers.ModelSerializer):
    """Serializer for EmployeeService model"""
    employee = EmployeeBasicSerializer(read_only=True)
    price = serializers.SerializerMethodField()
    duration = serializers.SerializerMethodField()
    
    class Meta:
        model = EmployeeService
        fields = [
            'id',
            'business',
            'employee',
            'service',
            'price',
            'duration',
            'is_active'
        ]
        extra_kwargs = {
            'business': {'required': False},
            'is_active': {'required': False}
        }
    
    def get_price(self, obj):
        """Get the effective price (either custom or service default)"""
        return obj.price
    
    def get_duration(self, obj):
        """Convert duration to minutes"""
        return int(obj.duration.total_seconds() / 60)

class EmployeeAddOnSerializer(serializers.ModelSerializer):
    """Serializer for EmployeeAddOn model"""
    addon_id = serializers.IntegerField(source='addon.id', read_only=True)
    addon_name = serializers.CharField(source='addon.name', read_only=True)
    addon_short_name = serializers.CharField(source='addon.short_name', read_only=True)
    addon_description = serializers.CharField(source='addon.description', read_only=True)
    addon_display_order = serializers.IntegerField(source='addon.display_order', read_only=True)
    category_id = serializers.IntegerField(source='addon.category.id', read_only=True, allow_null=True)
    category_name = serializers.CharField(source='addon.category.name', read_only=True, allow_null=True)
    category_display_order = serializers.IntegerField(source='addon.category.order', read_only=True, allow_null=True)
    price = serializers.SerializerMethodField()
    duration = serializers.SerializerMethodField()
    image = serializers.SerializerMethodField()
    price_source = serializers.SerializerMethodField()
    
    class Meta:
        model = EmployeeAddOn
        fields = [
            'addon_id',
            'addon_name',
            'addon_short_name',
            'addon_description',
            'addon_display_order',
            'category_id',
            'category_name',
            'category_display_order',
            'price',
            'duration',
            'image',
            'price_source'
        ]
    
    def get_image(self, obj):
        """Get the image URL if it exists"""
        if obj.addon.image:
            return obj.addon.image.url
        return None
    
    def get_price(self, obj):
        """Get the effective price (either custom or addon default)"""
        return obj.price
    
    def get_duration(self, obj):
        """Convert duration to minutes"""
        return int(obj.duration.total_seconds() / 60)
        
    def get_price_source(self, obj):
        """Determine the source of price and duration for this addon"""
        # Check if it's a custom employee price
        if hasattr(obj, 'id') and obj.id is not None:
            if obj.custom_price and obj.custom_price > 0:
                return "Employee Custom Price"
            if obj.custom_duration:
                return "Employee Custom Duration"
            return "Employee Standard Add-on"
        
        # It's coming from stylist level
        if obj.employee.stylist_level:
            return f"{obj.employee.stylist_level.name} Price"
        
        # Fallback to base addon
        return "Base Add-on Price"

class StylistLevelServiceSerializer(serializers.ModelSerializer):
    """Serializer for StylistLevelService model"""
    duration_minutes = serializers.SerializerMethodField()
    service_name = serializers.SerializerMethodField()
    stylist_level_display = serializers.SerializerMethodField()
    
    class Meta:
        model = StylistLevelService
        fields = [
            'id',
            'business',
            'service',
            'service_name',
            'stylist_level',
            'stylist_level_display',
            'price',
            'duration',
            'duration_minutes',
            'is_active',
            'created_at',
            'updated_at'
        ]
        extra_kwargs = {
            'business': {'required': True},
            'service': {'required': True},
            'is_active': {'required': False},
            'created_at': {'read_only': True},
            'updated_at': {'read_only': True}
        }
    
    def get_duration_minutes(self, obj):
        """Convert duration to minutes for API compatibility"""
        return int(obj.duration.total_seconds() / 60)
    
    def get_service_name(self, obj):
        """Get the service name"""
        return obj.service.name
    
    def get_stylist_level_display(self, obj):
        """Get the human-readable stylist level"""
        if obj.stylist_level:
            return obj.stylist_level.name
        return "No Level"
        
    def validate(self, data):
        """Validate business and service relationship"""
        business = data.get('business')
        service = data.get('service')
        
        if business and service and service.business_id != business.id:
            raise serializers.ValidationError(
                {'service': 'Service must belong to the specified business.'}
            )
        
        return data
    
    def create(self, validated_data):
        if 'duration' in validated_data and isinstance(validated_data['duration'], int):
            # Convert minutes to duration
            validated_data['duration'] = timedelta(minutes=validated_data['duration'])
        return super().create(validated_data)
    
    def update(self, instance, validated_data):
        if 'duration' in validated_data and isinstance(validated_data['duration'], int):
            # Convert minutes to duration
            validated_data['duration'] = timedelta(minutes=validated_data['duration'])
        return super().update(instance, validated_data)

class EmployeeWithServicesSerializer(serializers.ModelSerializer):
    """Serializer for Employee model including their services"""
    services = EmployeeServiceSerializer(source='employee_services', many=True, read_only=True)
    full_name = serializers.CharField(read_only=True)
    user_details = serializers.SerializerMethodField()
    stylist_level_display = serializers.SerializerMethodField()
    
    class Meta:
        model = Employee
        fields = [
            'id',
            'user',
            'business',
            'is_active',
            'full_name',
            'user_details',
            'stylist_level',
            'stylist_level_display',
            'services'
        ]
    
    def get_user_details(self, obj):
        """Get basic user details"""
        if obj.user:
            return {
                'id': obj.user.id,
                'email': obj.user.email,
                'first_name': obj.user.first_name,
                'last_name': obj.user.last_name
            }
        return None
        
    def get_stylist_level_display(self, obj):
        """Get the stylist level name"""
        if obj.stylist_level:
            return obj.stylist_level.name
        return "No Level"

class AppointmentServiceSerializer(serializers.ModelSerializer):
    """
    Serializer for the AppointmentService model (through model for appointments and services)
    """
    service_name = serializers.CharField(source='service.name', read_only=True)
    service_short_name = serializers.CharField(source='service.short_name', read_only=True)
    service_category = serializers.CharField(source='service.category.name', read_only=True)
    color = serializers.CharField(source='service.color', read_only=True)

    class Meta:
        model = Appointment.services.through
        fields = [
            'id',
            'service',
            'service_name',
            'service_short_name',
            'service_category',
            'color',
            'quantity',
            'base_price',
            'price_override',
            'duration',
            'buffer_time',
            'notes'
        ]
        read_only_fields = ['id', 'service_name', 'service_short_name', 'service_category', 'color']
        
    def to_representation(self, instance):
        """
        Override to_representation to ensure all fields are JSON serializable
        """
        ret = super().to_representation(instance)
        
        # Handle any decimal fields or complex objects
        for field_name, value in ret.items():
            if hasattr(value, 'is_finite') and not value.is_finite():  # Check for Decimal NaN, infinity, etc.
                ret[field_name] = None
            elif not isinstance(value, (str, int, float, bool, type(None), list, dict)):
                ret[field_name] = str(value)
                
        return ret

class AppointmentAddOnSerializer(serializers.ModelSerializer):
    """
    Serializer for the AppointmentAddOn model
    """
    add_on_name = serializers.CharField(source='add_on.name', read_only=True)
    add_on_short_name = serializers.CharField(source='add_on.short_name', read_only=True)
    color = serializers.CharField(source='add_on.color', read_only=True)

    class Meta:
        model = AppointmentAddOn
        fields = [
            'id',
            'add_on',
            'add_on_name',
            'add_on_short_name',
            'color',
            'add_on_price',
            'duration'
        ]
        read_only_fields = ['id', 'add_on_name', 'add_on_short_name', 'color']
        
    def to_representation(self, instance):
        """
        Override to_representation to ensure all fields are JSON serializable
        """
        ret = super().to_representation(instance)
        
        # Handle any decimal fields or complex objects
        for field_name, value in ret.items():
            if hasattr(value, 'is_finite') and not value.is_finite():  # Check for Decimal NaN, infinity, etc.
                ret[field_name] = None
            elif not isinstance(value, (str, int, float, bool, type(None), list, dict)):
                ret[field_name] = str(value)
                
        return ret


class AppointmentHistorySerializer(serializers.ModelSerializer):
    """
    Serializer for appointment history entries.
    """
    action_display = serializers.CharField(source='get_action_display', read_only=True)
    change_details = serializers.SerializerMethodField()

    class Meta:
        model = AppointmentHistory
        fields = [
            'id',
            'appointment',
            'action',
            'action_display',
            'modified_by',
            'change_summary',
            'change_details',
            'ip_address',
            'user_agent',
            'modified_at',
        ]
        read_only_fields = ['id', 'modified_by', 'modified_at']

    def get_change_details(self, obj):
        """Get detailed change information"""
        return obj.get_change_details()


class AppointmentSerializer(serializers.ModelSerializer):
    """
    Serializer for the Appointment model
    """
    end_time = serializers.DateTimeField(read_only=True)
    total_duration = serializers.IntegerField(read_only=True)
    total_price = serializers.DecimalField(max_digits=10, decimal_places=2, read_only=True)
    appointment_services = AppointmentServiceSerializer(many=True, read_only=False, required=False)
    appointment_add_ons = AppointmentAddOnSerializer(many=True, read_only=False, required=False)
    buffer_time = serializers.SerializerMethodField(read_only=True)
    customer = serializers.PrimaryKeyRelatedField(
        queryset=BusinessCustomer.objects.all(),
        required=True
    )
    customer_name = serializers.SerializerMethodField(read_only=True)
    employee_name = serializers.SerializerMethodField(read_only=True)
    
    class Meta:
        model = Appointment
        fields = [
            'id',
            'customer',
            'customer_name',
            'employee',
            'employee_name',
            'appointment_services',
            'appointment_add_ons',
            'start_time',
            'end_time',
            'status',
            'payment_status',
            'source',
            'notes_from_customer',
            'cancellation_reason',
            'total_duration',
            'buffer_time',
            'total_price',
            'created_at',
            'updated_at'
        ]
        read_only_fields = ['id', 'end_time', 'total_duration', 'buffer_time', 'total_price', 'created_at', 'updated_at']
    
    def get_buffer_time(self, obj):
        """
        Get the total buffer time for the appointment using the appointment-specific buffer_time
        """
        total_buffer = 0
        for app_service in obj.appointment_services.all():
            # Use the appointment-specific buffer_time (already in minutes)
            total_buffer += app_service.buffer_time
        return total_buffer
    
    def get_customer_name(self, obj):
        """Get the customer's full name"""
        try:
            user = obj.customer.customer.user
            return user.get_full_name() or user.email
        except AttributeError:
            return "Unknown Customer"
    
    def get_employee_name(self, obj):
        """Get the employee's full name"""
        try:
            user = obj.employee.user
            return user.get_full_name() or user.email
        except AttributeError:
            return "Unknown Employee"
    
    def validate(self, data):
        """
        Validate that the employee and customer belong to the same business
        """
        employee = data.get('employee')
        customer = data.get('customer')
        
        if employee and customer:
            # Check if employee and customer belong to the same business
            if employee.business_id != customer.business_id:
                raise serializers.ValidationError(
                    "Employee and customer must belong to the same business."
                )
        
        return data
    
    def create(self, validated_data):
        """
        Create an appointment with nested services and add-ons
        """
        from appointments.models import AppointmentService, AppointmentAddOn

        # Extract nested data
        services_data = validated_data.pop('appointment_services', [])
        addons_data = validated_data.pop('appointment_add_ons', [])

        # Create appointment
        appointment = Appointment.objects.create(**validated_data)

        # Create services with custom pricing if provided
        for service_data in services_data:
            # For new appointments, ensure required fields have defaults if not provided
            if 'quantity' not in service_data:
                service_data['quantity'] = 1
            if 'buffer_time' not in service_data:
                service_data['buffer_time'] = 0
            if 'notes' not in service_data:
                service_data['notes'] = ''

            # Check if custom pricing/duration is provided
            has_custom_price = 'base_price' in service_data
            has_custom_duration = 'duration' in service_data
            skip_auto_pricing = has_custom_price or has_custom_duration

            service = AppointmentService(appointment=appointment, **service_data)
            service.save(skip_auto_pricing=skip_auto_pricing)

        # Create add-ons with custom pricing if provided
        for addon_data in addons_data:
            # Check if custom pricing/duration is provided
            has_custom_price = 'add_on_price' in addon_data
            has_custom_duration = 'duration' in addon_data
            skip_auto_pricing = has_custom_price or has_custom_duration

            addon = AppointmentAddOn(appointment=appointment, **addon_data)
            addon.save(skip_auto_pricing=skip_auto_pricing)

        return appointment
    
    def update(self, instance, validated_data):
        """
        Update an appointment with nested services and add-ons.
        Updates existing services/add-ons in place instead of deleting and recreating.
        """
        from appointments.models import AppointmentService, AppointmentAddOn
        from appointments.history import AppointmentHistoryTracker

        # Start batching history entries for this update
        AppointmentHistoryTracker.start_batch_tracking()

        try:
            # Extract nested data
            services_data = validated_data.pop('appointment_services', None)
            addons_data = validated_data.pop('appointment_add_ons', None)

            # Check if start_time is being updated and handle status change logic
            new_start_time = validated_data.get('start_time')
            if new_start_time and instance.start_time != new_start_time:
                # Handle status change when rescheduling
                if instance.handle_status_on_reschedule(new_start_time):
                    validated_data['status'] = 'accepted'

            # Update appointment fields
            for attr, value in validated_data.items():
                setattr(instance, attr, value)
            instance.save()

            # Update services if provided
            if services_data is not None:
                # Get existing services by service ID
                existing_services = {
                    svc.service_id: svc
                    for svc in instance.appointment_services.all()
                }

                # Track which services are in the request
                requested_service_ids = set()

                for service_data in services_data:
                    service_id = service_data['service'].id if hasattr(service_data['service'], 'id') else service_data['service']
                    requested_service_ids.add(service_id)

                    if service_id in existing_services:
                        # Update existing service
                        existing_service = existing_services[service_id]

                        # Check what fields actually changed
                        changed_fields = []
                        for field, new_value in service_data.items():
                            if field == 'service':
                                continue  # Skip service field itself

                            current_value = getattr(existing_service, field, None)
                            if current_value != new_value:
                                setattr(existing_service, field, new_value)
                                changed_fields.append(field)

                        # Only save if something actually changed
                        if changed_fields:
                            # Determine if we should skip auto pricing
                            has_custom_price = 'base_price' in service_data
                            has_custom_duration = 'duration' in service_data
                            skip_auto_pricing = has_custom_price or has_custom_duration

                            existing_service.save(skip_auto_pricing=skip_auto_pricing)
                    else:
                        # Create new service
                        if 'quantity' not in service_data:
                            service_data['quantity'] = 1
                        if 'buffer_time' not in service_data:
                            service_data['buffer_time'] = 0
                        if 'notes' not in service_data:
                            service_data['notes'] = ''

                        has_custom_price = 'base_price' in service_data
                        has_custom_duration = 'duration' in service_data
                        skip_auto_pricing = has_custom_price or has_custom_duration

                        service = AppointmentService(appointment=instance, **service_data)
                        service.save(skip_auto_pricing=skip_auto_pricing)

                # Remove services that are no longer in the request
                for service_id, existing_service in existing_services.items():
                    if service_id not in requested_service_ids:
                        existing_service.delete()

            # Update add-ons if provided
            if addons_data is not None:
                # Get existing add-ons by add-on ID
                existing_addons = {
                    addon.add_on_id: addon
                    for addon in instance.appointment_add_ons.all()
                }

                # Track which add-ons are in the request
                requested_addon_ids = set()

                for addon_data in addons_data:
                    addon_id = addon_data['add_on'].id if hasattr(addon_data['add_on'], 'id') else addon_data['add_on']
                    requested_addon_ids.add(addon_id)

                    if addon_id in existing_addons:
                        # Update existing add-on
                        existing_addon = existing_addons[addon_id]

                        # Check what fields actually changed
                        changed_fields = []
                        for field, new_value in addon_data.items():
                            if field == 'add_on':
                                continue  # Skip add_on field itself

                            current_value = getattr(existing_addon, field, None)
                            if current_value != new_value:
                                setattr(existing_addon, field, new_value)
                                changed_fields.append(field)

                        # Only save if something actually changed
                        if changed_fields:
                            # Determine if we should skip auto pricing
                            has_custom_price = 'add_on_price' in addon_data
                            has_custom_duration = 'duration' in addon_data
                            skip_auto_pricing = has_custom_price or has_custom_duration

                            existing_addon.save(skip_auto_pricing=skip_auto_pricing)
                    else:
                        # Create new add-on
                        has_custom_price = 'add_on_price' in addon_data
                        has_custom_duration = 'duration' in addon_data
                        skip_auto_pricing = has_custom_price or has_custom_duration

                        addon = AppointmentAddOn(appointment=instance, **addon_data)
                        addon.save(skip_auto_pricing=skip_auto_pricing)

                # Remove add-ons that are no longer in the request
                for addon_id, existing_addon in existing_addons.items():
                    if addon_id not in requested_addon_ids:
                        existing_addon.delete()

        finally:
            # End batching and create consolidated history entry
            AppointmentHistoryTracker.end_batch_tracking()

        return instance
        
    def to_representation(self, instance):
        """
        Override to_representation to ensure all fields are JSON serializable
        """
        ret = super().to_representation(instance)
        
        # Handle any decimal fields that haven't been properly converted
        for field_name, value in ret.items():
            if hasattr(value, 'is_finite') and not value.is_finite():  # Check for Decimal NaN, infinity, etc.
                ret[field_name] = None
            elif not isinstance(value, (str, int, float, bool, type(None), list, dict)):
                ret[field_name] = str(value)
                
        return ret

class DetailedAppointmentSerializer(AppointmentSerializer):
    """
    Detailed serializer for Appointment including nested data for customer, employee and services
    """
    customer_full_name = serializers.SerializerMethodField()
    employee_full_name = serializers.SerializerMethodField()
    
    class Meta(AppointmentSerializer.Meta):
        fields = AppointmentSerializer.Meta.fields + [
            'customer_full_name',
            'employee_full_name'
        ]
    
    def get_customer_full_name(self, obj):
        """Get customer's full name"""
        if obj.customer and obj.customer.customer and obj.customer.customer.user:
            customer_user = obj.customer.customer.user
            return f"{customer_user.first_name} {customer_user.last_name}".strip()
        return ""
    
    def get_employee_full_name(self, obj):
        """Get employee's full name"""
        return f"{obj.employee.user.first_name} {obj.employee.user.last_name}".strip()

class EmployeeServiceWithDetailsSerializer(serializers.ModelSerializer):
    """
    Serializer for returning a service with employee-specific
    pricing and duration details.
    """
    service_details = serializers.SerializerMethodField()
    price = serializers.SerializerMethodField()
    duration = serializers.SerializerMethodField()
    service_id = serializers.IntegerField(source='service.id', read_only=True)
    service_name = serializers.CharField(source='service.name', read_only=True)
    service_short_name = serializers.CharField(source='service.short_name', read_only=True)
    category_id = serializers.IntegerField(source='service.category.id', read_only=True)
    category_name = serializers.CharField(source='service.category.name', read_only=True)
    service_display_order = serializers.IntegerField(source='service.display_order', read_only=True)
    category_display_order = serializers.IntegerField(source='service.category.order', read_only=True)
    price_source = serializers.SerializerMethodField()
    
    class Meta:
        model = EmployeeService
        fields = [
            'service_id',
            'service_name',
            'service_short_name',
            'category_id',
            'category_name',
            'service_display_order',
            'category_display_order',
            'price',
            'duration',
            'price_source',
            'service_details',
        ]
    
    def get_service_details(self, obj):
        """Get basic details about the service"""
        return {
            'description': obj.service.description,
            'image': obj.service.image.url if obj.service.image else None,
            'buffer_time': int(obj.service.buffer_time.total_seconds() / 60),
            'show_online': obj.service.show_online
        }
    
    def get_price(self, obj):
        """Get the effective price (using employee service logic)"""
        return obj.price
    
    def get_duration(self, obj):
        """Convert duration to minutes (using employee service logic)"""
        return int(obj.duration.total_seconds() / 60)
        
    def get_price_source(self, obj):
        """Determine the source of price and duration for this service"""
        # Check if it's a custom employee price
        if hasattr(obj, 'id') and obj.id is not None:
            if obj.custom_price and obj.custom_price > 0:
                return "Employee Custom Price"
            if obj.custom_duration:
                return "Employee Custom Duration"
            return "Employee Standard Service"
        
        # It's coming from stylist level
        if obj.employee.stylist_level:
            return f"{obj.employee.stylist_level.name} Price"
        
        # Fallback to base service
        return "Base Service Price"