import { useState } from 'react'

/**
 * Hook for managing action sheet state
 * Extracted from CalendarActionSheet.jsx for better separation of concerns
 */
export const useActionSheet = () => {
  const [actionSheet, setActionSheet] = useState({
    isOpen: false,
    date: null,
    appointment: null,
    employee: null,
    position: { x: 0, y: 0 },
    timeSlotOccupied: false,
    conflictingAppointments: []
  })

  const showActionSheet = (config) => {
    setActionSheet({
      isOpen: true,
      ...config
    })
  }

  const hideActionSheet = () => {
    setActionSheet(prev => ({
      ...prev,
      isOpen: false
    }))
  }

  const showTimeSlotActionSheet = (date, employee, event, occupancyInfo = {}) => {
    const rect = event?.currentTarget?.getBoundingClientRect()
    showActionSheet({
      date,
      employee,
      appointment: null,
      position: rect ? { x: rect.right, y: rect.top } : { x: 0, y: 0 },
      timeSlotOccupied: occupancyInfo.timeSlotOccupied || false,
      conflictingAppointments: occupancyInfo.conflictingAppointments || []
    })
  }

  const showAppointmentActionSheet = (appointment, event) => {
    const rect = event?.currentTarget?.getBoundingClientRect()
    showActionSheet({
      date: appointment.start,
      appointment,
      employee: null,
      position: rect ? { x: rect.right, y: rect.top } : { x: 0, y: 0 }
    })
  }

  return {
    actionSheet,
    showActionSheet,
    hideActionSheet,
    showTimeSlotActionSheet,
    showAppointmentActionSheet
  }
} 