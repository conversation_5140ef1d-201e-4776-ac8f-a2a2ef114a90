from django.db import models
import uuid

class UserDevice(models.Model):
    """
    Model for storing user device information
    """
    class DeviceType(models.TextChoices):
        IOS = 'ios', 'iOS'
        ANDROID = 'android', 'Android'
        WEB = 'web', 'Web'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('accounts.User', on_delete=models.CASCADE, related_name='devices')
    device_id = models.CharField(max_length=255, unique=True)
    device_type = models.CharField(max_length=50, choices=DeviceType.choices)
    device_name = models.CharField(max_length=255)
    push_token = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    last_used = models.DateTimeField(auto_now=True)
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'User Device'
        verbose_name_plural = 'User Devices'
        ordering = ['-last_used']

    def __str__(self):
        return f"{self.device_name} ({self.device_type}) - {self.user.email}" 