import React, { useState, useEffect, useMemo } from 'react'
import { PlusIcon, ChevronDoubleLeftIcon, ChevronDoubleRightIcon } from '@heroicons/react/24/outline'
import { employeeApiService } from '../services'
import { 
  EmployeeModal, 
  EmployeeListItem, 
  EmployeeDetailPanel 
} from '../components'
import Toast from '../../../pages/Settings/components/Toast'
import ConfirmDialog from '../../../pages/Settings/components/ConfirmDialog'

// Main Employee Profile component (named EmployeeProfile for Settings module)
const EmployeeProfile = () => {
  const [employees, setEmployees] = useState([])
  const [selectedEmployee, setSelectedEmployee] = useState(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState(null)
  const [showAddModal, setShowAddModal] = useState(false)
  const [editingEmployee, setEditingEmployee] = useState(null)
  const [loadingStates, setLoadingStates] = useState({})
  const [toast, setToast] = useState({ message: '', type: 'success', isVisible: false })
  const [confirmDialog, setConfirmDialog] = useState({ isOpen: false, employee: null, action: null })
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false)

  // Toggle sidebar collapse
  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  // Fetch employees on component mount
  useEffect(() => {
    fetchEmployees()
  }, [])

  const fetchEmployees = async () => {
    try {
      setIsLoading(true)
      setError(null)
      const employeeData = await employeeApiService.getAllEmployees()
      
      // Transform employee data if needed
      const transformedEmployees = employeeData.map((emp, index) => {
        const fullName = emp.full_name || 'Unknown Employee'
        const nameParts = fullName.split(' ')
        const firstName = nameParts[0] || ''
        const lastName = nameParts.slice(1).join(' ') || ''
        
        return {
          ...emp,
          avatar: (firstName[0] || '') + (lastName[0] || '') || (fullName[0] || '') + (fullName[1] || '') || 'U',
          color: emp.color || ['#3b82f6', '#10b981', '#f59e0b', '#ef4444', '#8b5cf6'][index % 5]
        }
      })
      
      setEmployees(transformedEmployees)
    } catch (err) {
      console.error('Failed to fetch employees:', err)
      setError('Failed to load employees. Please try again.')
    } finally {
      setIsLoading(false)
    }
  }

  // Filter employees based on search query
  const filteredEmployees = useMemo(() => {
    if (!searchQuery.trim()) return employees
    
    const query = searchQuery.toLowerCase()
    return employees.filter(employee => 
      employee.full_name?.toLowerCase().includes(query) ||
      employee.user_details?.email?.toLowerCase().includes(query) ||
      employee.stylist_level_display?.toLowerCase().includes(query)
    )
  }, [employees, searchQuery])

  const handleEmployeeSelect = (employee) => {
    setSelectedEmployee(employee)
  }

  const handleAddEmployee = () => {
    setEditingEmployee(null)
    setShowAddModal(true)
  }

  const handleEditEmployee = (employee) => {
    setEditingEmployee(employee)
    setShowAddModal(true)
  }

  const handleSaveEmployee = (savedEmployee) => {
    if (editingEmployee) {
      // Update existing employee in the list
      setEmployees(prev => prev.map(emp => 
        emp.id === savedEmployee.id ? savedEmployee : emp
      ))
    } else {
      // Add new employee to the list
      setEmployees(prev => [...prev, savedEmployee])
    }
    setShowAddModal(false)
    setEditingEmployee(null)
  }

  const handleConfirmAction = () => {
    if (confirmDialog.action === 'deactivate') {
      performToggleEmployeeStatus(confirmDialog.employee)
    }
    hideConfirmDialog()
  }

  const handleCloseModal = () => {
    setShowAddModal(false)
    setEditingEmployee(null)
  }

  const showToast = (message, type = 'success') => {
    setToast({ message, type, isVisible: true })
  }

  const hideToast = () => {
    setToast(prev => ({ ...prev, isVisible: false }))
  }

  const showConfirmDialog = (employee, action) => {
    setConfirmDialog({ isOpen: true, employee, action })
  }

  const hideConfirmDialog = () => {
    setConfirmDialog({ isOpen: false, employee: null, action: null })
  }

  const handleToggleEmployeeStatus = (employee) => {
    // If deactivating (currently active), show confirmation
    if (employee.is_active) {
      showConfirmDialog(employee, 'deactivate')
    } else {
      // If activating, proceed directly
      performToggleEmployeeStatus(employee)
    }
  }

  const performToggleEmployeeStatus = async (employee) => {
    setLoadingStates(prev => ({ ...prev, status: true }))
    try {
      const updatedEmployee = await employeeApiService.updateEmployee(employee.id, {
        is_active: !employee.is_active
      })
      
      const newStatus = !employee.is_active
      
      // Update employee in list
      setEmployees(prev => prev.map(emp => 
        emp.id === employee.id ? { ...emp, is_active: newStatus } : emp
      ))
      
      // Update selected employee if it's the same one
      if (selectedEmployee?.id === employee.id) {
        setSelectedEmployee(prev => ({ ...prev, is_active: newStatus }))
      }

      // Show success message
      showToast(
        `${employee.full_name} has been ${newStatus ? 'activated' : 'deactivated'}`,
        'success'
      )
    } catch (error) {
      console.error('Failed to toggle employee status:', error)
      showToast('Failed to update employee status. Please try again.', 'error')
    } finally {
      setLoadingStates(prev => ({ ...prev, status: false }))
    }
  }

  const handleToggleOnlineBooking = async (employee) => {
    setLoadingStates(prev => ({ ...prev, booking: true }))
    try {
      const updatedEmployee = await employeeApiService.updateEmployee(employee.id, {
        accept_online_bookings: !employee.accept_online_bookings
      })
      
      const newBookingStatus = !employee.accept_online_bookings
      
      // Update employee in list
      setEmployees(prev => prev.map(emp => 
        emp.id === employee.id ? { ...emp, accept_online_bookings: newBookingStatus } : emp
      ))
      
      // Update selected employee if it's the same one
      if (selectedEmployee?.id === employee.id) {
        setSelectedEmployee(prev => ({ ...prev, accept_online_bookings: newBookingStatus }))
      }

      // Show success message
      showToast(
        `Online booking ${newBookingStatus ? 'enabled' : 'disabled'} for ${employee.full_name}`,
        'success'
      )
    } catch (error) {
      console.error('Failed to toggle online booking:', error)
      showToast('Failed to update online booking setting. Please try again.', 'error')
    } finally {
      setLoadingStates(prev => ({ ...prev, booking: false }))
    }
  }

  if (error) {
    return (
      <div className="flex-1 flex items-center justify-center h-full">
        <div className="text-center">
          <svg className="w-12 h-12 text-red-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Error loading employees</h3>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={fetchEmployees}
            className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Try Again
          </button>
        </div>
      </div>
    )
  }

  return (
    <div className="flex-1 h-full relative">
      <div className="w-full h-full">
        <div className="flex h-full">
          {/* Sidebar - Employee List Panel */}
          {!sidebarCollapsed && (
            <div className="w-80 xl:w-96 bg-white border-r border-gray-200 flex flex-col transition-all duration-300 ease-in-out">
              {/* Header */}
              <div className="p-4 lg:p-6 border-b border-gray-200 flex-shrink-0">
                <div className="flex items-center justify-between">
                  <h1 className="text-lg lg:text-xl font-semibold text-gray-900">Employee Profiles</h1>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={handleAddEmployee}
                      className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
                      title="Add Employee"
                    >
                      <PlusIcon className="w-5 h-5" />
                    </button>
                    <button
                      onClick={toggleSidebar}
                      className="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg"
                      title="Collapse sidebar"
                    >
                      <ChevronDoubleLeftIcon className="w-5 h-5" />
                    </button>
                  </div>
                </div>
                
                {/* Search */}
                <div className="relative mt-4">
                  <svg className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  <input
                    type="text"
                    placeholder="Search employees..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500 text-sm"
                  />
                </div>
              </div>

              {/* Employee List */}
              <div className="flex-1 overflow-y-auto min-h-0">
                {isLoading ? (
                  <div className="flex items-center justify-center py-12">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                  </div>
                ) : filteredEmployees.length === 0 ? (
                  <div className="text-center py-12">
                    <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                    </svg>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">
                      {searchQuery ? 'No employees found' : 'No employees yet'}
                    </h3>
                    <p className="text-gray-600">
                      {searchQuery ? 'Try adjusting your search terms' : 'Get started by adding your first employee'}
                    </p>
                  </div>
                ) : (
                  filteredEmployees.map((employee) => (
                    <EmployeeListItem
                      key={employee.id}
                      employee={employee}
                      isSelected={selectedEmployee?.id === employee.id}
                      onClick={handleEmployeeSelect}
                    />
                  ))
                )}
              </div>
            </div>
          )}

          {/* Main Content - Employee Detail Panel */}
          <div className="flex-1 bg-white min-h-0">
            <EmployeeDetailPanel
              employee={selectedEmployee}
              onClose={() => setSelectedEmployee(null)}
              onEdit={handleEditEmployee}
              onToggleStatus={handleToggleEmployeeStatus}
              onToggleBooking={handleToggleOnlineBooking}
              loadingStates={loadingStates}
            />
          </div>
        </div>
      </div>

      {/* Floating Sidebar Toggle - Only shown when collapsed */}
      {sidebarCollapsed && (
        <div className="absolute left-0 top-1/2 transform -translate-y-1/2 z-10">
          <button
            onClick={toggleSidebar}
            className="bg-white border border-gray-300 rounded-r-lg shadow-lg hover:shadow-xl hover:bg-gray-50 transition-all duration-200 group px-1.5 py-4"
            title="Expand sidebar"
          >
            <ChevronDoubleRightIcon className="w-2 h-5 text-gray-600 group-hover:text-gray-800" />
          </button>
        </div>
      )}

      {/* Employee Modal */}
      <EmployeeModal
        isOpen={showAddModal}
        onClose={handleCloseModal}
        employee={editingEmployee}
        onSave={handleSaveEmployee}
      />

      {/* Toast Notifications */}
      <Toast
        message={toast.message}
        type={toast.type}
        isVisible={toast.isVisible}
        onClose={hideToast}
      />

      {/* Confirmation Dialog */}
      <ConfirmDialog
        isOpen={confirmDialog.isOpen}
        onClose={hideConfirmDialog}
        onConfirm={handleConfirmAction}
        title="Deactivate Employee"
        message={`Are you sure you want to deactivate ${confirmDialog.employee?.full_name}? They will no longer be able to accept new appointments or access the system.`}
        confirmText="Deactivate"
        cancelText="Cancel"
        type="warning"
      />
    </div>
  )
}

export default EmployeeProfile 