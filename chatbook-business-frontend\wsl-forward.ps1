# Run this script on Windows as Administrator to forward port 3000 to WSL
# Save this file as wsl-forward.ps1 and run from PowerShell as admin

# Get the WSL IP address
$wslIP = (wsl -- ip -o -4 -json addr list eth0 | ConvertFrom-Json).addr_info.local

# Remove any existing port proxy
netsh interface portproxy delete v4tov4 listenport=3000 listenaddress=0.0.0.0

# Add new port proxy
netsh interface portproxy add v4tov4 listenport=3000 listenaddress=0.0.0.0 connectport=3000 connectaddress=$wslIP

# Show the configured port proxies
netsh interface portproxy show all

Write-Host "Port forwarding from Windows port 3000 to WSL IP $wslIP port 3000 is now set up."
Write-Host "You should now be able to access http://localhost:3000 from your Windows browser." 