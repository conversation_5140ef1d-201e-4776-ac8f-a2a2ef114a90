#!/bin/bash

# Check if the original file exists
if [ -f "business/migrations/0002_add_stylist_level.py" ]; then
    # Make a copy with the new name
    cp business/migrations/0002_add_stylist_level.py business/migrations/0012_add_stylist_level.py
    
    # Remove the old file
    rm business/migrations/0002_add_stylist_level.py
    
    echo "Successfully renamed migration file to 0012_add_stylist_level.py"
else
    echo "Original migration file not found. It may have already been renamed."
fi

# Remove any __pycache__ files to prevent cached imports
find business/migrations/ -name "__pycache__" -type d -exec rm -rf {} +
find employees/migrations/ -name "__pycache__" -type d -exec rm -rf {} +
find services/migrations/ -name "__pycache__" -type d -exec rm -rf {} +

echo "Cleared migration cache files."
echo "Run 'python manage.py migrate' to apply migrations." 