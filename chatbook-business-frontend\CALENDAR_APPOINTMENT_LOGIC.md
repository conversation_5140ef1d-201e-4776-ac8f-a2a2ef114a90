# 📅 Calendar Appointment Logic - Complete Business Logic Guide

## 📋 Table of Contents

1. [Working Hours Management](#1-working-hours-management)
2. [<PERSON>uff<PERSON> Time System](#2-buffer-time-system)
3. [Drag & Drop Implementation](#3-drag--drop-implementation)
4. [Appointment Block Resize Logic](#4-appointment-block-resize-logic)
5. [Django API Integration](#5-django-api-integration)
6. [iOS to React Integration](#6-ios-to-react-integration)
7. [Implementation Examples](#7-implementation-examples)

---

## 1. 🕐 Working Hours Management

### Business Logic Overview
The calendar system enforces business working hours to prevent appointments from being scheduled during non-operational times. This ensures staff availability and maintains business operations within defined boundaries.

### Working Hours Validation Scenarios

#### **Scenario 1: Appointment Creation Outside Working Hours**
- **Business Rule**: Appointments cannot be created before business opens or after business closes
- **Example**: Business hours are 9:00 AM - 6:00 PM
  - ✅ **Allowed**: Customer tries to book at 2:00 PM → Appointment is created
  - ❌ **Blocked**: Customer tries to book at 7:00 AM → System shows "Business is closed at this time"
  - ❌ **Blocked**: Customer tries to book at 8:00 PM → System shows "Business is closed at this time"

#### **Scenario 2: Appointment Dragging Outside Working Hours**
- **Business Rule**: When dragging an appointment, the system prevents dropping it outside working hours
- **User Experience**: 
  - User drags appointment from 10:00 AM to 7:00 PM
  - System shows red background/warning indicator
  - Drop is prevented, appointment snaps back to original position
  - Error message: "Cannot schedule appointments outside business hours (9:00 AM - 6:00 PM)"

#### **Scenario 3: Working Hours Boundary Handling**
- **Business Rule**: Appointments can start exactly at opening time but must end by closing time
- **Example Cases**:
  - **9:00 AM Start**: ✅ Allowed - business just opened
  - **5:30 PM Start (30min service)**: ✅ Allowed - ends exactly at 6:00 PM
  - **5:45 PM Start (30min service)**: ❌ Blocked - would end at 6:15 PM (after closing)

#### **Scenario 4: Weekend and Holiday Handling**
- **Business Rule**: Configurable weekend scheduling based on business type
- **Salon Example**: 
  - Monday-Friday: 9:00 AM - 6:00 PM (Regular hours)
  - Saturday: 9:00 AM - 4:00 PM (Reduced hours)
  - Sunday: Closed (No appointments allowed)
- **Medical Practice Example**:
  - Monday-Friday: 8:00 AM - 5:00 PM
  - Saturday: 8:00 AM - 12:00 PM (Emergency only)
  - Sunday: Closed

### Visual Feedback System

#### **Non-Working Hours Display**
- **Gray Background**: Time slots outside working hours are visually dimmed
- **No Interaction**: These slots are not clickable for appointment creation
- **Hover State**: Shows tooltip "Outside business hours" when user hovers

#### **Working Hours Boundaries**
- **Green Border**: Working hours start time has a subtle green top border
- **Red Border**: Working hours end time has a subtle red bottom border
- **Gradient**: Visual gradient from working to non-working hours

### API Endpoints for Working Hours

#### **GET /api/v1/business/working-hours/**
- **Purpose**: Retrieve current working hours configuration
- **Response**: Daily schedules with start/end times
- **Used**: On calendar initialization and date changes

#### **POST /api/v1/appointments/validate-time/**
- **Purpose**: Validate if a proposed appointment time is within working hours
- **Request**: `{ "start_time": "2024-01-15T07:00:00", "duration": 60 }`
- **Response**: `{ "valid": false, "reason": "outside_working_hours" }`

---

## 2. 🕒 Buffer Time System

### Business Logic Overview
Buffer time represents the cleanup/preparation period between appointments, ensuring adequate time for sanitization, setup, and customer transition without overlapping services.

### Buffer Time Scenarios

#### **Scenario 1: Standard Service Buffer**
- **Business Rule**: Each service has a predefined buffer time for proper service delivery
- **Example - Hair Salon**:
  - Haircut: 45 minutes service + 15 minutes buffer = 60 minutes total
  - Hair Color: 90 minutes service + 30 minutes buffer = 120 minutes total
  - **Reasoning**: Buffer allows for cleanup, tool sanitization, and setup for next client

#### **Scenario 2: Buffer Time Visualization**
- **Business Rule**: Buffer time is visually distinct from service time
- **Display Logic**:
  - **Service Portion**: Blue background with service name and client
  - **Buffer Portion**: Orange/yellow background with "Buffer (15m)" text
  - **Height Ratio**: If appointment is 60 minutes total (45 service + 15 buffer):
    - Service segment: 75% of block height
    - Buffer segment: 25% of block height

#### **Scenario 3: Appointment Overlap Prevention**
- **Business Rule**: Buffer time prevents appointments from being scheduled too close together
- **Example Timeline**:
  - 10:00 AM - 11:00 AM: Client A (45min service + 15min buffer)
  - 11:00 AM - 12:00 PM: Client B (45min service + 15min buffer)
  - **Result**: No overlap, proper transition time maintained

#### **Scenario 4: Emergency Override**
- **Business Rule**: Admin can override buffer time in emergency situations
- **Process**:
  1. Admin attempts to schedule appointment during buffer time
  2. System shows warning: "This will overlap with buffer time"
  3. Admin can confirm with "Schedule Anyway" button
  4. System logs the override for reporting

### Buffer Time During Modifications

#### **Drag Operations**
- **Business Rule**: Buffer time moves with the appointment as a unit
- **Example**: Moving a 60-minute appointment (45 service + 15 buffer) from 10:00 AM to 2:00 PM
  - **Before**: 10:00-10:45 (service) + 10:45-11:00 (buffer)
  - **After**: 2:00-2:45 (service) + 2:45-3:00 (buffer)

#### **Resize Operations**
- **Business Rule**: Resizing affects service time, buffer time remains constant
- **Example**: Extending a haircut from 45 to 60 minutes
  - **Before**: 45min service + 15min buffer = 60min total
  - **After**: 60min service + 15min buffer = 75min total
  - **Buffer Unchanged**: Cleanup time requirements don't change

### API Endpoints for Buffer Time

#### **GET /api/v1/services/{id}/buffer-time/**
- **Purpose**: Get buffer time configuration for specific service
- **Response**: `{ "service_id": 1, "buffer_minutes": 15, "reason": "sanitization" }`

#### **PUT /api/v1/appointments/{id}/buffer-override/**
- **Purpose**: Override buffer time for specific appointment
- **Request**: `{ "new_buffer_minutes": 5, "reason": "emergency", "admin_approval": true }`

---

## 3. 🎯 Drag & Drop Implementation

### Business Logic Overview
The drag and drop system allows staff to efficiently reschedule appointments while maintaining business rules, customer communication, and operational constraints.

### Drag & Drop Scenarios

#### **Scenario 1: Simple Time Change**
- **Business Rule**: Moving appointment to different time slot on same day with same employee
- **Example Process**:
  1. **Original**: Client A at 10:00 AM with Sarah
  2. **User Action**: Admin drags appointment to 2:00 PM
  3. **System Validation**: 
     - Checks if 2:00 PM slot is available for Sarah
     - Verifies it's within working hours
     - Confirms no conflicts with existing appointments
  4. **Confirmation**: "Move Client A's appointment from 10:00 AM to 2:00 PM?"
  5. **Customer Notification**: Optional checkbox to notify customer of change

#### **Scenario 2: Employee Change**
- **Business Rule**: Moving appointment to different employee (column change)
- **Example Process**:
  1. **Original**: Client A at 10:00 AM with Sarah
  2. **User Action**: Admin drags appointment to Mike's 10:00 AM slot
  3. **System Validation**:
     - Checks if Mike is available at 10:00 AM
     - Verifies Mike can perform the requested service
     - Confirms client has no preference restrictions
  4. **Confirmation**: "Move Client A's appointment from Sarah to Mike at 10:00 AM?"
  5. **Additional Checks**: 
     - Service compatibility (Mike can do haircuts)
     - Client gender preferences (if applicable)
     - Skill level requirements

#### **Scenario 3: Day Change**
- **Business Rule**: Moving appointment to different day entirely
- **Example Process**:
  1. **Original**: Client A on Monday at 10:00 AM
  2. **User Action**: Admin drags to Tuesday at 10:00 AM
  3. **System Validation**:
     - Checks Tuesday working hours
     - Verifies employee availability on Tuesday
     - Confirms no conflicts with existing Tuesday appointments
  4. **Enhanced Confirmation**: 
     - "Move Client A's appointment from Monday 10:00 AM to Tuesday 10:00 AM?"
     - "This is a significant change. Customer will be notified automatically."

#### **Scenario 4: Invalid Drop Locations**
- **Business Rule**: Certain locations cannot accept dropped appointments
- **Blocked Scenarios**:
  - **Existing Appointment**: Cannot drop on occupied time slots
  - **Break Times**: Cannot drop during scheduled staff breaks
  - **Non-Working Hours**: Cannot drop outside business hours
  - **Service Mismatch**: Cannot drop haircut appointment on massage therapist
  - **Equipment Unavailable**: Cannot drop equipment-dependent service when equipment is booked

### Visual Feedback During Drag

#### **Ghost Copy System**
- **Purpose**: Shows where appointment will be placed during drag
- **Visual Elements**:
  - **Semi-transparent copy** of original appointment follows cursor
  - **Original appointment** remains visible but dimmed
  - **Target drop zone** highlighted when hovering over valid locations

#### **Validation Indicators**
- **Green Highlight**: Valid drop location (available slot)
- **Red Highlight**: Invalid drop location (occupied/restricted)
- **Orange Highlight**: Warning drop location (requires confirmation)
- **Gray Highlight**: Neutral drop location (no change)

#### **Smart Snapping**
- **Business Rule**: Appointments snap to nearest valid time intervals
- **Snapping Logic**:
  - **Primary**: Snap to appointment boundaries (start/end of existing appointments)
  - **Secondary**: Snap to 15-minute intervals (standard appointment increments)
  - **Tertiary**: Snap to 5-minute intervals (fine-tuning)

### Confirmation System

#### **Minimal Change Threshold**
- **Business Rule**: No confirmation needed for very small changes
- **Example**: Moving appointment by 5 minutes or less
  - **10:00 AM → 10:05 AM**: No confirmation (minimal change)
  - **10:00 AM → 10:30 AM**: Confirmation required (significant change)

#### **Automatic vs Manual Confirmation**
- **Automatic Confirmation**: Same day, same employee, same time zone
- **Manual Confirmation**: Different day, different employee, or major time change
- **Always Confirm**: Cross-day moves, employee changes, or moves > 2 hours

### API Endpoints for Drag & Drop

#### **PUT /api/v1/appointments/{id}/move/**
- **Purpose**: Move appointment to new time/employee
- **Request**: 
  ```json
  {
    "new_start_time": "2024-01-15T14:00:00",
    "new_employee_id": 5,
    "notify_customer": true,
    "reason": "employee_availability"
  }
  ```

#### **POST /api/v1/appointments/validate-move/**
- **Purpose**: Validate proposed move before execution
- **Request**: 
  ```json
  {
    "appointment_id": 123,
    "new_start_time": "2024-01-15T14:00:00",
    "new_employee_id": 5
  }
  ```
- **Response**: 
  ```json
  {
    "valid": true,
    "conflicts": [],
    "warnings": ["Different employee selected"]
  }
  ```

---

## 4. 📏 Appointment Block Resize Logic

### Business Logic Overview
Appointment resizing allows staff to adjust service duration based on actual service needs while maintaining schedule integrity and preventing conflicts.

### Resize Scenarios - When to Expand

#### **Scenario 1: Service Taking Longer Than Expected**
- **Business Context**: Haircut scheduled for 45 minutes but client wants additional styling
- **Resize Action**: Extend appointment from 45 to 75 minutes
- **System Validation**:
  - Check if next 30 minutes are available
  - Verify no conflicts with following appointments
  - Ensure extended time doesn't exceed working hours
- **Example Timeline**:
  - **Before**: 10:00-10:45 AM (45 minutes)
  - **After**: 10:00-11:15 AM (75 minutes)
  - **Validation**: Check if 10:45-11:15 AM slot is free

#### **Scenario 2: Adding Additional Services**
- **Business Context**: Client decides to add eyebrow shaping to haircut
- **Resize Action**: Extend appointment to accommodate additional service
- **Process**:
  1. **Original**: Haircut (45 minutes)
  2. **Addition**: Eyebrow shaping (+20 minutes)
  3. **New Total**: 65 minutes
  4. **System Check**: Verify next 20 minutes are available
  5. **Buffer Adjustment**: Recalculate buffer time if needed

#### **Scenario 3: Complex Service Requirements**
- **Business Context**: Hair coloring process taking longer due to hair condition
- **Resize Action**: Extend appointment in real-time
- **Considerations**:
  - **Processing Time**: Color may need extra development time
  - **Client Comfort**: Ensure client is comfortable with extended appointment
  - **Staff Availability**: Confirm staff can stay for extended time
  - **Equipment Availability**: Ensure coloring station remains available

### Resize Scenarios - When to Shrink

#### **Scenario 1: Service Completed Early**
- **Business Context**: Simple haircut finished 15 minutes early
- **Resize Action**: Reduce appointment from 45 to 30 minutes
- **Benefits**:
  - **Earlier Start**: Next client can be seen sooner
  - **Efficiency**: Better time utilization
  - **Customer Satisfaction**: Reduced wait times
- **Process**:
  1. **Original**: 10:00-10:45 AM (45 minutes)
  2. **Completed**: 10:30 AM (30 minutes actual)
  3. **Resize**: 10:00-10:30 AM (30 minutes)
  4. **Next Client**: Can start at 10:30 AM instead of 10:45 AM

#### **Scenario 2: Client Cancels Part of Service**
- **Business Context**: Client cancels hair styling, keeps only cut
- **Resize Action**: Reduce appointment duration
- **Example**:
  - **Original**: Cut + Style (90 minutes)
  - **Modified**: Cut only (45 minutes)
  - **Time Saved**: 45 minutes
  - **Options**: 
    - Offer earlier start to next client
    - Allow staff break time
    - Add walk-in availability

#### **Scenario 3: Service Simplification**
- **Business Context**: Complex color service simplified to single process
- **Resize Action**: Reduce appointment time
- **Considerations**:
  - **Price Adjustment**: Service price may need updating
  - **Equipment Release**: Free up equipment for other clients
  - **Staff Reallocation**: Staff can assist with other services

### Resize Constraints and Limitations

#### **Minimum Duration Constraints**
- **Business Rule**: Appointments cannot be shorter than service minimum
- **Examples**:
  - **Haircut**: Minimum 15 minutes (basic trim)
  - **Massage**: Minimum 30 minutes (meaningful session)
  - **Facial**: Minimum 45 minutes (proper treatment)
- **Validation**: System prevents resizing below these thresholds

#### **Maximum Duration Constraints**
- **Business Rule**: Appointments cannot exceed practical maximums
- **Examples**:
  - **Single Service**: Maximum 4 hours (staff endurance)
  - **Multiple Services**: Maximum 6 hours (client comfort)
  - **Working Hours**: Cannot extend beyond business closing
- **Validation**: System prevents resizing beyond these limits

#### **Conflict Prevention**
- **Business Rule**: Resizing cannot create appointment overlaps
- **Validation Process**:
  1. **Check Forward**: Ensure extension doesn't conflict with next appointment
  2. **Check Equipment**: Ensure resources remain available
  3. **Check Staff**: Confirm staff availability for extended time
  4. **Check Working Hours**: Ensure new end time is within business hours

### Resize Operation Details

#### **Resize Handles**
- **Top Handle**: Adjust start time (earlier start)
- **Bottom Handle**: Adjust end time (later end)
- **Business Logic**: Top handle changes are rare (usually extend end time)

#### **Resize Increment Rules**
- **Standard Increment**: 15 minutes (standard appointment blocks)
- **Fine Increment**: 5 minutes (precise adjustments)
- **Snap Behavior**: Automatically snaps to nearest valid increment

#### **Real-time Validation**
- **Visual Feedback**: 
  - **Green**: Valid resize (no conflicts)
  - **Yellow**: Warning resize (requires confirmation)
  - **Red**: Invalid resize (blocked by conflicts)
- **Conflict Display**: Show which appointments would be affected

### API Endpoints for Resize Operations

#### **PUT /api/v1/appointments/{id}/resize/**
- **Purpose**: Resize appointment duration
- **Request**: 
  ```json
  {
    "new_duration_minutes": 75,
    "resize_reason": "additional_services",
    "notify_customer": true,
    "price_adjustment": 25.00
  }
  ```

#### **POST /api/v1/appointments/{id}/validate-resize/**
- **Purpose**: Validate proposed resize before execution
- **Request**: 
  ```json
  {
    "appointment_id": 123,
    "new_duration_minutes": 75,
    "resize_direction": "extend"
  }
  ```
- **Response**: 
  ```json
  {
    "valid": true,
    "conflicts": [],
    "warnings": ["Will extend past originally scheduled time"],
    "affected_appointments": [124, 125]
  }
  ```

---

## 5. 🔗 Django API Integration

### API Architecture Overview
The calendar system integrates with Django REST API endpoints to ensure data consistency, proper validation, and real-time synchronization between the frontend interface and backend business logic.

### Core API Endpoints

#### **Appointment Management Endpoints**

##### **GET /api/v1/appointments/**
- **Purpose**: Retrieve appointments for calendar display
- **Query Parameters**:
  - `start_date`: Filter appointments from this date
  - `end_date`: Filter appointments until this date
  - `employee_id`: Filter by specific employee
  - `status`: Filter by appointment status
- **Response**: List of appointments with full details
- **Usage**: Calendar initialization and date range changes

##### **POST /api/v1/appointments/**
- **Purpose**: Create new appointment
- **Request Body**: Complete appointment data
- **Validation**: Working hours, conflicts, service availability
- **Response**: Created appointment with generated ID
- **Usage**: New appointment booking

##### **PUT /api/v1/appointments/{id}/**
- **Purpose**: Complete appointment update
- **Request Body**: Full appointment data
- **Validation**: All business rules applied
- **Response**: Updated appointment
- **Usage**: Major appointment modifications

##### **PATCH /api/v1/appointments/{id}/**
- **Purpose**: Partial appointment update
- **Request Body**: Only changed fields
- **Validation**: Incremental validation
- **Response**: Updated appointment
- **Usage**: Drag operations, status changes

##### **DELETE /api/v1/appointments/{id}/**
- **Purpose**: Cancel/delete appointment
- **Query Parameters**: `reason`, `notify_customer`
- **Response**: Confirmation message
- **Usage**: Appointment cancellation

#### **Validation and Conflict Endpoints**

##### **POST /api/v1/appointments/validate-time/**
- **Purpose**: Validate proposed appointment time
- **Request**: 
  ```json
  {
    "start_time": "2024-01-15T10:00:00",
    "duration_minutes": 60,
    "employee_id": 5,
    "exclude_appointment_id": 123
  }
  ```
- **Response**: 
  ```json
  {
    "valid": true,
    "conflicts": [],
    "warnings": ["Close to lunch break"]
  }
  ```

##### **POST /api/v1/appointments/check-conflicts/**
- **Purpose**: Check for scheduling conflicts
- **Request**: 
  ```json
  {
    "employee_id": 5,
    "start_time": "2024-01-15T10:00:00",
    "end_time": "2024-01-15T11:00:00",
    "exclude_appointment_id": 123
  }
  ```
- **Response**: 
  ```json
  {
    "has_conflict": false,
    "conflicting_appointments": [],
    "suggestions": ["Try 10:30 AM instead"]
  }
  ```

#### **Business Logic Endpoints**

##### **GET /api/v1/business/working-hours/**
- **Purpose**: Get working hours configuration
- **Response**: 
  ```json
  {
    "monday": {"start": "09:00", "end": "18:00", "closed": false},
    "tuesday": {"start": "09:00", "end": "18:00", "closed": false},
    "sunday": {"closed": true}
  }
  ```

##### **GET /api/v1/services/{id}/buffer-time/**
- **Purpose**: Get service buffer time configuration
- **Response**: 
  ```json
  {
    "service_id": 1,
    "buffer_minutes": 15,
    "minimum_duration": 30,
    "maximum_duration": 240
  }
  ```

### Data Flow and Transformation

#### **Frontend to API Data Flow**

1. **User Action**: User drags appointment in calendar
2. **Frontend Validation**: Basic validation (working hours, conflicts)
3. **API Call**: PATCH request to update appointment
4. **Backend Validation**: Comprehensive business rule validation
5. **Database Update**: Update appointment in database
6. **Response**: Return updated appointment data
7. **Frontend Update**: Update calendar display
8. **Customer Notification**: Send notification if requested

#### **API to Frontend Data Flow**

1. **API Response**: Django returns appointment data
2. **Data Transformation**: Convert API format to frontend format
3. **State Update**: Update React state with new data
4. **UI Refresh**: Re-render calendar components
5. **User Feedback**: Show success/error messages

### Error Handling and Response Codes

#### **Success Responses**
- **200 OK**: Successful retrieval or update
- **201 Created**: Successful creation
- **204 No Content**: Successful deletion

#### **Error Responses**
- **400 Bad Request**: Invalid data or validation errors
- **401 Unauthorized**: Authentication required
- **403 Forbidden**: Permission denied
- **404 Not Found**: Appointment not found
- **409 Conflict**: Scheduling conflict detected
- **500 Internal Server Error**: Server error

#### **Business Logic Error Examples**

##### **Conflict Error (409)**
```json
{
  "error": "scheduling_conflict",
  "message": "Appointment conflicts with existing booking",
  "details": {
    "conflicting_appointment": {
      "id": 124,
      "start_time": "2024-01-15T10:30:00",
      "client_name": "John Doe"
    }
  }
}
```

##### **Validation Error (400)**
```json
{
  "error": "validation_error",
  "message": "Appointment outside working hours",
  "details": {
    "field": "start_time",
    "value": "2024-01-15T07:00:00",
    "working_hours": {"start": "09:00", "end": "18:00"}
  }
}
```

---

## 6. 📱 iOS to React Integration

### Integration Strategy Overview
Adopting proven iOS patterns in React implementation ensures consistent user experience and leverages battle-tested interaction paradigms for professional calendar functionality.

### Move Mode State Management

#### **Business Logic**
- **Purpose**: Provide clear visual feedback during appointment manipulation
- **User Experience**: When user enters "move mode", the interface adapts to show dragging state
- **Visual Changes**: 
  - All other appointments are dimmed (30% opacity)
  - Moving appointment remains full brightness
  - Drop zones are highlighted
  - Interface chrome is simplified

#### **State Transitions**
1. **Normal Mode**: All appointments visible, normal interactions
2. **Move Mode Entry**: Long press on appointment or explicit move button
3. **Move Mode Active**: Drag operations enabled, visual feedback active
4. **Move Mode Exit**: Drop completion or cancellation

### Visual Feedback System

#### **Dimming Strategy**
- **Business Rule**: Reduce visual noise during drag operations
- **Implementation**: 
  - **Active Appointment**: 100% opacity (focus)
  - **Other Appointments**: 30% opacity (context)
  - **Empty Slots**: 50% opacity (drop targets)
  - **Non-droppable Areas**: 10% opacity (unavailable)

#### **Ghost Copy System**
- **Purpose**: Show appointment preview during drag
- **Visual Elements**:
  - **Semi-transparent copy** follows cursor/finger
  - **Original appointment** remains in place but dimmed
  - **Size matches** original appointment dimensions
  - **Content simplified** (just client name)

### Haptic Feedback Integration

#### **Feedback Triggers**
- **Selection**: Light tap when appointment is selected
- **Snap**: Medium tap when appointment snaps to grid
- **Conflict**: Strong tap when invalid drop is attempted
- **Confirmation**: Success pattern when move is completed

#### **Business Benefits**
- **User Confidence**: Confirms actions are registered
- **Error Prevention**: Alerts user to invalid actions
- **Professional Feel**: Matches native app expectations

### Long Press Gesture System

#### **Business Logic**
- **Purpose**: Distinguish between selection and move operations
- **Timing**: 500ms long press to enter move mode
- **Feedback**: Haptic feedback confirms long press detection
- **Cancellation**: Moving finger during long press cancels operation

#### **Use Cases**
- **Quick Selection**: Tap to select appointment for details
- **Move Operation**: Long press to enter move mode
- **Context Menu**: Long press + hold for additional options

### Confirmation Dialog System

#### **Business Rules for Confirmation**

##### **Always Confirm**
- **Cross-day moves**: Moving appointment to different date
- **Employee changes**: Changing service provider
- **Significant time changes**: Moves > 2 hours
- **Status changes**: Moving confirmed to pending

##### **Optional Confirmation**
- **Same day, same employee**: Minor time adjustments
- **Buffer time adjustments**: Changes within service window
- **Emergency moves**: Admin override situations

##### **Never Confirm**
- **Minimal movements**: < 5 minutes
- **Same slot movements**: Visual adjustments only
- **Cancelled operations**: User cancels before drop

#### **Confirmation Content**
- **Client Information**: Name, service, contact details
- **Change Summary**: From/to times, employee changes
- **Impact Assessment**: Conflicts, notifications, pricing
- **Action Options**: Confirm, Cancel, Notify Customer

### Integration Checklist

#### **Core iOS Features to Implement**

##### **Move Mode Management**
- [ ] **Global State**: Track move mode across calendar
- [ ] **Visual Dimming**: Reduce opacity of non-active appointments
- [ ] **Mode Transitions**: Clean entry/exit animations
- [ ] **Cancellation**: Clear cancellation paths

##### **Drag Operations**
- [ ] **Ghost Copy**: Semi-transparent drag preview
- [ ] **Smart Snapping**: Grid and appointment boundary snapping
- [ ] **Constraint Validation**: Real-time validation during drag
- [ ] **Drop Feedback**: Clear valid/invalid drop indicators

##### **User Feedback**
- [ ] **Haptic Patterns**: Web vibration API integration
- [ ] **Visual Feedback**: Smooth animations and transitions
- [ ] **Audio Cues**: Optional sound feedback for actions
- [ ] **Toast Messages**: Success/error notifications

##### **Confirmation System**
- [ ] **Smart Confirmation**: Context-aware confirmation logic
- [ ] **Customer Notification**: Optional customer notification
- [ ] **Change Summary**: Clear before/after comparison
- [ ] **Quick Actions**: Keyboard shortcuts for power users

#### **React-Specific Enhancements**

##### **Web Platform Features**
- [ ] **Keyboard Navigation**: Full keyboard accessibility
- [ ] **Screen Reader Support**: ARIA labels and descriptions
- [ ] **Responsive Design**: Mobile-first approach
- [ ] **Progressive Enhancement**: Works without JavaScript

##### **Performance Optimizations**
- [ ] **Virtual Scrolling**: Handle large appointment lists
- [ ] **Memoization**: Prevent unnecessary re-renders
- [ ] **Lazy Loading**: Load appointments as needed
- [ ] **Caching**: Cache frequently accessed data

##### **Developer Experience**
- [ ] **TypeScript**: Type safety for appointment data
- [ ] **Error Boundaries**: Graceful error handling
- [ ] **Development Tools**: React DevTools integration
- [ ] **Testing**: Unit and integration tests

---

## 7. 📝 Implementation Priority and Timeline

### Phase 1: Core Functionality (Weeks 1-2)
- **Working Hours Validation**: Implement business hours enforcement
- **Basic Drag & Drop**: Simple appointment time changes
- **API Integration**: Core CRUD operations
- **Visual Feedback**: Basic dimming and highlighting

### Phase 2: Advanced Features (Weeks 3-4)
- **Buffer Time System**: Visual representation and validation
- **Resize Operations**: Appointment duration adjustments
- **Conflict Detection**: Real-time validation
- **Confirmation System**: Smart confirmation dialogs

### Phase 3: Polish and Optimization (Weeks 5-6)
- **iOS Feature Adoption**: Move mode, haptic feedback
- **Performance Optimization**: Virtual scrolling, caching
- **Accessibility**: Screen reader support, keyboard navigation
- **Testing**: Comprehensive test coverage

### Phase 4: Advanced Business Logic (Weeks 7-8)
- **Multi-day Operations**: Cross-day appointment moves
- **Employee Scheduling**: Staff availability integration
- **Service Compatibility**: Complex service requirements
- **Customer Communication**: Automated notifications

---

## 🎯 Success Metrics and KPIs

### User Experience Metrics
- **Drag Success Rate**: % of successful drag operations
- **Error Rate**: % of operations resulting in errors
- **Task Completion Time**: Average time to reschedule appointment
- **User Satisfaction**: Feedback scores from staff

### Business Impact Metrics
- **Scheduling Efficiency**: Reduction in scheduling conflicts
- **Staff Productivity**: Appointments scheduled per hour
- **Customer Satisfaction**: Reduced wait times and errors
- **Revenue Impact**: Optimized appointment utilization

### Technical Performance Metrics
- **API Response Time**: Average API call duration
- **Frontend Performance**: Page load and interaction times
- **Error Handling**: Graceful degradation success rate
- **Data Consistency**: Sync accuracy between frontend and backend

This comprehensive guide provides the business logic foundation for building a professional calendar system that matches iOS quality standards while leveraging React's web platform capabilities. 