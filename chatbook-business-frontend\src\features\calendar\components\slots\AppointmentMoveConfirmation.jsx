import React, { useState } from 'react'
import { 
  ClockI<PERSON>, 
  ExclamationTriangleIcon,
  CheckIcon,
  XMarkIcon,
  BellIcon,
  BellSlashIcon
} from '@heroicons/react/24/outline'
import Toggle from '../ui/Toggle'

/**
 * AppointmentMoveConfirmation - Confirmation dialog for appointment time changes
 * Mirrors iOS CalendarActionSheet functionality for appointment moves
 */
const AppointmentMoveConfirmation = ({
  isOpen,
  onClose,
  appointment,
  originalTime,
  newTime,
  onConfirm,
  position = { x: 0, y: 0 },
  movementType = 'time-only', // New prop for movement type
  targetEmployee = null, // New prop for employee changes
  targetColumn = null // New prop for cross-day/column changes
}) => {
  const [isClosing, setIsClosing] = useState(false)
  const [notifyCustomer, setNotifyCustomer] = useState(true)

  console.log('🔍 AppointmentMoveConfirmation render:', {
    isOpen,
    appointment: appointment?.id,
    originalTime,
    newTime,
    movementType,
    targetEmployee: targetEmployee?.name,
    willRender: isOpen && appointment
  })

  // Detect if this is a cross-employee move
  const isEmployeeChange = (movementType === 'employee-change' || movementType === 'employee-switch') && targetEmployee
  const isTimeChange = originalTime && newTime && Math.abs(new Date(newTime) - new Date(originalTime)) >= 5 * 60 * 1000 // 5+ minute change

  if (!isOpen || !appointment) return null

  const handleClose = () => {
    setIsClosing(true)
    setTimeout(() => {
      setIsClosing(false)
      onClose()
    }, 150)
  }

  const handleConfirm = (shouldNotify) => {
    setIsClosing(true)
    setTimeout(() => {
      setIsClosing(false)
      onConfirm(shouldNotify)
    }, 100)
  }

  const formatDateTime = (date) => {
    const dateObj = new Date(date)
    return {
      date: dateObj.toLocaleDateString('en-US', { 
        weekday: 'short', 
        month: 'short', 
        day: 'numeric'
      }),
      time: dateObj.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit',
        hour12: true
      })
    }
  }

  const originalDateTime = formatDateTime(originalTime)
  const newDateTime = formatDateTime(newTime)
  
  // Calculate time difference
  const timeDiff = new Date(newTime) - new Date(originalTime)
  const minutesDiff = Math.round(timeDiff / (1000 * 60))
  const hoursDiff = Math.floor(Math.abs(minutesDiff) / 60)
  const remainingMinutes = Math.abs(minutesDiff) % 60
  
  const formatTimeDifference = () => {
    const isEarlier = minutesDiff < 0
    const prefix = isEarlier ? 'Moved earlier by' : 'Moved later by'
    
    if (hoursDiff > 0) {
      return `${prefix} ${hoursDiff}h ${remainingMinutes}m`
    } else {
      return `${prefix} ${remainingMinutes}m`
    }
  }

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={handleClose}
      />
      
      {/* Confirmation Dialog */}
      <div 
        className={`
          relative bg-white rounded-xl shadow-xl max-w-sm w-full mx-4
          transform transition-all duration-150 ease-out
          ${isClosing 
            ? 'scale-95 opacity-0 translate-y-4' 
            : 'scale-100 opacity-100 translate-y-0'
          }
        `}
      >
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="flex items-center">
            {isEmployeeChange ? (
              <div className="w-6 h-6 text-purple-600 mr-3 flex-shrink-0">
                👥
              </div>
            ) : (
              <ClockIcon className="w-6 h-6 text-blue-600 mr-3" />
            )}
            <div>
              <div className="text-lg font-semibold text-gray-900">
                {isEmployeeChange ? 'Reassign Appointment' : 'Move Appointment'}
              </div>
              <div className="text-sm text-gray-600">
                {appointment.clientName || 'Customer'}
              </div>
              {isEmployeeChange && (
                <div className="text-xs text-purple-600 font-medium mt-1">
                  Moving to {targetEmployee.name || targetEmployee.full_name}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Movement Details */}
        <div className="p-4 space-y-4">
          {/* Employee Change Section - Show prominently if cross-employee */}
          {isEmployeeChange && (
            <div className="bg-purple-50 border border-purple-200 p-4 rounded-lg">
              <div className="flex items-center text-purple-800 font-medium mb-2">
                <span className="text-lg mr-2">👥</span>
                Reassigning to New Employee
              </div>
              <div className="text-sm text-purple-700">
                This appointment will be assigned to <strong>{targetEmployee.name || targetEmployee.full_name}</strong>
              </div>
              <div className="text-xs text-purple-600 mt-1 italic">
                The appointment will appear in {targetEmployee.name || targetEmployee.full_name}'s schedule
              </div>
            </div>
          )}

          {/* Time Change Section - Only show if there's also a time change */}
          {isTimeChange && (
            <>
              {/* Original Time */}
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">From:</div>
                <div className="text-sm font-medium text-gray-900">
                  {originalDateTime.date} at {originalDateTime.time}
                </div>
              </div>
              
              {/* Arrow */}
              <div className="flex justify-center">
                <div className="w-8 h-0.5 bg-gray-300 relative">
                  <div className="absolute right-0 top-[-3px] w-0 h-0 border-l-4 border-l-gray-300 border-t-2 border-t-transparent border-b-2 border-b-transparent" />
                </div>
              </div>
              
              {/* New Time */}
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-600">To:</div>
                <div className="text-sm font-medium text-blue-600">
                  {newDateTime.date} at {newDateTime.time}
                </div>
              </div>
            </>
          )}

          {/* Employee-only change message */}
          {isEmployeeChange && !isTimeChange && (
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="text-sm text-blue-800 font-medium">
                Time remains the same
              </div>
              <div className="text-xs text-blue-600 mt-1">
                Only the assigned employee will change
              </div>
            </div>
          )}
          
          {/* Time Difference - Only show if there's a time change */}
          {isTimeChange && (
            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="text-sm text-blue-800 font-medium">
                {formatTimeDifference()}
              </div>
            </div>
          )}
          
          {/* Customer Notification Toggle */}
          <div className="bg-gradient-to-r from-gray-50 to-blue-50/30 p-4 rounded-xl border border-gray-100">
            <div className="flex items-center justify-between">
              <div className="flex items-start space-x-3 flex-1">
                <div className="flex-shrink-0 mt-0.5">
                  {notifyCustomer ? (
                    <BellIcon className="w-5 h-5 text-blue-600" />
                  ) : (
                    <BellSlashIcon className="w-5 h-5 text-gray-400" />
                  )}
                </div>
                <div className="flex-1">
                  <div className="text-sm font-medium text-gray-900">
                    Notify Customer
                  </div>
                  <div className="text-xs text-gray-600 mt-0.5">
                    {isEmployeeChange && !isTimeChange 
                      ? 'Send SMS notification about employee reassignment'
                      : isEmployeeChange && isTimeChange
                      ? 'Send SMS notification about time and employee change'  
                      : 'Send SMS notification about time change'
                    }
                  </div>
                </div>
              </div>
              <div className="ml-4">
                <Toggle
                  checked={notifyCustomer}
                  onChange={setNotifyCustomer}
                  color={isEmployeeChange ? 'purple' : 'blue'}
                />
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="p-4 border-t border-gray-200 space-y-2">
          {/* Confirm Button */}
          <button
            onClick={() => handleConfirm(notifyCustomer)}
            className={`w-full font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center ${
              isEmployeeChange 
                ? 'bg-purple-600 hover:bg-purple-700 text-white' 
                : 'bg-blue-600 hover:bg-blue-700 text-white'
            }`}
          >
            {isEmployeeChange ? (
              <span className="text-lg mr-2">👥</span>
            ) : (
              <CheckIcon className="w-5 h-5 mr-2" />
            )}
            {isEmployeeChange && !isTimeChange 
              ? 'Confirm Reassignment'
              : isEmployeeChange && isTimeChange
              ? 'Confirm Move & Reassignment'
              : 'Confirm Move'
            }
            {notifyCustomer && (
              <span className="ml-2 text-xs opacity-80">& Notify</span>
            )}
          </button>
          
          {/* Cancel Button */}
          <button
            onClick={handleClose}
            className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium py-3 px-4 rounded-lg transition-colors duration-200 flex items-center justify-center"
          >
            <XMarkIcon className="w-5 h-5 mr-2" />
            Cancel
          </button>
        </div>
        
        {/* Warning for potential conflicts */}
        {isTimeChange && Math.abs(minutesDiff) > 60 && (
          <div className="px-4 pb-4">
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3 flex items-start">
              <ExclamationTriangleIcon className="w-5 h-5 text-yellow-600 mr-2 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-yellow-800">
                <div className="font-medium">Large time change detected</div>
                <div className="text-xs mt-1">
                  Please verify this change doesn't conflict with other appointments
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Warning for employee reassignment */}
        {isEmployeeChange && (
          <div className="px-4 pb-4">
            <div className="bg-purple-50 border border-purple-200 rounded-lg p-3 flex items-start">
              <span className="text-lg mr-2 mt-0.5 flex-shrink-0">⚠️</span>
              <div className="text-sm text-purple-800">
                <div className="font-medium">Employee Reassignment</div>
                <div className="text-xs mt-1">
                  Please verify {targetEmployee?.name || targetEmployee?.full_name} is available at this time
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default AppointmentMoveConfirmation 