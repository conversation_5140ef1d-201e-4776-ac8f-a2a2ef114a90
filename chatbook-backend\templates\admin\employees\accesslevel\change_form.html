{% extends "admin/change_form.html" %}
{% load static %}

{% block extrahead %}
{{ block.super }}
<style>
  .permission-section {
    margin-bottom: 30px;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 5px;
  }
  
  .permission-section h2 {
    margin-top: 0;
    font-size: 24px;
    color: #333;
    border-bottom: 1px solid #ddd;
    padding-bottom: 10px;
    margin-bottom: 15px;
  }
  
  .permission-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
  }
  
  .permission-item:last-child {
    border-bottom: none;
  }
  
  .permission-description {
    flex: 1;
  }
  
  .permission-description h3 {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: bold;
  }
  
  .permission-description p {
    margin: 0;
    color: #666;
    font-size: 14px;
  }
  
  .permission-controls {
    display: flex;
    gap: 10px;
  }
  
  .btn-view, .btn-modify {
    padding: 8px 15px;
    border-radius: 20px;
    border: 1px solid #ccc;
    background: white;
    cursor: pointer;
    font-weight: 500;
    outline: none;
    transition: all 0.2s ease;
  }
  
  .btn-view {
    color: #333;
  }
  
  .btn-modify {
    color: #333;
  }
  
  .btn-view.active, .btn-modify.active, .single-btn.active {
    background: #4CAF50 !important;
    color: white !important;
    border-color: #4CAF50 !important;
    font-weight: bold;
  }
  
  .btn-group {
    display: flex;
    overflow: hidden;
    border-radius: 20px;
    border: 1px solid #ccc;
  }
  
  .btn-group .btn-view, .btn-group .btn-modify {
    border-radius: 0;
    border: none;
    border-right: 1px solid #ccc;
  }
  
  .btn-group .btn-modify {
    border-right: none;
  }
  
  .single-btn {
    border-radius: 20px;
    border: 1px solid #ccc;
    background: white;
    padding: 8px 15px;
    cursor: pointer;
    outline: none;
    font-weight: 500;
    transition: all 0.2s ease;
  }
  
  .single-btn.active {
    background: #4CAF50 !important;
    color: white !important;
    border-color: #4CAF50 !important;
    font-weight: bold;
  }
  
  /* Button hover states */
  .btn-view:hover, .btn-modify:hover, .single-btn:hover {
    background-color: #f0f0f0;
  }
  
  .btn-view.active:hover, .btn-modify.active:hover, .single-btn.active:hover {
    background-color: #43a047 !important;
  }
  
  /* Price Reduction and Discount Table Styles */
  .price-discount-table {
    margin: 20px 0;
    border: 1px solid #ddd;
    border-radius: 5px;
    overflow: hidden;
  }
  
  .permission-table {
    width: 100%;
    border-collapse: collapse;
  }
  
  .permission-table th,
  .permission-table td {
    padding: 12px 15px;
    text-align: left;
    border-bottom: 1px solid #ddd;
  }
  
  .permission-table th {
    background-color: #f5f5f5;
    font-weight: bold;
    color: #333;
  }
  
  .permission-table th:first-child {
    width: 25%;
  }
  
  .permission-table tr:last-child td {
    border-bottom: none;
  }
  
  .permission-table td:first-child {
    font-weight: 500;
  }
  
  .permission-table .single-btn {
    width: 100%;
    text-align: center;
  }
  
  /* Toggle Switch */
  .toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
  }
  
  .toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
  }
  
  .toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    -webkit-transition: .4s;
    transition: .4s;
    border-radius: 34px;
  }
  
  .toggle-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    -webkit-transition: .4s;
    transition: .4s;
    border-radius: 50%;
  }
  
  input:checked + .toggle-slider {
    background-color: #4CAF50;
  }
  
  input:focus + .toggle-slider {
    box-shadow: 0 0 1px #4CAF50;
  }
  
  input:checked + .toggle-slider:before {
    -webkit-transform: translateX(26px);
    -ms-transform: translateX(26px);
    transform: translateX(26px);
  }
  
  /* JSON Viewer styles */
  .json-viewer {
    margin-top: 30px;
    padding: 20px;
    background-color: #f5f5f5;
    border: 1px solid #ddd;
    border-radius: 5px;
  }
  
  .json-viewer h3 {
    margin-top: 0;
    color: #333;
    font-size: 18px;
    margin-bottom: 15px;
  }
  
  .json-viewer-actions {
    display: flex;
    margin-bottom: 15px;
    gap: 10px;
  }
  
  #json-display {
    background-color: #fff;
    padding: 15px;
    border: 1px solid #ccc;
    border-radius: 4px;
    overflow: auto;
    max-height: 400px;
    white-space: pre-wrap;
    font-family: monospace;
    font-size: 13px;
    line-height: 1.5;
  }
  
  /* Hide the original permissions JSON field */
  .field-permissions {
    position: absolute;
    opacity: 0;
    height: 0;
    overflow: hidden;
    pointer-events: none;
  }
  
  /* Permissions Control Panel */
  .permissions-control-panel {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 15px;
    margin-bottom: 20px;
    padding: 15px;
    background-color: #e9f7ef;
    border-radius: 5px;
    border: 1px solid #ccc;
  }
  
  .btn-primary {
    padding: 10px 15px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
  }
  
  .btn-secondary {
    padding: 10px 15px;
    background-color: #f44336;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: bold;
  }
  
  .btn-primary:hover {
    background-color: #45a049;
  }
  
  .btn-secondary:hover {
    background-color: #d32f2f;
  }
  
  .permissions-status {
    margin-left: auto;
    font-weight: bold;
    color: #333;
  }
</style>
{% endblock %}

{% block after_field_sets %}
{{ block.super }}

<!-- Custom permission UI -->
<div id="permission-ui">
  <div class="permissions-control-panel">
    <button type="button" id="grant-all-permissions" class="btn-primary">Grant All Permissions</button>
    <button type="button" id="revoke-all-permissions" class="btn-secondary">Revoke All Permissions</button>
    <div class="permissions-status">
      <span id="permissions-count">0</span> permissions enabled
    </div>
  </div>

  <!-- Calendar Section -->
  <div class="permission-section">
    <h2>CALENDAR</h2>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Calendar Configuration Management</h3>
        <p>Calendar configurations can be modified.</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="calendar_config_view">View</button>
        <button type="button" class="btn-modify" data-permission="calendar_config_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Control Own Calendar</h3>
        <p>View and/or modify their own calendar.</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="calendar_own_view">View</button>
        <button type="button" class="btn-modify" data-permission="calendar_own_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Control Other Employee Calendars</h3>
        <p>View and/or modify other employee calendars.</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="calendar_others_view">View</button>
        <button type="button" class="btn-modify" data-permission="calendar_others_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Accept Own Appointments</h3>
        <p>Ability to accept their own appointments.</p>
      </div>
      <div class="permission-controls">
        <button type="button" class="single-btn" data-permission="accept_own_appointments">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Accept Other Employee Appointments</h3>
        <p>Ability to accept other employee appointments.</p>
      </div>
      <div class="permission-controls">
        <button type="button" class="single-btn" data-permission="accept_others_appointments">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Accept Appointment Requests and Waitlists</h3>
        <p>Ability to accept their own appointment requests and use waitlists.</p>
      </div>
      <div class="permission-controls">
        <button type="button" class="single-btn" data-permission="accept_own_requests">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Accept Other Employee Appointment Requests and Waitlist</h3>
        <p>Ability to accept other employee appointment requests and waitlists.</p>
      </div>
      <div class="permission-controls">
        <button type="button" class="single-btn" data-permission="accept_others_requests">Modify</button>
      </div>
    </div>
  </div>

  <!-- Checkout Section -->
  <div class="permission-section">
    <h2>CHECKOUT</h2>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Ability to undo checkout the appointment</h3>
        <p>Ability to undo checkout appointment for which he/she has modify calendar rights.</p>
      </div>
      <div class="permission-controls">
        <button type="button" class="single-btn" data-permission="undo_checkout">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Customer Checkout</h3>
        <p>Ability to utilize the checkout screen and modify customer checkouts.</p>
      </div>
      <div class="permission-controls">
        <button type="button" class="single-btn" data-permission="customer_checkout">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Modify Price and Discount</h3>
        <p>Ability to add or change the price/discount at booking and checkout, and approve other employees to do the same. 'Can Modify With Limit' is the percentage the employees can edit the price/discount by. Approval is required if the employee modifies the price/discount over the set percentage.</p>
      </div>
      <div class="permission-controls">
        <button type="button" class="single-btn" data-permission="modify_price_discount">Modify</button>
      </div>
    </div>

    <!-- Price Reduction and Discount Table -->
    <div class="price-discount-table">
      <table class="permission-table">
        <thead>
          <tr>
            <th></th>
            <th>Price Reduction</th>
            <th>Discount</th>
          </tr>
        </thead>
        <tbody>
          <tr>
            <td>Services</td>
            <td>
              <button type="button" class="single-btn" data-permission="services_price_reduction">Cannot Modify</button>
            </td>
            <td>
              <button type="button" class="single-btn" data-permission="services_discount">Can Modify</button>
            </td>
          </tr>
          <tr>
            <td>Classes</td>
            <td>
              <button type="button" class="single-btn" data-permission="classes_price_reduction">Cannot Modify</button>
            </td>
            <td>
              <button type="button" class="single-btn" data-permission="classes_discount">Cannot Modify</button>
            </td>
          </tr>
          <tr>
            <td>Products</td>
            <td>
              <button type="button" class="single-btn" data-permission="products_price_reduction">Cannot Modify</button>
            </td>
            <td>
              <button type="button" class="single-btn" data-permission="products_discount">Cannot Modify</button>
            </td>
          </tr>
          <tr>
            <td>Gift Cards</td>
            <td>
              <button type="button" class="single-btn" data-permission="gift_cards_price_reduction">Cannot Modify</button>
            </td>
            <td>
              <button type="button" class="single-btn" data-permission="gift_cards_discount">Cannot Modify</button>
            </td>
          </tr>
          <tr>
            <td>Memberships</td>
            <td>
              <button type="button" class="single-btn" data-permission="memberships_price_reduction">Cannot Modify</button>
            </td>
            <td>
              <button type="button" class="single-btn" data-permission="memberships_discount">Cannot Modify</button>
            </td>
          </tr>
          <tr>
            <td>Packages</td>
            <td>
              <button type="button" class="single-btn" data-permission="packages_price_reduction">Cannot Modify</button>
            </td>
            <td>
              <button type="button" class="single-btn" data-permission="packages_discount">Cannot Modify</button>
            </td>
          </tr>
        </tbody>
      </table>
    </div>

    <div class="permission-item">
      <div class="permission-description">
        <h3>Taxes</h3>
        <p>Can remove or re-apply taxes from in-house checkout</p>
      </div>
      <div class="permission-controls">
        <button type="button" class="single-btn" data-permission="modify_taxes">Modify</button>
      </div>
    </div>

    <div class="permission-item">
      <div class="permission-description">
        <h3>Fees</h3>
        <p>Can remove automatic fees, and/or manually add new fees for in-house checkout</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="remove_fees">Remove</button>
        <button type="button" class="btn-modify" data-permission="add_fees">Add</button>
      </div>
    </div>

    <div class="permission-item">
      <div class="permission-description">
        <h3>Credit Card Processing</h3>
        <p>Credit Card Processing Configuration</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="cc_processing_view">View</button>
        <button type="button" class="btn-modify" data-permission="cc_processing_modify">Modify</button>
      </div>
    </div>

    <div class="permission-item">
      <div class="permission-description">
        <h3>Manage Refund</h3>
        <p>View and modify Refund</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="refund_view">View</button>
        <button type="button" class="btn-modify" data-permission="refund_modify">Modify</button>
      </div>
    </div>

    <div class="permission-item">
      <div class="permission-description">
        <h3>Invoice Management</h3>
        <p>Invoice can be added/edited/deleted.</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="invoice_view">View</button>
        <button type="button" class="btn-modify" data-permission="invoice_modify">Modify</button>
      </div>
    </div>
  </div>

  <!-- Customers Section -->
  <div class="permission-section">
    <h2>CUSTOMERS</h2>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Customer Management</h3>
        <p>Customers can be edited/deleted.</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="customer_management_view">View</button>
        <button type="button" class="btn-modify" data-permission="customer_management_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Customer Notes, SOAP Notes, Forms & Files</h3>
        <p>Ability to add, modify & delete customer notes, SOAP notes, forms & files.</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="customer_notes_view">View</button>
        <button type="button" class="btn-modify" data-permission="customer_notes_modify">Modify</button>
      </div>
    </div>
  </div>

  <!-- Forms Section -->
  <div class="permission-section">
    <h2>FORMS</h2>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Forms Management</h3>
        <p>Forms can be added/edited/deleted.</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="forms_management_view">View</button>
        <button type="button" class="btn-modify" data-permission="forms_management_modify">Modify</button>
      </div>
    </div>
  </div>

  <!-- Marketing Section -->
  <div class="permission-section">
    <h2>MARKETING</h2>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Venue Gallery/Portfolio Management</h3>
        <p>Venue Gallery/Portfolio can be added/edited/deleted</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="gallery_view">View</button>
        <button type="button" class="btn-modify" data-permission="gallery_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Website Builder</h3>
        <p>Ability to customize your website</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="website_view">View</button>
        <button type="button" class="btn-modify" data-permission="website_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Manage Widget</h3>
        <p>Ability to customize your Widget</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="widget_view">View</button>
        <button type="button" class="btn-modify" data-permission="widget_modify">Modify</button>
      </div>
    </div>
  </div>

  <!-- Reports Section -->
  <div class="permission-section">
    <h2>REPORTS</h2>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Ability to run own reports</h3>
        <p>Ability to run his/her own reports</p>
      </div>
      <div class="permission-controls">
        <button type="button" class="single-btn" data-permission="run_own_reports">View</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Ability to run others reports</h3>
        <p>Ability to run others reports</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="run_others_reports_view">View</button>
        <button type="button" class="btn-modify" data-permission="run_others_reports_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Multi-Location Reports</h3>
        <p>View multi-location business filters in all reports.</p>
      </div>
      <div class="permission-controls">
        <button type="button" class="single-btn" data-permission="multi_location_reports">View</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Time Card Screen</h3>
        <p>Users can view / edit Employee Time Card Entries</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="time_card_view">View</button>
        <button type="button" class="btn-modify" data-permission="time_card_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Ability to edit own time card</h3>
        <p>Ability to edit his/her own time card</p>
      </div>
      <div class="permission-controls">
        <button type="button" class="single-btn" data-permission="edit_own_time_card">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Gift Certificate</h3>
        <p>Users can add / edit / view histories of gift certificate</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="gift_certificate_view">View</button>
        <button type="button" class="btn-modify" data-permission="gift_certificate_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Packages</h3>
        <p>Users can edit / view histories of package.</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="packages_view">View</button>
        <button type="button" class="btn-modify" data-permission="packages_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Memberships</h3>
        <p>Users can edit/view histories of membership.</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="memberships_view">View</button>
        <button type="button" class="btn-modify" data-permission="memberships_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Cancellations & No shows Report</h3>
        <p>Users can view List of Appointments with No Show, Denied and Cancellations</p>
      </div>
      <div class="permission-controls">
        <button type="button" class="single-btn" data-permission="cancellation_report">View</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Control his/her own Push Notification History</h3>
        <p>Ability to modify own Push Notification History</p>
      </div>
      <div class="permission-controls">
        <button type="button" class="single-btn" data-permission="own_push_notification">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Control other People's Push Notification History</h3>
        <p>Ability to modify other people's Push Notification History</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="others_push_notification_view">View</button>
        <button type="button" class="btn-modify" data-permission="others_push_notification_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Pending Shipment Report</h3>
        <p>View and modify pending shipment report</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="pending_shipment_view">View</button>
        <button type="button" class="btn-modify" data-permission="pending_shipment_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Ability to run own payroll</h3>
        <p>Ability to run their own reports, commissions, hourly pay, and salary</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="own_payroll_view">View</button>
        <button type="button" class="btn-modify" data-permission="own_payroll_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Ability to run others Payroll</h3>
        <p>Ability to run others reports, commissions, hourly pay, and salary</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="others_payroll_view">View</button>
        <button type="button" class="btn-modify" data-permission="others_payroll_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Dashboard</h3>
        <p>View and modify widgets</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="dashboard_view">View</button>
        <button type="button" class="btn-modify" data-permission="dashboard_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Failed Payment Report</h3>
        <p>View and modify failed payment report</p>
      </div>
      <div class="permission-controls">
        <button type="button" class="single-btn" data-permission="failed_payment_report">View</button>
      </div>
    </div>
  </div>

  <!-- Settings Section -->
  <div class="permission-section">
    <h2>SETTINGS</h2>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>General Setting</h3>
        <p>Business Profile and Facility Information can be edited.</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="general_settings_view">View</button>
        <button type="button" class="btn-modify" data-permission="general_settings_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Manage Own Profile</h3>
        <p>Users can be able to edit/delete his/her profile</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="own_profile_view">View</button>
        <button type="button" class="btn-modify" data-permission="own_profile_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Manage Others Profile</h3>
        <p>Users can be added/edited/deleted.</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="others_profile_view">View</button>
        <button type="button" class="btn-modify" data-permission="others_profile_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Access Level Management</h3>
        <p>Access levels can be added/edited/deleted.</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="access_level_view">View</button>
        <button type="button" class="btn-modify" data-permission="access_level_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Service, Resource, Class, and Add-Ons Management</h3>
        <p>Service, Resource, Class, and Add-Ons can be created and edited.</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="service_management_view">View</button>
        <button type="button" class="btn-modify" data-permission="service_management_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Inventory</h3>
        <p>Products can be added/edited/deleted</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="inventory_view">View</button>
        <button type="button" class="btn-modify" data-permission="inventory_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Vagaro Connect (Customers)</h3>
        <p>Ability to view and reply to customers messages in Vagaro Connect</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="vagaro_connect_view">View</button>
        <button type="button" class="btn-modify" data-permission="vagaro_connect_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Manage Online Shopping Cart</h3>
        <p>View and modify manage shopping cart</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="shopping_cart_view">View</button>
        <button type="button" class="btn-modify" data-permission="shopping_cart_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Vagaro Drive</h3>
        <p>View and modify Vagaro Drive</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="vagaro_drive_view">View</button>
        <button type="button" class="btn-modify" data-permission="vagaro_drive_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Memberships</h3>
        <p>View and modify Membership(s)</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="settings_memberships_view">View</button>
        <button type="button" class="btn-modify" data-permission="settings_memberships_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Packages</h3>
        <p>Packages can be added/edited/deleted.</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="settings_packages_view">View</button>
        <button type="button" class="btn-modify" data-permission="settings_packages_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Discounts</h3>
        <p>Ability to view, create, edit and delete discounts.</p>
      </div>
      <div class="permission-controls btn-group">
        <button type="button" class="btn-view" data-permission="discounts_view">View</button>
        <button type="button" class="btn-modify" data-permission="discounts_modify">Modify</button>
      </div>
    </div>
    
    <div class="permission-item">
      <div class="permission-description">
        <h3>Login from any IP address</h3>
        <p>Ability to login from any IP address</p>
      </div>
      <div class="permission-controls">
        <label class="toggle-switch">
          <input type="checkbox" data-permission="login_any_ip">
          <span class="toggle-slider"></span>
        </label>
      </div>
    </div>
  </div>

</div>

<!-- Raw JSON Permissions Viewer -->
<div class="json-viewer">
  <h3>Raw JSON Permissions Data</h3>
  <div class="json-viewer-actions">
    <button type="button" id="copy-json" class="btn-secondary">Copy to Clipboard</button>
    <button type="button" id="toggle-json-view" class="btn-primary">Show/Hide JSON</button>
  </div>
  <pre id="json-display" style="display: block;"></pre>
</div>

<script>
// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
  console.log('DOM loaded, initializing permission buttons');
  
  // We'll handle the UI ourselves to avoid conflicts with accesslevel.js
  if (window.hasInitializedPermissionUI) {
    console.log('Permission UI already initialized by external script. Skipping inline initialization.');
    return;
  }
  
  window.hasInitializedPermissionUI = true;
  
  // Add ID to the form so we can target it reliably
  const form = document.querySelector('.module form');
  if (form) {
    form.id = 'accesslevel_form';
    console.log('Added ID to form:', form.id);
  } else {
    console.error('Form not found for ID assignment');
  }
  
  // Get existing permissions JSON
  const permissionsField = document.querySelector('#id_permissions');
  let permissions = {};
  
  try {
    if (permissionsField.value) {
      permissions = JSON.parse(permissionsField.value);
      console.log('Loaded permissions:', permissions);
    }
  } catch (e) {
    console.error('Error parsing permissions JSON:', e);
    console.log('Raw value:', permissionsField.value);
  }

  // Apply active state to buttons
  function applyActiveState(button) {
    console.log(`Applying active state to ${button.dataset.permission}`);
    // Apply both inline style and CSS class for maximum compatibility
    button.classList.add('active');
    button.style.backgroundColor = '#4CAF50';
    button.style.color = 'white';
    button.style.borderColor = '#4CAF50';
    button.style.fontWeight = 'bold';
    button.setAttribute('data-active', 'true');
  }

  function removeActiveState(button) {
    console.log(`Removing active state from ${button.dataset.permission}`);
    // Remove both inline style and CSS class
    button.classList.remove('active');
    button.style.backgroundColor = 'white';
    button.style.color = '#333';
    button.style.borderColor = '#ccc';
    button.style.fontWeight = '500';
    button.setAttribute('data-active', 'false');
  }
  
  // Function to grant all permissions
  function grantAllPermissions() {
    document.querySelectorAll('.btn-view, .btn-modify, .single-btn').forEach(button => {
      applyActiveState(button);
    });
    
    // Also enable the toggle switch
    const toggleSwitch = document.querySelector('input[data-permission="login_any_ip"]');
    if (toggleSwitch) {
      toggleSwitch.checked = true;
    }
    
    // Check the full_access checkbox in the form
    const fullAccessCheckbox = document.querySelector('#id_full_access');
    if (fullAccessCheckbox) {
      fullAccessCheckbox.checked = true;
    }
    
    // Update permissions
    updatePermissions();
    updatePermissionsCount();
  }
  
  // Function to revoke all permissions
  function revokeAllPermissions() {
    document.querySelectorAll('.btn-view, .btn-modify, .single-btn').forEach(button => {
      removeActiveState(button);
    });
    
    // Also disable the toggle switch
    const toggleSwitch = document.querySelector('input[data-permission="login_any_ip"]');
    if (toggleSwitch) {
      toggleSwitch.checked = false;
    }
    
    // Uncheck the full_access checkbox in the form
    const fullAccessCheckbox = document.querySelector('#id_full_access');
    if (fullAccessCheckbox) {
      fullAccessCheckbox.checked = false;
    }
    
    // Update permissions
    updatePermissions();
    updatePermissionsCount();
  }
  
  // Function to update permissions count display
  function updatePermissionsCount() {
    const activeButtons = document.querySelectorAll('.btn-view[data-active="true"], .btn-modify[data-active="true"], .single-btn[data-active="true"]');
    const toggleSwitch = document.querySelector('input[data-permission="login_any_ip"]');
    let count = activeButtons.length;
    
    // Add toggle switch if checked
    if (toggleSwitch && toggleSwitch.checked) {
      count += 1;
    }
    
    const countElement = document.querySelector('#permissions-count');
    if (countElement) {
      countElement.textContent = count;
    }
  }
  
  // Set initial button states based on saved permissions
  console.log('Setting initial button states');
  document.querySelectorAll('.btn-view, .btn-modify, .single-btn').forEach(button => {
    const permName = button.dataset.permission;
    if (permissions[permName] === true) {
      console.log(`Setting ${permName} button to active`);
      applyActiveState(button);
    } else {
      console.log(`Setting ${permName} button to inactive`);
      removeActiveState(button);
    }
  });
  
  // Set initial toggle switch state
  const toggleSwitch = document.querySelector('input[data-permission="login_any_ip"]');
  if (toggleSwitch) {
    toggleSwitch.checked = permissions['login_any_ip'] === true;
    console.log(`Setting login_any_ip toggle to ${toggleSwitch.checked}`);
    
    // Add event listener for toggle switch
    toggleSwitch.addEventListener('change', function() {
      console.log(`Toggle switch changed to ${this.checked}`);
      // Update permissions immediately
      updatePermissions();
      updatePermissionsCount();
    });
  }
  
  // Add event listeners to all buttons with proper toggle functionality
  console.log('Adding click event listeners to buttons');
  document.querySelectorAll('.btn-view, .btn-modify, .single-btn').forEach(button => {
    // First remove any existing listeners to avoid duplicates
    const newButton = button.cloneNode(true);
    button.parentNode.replaceChild(newButton, button);
    
    newButton.addEventListener('click', handleButtonClick);
  });
  
  function handleButtonClick(e) {
    e.preventDefault();
    e.stopPropagation();
    
    const button = e.currentTarget; // Use currentTarget instead of target to get the button itself
    const permName = button.dataset.permission;
    const isActive = button.getAttribute('data-active') === 'true';
    
    console.log(`Button ${permName} clicked, current state: ${isActive}`);
    
    // Toggle the state
    if (isActive) {
      removeActiveState(button);
      console.log(`${permName} toggled OFF`);
    } else {
      applyActiveState(button);
      console.log(`${permName} toggled ON`);
    }
    
    // Update permissions object
    updatePermissions();
    updatePermissionsCount();
  }
  
  // Add event listeners to Grant All and Revoke All buttons
  const grantAllButton = document.querySelector('#grant-all-permissions');
  if (grantAllButton) {
    grantAllButton.addEventListener('click', grantAllPermissions);
  }
  
  const revokeAllButton = document.querySelector('#revoke-all-permissions');
  if (revokeAllButton) {
    revokeAllButton.addEventListener('click', revokeAllPermissions);
  }
  
  // Update permissions JSON when form is submitted
  const accessLevelForm = document.querySelector('form#accesslevel_form');
  if (accessLevelForm) {
    accessLevelForm.addEventListener('submit', function() {
      updatePermissions();
      console.log('Form submitted, permissions updated');
    });
  } else {
    console.error('Form not found');
    document.querySelectorAll('form').forEach((f, i) => {
      console.log(`Form ${i}:`, f.id || 'no id');
    });
  }
  
  function updatePermissions() {
    // Collect all permissions from button states
    const newPermissions = {};
    
    // Get button permissions
    document.querySelectorAll('.btn-view, .btn-modify, .single-btn').forEach(button => {
      const permName = button.dataset.permission;
      const isActive = button.getAttribute('data-active') === 'true';
      newPermissions[permName] = isActive;
    });
    
    // Get toggle switch permissions
    const toggleSwitch = document.querySelector('input[data-permission="login_any_ip"]');
    if (toggleSwitch) {
      newPermissions['login_any_ip'] = toggleSwitch.checked;
    }
    
    // Update the hidden permissions field
    permissionsField.value = JSON.stringify(newPermissions);
    console.log('Updated permissions JSON:', permissionsField.value);
  }
  
  // Initial permissions count
  updatePermissionsCount();
});

// Additional script to handle JSON viewer
document.addEventListener('DOMContentLoaded', function() {
  const jsonDisplay = document.getElementById('json-display');
  const toggleJsonButton = document.getElementById('toggle-json-view');
  const copyJsonButton = document.getElementById('copy-json');
  
  // Get the permissions field - ensure we're using the right selector
  const permissionsField = document.querySelector('#id_permissions');
  
  console.log('JSON Viewer initialized');
  console.log('Permissions field found:', permissionsField ? 'Yes' : 'No');
  
  // Update JSON display
  function updateJsonDisplay() {
    if (permissionsField && jsonDisplay) {
      try {
        // Format the JSON nicely
        const permissionsValue = permissionsField.value || '{}';
        console.log('Raw permissions value:', permissionsValue);
        
        const permissionsObj = JSON.parse(permissionsValue);
        const formattedJson = JSON.stringify(permissionsObj, null, 2);
        jsonDisplay.textContent = formattedJson;
        console.log('Formatted JSON updated:', formattedJson.substring(0, 100) + (formattedJson.length > 100 ? '...' : ''));
      } catch (e) {
        console.error('Error parsing JSON:', e);
        jsonDisplay.textContent = 'Error parsing JSON: ' + e.message + '\n\nRaw value:\n' + permissionsField.value;
      }
    } else {
      console.error('Missing elements - jsonDisplay:', !!jsonDisplay, 'permissionsField:', !!permissionsField);
      if (jsonDisplay) {
        jsonDisplay.textContent = 'Error: Could not find the permissions field.';
      }
    }
  }
  
  // Toggle JSON display
  if (toggleJsonButton) {
    toggleJsonButton.addEventListener('click', function() {
      if (jsonDisplay) {
        const isCurrentlyHidden = jsonDisplay.style.display === 'none';
        jsonDisplay.style.display = isCurrentlyHidden ? 'block' : 'none';
        updateJsonDisplay();
        console.log('JSON display toggled to:', jsonDisplay.style.display);
      }
    });
  }
  
  // Copy JSON to clipboard
  if (copyJsonButton && permissionsField) {
    copyJsonButton.addEventListener('click', function() {
      try {
        // Make sure we have the latest value
        updateJsonDisplay();
        
        // Get the formatted JSON from the display
        const jsonText = jsonDisplay.textContent || '{}';
        
        // Copy to clipboard
        navigator.clipboard.writeText(jsonText).then(function() {
          alert('JSON copied to clipboard!');
          console.log('JSON copied to clipboard');
        }).catch(function(err) {
          console.error('Could not copy using clipboard API:', err);
          
          // Fallback method for copying
          const tempTextArea = document.createElement('textarea');
          tempTextArea.value = jsonText;
          document.body.appendChild(tempTextArea);
          tempTextArea.select();
          
          try {
            const successful = document.execCommand('copy');
            const msg = successful ? 'JSON copied to clipboard!' : 'Could not copy JSON';
            alert(msg);
            console.log(msg);
          } catch (err) {
            console.error('Fallback copy failed:', err);
            alert('Failed to copy JSON. Please copy it manually.');
          }
          
          document.body.removeChild(tempTextArea);
        });
      } catch (e) {
        console.error('Error during copy:', e);
        alert('Failed to copy JSON: ' + e.message);
      }
    });
  }
  
  // Listen for the custom event from external JS
  document.addEventListener('permissionsChanged', function(e) {
    console.log('Permissions changed event received');
    updateJsonDisplay();
  });
  
  // Function to periodically check and update JSON display
  function setupPeriodicUpdate() {
    // Initial update
    updateJsonDisplay();
    
    // Set up timer to update periodically (every 2 seconds)
    setInterval(updateJsonDisplay, 2000);
    
    // Also set up event listener for form changes
    const form = document.querySelector('form#accesslevel_form') || document.querySelector('form');
    if (form) {
      form.addEventListener('change', updateJsonDisplay);
      console.log('Form change listener added');
    }
    
    // Listen for permission button clicks
    document.addEventListener('click', function(e) {
      if (e.target.matches('.btn-view, .btn-modify, .single-btn') || 
          e.target.closest('.btn-view, .btn-modify, .single-btn')) {
        // Wait a moment for the permissions to update
        setTimeout(updateJsonDisplay, 100);
      }
    });
  }
  
  // Start the setup
  setupPeriodicUpdate();
});
</script>
{% endblock %} 