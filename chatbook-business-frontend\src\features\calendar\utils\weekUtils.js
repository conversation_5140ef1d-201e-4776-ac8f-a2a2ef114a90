/**
 * Week Utilities for Dynamic Calendar Configuration
 * Handles week start day calculations and dynamic shifting behavior
 */

// Day name mappings
export const DAY_NAMES = [
  'Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'
]

export const DAY_NAMES_SHORT = [
  'Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'
]

/**
 * Generate week days starting from a specific day of the week
 * @param {Date} selectedDate - The currently selected date
 * @param {number} weekStartDay - Day of week to start from (0=Sunday, 1=Monday, etc.)
 * @returns {Date[]} Array of 7 dates representing the week
 */
export const generateWeekDays = (selectedDate, weekStartDay = 1) => {
  const start = new Date(selectedDate)
  const currentDay = start.getDay() // 0=Sunday, 1=Monday, ..., 6=Saturday
  const startDay = weekStartDay !== undefined ? weekStartDay : 1 // Default to Monday if not set, but allow 0 (Sunday)
  
  // Calculate the difference to get to the start of the week
  let diff = currentDay - startDay
  
  // Handle week wrapping (e.g., if start day is Tuesday and current is Monday)
  if (diff < 0) {
    diff += 7
  }
  
  // Set to the start of the configured week
  start.setDate(start.getDate() - diff)
  
  const weekDays = []
  for (let i = 0; i < 7; i++) {
    const day = new Date(start)
    day.setDate(start.getDate() + i)
    weekDays.push(day)
  }
  return weekDays
}

/**
 * Get the week day headers based on the start day configuration
 * @param {number} weekStartDay - Day of week to start from (0=Sunday, 1=Monday, etc.)
 * @param {boolean} useShortNames - Whether to use short day names (default: false)
 * @returns {string[]} Array of day names starting from the configured start day
 */
export const getWeekDayHeaders = (weekStartDay = 1, useShortNames = false) => {
  const dayNames = useShortNames ? DAY_NAMES_SHORT : DAY_NAMES
  const headers = []
  
  for (let i = 0; i < 7; i++) {
    const dayIndex = (weekStartDay + i) % 7
    headers.push(dayNames[dayIndex])
  }
  
  return headers
}

/**
 * Calculate which days will be pushed out and added when changing week start day
 * @param {Date[]} currentWeekDays - Current week days array
 * @param {number} newWeekStartDay - New week start day to change to
 * @returns {object} Object with pushedOut and added day information
 */
export const calculateWeekShift = (currentWeekDays, newWeekStartDay) => {
  if (!currentWeekDays || currentWeekDays.length !== 7) {
    return { pushedOut: [], added: [], message: 'Invalid week days' }
  }
  
  // Get the current week start day from the week days
  const firstDay = currentWeekDays[0]
  const currentStartDay = firstDay.getDay()
  
  if (currentStartDay === newWeekStartDay) {
    return { pushedOut: [], added: [], message: 'No change needed' }
  }
  
  // Calculate how many days to shift
  let shift = newWeekStartDay - currentStartDay
  if (shift < 0) {
    shift += 7
  }
  
  const pushedOut = []
  const added = []
  
  // Days that will be pushed out (from the beginning)
  for (let i = 0; i < shift; i++) {
    pushedOut.push({
      date: currentWeekDays[i],
      dayName: DAY_NAMES[currentWeekDays[i].getDay()],
      dayNumber: currentWeekDays[i].getDate()
    })
  }
  
  // Days that will be added (at the end)
  for (let i = 0; i < shift; i++) {
    const newDay = new Date(currentWeekDays[6])
    newDay.setDate(newDay.getDate() + i + 1)
    added.push({
      date: newDay,
      dayName: DAY_NAMES[newDay.getDay()],
      dayNumber: newDay.getDate()
    })
  }
  
  const message = shift > 0 
    ? `${pushedOut.map(d => `${d.dayName} ${d.dayNumber}`).join(', ')} will be pushed out. ${added.map(d => `${d.dayName} ${d.dayNumber}`).join(', ')} will be added.`
    : 'No changes needed'
    
  return { pushedOut, added, message, shift }
}

/**
 * Check if a date is in the current week based on week start day
 * @param {Date} date - Date to check
 * @param {Date} selectedDate - Currently selected date
 * @param {number} weekStartDay - Week start day configuration
 * @returns {boolean} True if date is in the current week
 */
export const isDateInCurrentWeek = (date, selectedDate, weekStartDay = 1) => {
  const weekDays = generateWeekDays(selectedDate, weekStartDay)
  const dateStr = date.toDateString()
  return weekDays.some(day => day.toDateString() === dateStr)
}

/**
 * Get a human-readable description of the week start day change
 * @param {number} oldStartDay - Current week start day
 * @param {number} newStartDay - New week start day
 * @returns {string} Description of the change
 */
export const getWeekStartChangeDescription = (oldStartDay, newStartDay) => {
  if (oldStartDay === newStartDay) {
    return 'No change'
  }
  
  const oldDayName = DAY_NAMES[oldStartDay]
  const newDayName = DAY_NAMES[newStartDay]
  
  return `Week will now start on ${newDayName} instead of ${oldDayName}`
}

/**
 * Format week range for display
 * @param {Date[]} weekDays - Array of week days
 * @returns {string} Formatted week range (e.g., "Dec 11 - Dec 17, 2023")
 */
export const formatWeekRange = (weekDays) => {
  if (!weekDays || weekDays.length !== 7) {
    return ''
  }
  
  const firstDay = weekDays[0]
  const lastDay = weekDays[6]
  
  const formatOptions = { month: 'short', day: 'numeric' }
  const yearOptions = { year: 'numeric' }
  
  const firstStr = firstDay.toLocaleDateString('en-US', formatOptions)
  const lastStr = lastDay.toLocaleDateString('en-US', formatOptions)
  const year = lastDay.toLocaleDateString('en-US', yearOptions)
  
  return `${firstStr} - ${lastStr}, ${year}`
} 