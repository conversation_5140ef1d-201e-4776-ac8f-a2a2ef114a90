import { useState, useEffect } from "react";

function BookingPercentage() {
  // ========== State Management ==========

  // Filter conditions
  const [filters, setFilters] = useState({
    startDate: new Date(2025, 5, 1), // Jun 1, 2025
    endDate: new Date(2025, 5, 30), // Jun 30, 2025
    employee: "all",
    includePastEmployees: false,
  });

  // Advanced filter conditions
  const [advancedFilters, setAdvancedFilters] = useState({
    timeRange: "week",
    includeBlockedTime: true,
    includeBreaks: false,
    showOnlyAvailable: false,
  });

  // Update booking data when time range changes
  const handleTimeRangeChange = (newTimeRange) => {
    setAdvancedFilters((prev) => ({ ...prev, timeRange: newTimeRange }));
    setBookingData(getBookingDataByTimeRange(newTimeRange));
  };

  // Booking percentage data based on time range
  const getBookingDataByTimeRange = (timeRange) => {
    switch (timeRange) {
      case "day":
        return [
          {
            id: 1,
            dateRange: "06/23/25",
            percentage: 0,
            timeAvailable: 17,
            timeBooked: 0,
          },
          {
            id: 2,
            dateRange: "06/29/25",
            percentage: 0,
            timeAvailable: 17,
            timeBooked: 0,
          },
          {
            id: 3,
            dateRange: "06/30/25",
            percentage: 2.98,
            timeAvailable: 25,
            timeBooked: 1.25,
          },
        ];
      case "week":
        return [
          {
            id: 1,
            dateRange: "06/23/25 - 06/29/25",
            percentage: 0,
            timeAvailable: 17,
            timeBooked: 0,
          },
          {
            id: 2,
            dateRange: "06/30/25 - 06/30/25",
            percentage: 2.98,
            timeAvailable: 42,
            timeBooked: 1.25,
          },
        ];
      case "month":
        return [
          {
            id: 1,
            dateRange: "Jun-25",
            percentage: 2.98,
            timeAvailable: 59,
            timeBooked: 1.25,
          },
        ];
      case "year":
        return [
          {
            id: 1,
            dateRange: "2025",
            percentage: 2.98,
            timeAvailable: 59,
            timeBooked: 1.25,
          },
        ];
      default:
        return [
          {
            id: 1,
            dateRange: "06/23/25 - 06/29/25",
            percentage: 0,
            timeAvailable: 17,
            timeBooked: 0,
          },
          {
            id: 2,
            dateRange: "06/30/25 - 06/30/25",
            percentage: 2.98,
            timeAvailable: 42,
            timeBooked: 1.25,
          },
        ];
    }
  };

  const [bookingData, setBookingData] = useState(
    getBookingDataByTimeRange("week")
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasRunReport, setHasRunReport] = useState(false);

  // Show/hide advanced filters
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // View type for chart or grid display
  const [viewType, setViewType] = useState("chart"); // 'chart' or 'grid'

  // Dropdown options data
  const [employees, setEmployees] = useState([
    { id: "all", name: "All Employees" },
  ]);

  // ========== Initial Data Loading ==========

  useEffect(() => {
    loadInitialData();
  }, []);

  // Load initial data (employees)
  const loadInitialData = async () => {
    try {
      // Temporarily use mock employee data to avoid API calls
      const mockEmployees = [
        { id: "all", name: "All Employees" },
        { id: "emp1", name: "John Smith" },
        { id: "emp2", name: "Sarah Johnson" },
        { id: "emp3", name: "Mike Wilson" },
      ];

      setEmployees(mockEmployees);
    } catch (error) {
      console.error("Failed to load initial data:", error);
      // Set default data
      setEmployees([
        { id: "all", name: "All Employees" },
        { id: "emp1", name: "John Smith" },
        { id: "emp2", name: "Sarah Johnson" },
        { id: "emp3", name: "Mike Wilson" },
      ]);
    }
  };

  // ========== Event Handlers ==========

  // Handle filter condition changes
  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Handle advanced filter condition changes
  const handleAdvancedFilterChange = (key, value) => {
    setAdvancedFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Run report
  const handleRunReport = async () => {
    setLoading(true);
    setError(null);
    setHasRunReport(true);

    try {
      // Simulate loading delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Temporarily skip API call and show report directly
      // Build API query parameters
      const queryParams = {
        startDate: filters.startDate.toISOString().split("T")[0],
        endDate: filters.endDate.toISOString().split("T")[0],
        employee: filters.employee !== "all" ? filters.employee : undefined,
        timeRange: advancedFilters.timeRange,
        includeBlockedTime: advancedFilters.includeBlockedTime,
        includeBreaks: advancedFilters.includeBreaks,
        showOnlyAvailable: advancedFilters.showOnlyAvailable,
      };

      // Temporarily comment out API call and complete directly
      // const response = await bookingApi.getBookingPercentage(queryParams);
      // setBookingData(response);

      // Simulate successful response
      console.log("Booking percentage query params:", queryParams);
    } catch (err) {
      setError(err.message || "Failed to load booking percentage report");
    } finally {
      setLoading(false);
    }
  };

  // Export report
  const handleExport = async () => {
    try {
      // Temporarily use mock export
      alert("Export functionality will be implemented when API is ready");
    } catch (err) {
      setError(err.message || "Failed to export booking percentage report");
    }
  };

  // Print report
  const handlePrint = () => {
    window.print();
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Filter panel */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 items-end">
          {/* Date range */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Date Range:
            </label>
            <div className="relative">
              <input
                type="text"
                value="Jun 1, 2025 - Jun 30, 2025"
                readOnly
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
              />
              <svg
                className="absolute right-3 top-2.5 h-5 w-5 text-gray-400"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                />
              </svg>
            </div>
          </div>

          {/* Employees */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Employees:
            </label>
            <div className="relative">
              <select
                value={filters.employee}
                onChange={(e) => handleFilterChange("employee", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
              >
                {employees.map((employee) => (
                  <option key={employee.id} value={employee.id}>
                    {employee.name}
                  </option>
                ))}
              </select>
              <svg
                className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </div>
          </div>

          {/* Run report button */}
          <div>
            <button
              onClick={handleRunReport}
              disabled={loading}
              className="w-full bg-red-600 text-white hover:bg-red-700 focus:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 py-2.5 px-4 rounded-md disabled:opacity-50 disabled:cursor-not-allowed font-medium shadow-sm transition-colors duration-200"
            >
              {loading ? "Loading..." : "Run Report"}
            </button>
          </div>
        </div>
      </div>

      {/* Booking Percentage title and advanced filters */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-gray-800">
          Booking Percentage
        </h2>
        <button
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
        >
          Advanced Filters {showAdvancedFilters ? "▲" : "▼"}
        </button>
      </div>

      {/* Advanced filter panel */}
      {showAdvancedFilters && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
          <div>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={filters.includePastEmployees || false}
                onChange={(e) =>
                  handleFilterChange("includePastEmployees", e.target.checked)
                }
                className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">
                Include Past Employees
              </span>
            </label>
          </div>
        </div>
      )}

      {/* Main content area */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {!hasRunReport ? (
          // Placeholder content
          <div className="flex flex-col items-center justify-center py-20">
            <div className="text-6xl text-gray-400 mb-4">↑</div>
            <p className="text-lg text-gray-600 font-medium">
              Select filters above, then press Run Report.
            </p>
          </div>
        ) : (
          // Report results area
          <div className="p-6">
            {loading ? (
              <div className="flex items-center justify-center py-20">
                <div className="text-lg text-gray-600">Loading...</div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center py-20">
                <div className="text-lg text-red-600">Error: {error}</div>
              </div>
            ) : (
              // Booking percentage report content
              <div>
                {/* View type selector */}
                <div className="flex justify-between items-center mb-6">
                  <div className="flex gap-2">
                    <button
                      onClick={() => setViewType("chart")}
                      className={`px-4 py-2 rounded-md text-sm font-medium flex items-center gap-2 ${
                        viewType === "chart"
                          ? "bg-blue-600 text-white"
                          : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                      }`}
                    >
                      📊 Chart View
                    </button>
                    <button
                      onClick={() => setViewType("grid")}
                      className={`px-4 py-2 rounded-md text-sm font-medium flex items-center gap-2 ${
                        viewType === "grid"
                          ? "bg-blue-600 text-white"
                          : "bg-gray-200 text-gray-700 hover:bg-gray-300"
                      }`}
                    >
                      ⚏ Grid View
                    </button>
                  </div>

                  {viewType === "chart" && (
                    <div className="flex items-center gap-4">
                      <select
                        value={advancedFilters.timeRange}
                        onChange={(e) => handleTimeRangeChange(e.target.value)}
                        className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
                      >
                        <option value="day">Day</option>
                        <option value="week">Week</option>
                        <option value="month">Month</option>
                        <option value="year">Year</option>
                      </select>
                    </div>
                  )}
                </div>

                {viewType === "chart" ? (
                  // Chart view
                  <div className="space-y-6">
                    {/* Chart container */}
                    <div className="bg-white border border-gray-200 rounded-lg p-6 pb-12 flex relative">
                      {/* Chart area */}
                      <div
                        className="flex-1 relative"
                        style={{ height: "400px" }}
                      >
                        {/* Y-axis labels (left) */}
                        <div className="absolute left-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500 pr-2">
                          {(() => {
                            const maxValue = Math.max(
                              ...bookingData.map((item) => item.timeAvailable)
                            );
                            const scale =
                              maxValue <= 30 ? 30 : maxValue <= 60 ? 60 : 100;
                            const steps = [
                              scale,
                              scale * 0.8,
                              scale * 0.6,
                              scale * 0.4,
                              scale * 0.2,
                              0,
                            ];
                            return steps.map((value, index) => (
                              <span key={index}>{Math.round(value)}</span>
                            ));
                          })()}
                        </div>

                        {/* Y-axis labels (right) */}
                        <div className="absolute right-0 top-0 h-full flex flex-col justify-between text-xs text-gray-500 pl-2">
                          <span>100</span>
                          <span>80</span>
                          <span>60</span>
                          <span>40</span>
                          <span>20</span>
                          <span>0</span>
                        </div>

                        {/* Chart content area */}
                        <div className="ml-8 mr-8 h-full relative">
                          {/* Grid lines */}
                          <div className="absolute inset-0">
                            {[0, 20, 40, 60, 80, 100].map((value) => (
                              <div
                                key={value}
                                className="absolute w-full border-t border-gray-200"
                                style={{ bottom: `${value}%` }}
                              />
                            ))}
                          </div>

                          {/* Chart SVG */}
                          <svg className="absolute inset-0 w-full h-full">
                            {/* Render data points and lines based on bookingData */}
                            {bookingData.map((item, index) => {
                              const xPosition =
                                bookingData.length === 1
                                  ? 50
                                  : bookingData.length === 2
                                  ? index === 0
                                    ? 25
                                    : 75
                                  : bookingData.length === 3
                                  ? index === 0
                                    ? 20
                                    : index === 1
                                    ? 50
                                    : 80
                                  : 20 +
                                    (index * 60) / (bookingData.length - 1);

                              // Calculate Y position based on time available with dynamic scale
                              const maxValue = Math.max(
                                ...bookingData.map((item) => item.timeAvailable)
                              );
                              const scale =
                                maxValue <= 30 ? 30 : maxValue <= 60 ? 60 : 100;
                              const yPosition =
                                100 - (item.timeAvailable / scale) * 100;

                              return (
                                <g key={item.id}>
                                  {/* Data point */}
                                  <circle
                                    cx={`${xPosition}%`}
                                    cy={`${yPosition}%`}
                                    r="4"
                                    fill="#374151"
                                  />

                                  {/* Data label */}
                                  <text
                                    x={`${xPosition}%`}
                                    y={`${yPosition - 5}%`}
                                    textAnchor="middle"
                                    fontSize="12"
                                    fill="#374151"
                                    fontWeight="bold"
                                  >
                                    {item.timeAvailable}hrs
                                  </text>

                                  {/* Time Booked point if exists */}
                                  {item.timeBooked > 0 && (
                                    <>
                                      <circle
                                        cx={`${xPosition}%`}
                                        cy="97%"
                                        r="3"
                                        fill="#EF4444"
                                      />
                                      <text
                                        x={`${xPosition + 5}%`}
                                        y="94%"
                                        textAnchor="middle"
                                        fontSize="12"
                                        fill="#EF4444"
                                        fontWeight="bold"
                                      >
                                        {item.timeBooked}hrs
                                      </text>
                                    </>
                                  )}
                                </g>
                              );
                            })}

                            {/* Connect points with lines if more than one */}
                            {bookingData.length > 1 && (
                              <polyline
                                points={bookingData
                                  .map((item, index) => {
                                    const xPosition =
                                      bookingData.length === 2
                                        ? index === 0
                                          ? 25
                                          : 75
                                        : bookingData.length === 3
                                        ? index === 0
                                          ? 20
                                          : index === 1
                                          ? 50
                                          : 80
                                        : 20 +
                                          (index * 60) /
                                            (bookingData.length - 1);
                                    const maxValue = Math.max(
                                      ...bookingData.map(
                                        (item) => item.timeAvailable
                                      )
                                    );
                                    const scale =
                                      maxValue <= 30
                                        ? 30
                                        : maxValue <= 60
                                        ? 60
                                        : 100;
                                    const yPosition =
                                      100 - (item.timeAvailable / scale) * 100;
                                    return `${xPosition}% ${yPosition}%`;
                                  })
                                  .join(" ")}
                                stroke="#374151"
                                strokeWidth="2"
                                fill="none"
                              />
                            )}
                          </svg>

                          {/* Blue percentage bar at bottom - narrower */}
                          <div className="absolute bottom-4 left-0 w-1/4 h-2 bg-blue-400 rounded"></div>
                        </div>

                        {/* X-axis labels */}
                        <div className="absolute -bottom-10 left-8 right-8 flex justify-between text-xs text-gray-500">
                          {bookingData.map((item, index) => {
                            const xPosition =
                              bookingData.length === 1
                                ? 50
                                : bookingData.length === 2
                                ? index === 0
                                  ? 25
                                  : 75
                                : bookingData.length === 3
                                ? index === 0
                                  ? 20
                                  : index === 1
                                  ? 50
                                  : 80
                                : 20 + (index * 60) / (bookingData.length - 1);

                            return (
                              <div
                                key={item.id}
                                className="transform -rotate-45 origin-left absolute"
                                style={{
                                  left: `${xPosition}%`,
                                  transform: "translateX(-50%) rotate(-45deg)",
                                }}
                              >
                                <span className="whitespace-nowrap">
                                  {item.dateRange}
                                </span>
                              </div>
                            );
                          })}
                        </div>
                      </div>

                      {/* Legend (right side) */}
                      <div className="w-40 ml-6 space-y-3">
                        <div className="flex items-center">
                          <div className="w-4 h-4 bg-blue-400 rounded mr-2"></div>
                          <span className="text-sm text-gray-700">
                            Percentage
                          </span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-4 h-4 bg-gray-600 rounded mr-2"></div>
                          <span className="text-sm text-gray-700">
                            Time Available
                          </span>
                        </div>
                        <div className="flex items-center">
                          <div className="w-4 h-4 bg-red-400 rounded mr-2"></div>
                          <span className="text-sm text-gray-700">
                            Time Booked
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                ) : (
                  // Grid view
                  <div className="bg-white border border-gray-200 rounded-lg overflow-hidden">
                    <table className="w-full">
                      <thead className="bg-gray-50">
                        <tr>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                            Date
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                            Percentage
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                            Time Available
                          </th>
                          <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                            Time Booked
                          </th>
                        </tr>
                      </thead>
                      <tbody className="bg-white divide-y divide-gray-200">
                        {bookingData.map((item, index) => (
                          <tr key={item.id} className="hover:bg-gray-50">
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {item.dateRange}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {item.percentage}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {item.timeAvailable}
                            </td>
                            <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                              {item.timeBooked}
                            </td>
                          </tr>
                        ))}
                      </tbody>
                    </table>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
      </div>

      {/* Bottom buttons */}
      {hasRunReport && !loading && !error && (
        <div className="flex justify-end gap-4 mt-6">
          <button
            onClick={handleExport}
            className="px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700 flex items-center font-medium shadow-sm"
          >
            Export
            <span className="ml-1">▼</span>
          </button>
          <button
            onClick={handlePrint}
            className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium"
          >
            Print
          </button>
        </div>
      )}
    </div>
  );
}

export default BookingPercentage;
