import { useState, useMemo } from 'react'

export default function useCustomerData(initialCustomers = []) {
    // Use provided customer list directly so updates propagate
    const customers = initialCustomers

    // search and sort state
    const [searchTerm, setSearchTerm] = useState('')
    const [sortBy, setSortBy] = useState('name')
    const [sortOrder, setSortOrder] = useState('asc')
    
    // pagination state
    const [currentPage, setCurrentPage] = useState(1)
    const itemsPerPage = 10

    // handler for clicking table headers
    const handleSort = (field) => {
        if (sortBy === field) {
            setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
        } else {
            setSortBy(field)
            setSortOrder('asc')
        }
        // Reset to first page when sorting changes
        setCurrentPage(1)
    }

    // compute filtered + sorted list (all results)
    const allFilteredCustomers = useMemo(() => {
        let list = customers.filter(c => {
            if (!searchTerm) return true
            
            const txt = searchTerm.toLowerCase()
            return (
                c.name.toLowerCase().includes(txt) ||
                c.email.toLowerCase().includes(txt) ||
                c.phone.includes(searchTerm)
            )
        })

        list.sort((a, b) => {
            let A = a[sortBy] ?? ''
            let B = b[sortBy] ?? ''
            
            // Handle nested address fields
            if (sortBy.includes('address.')) {
                const addressField = sortBy.split('.')[1]
                A = a.address?.[addressField] ?? ''
                B = b.address?.[addressField] ?? ''
            }
            
            A = String(A).toLowerCase()
            B = String(B).toLowerCase()
            if (sortOrder === 'asc') return A < B ? -1 : A > B ? 1 : 0
            return A > B ? -1 : A < B ? 1 : 0
        })

        return list
    }, [customers, searchTerm, sortBy, sortOrder])

    // compute paginated results
    const filteredCustomers = useMemo(() => {
        const startIndex = (currentPage - 1) * itemsPerPage
        const endIndex = startIndex + itemsPerPage
        return allFilteredCustomers.slice(startIndex, endIndex)
    }, [allFilteredCustomers, currentPage, itemsPerPage])

    // pagination info
    const totalItems = allFilteredCustomers.length
    const totalPages = Math.ceil(totalItems / itemsPerPage)
    const startItem = totalItems === 0 ? 0 : (currentPage - 1) * itemsPerPage + 1
    const endItem = Math.min(currentPage * itemsPerPage, totalItems)

    // pagination handlers
    const goToPage = (page) => {
        setCurrentPage(Math.max(1, Math.min(page, totalPages)))
    }

    const goToNextPage = () => {
        if (currentPage < totalPages) {
            setCurrentPage(currentPage + 1)
        }
    }

    const goToPrevPage = () => {
        if (currentPage > 1) {
            setCurrentPage(currentPage - 1)
        }
    }

    // Reset to first page when search changes
    const setSearchTermAndResetPage = (term) => {
        setSearchTerm(term)
        setCurrentPage(1)
    }

    return {
        filteredCustomers,
        searchTerm, 
        setSearchTerm: setSearchTermAndResetPage,
        sortBy, 
        sortOrder, 
        handleSort,
        // pagination
        currentPage,
        totalPages,
        totalItems,
        startItem,
        endItem,
        itemsPerPage,
        goToPage,
        goToNextPage,
        goToPrevPage,
    }
}