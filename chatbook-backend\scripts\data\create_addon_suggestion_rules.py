#!/usr/bin/env python
import os
import sys
import django
from django.db import transaction

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from services.models import AddOn, AddonSuggestionRule
from business.models import Business

def get_business():
    """Get the Clement Lash business"""
    try:
        business = Business.objects.get(name="<PERSON> Lash")
        print(f'Found business: {business.name}')
        return business
    except Business.DoesNotExist:
        raise Exception("Business 'Clement Lash' not found. Please create it first.")

def get_addons(business):
    """Get the required add-ons for suggestion rules"""
    addons = {}
    
    # Get Consulting Session addon
    try:
        consulting_addon = AddOn.objects.get(
            business=business,
            name="Consulting Session"
        )
        addons['consulting'] = consulting_addon
        print(f'Found consulting addon: {consulting_addon.name}')
    except AddOn.DoesNotExist:
        raise Exception("Consulting Session addon not found. Please create lash services first.")
    
    # Get Lash Removal addon
    try:
        removal_addon = AddOn.objects.get(
            business=business,
            name="Lash Removal"
        )
        addons['removal'] = removal_addon
        print(f'Found removal addon: {removal_addon.name}')
    except AddOn.DoesNotExist:
        raise Exception("Lash Removal addon not found. Please create lash services first.")
    
    # Get Bottom Lash addon
    try:
        bottom_lash_addon = AddOn.objects.get(
            business=business,
            name="Bottom Lash(as an add-on service)"
        )
        addons['bottom_lash'] = bottom_lash_addon
        print(f'Found bottom lash addon: {bottom_lash_addon.name}')
    except AddOn.DoesNotExist:
        raise Exception("Bottom Lash addon not found. Please create lash services first.")
    
    return addons

def clean_existing_rules(business):
    """Remove existing addon suggestion rules for this business"""
    existing_rules = AddonSuggestionRule.objects.filter(business=business)
    count = existing_rules.count()
    
    if count > 0:
        print(f"Removing {count} existing addon suggestion rules...")
        existing_rules.delete()
        print("✅ Existing rules cleaned up.")
    else:
        print("No existing addon suggestion rules found.")

def create_suggestion_rules(business, addons):
    """Create the addon suggestion rules"""
    rules_created = []

    print("\nCreating addon suggestion rules...")

    # Rule 1: Suggest Consulting Session to new clients only
    consulting_rule = AddonSuggestionRule.objects.create(
        business=business,
        suggested_addon=addons['consulting'],
        new_client_only=True,
        # All other fields remain null/default
    )
    rules_created.append(consulting_rule)
    print(f"✅ Created rule: {consulting_rule}")

    # Rule 2: Suggest Lash Removal based on time range only (30-60 days)
    # No specific add-on criteria - just based on last visit timing
    removal_rule = AddonSuggestionRule.objects.create(
        business=business,
        suggested_addon=addons['removal'],
        new_client_only=False,
        last_addon=None,  # No specific add-on requirement
        min_days_since=30,
        max_days_since=60
    )
    rules_created.append(removal_rule)
    print(f"✅ Created rule: {removal_rule}")

    # Rule 3: Suggest Bottom Lash after Bottom Lash (self-referencing)
    bottom_lash_rule = AddonSuggestionRule.objects.create(
        business=business,
        suggested_addon=addons['bottom_lash'],
        last_addon=addons['bottom_lash'],
        new_client_only=False,
        # min_days_since and max_days_since remain null (any time)
    )
    rules_created.append(bottom_lash_rule)
    print(f"✅ Created rule: {bottom_lash_rule}")

    return rules_created

def create_addon_suggestion_rules():
    """Main function to create addon suggestion rules"""
    print("Creating Add-on Suggestion Rules for Clement Lash")
    print("=" * 60)
    
    try:
        with transaction.atomic():
            # Get the business
            business = get_business()
            
            # Get required addons
            addons = get_addons(business)
            
            # Clean existing rules
            clean_existing_rules(business)
            
            # Create new rules
            rules = create_suggestion_rules(business, addons)
            
            print(f"\n✅ Successfully created {len(rules)} addon suggestion rules!")
            print("\nRules created:")
            for i, rule in enumerate(rules, 1):
                print(f"  {i}. {rule}")
            
            return rules
            
    except Exception as e:
        print(f"❌ Error creating addon suggestion rules: {str(e)}")
        raise

def main():
    """Entry point for the script"""
    try:
        rules = create_addon_suggestion_rules()
        print(f"\n🎉 Add-on suggestion rules setup completed successfully!")
        print(f"Created {len(rules)} rules for Clement Lash business.")
        
    except Exception as e:
        print(f"\n💥 Script failed: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
