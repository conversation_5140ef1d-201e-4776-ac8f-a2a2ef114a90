import { useState, useRef, useEffect } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { useAuthStore } from '../../stores/authStore';
import LogoutConfirmModal from '../ui/LogoutConfirmModal';
import './Layout.css';

const Header = ({ businessInfo }) => {
    const [showUserMenu, setShowUserMenu] = useState(false);
    const [showLogoutModal, setShowLogoutModal] = useState(false);
    const userMenuRef = useRef(null);
    const { user, isAuthenticated, logout } = useAuthStore();
    const navigate = useNavigate();

    // Default info if none provided
    const info = businessInfo || {
        name: 'Clément Lash',
        location: 'Bellevue, WA',
        logo: '/images/logo.png'
    };

    const scrollToBooking = () => {
        document.getElementById('booking')?.scrollIntoView({ behavior: 'smooth' });
    };

    const handleLogoutClick = () => {
        setShowUserMenu(false);
        setShowLogoutModal(true);
    };

    const handleLogoutConfirm = async () => {
        setShowLogoutModal(false);
        await logout();
        navigate('/');
    };

    const handleLogoutCancel = () => {
        setShowLogoutModal(false);
    };

    // Close user menu when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (userMenuRef.current && !userMenuRef.current.contains(event.target)) {
                setShowUserMenu(false);
            }
        };

        document.addEventListener('mousedown', handleClickOutside);
        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, []);

    const getUserInitials = () => {
        if (!user) return '';
        const firstName = user.first_name || '';
        const lastName = user.last_name || '';
        return `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase();
    };

    return (
        <header className="profile-header">
            <div className="profile-card">
                {/* Auth Section - Top Right */}
                <div className="auth-section">
                    {isAuthenticated ? (
                        <div className="user-menu-container" ref={userMenuRef}>
                            <button
                                onClick={() => setShowUserMenu(!showUserMenu)}
                                className="user-menu-button"
                            >
                                <span className="user-avatar">
                                    {getUserInitials() || 'U'}
                                </span>
                            </button>

                            {showUserMenu && (
                                <div className="user-dropdown">
                                    <div className="user-info">
                                        <p className="user-name">
                                            {user.first_name} {user.last_name}
                                        </p>
                                        <p className="user-email">
                                            {user.email}
                                        </p>
                                    </div>
                                    <div className="dropdown-divider"></div>
                                    <Link
                                        to="/profile"
                                        className="dropdown-item"
                                        onClick={() => setShowUserMenu(false)}
                                    >
                                        Your Profile
                                    </Link>
                                    <Link
                                        to="/appointments"
                                        className="dropdown-item"
                                        onClick={() => setShowUserMenu(false)}
                                    >
                                        Appointments
                                    </Link>
                                    <button
                                        onClick={handleLogoutClick}
                                        className="dropdown-item logout-btn"
                                    >
                                        Sign out
                                    </button>
                                </div>
                            )}
                        </div>
                    ) : (
                        <Link to="/login" className="login-btn">
                            Login
                        </Link>
                    )}
                </div>

                {/* Main Profile Content */}
                <div className="profile-content">
                    <div className="profile-image">
                        <img src={info.logo} alt={`${info.name} Logo`} />
                    </div>

                    <div className="profile-info">
                        <h1>{info.name}</h1>
                        <p>{info.location}</p>
                        <button onClick={scrollToBooking} className="book-now-btn">
                            Book Now
                        </button>
                    </div>
                </div>
            </div>

            {/* Logout Confirmation Modal */}
            <LogoutConfirmModal
                isOpen={showLogoutModal}
                onConfirm={handleLogoutConfirm}
                onCancel={handleLogoutCancel}
            />
        </header>
    );
};

export default Header;
