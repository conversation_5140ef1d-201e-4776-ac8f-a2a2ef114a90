import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import DatePicker from 'react-datepicker';
import 'react-datepicker/dist/react-datepicker.css';
import toast from 'react-hot-toast';
import { useAuthStore } from '../../stores/authStore';
import { useBookingStore } from '../../stores/bookingStore';
import { useEmployeesWithFallback } from '../../hooks/useEmployees';
import { useAvailabilityWithFallback } from '../../hooks/useAvailability';
import { handlePostTimeSlotSelection } from '../../utils/navigationUtils';
import ServiceAddOnOverlay from '../services/ServiceAddOnOverlay.jsx';
import './Booking.css';

const BookingPage = ({ selectedService: initialService, addOns }) => {
    const navigate = useNavigate();
    const { isAuthenticated, user } = useAuthStore();
    const {
        bookingData,
        setSelectedService: setBookingService,
        setSelectedEmployee: setBookingEmployee,
        setSelectedDateTime,
        setSelectedAddOns: setBookingAddOns,
        updateBookingData,
        canProceedToStep
    } = useBookingStore();

    // Use booking context data or fallback to props
    const [selectedService, setSelectedService] = useState(bookingData.selectedService || initialService);
    const [selectedAddOns, setSelectedAddOns] = useState(bookingData.selectedAddOns || addOns || []);

    // Use React Query for employees
    const { data: employees = [], isLoading: employeesLoading } = useEmployeesWithFallback();

    const [selectedEmployeeId, setSelectedEmployeeId] = useState(bookingData.selectedEmployee?.id || '');
    const [selectedDate, setSelectedDate] = useState(bookingData.selectedDate || '');
    const [selectedDateObj, setSelectedDateObj] = useState(
        bookingData.selectedDate ? new Date(bookingData.selectedDate) : new Date()
    );
    const [currentWeek, setCurrentWeek] = useState([]);
    const [showAddOnOverlay, setShowAddOnOverlay] = useState(false);
    const [tempSelectedService, setTempSelectedService] = useState(null);
    const [dropdownOpen, setDropdownOpen] = useState(false);

    // Use React Query for availability
    // Only pass employeeId if a specific employee is selected (not "Any employee")
    const employeeId = selectedEmployeeId && selectedEmployeeId !== '' ? parseInt(selectedEmployeeId) : null;
    const serviceId = selectedService?.id;
    const {
        data: availableTimeSlots = {},
        isLoading: isCalendarLoading,
        error: availabilityError
    } = useAvailabilityWithFallback(employeeId, selectedDate, serviceId);

    const noTimesAvailable = !isCalendarLoading && Object.keys(availableTimeSlots).length === 0;

    // Format date as YYYY-MM-DD
    function formatDate(date) {
        if (typeof date === 'string') {
            if (/^\d{4}-\d{2}-\d{2}$/.test(date)) {
                return date;
            }
            date = new Date(date);
        }
        
        if (!(date instanceof Date) || isNaN(date)) {
            console.error('Invalid date:', date);
            return '';
        }
        
        return date.toISOString().split('T')[0];
    }

    // Get days of the week starting from a given date
    function getWeekDays(startDate) {
        const days = [];
        const currentDate = new Date(startDate);
        const today = new Date();
        
        // Start from the nearest Sunday
        const day = currentDate.getDay(); // 0 is Sunday
        currentDate.setDate(currentDate.getDate() - day);
        
        // Create 7 days
        for (let i = 0; i < 7; i++) {
            const date = new Date(currentDate);
            const startOfToday = new Date(today);
            startOfToday.setHours(0, 0, 0, 0);
            const isPastDate = date < startOfToday;
            
            days.push({
                date: formatDate(date),
                day: date.getDate(),
                dayOfWeek: getDayName(date.getDay()),
                isToday: isSameDay(date, today),
                isPastDate: isPastDate
            });
            currentDate.setDate(currentDate.getDate() + 1);
        }
        
        return days;
    }

    // Helper to check if two dates are the same day
    function isSameDay(date1, date2) {
        return (
            date1.getFullYear() === date2.getFullYear() &&
            date1.getMonth() === date2.getMonth() &&
            date1.getDate() === date2.getDate()
        );
    }

    // Get day name (SUN, MON, etc.)
    function getDayName(dayIndex) {
        const days = ['SUN', 'MON', 'TUE', 'WED', 'THU', 'FRI', 'SAT'];
        return days[dayIndex];
    }

    // Format date for display - May 15, 2023
    function formatDisplayDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString('en-US', { 
            month: 'long', 
            day: 'numeric', 
            year: 'numeric' 
        });
    }

    // Get month name for display
    function getMonthName() {
        const date = new Date(selectedDate);
        return date.toLocaleDateString('en-US', { month: 'long' });
    }

    // Initialize with current week
    useEffect(() => {
        const today = new Date();
        const weekDays = getWeekDays(today);
        setCurrentWeek(weekDays);
        setSelectedDate(formatDate(today));
    }, []);

    // Set default date to today when component mounts
    useEffect(() => {
        if (!selectedDate) {
            const today = new Date();
            const formattedToday = formatDate(today);
            console.log('Setting default date to today:', formattedToday);
            setSelectedDate(formattedToday);
            setSelectedDateObj(today);

            // Also set the current week
            const weekContainingDate = getWeekDays(today);
            setCurrentWeek(weekContainingDate);
        }
    }, [selectedDate]);

    // React Query now handles availability fetching automatically





    // Event handlers
    const handleDaySelect = (date) => {
        const dateObj = typeof date === 'string' ? new Date(date) : date;
        setSelectedDate(formatDate(dateObj));
        setSelectedDateObj(dateObj);
    };

    const handleServiceSelect = (service) => {
        setTempSelectedService(service);
        setShowAddOnOverlay(true);
        setDropdownOpen(false);
    };

    const handleCloseAddOnOverlay = () => {
        setShowAddOnOverlay(false);
        setTempSelectedService(null);
    };

    const handleContinueWithAddOns = (service, addOns) => {
        setSelectedService(service);
        setSelectedAddOns(addOns);
        setShowAddOnOverlay(false);

        // Update booking context with service and add-ons
        setServiceAndAddOns(service, addOns);
    };

    const handleSearch = () => {
        alert('Search functionality would be implemented here');
    };



    const handleDateChange = (date) => {
        if (!date) return;
        setSelectedDateObj(date);
        setSelectedDate(formatDate(date));
        
        const weekContainingDate = getWeekDays(date);
        setCurrentWeek(weekContainingDate);
    };

    const goToNextWeek = () => {
        const lastDayOfCurrentWeek = new Date(currentWeek[currentWeek.length - 1].date);
        const firstDayOfNextWeek = new Date(lastDayOfCurrentWeek);
        firstDayOfNextWeek.setDate(lastDayOfCurrentWeek.getDate() + 1);
        
        const newWeek = getWeekDays(firstDayOfNextWeek);
        setCurrentWeek(newWeek);
    };

    const goToPreviousWeek = () => {
        const firstDayOfCurrentWeek = new Date(currentWeek[0].date);
        const lastDayOfPreviousWeek = new Date(firstDayOfCurrentWeek);
        lastDayOfPreviousWeek.setDate(firstDayOfCurrentWeek.getDate() - 7);
        
        const newWeek = getWeekDays(lastDayOfPreviousWeek);
        setCurrentWeek(newWeek);
    };

    const toggleDropdown = () => {
        if (selectedService) {
            handleEditCurrentService();
        } else {
            setDropdownOpen(!dropdownOpen);
        }
    };

    const handleEditCurrentService = () => {
        if (selectedService) {
            setTempSelectedService(selectedService);
            setShowAddOnOverlay(true);
        }
    };

    const getServiceDisplayText = () => {
        if (!selectedService) return "Select Service";

        if (selectedAddOns && selectedAddOns.length > 0) {
            return `${selectedService.name} & ${selectedAddOns.length} add-on${selectedAddOns.length > 1 ? 's' : ''}`;
        }

        return selectedService.name;
    };

    // Handle time slot selection - require authentication first
    const handleTimeSlotSelect = async (time, employee) => {
        console.log('🕐 Time slot selected:', { time, employee, selectedService, selectedDate, selectedAddOns });
        console.log('📋 Current booking context before update:', bookingData);
        console.log('🔐 Authentication status:', { isAuthenticated, user: user?.email || 'No user' });

        // Provide immediate visual feedback
        toast.loading('Processing your selection...', { duration: 1000 });

        // Ensure we have the service data - use local state if booking context doesn't have it
        const serviceToUse = bookingData.selectedService || selectedService;
        const addOnsToUse = bookingData.selectedAddOns || selectedAddOns;

        // Ensure we have a valid employee object
        const employeeToUse = employee || {
            id: selectedEmployeeId || 'any',
            display_name: selectedEmployeeId ? `Employee #${selectedEmployeeId}` : 'Any Employee',
            full_name: selectedEmployeeId ? `Employee #${selectedEmployeeId}` : 'Any Employee'
        };

        // Update booking store with complete booking data including service
        setBookingService(serviceToUse);
        setBookingAddOns(addOnsToUse);
        setBookingEmployee(employeeToUse);
        setSelectedDateTime(selectedDate, time);

        console.log('Updated booking context with complete data');
        console.log('Service preserved:', serviceToUse?.name);
        console.log('Can proceed to consent:', canProceedToStep('consent'));
        console.log('User authenticated:', isAuthenticated);
        console.log('User data:', user);

        if (!isAuthenticated || !user) {
            // Show user feedback and redirect to login
            toast.error('Please log in to book an appointment', {
                duration: 3000,
                position: 'top-center',
            });
            console.log('🔒 User not authenticated, redirecting to login');
            console.log('📝 Booking data saved to store for after login');
            navigate('/login');
            return;
        }

        // Use centralized navigation logic for consent status checking
        // This will check auth store and navigate appropriately WITHOUT making API calls
        try {
            console.log('🎯 Using navigation orchestration for authenticated user');
            const nextRoute = await handlePostTimeSlotSelection(user, navigate);
            console.log('✅ Navigation orchestration completed, went to:', nextRoute);
        } catch (error) {
            console.error('❌ Error in post-time-slot navigation:', error);
            toast.error('Something went wrong. Please try again.');
            // Fallback to login to refresh everything
            navigate('/login');
        }
    };

    // Format time for display (convert 24h to 12h format)
    const formatTimeDisplay = (timeStr) => {
        const [hours, minutes] = timeStr.split(':');
        const hour = parseInt(hours);
        const ampm = hour >= 12 ? 'PM' : 'AM';
        const displayHour = hour === 0 ? 12 : hour > 12 ? hour - 12 : hour;
        return `${displayHour}:${minutes} ${ampm}`;
    };

    return (
        <div className="booking-page">
            <div className="booking-content">
                <div className="booking-search-bar">
                    <div className="service-selection dropdown">
                        <div className="custom-select">
                            <div 
                                className="selected-service-display" 
                                onClick={toggleDropdown}
                            >
                                {getServiceDisplayText()}
                                <span className="dropdown-arrow">▼</span>
                            </div>
                            {!selectedService && dropdownOpen && (
                                <div className="custom-dropdown-menu">
                                    <div className="dropdown-item">
                                        <span>Please select a service from the main page</span>
                                    </div>
                                </div>
                            )}
                        </div>
                    </div>
                    
                    <div className="employee-selection dropdown">
                        <select
                            value={selectedEmployeeId}
                            onChange={(e) => setSelectedEmployeeId(e.target.value)}
                        >
                            <option value="">Any Employee</option>
                            {employees
                                .sort((a, b) => (a.display_name || '').localeCompare(b.display_name || ''))
                                .map(employee => (
                                    <option key={employee.id} value={employee.id}>
                                        {employee.display_name || `Employee #${employee.id}`}
                                    </option>
                                ))
                            }
                        </select>
                        <span className="dropdown-arrow">▼</span>
                    </div>
                    
                    <div className="date-selection dropdown">
                        <DatePicker
                            selected={selectedDateObj}
                            onChange={handleDateChange}
                            minDate={new Date()}
                            dateFormat="MMMM d, yyyy"
                            showMonthDropdown
                            showYearDropdown
                            dropdownMode="select"
                            className="date-picker-input"
                            filterDate={date => {
                                const today = new Date();
                                today.setHours(0, 0, 0, 0);
                                return date >= today;
                            }}
                        />
                        <span className="dropdown-arrow">▼</span>
                    </div>
                    
                    <button className="search-button" onClick={handleSearch}>
                        Search
                    </button>
                </div>

                <div className="add-service-section">
                    <button className="add-service-btn">
                        + Add Service
                    </button>
                </div>

                <div className="calendar-section">
                    <div className="calendar-header">
                        <h3>{getMonthName()}</h3>
                        <span className="this-week-label">This Week</span>
                    </div>

                    <div className="week-calendar">
                        <button
                            className="week-nav-btn prev"
                            onClick={goToPreviousWeek}
                            disabled={currentWeek.length > 0 && new Date(currentWeek[0].date) <= new Date()}
                        >
                            &lt;
                        </button>

                        <div className="week-days">
                            {currentWeek.map((day, index) => (
                                <div
                                    key={index}
                                    className={`day ${day.isToday ? 'today' : ''} ${selectedDate === day.date ? 'selected' : ''} ${day.isPastDate ? 'past-date' : ''}`}
                                    onClick={() => !day.isPastDate && handleDaySelect(day.date)}
                                >
                                    <div className="day-name">{day.dayOfWeek}</div>
                                    <div className="day-number">{day.day}</div>
                                </div>
                            ))}
                        </div>

                        <button
                            className="week-nav-btn next"
                            onClick={goToNextWeek}
                        >
                            &gt;
                        </button>
                    </div>
                </div>

                <div className="time-slots-section">
                    {!selectedService ? (
                        <div className="no-service-message">
                            <h3>Please select a service</h3>
                            <p>Choose a service from the dropdown menu to see available appointment times.</p>
                        </div>
                    ) : isCalendarLoading ? (
                        <div className="loading-message">
                            <h3>Loading available times...</h3>
                            <p>Please wait while we check availability.</p>
                        </div>
                    ) : noTimesAvailable ? (
                        <div className="no-times-message">
                            <h3>No times available</h3>
                            <p>There are no available appointment times for the selected date and service. Please try a different date.</p>
                        </div>
                    ) : (
                        <div className="time-slots-container">
                            <h3>Available Times</h3>
                            <div className="selected-details">
                                <p><strong>Service:</strong> {selectedService.name}</p>
                                <p><strong>Date:</strong> {formatDisplayDate(selectedDate)}</p>
                                {selectedEmployeeId && (
                                    <p><strong>Employee:</strong> {employees.find(emp => emp.id.toString() === selectedEmployeeId)?.display_name || 'Selected Employee'}</p>
                                )}
                                {selectedAddOns && selectedAddOns.length > 0 && (
                                    <p><strong>Add-ons:</strong> {selectedAddOns.map(addon => addon.name).join(', ')}</p>
                                )}
                            </div>

                            <div className="time-slots-container">
                                {/* Handle object format with employee names as keys */}
                                {typeof availableTimeSlots === 'object' && !Array.isArray(availableTimeSlots) && (
                                    Object.entries(availableTimeSlots).map(([employeeName, slots]) => (
                                        <div key={employeeName} className="employee-slots">
                                            <h4 className="employee-name">{employeeName}</h4>
                                            <div className="time-slots-grid">
                                                {(Array.isArray(slots) ? slots : [])
                                                    .filter(slot => slot.available)
                                                    .map((slot, index) => {
                                                        const employee = employees.find(emp =>
                                                            emp.display_name === employeeName ||
                                                            emp.full_name === employeeName
                                                        );

                                                        return (
                                                            <button
                                                                key={`${employeeName}-${index}`}
                                                                className="time-slot-btn"
                                                                onClick={() => handleTimeSlotSelect(slot.time, employee)}
                                                            >
                                                                <div className="time-slot-time">{formatTimeDisplay(slot.time)}</div>
                                                            </button>
                                                        );
                                                    })}
                                            </div>
                                        </div>
                                    ))
                                )}

                                {/* Handle array format with employee objects */}
                                {Array.isArray(availableTimeSlots) && availableTimeSlots.length > 0 && availableTimeSlots[0].employee && (
                                    availableTimeSlots.map((empData, empIndex) => (
                                        <div key={empIndex} className="employee-slots">
                                            <h4 className="employee-name">{empData.employee.display_name}</h4>
                                            <div className="time-slots-grid">
                                                {empData.slots
                                                    .filter(slot => slot.available)
                                                    .map((slot, index) => (
                                                        <button
                                                            key={`${empData.employee.id}-${index}`}
                                                            className="time-slot-btn"
                                                            onClick={() => handleTimeSlotSelect(slot.time, empData.employee)}
                                                        >
                                                            <div className="time-slot-time">{formatTimeDisplay(slot.time)}</div>
                                                        </button>
                                                    ))}
                                            </div>
                                        </div>
                                    ))
                                )}

                                {/* Handle simple array format (fallback) */}
                                {Array.isArray(availableTimeSlots) && availableTimeSlots.length > 0 && !availableTimeSlots[0].employee && (
                                    <div className="time-slots-grid">
                                        {availableTimeSlots.map((slot, index) => {
                                            // Handle different API response formats
                                            const time = slot.time || slot.start_time;
                                            const employeeName = slot.employee_name || slot.employee?.display_name || slot.employee?.full_name;
                                            const employee = employees.find(emp =>
                                                emp.display_name === employeeName ||
                                                emp.full_name === employeeName ||
                                                emp.id === slot.employee_id
                                            );

                                            return (
                                                <button
                                                    key={`${time}-${index}`}
                                                    className="time-slot-btn"
                                                    onClick={() => handleTimeSlotSelect(time, employee)}
                                                >
                                                    <div className="time-slot-time">{formatTimeDisplay(time)}</div>
                                                    {employeeName && (
                                                        <div className="time-slot-employee">{employeeName}</div>
                                                    )}
                                                </button>
                                            );
                                        })}
                                    </div>
                                )}
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {showAddOnOverlay && tempSelectedService && (
                <ServiceAddOnOverlay
                    service={tempSelectedService}
                    onClose={handleCloseAddOnOverlay}
                    onContinue={handleContinueWithAddOns}
                    initialAddOns={selectedAddOns}
                />
            )}
        </div>
    );
};

export default BookingPage;
