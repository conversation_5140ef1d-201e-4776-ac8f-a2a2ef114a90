import { useState, useEffect } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { useBookingStore } from '../stores/bookingStore';
import { canProceedToBookingStep, getBookingRedirectRoute } from '../utils/navigationUtils';
import CancellationPolicyDisplay from '../components/booking/CancellationPolicyDisplay.jsx';
import AppointmentSummary from '../components/booking/AppointmentSummary';
import '../components/booking/Booking.css';

const ReviewBookingPage = () => {
    const location = useLocation();
    const navigate = useNavigate();
    const { user, isAuthenticated } = useAuthStore();
    const {
        bookingData,
        setCustomerInfo,
        canProceedToStep,
        clearBookingData,
        setConsentData,
        calculateTotals
    } = useBookingStore();
    const [customerInfo, setLocalCustomerInfo] = useState(bookingData.customerInfo);

    const [formErrors, setFormErrors] = useState({});
    const [isLoading, setIsLoading] = useState(false);
    const [bookingError, setBookingError] = useState(null);
    const [isOrderSummaryExpanded, setIsOrderSummaryExpanded] = useState(false);



    useEffect(() => {
        console.log('🔍 ReviewBookingPage useEffect - Current booking data:', bookingData);
        console.log('🔍 Authentication status:', { isAuthenticated, user: user?.email });
        console.log('🔍 Can proceed to review step:', canProceedToBookingStep('review', bookingData, user));
        console.log('🔍 Consent status:', bookingData.consentData);

        // Check authentication first
        if (!isAuthenticated) {
            console.log('User not authenticated, redirecting to login');
            navigate('/login');
            return;
        }

        // For users with completed forms who are navigated directly to review,
        // we need to set consent as agreed since they bypassed the consent form
        if (bookingData.selectedService && bookingData.selectedEmployee &&
            bookingData.selectedDate && bookingData.selectedTime &&
            !bookingData.consentData.consentAgreed) {
            console.log('🔧 Setting consent as agreed for user with completed forms');
            setConsentData({ consentAgreed: true });
        }

        // Ensure totals are calculated
        if (bookingData.selectedService && bookingData.total === 0) {
            console.log('🧮 Calculating totals for booking');
            calculateTotals();
        }

        // Check if we have the required booking data to proceed to review
        if (!canProceedToBookingStep('review', bookingData, user)) {
            console.log('Missing required booking data for review, redirecting to appropriate page');
            console.log('Required data check:', {
                selectedService: !!bookingData.selectedService,
                selectedEmployee: !!bookingData.selectedEmployee,
                selectedDate: !!bookingData.selectedDate,
                selectedTime: !!bookingData.selectedTime,
                consentAgreed: bookingData.consentData.consentAgreed
            });
            const redirectRoute = getBookingRedirectRoute(bookingData, user);
            navigate(redirectRoute);
            return;
        }

        // Pre-fill customer info with user data if available and not already filled
        if (user && !customerInfo.name) {
            const updatedCustomerInfo = {
                ...customerInfo,
                name: `${user.first_name || ''} ${user.last_name || ''}`.trim(),
                email: user.email || '',
                phone: user.phone || ''
            };
            setLocalCustomerInfo(updatedCustomerInfo);
            setCustomerInfo(updatedCustomerInfo);
        }
    }, [isAuthenticated, navigate, canProceedToStep, user, customerInfo, setCustomerInfo, bookingData, setConsentData, calculateTotals]);

    // Use total cost from booking context (automatically calculated)
    const totalCost = bookingData.total || 0;

    // Format date
    const formattedDate = bookingData?.selectedDate ? new Date(bookingData.selectedDate).toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    }) : '';

    // Format time
    const formatTime = (timeStr) => {
        if (!timeStr) return '';

        try {
            const [hours, minutes] = timeStr.split(':');
            const hour = parseInt(hours, 10);
            const ampm = hour >= 12 ? 'PM' : 'AM';
            const hour12 = hour % 12 || 12;
            return `${hour12}:${minutes} ${ampm}`;
        } catch (error) {
            console.error('Error formatting time:', error);
            return timeStr;
        }
    };

    // Handle input changes
    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        const updatedValue = type === 'checkbox' ? checked : value;

        // Update local state
        setLocalCustomerInfo(prev => ({
            ...prev,
            [name]: updatedValue
        }));

        // Update booking context
        setCustomerInfo({
            [name]: updatedValue
        });

        // Clear error for this field
        if (formErrors[name]) {
            setFormErrors({
                ...formErrors,
                [name]: null
            });
        }
    };

    const validateForm = () => {
        const errors = {};

        if (!customerInfo.name.trim()) errors.name = 'Name is required';
        if (!customerInfo.email.trim()) errors.email = 'Email is required';
        if (!/^\S+@\S+\.\S+$/.test(customerInfo.email)) errors.email = 'Valid email is required';
        if (!customerInfo.phone.trim()) errors.phone = 'Phone is required';
        if (!customerInfo.address1.trim()) errors.address1 = 'Address is required';
        if (!customerInfo.creditCardName.trim()) errors.creditCardName = 'Name on card is required';
        if (!customerInfo.creditCardNumber.trim()) errors.creditCardNumber = 'Card number is required';
        if (!customerInfo.creditCardExpiry.trim()) errors.creditCardExpiry = 'Expiry date is required';
        if (!customerInfo.creditCardCVV.trim()) errors.creditCardCVV = 'Security code is required';
        if (!customerInfo.agree) errors.agree = 'You must agree to the cancellation policy';

        setFormErrors(errors);
        return Object.keys(errors).length === 0;
    };

    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!validateForm()) {
            // Scroll to the first error
            const firstErrorField = document.querySelector('.error-message');
            if (firstErrorField) {
                firstErrorField.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
            return;
        }

        try {
            setIsLoading(true);
            setBookingError(null);

            // Get formatted booking data for API submission
            const completeBookingData = getBookingDataForAPI();

            // Simulate API call for now
            setTimeout(() => {
                console.log('Booking submitted:', completeBookingData);
                setIsLoading(false);

                // Clear booking data after successful submission
                clearBookingData();

                // Redirect to confirmation page
                navigate('/booking-confirmation', {
                    state: {
                        bookingData: completeBookingData,
                        customerInfo,
                        totalCost
                    }
                });
            }, 1500);

        } catch (error) {
            console.error('Error creating booking:', error);
            setBookingError('Failed to create booking. Please try again.');
            setIsLoading(false);
        }
    };

    const handleBack = () => {
        // Go back to the time selection page (booking calendar)
        // This allows users to change their selected time slot
        navigate('/');
    };

    // If we don't have required booking data, the useEffect will redirect
    if (!canProceedToBookingStep('review', bookingData, user)) {
        return <div className="loading">Loading booking information...</div>;
    }

    return (
        <div className="min-h-screen bg-white">
            {/* Simple Top Header */}
            <div className="bg-white">
                <div className="max-w-6xl mx-auto border-b border-gray-200 px-6 py-3">
                    <div className="flex justify-end items-center">
                        <div className="flex items-center space-x-3">
                            <span className="text-sm text-gray-700">Welcome, DylanStylistTest Z</span>
                            <div className="w-8 h-8 bg-gray-600 rounded-full flex items-center justify-center text-white text-sm font-medium">
                                DZ
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Mobile Order Summary - Collapsible */}
            <div className="lg:hidden bg-white border-b border-gray-200">
                <div className="max-w-6xl mx-auto px-6">
                    <button
                        onClick={() => setIsOrderSummaryExpanded(!isOrderSummaryExpanded)}
                        className="w-full py-4 flex justify-between items-center text-left"
                    >
                        <h3 className="text-lg font-semibold text-gray-900">Order Summary</h3>
                        <span className="text-gray-500 text-xl">
                            {isOrderSummaryExpanded ? '−' : '+'}
                        </span>
                    </button>

                    {isOrderSummaryExpanded && (
                        <div className="pb-6 border-t border-gray-200">
                            <div className="pt-4">
                                {/* Date and Time */}
                                <div className="mb-4">
                                    <div className="text-sm text-gray-900 font-medium">
                                        {bookingData.selectedDate ?
                                            new Date(bookingData.selectedDate).toLocaleDateString('en-US', {
                                                weekday: 'short',
                                                month: 'short',
                                                day: 'numeric',
                                                year: 'numeric'
                                            }) : 'Date not selected'
                                        } {bookingData.selectedTime ?
                                            new Date(`2000-01-01 ${bookingData.selectedTime}`).toLocaleTimeString('en-US', {
                                                hour: 'numeric',
                                                minute: '2-digit',
                                                hour12: true
                                            }) : 'Time not selected'
                                        }
                                    </div>
                                </div>

                                {/* Stylist Info */}
                                <div className="mb-4 pb-4 border-b border-gray-300">
                                    <div className="flex items-start space-x-3">
                                        <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                                            <span className="text-sm font-medium text-gray-700">
                                                {(bookingData.selectedEmployee?.display_name || bookingData.selectedEmployee?.name || bookingData.selectedEmployee?.full_name)?.charAt(0) || 'S'}
                                            </span>
                                        </div>
                                        <div className="flex-1">
                                            <div className="text-sm text-gray-600">
                                                {bookingData.selectedEmployee?.title || bookingData.selectedEmployee?.role || 'Stylist'}
                                            </div>
                                            <div className="font-medium text-gray-900">
                                                {bookingData.selectedEmployee?.display_name ||
                                                 bookingData.selectedEmployee?.name ||
                                                 bookingData.selectedEmployee?.full_name ||
                                                 'Stylist'}
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                {/* Services and Total */}
                                <div className="mb-4">
                                    {/* Main Service */}
                                    <div className="flex justify-between items-center mb-2">
                                        <div className="text-sm text-gray-900">
                                            {bookingData.selectedService?.name || 'Classic Lash Fullset(C01-C02)'}
                                        </div>
                                        <div className="font-medium text-gray-900">
                                            ${bookingData.selectedService?.price || '199.00'}
                                        </div>
                                    </div>

                                    {/* Add-ons */}
                                    {bookingData.selectedAddOns && bookingData.selectedAddOns.length > 0 && (
                                        bookingData.selectedAddOns.map((addon, index) => (
                                            <div key={index} className="flex justify-between items-center mb-2">
                                                <div className="text-sm text-gray-900">
                                                    {addon.name}
                                                </div>
                                                <div className="font-medium text-gray-900">
                                                    ${addon.price}
                                                </div>
                                            </div>
                                        ))
                                    )}

                                    {/* Total */}
                                    <div className="flex justify-between items-center text-lg font-semibold mt-4">
                                        <span>Total</span>
                                        <span>${totalCost.toFixed(2)}</span>
                                    </div>
                                </div>

                                {/* Payment Summary */}
                                <div className="pt-4 border-t border-gray-300">
                                    <div className="flex justify-between items-center mb-2">
                                        <span className="text-sm text-gray-900">Total Due Now</span>
                                        <span className="font-medium text-gray-900">$0.00</span>
                                    </div>
                                    <div className="flex justify-between items-center">
                                        <span className="text-sm text-gray-900">Total Due at Business</span>
                                        <span className="font-medium text-gray-900">${totalCost.toFixed(2)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    )}
                </div>
            </div>

            {/* Main Content - Centered with Padding */}
            <div className="max-w-6xl mx-auto">
                <div className="flex">
                    {/* Main Content */}
                    <div className="flex-1 bg-white p-4 lg:p-8">
                            <div className="business-header">
                                <h1 className="text-2xl font-bold text-gray-900 mb-6">Clément Lash</h1>
                            </div>
                    <form onSubmit={handleSubmit} className="customer-info-form">
                        <div className="review-section who-is-booking">
                            <h3>Who Are You Booking For?</h3>
                            <div className="form-field">
                                <label htmlFor="name">Full Name</label>
                                <input
                                    type="text"
                                    id="name"
                                    name="name"
                                    value={customerInfo.name}
                                    onChange={handleInputChange}
                                    className={formErrors.name ? 'error' : ''}
                                />
                                {formErrors.name && <div className="error-message">{formErrors.name}</div>}
                            </div>

                            <div className="form-field">
                                <label htmlFor="email">Email</label>
                                <input
                                    type="email"
                                    id="email"
                                    name="email"
                                    value={customerInfo.email}
                                    onChange={handleInputChange}
                                    className={formErrors.email ? 'error' : ''}
                                />
                                {formErrors.email && <div className="error-message">{formErrors.email}</div>}
                            </div>

                            <div className="form-field">
                                <label htmlFor="phone">Phone Number</label>
                                <input
                                    type="tel"
                                    id="phone"
                                    name="phone"
                                    value={customerInfo.phone}
                                    onChange={handleInputChange}
                                    className={formErrors.phone ? 'error' : ''}
                                />
                                {formErrors.phone && <div className="error-message">{formErrors.phone}</div>}
                            </div>
                        </div>

                        <div className="review-section about-appointment">
                            <h3>About your appointment</h3>
                            <textarea
                                placeholder="Do you have any special requests or ideas to share with your service provider? (optional)"
                                rows={4}
                            />
                        </div>

                        <div className="review-section card-details">
                            <h3>This Business Requires a Card to Hold</h3>
                            <p className="hold-explanation">
                                You will <strong>NOT</strong> be charged right now. A payment card is required to hold your appointment. We will issue a hold for the amount only for no-show and cancellation. We will not charge it. The hold should be released after service date.
                            </p>

                            <div className="billing-address">
                                <h4>Billing Address</h4>
                                <div className="form-field">
                                    <label htmlFor="address1">Address Line 1</label>
                                    <input
                                        type="text"
                                        id="address1"
                                        name="address1"
                                        value={customerInfo.address1}
                                        onChange={handleInputChange}
                                        className={formErrors.address1 ? 'error' : ''}
                                    />
                                    {formErrors.address1 && <div className="error-message">{formErrors.address1}</div>}
                                </div>

                                <div className="form-field">
                                    <label htmlFor="address2">Address Line 2 (optional)</label>
                                    <input
                                        type="text"
                                        id="address2"
                                        name="address2"
                                        value={customerInfo.address2}
                                        onChange={handleInputChange}
                                    />
                                </div>
                            </div>

                            <div className="credit-card">
                                <h4>Credit Card</h4>
                                <div className="form-field">
                                    <label htmlFor="creditCardName">Name on Card</label>
                                    <input
                                        type="text"
                                        id="creditCardName"
                                        name="creditCardName"
                                        value={customerInfo.creditCardName}
                                        onChange={handleInputChange}
                                        className={formErrors.creditCardName ? 'error' : ''}
                                    />
                                    {formErrors.creditCardName && <div className="error-message">{formErrors.creditCardName}</div>}
                                </div>

                                <div className="form-field">
                                    <label htmlFor="creditCardNumber">Card Number</label>
                                    <input
                                        type="text"
                                        id="creditCardNumber"
                                        name="creditCardNumber"
                                        value={customerInfo.creditCardNumber}
                                        onChange={handleInputChange}
                                        className={formErrors.creditCardNumber ? 'error' : ''}
                                    />
                                    {formErrors.creditCardNumber && <div className="error-message">{formErrors.creditCardNumber}</div>}
                                </div>

                                <div className="form-row">
                                    <div className="form-field">
                                        <label htmlFor="creditCardExpiry">Expiry (MM/YY)</label>
                                        <input
                                            type="text"
                                            id="creditCardExpiry"
                                            name="creditCardExpiry"
                                            value={customerInfo.creditCardExpiry}
                                            onChange={handleInputChange}
                                            placeholder="MM/YY"
                                            className={formErrors.creditCardExpiry ? 'error' : ''}
                                        />
                                        {formErrors.creditCardExpiry && <div className="error-message">{formErrors.creditCardExpiry}</div>}
                                    </div>

                                    <div className="form-field">
                                        <label htmlFor="creditCardCVV">Security Code</label>
                                        <input
                                            type="text"
                                            id="creditCardCVV"
                                            name="creditCardCVV"
                                            value={customerInfo.creditCardCVV}
                                            onChange={handleInputChange}
                                            className={formErrors.creditCardCVV ? 'error' : ''}
                                        />
                                        {formErrors.creditCardCVV && <div className="error-message">{formErrors.creditCardCVV}</div>}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div className="review-section cancellation-policy-section">
                            <CancellationPolicyDisplay
                                businessId={bookingData.selectedService?.business || 1}
                                selectedService={bookingData.selectedService}
                            />

                            <div className="policy-checkbox">
                                <label>
                                    <input
                                        type="checkbox"
                                        name="agree"
                                        checked={customerInfo.agree}
                                        onChange={handleInputChange}
                                    />
                                    By clicking Book, you agree to the Cancellation Policy and conditions of this business.
                                </label>
                                {formErrors.agree && <div className="error-message">{formErrors.agree}</div>}
                            </div>
                        </div>

                        {bookingError && <div className="booking-error">{bookingError}</div>}

                        <div className="form-actions">
                            <button
                                type="button"
                                className="back-btn"
                                onClick={handleBack}
                            >
                                ← Back
                            </button>
                            <button type="submit" className="next-btn" disabled={isLoading}>
                                {isLoading ? 'Processing...' : 'Book'}
                            </button>
                        </div>

                            <div className="secure-info flex items-center space-x-2 text-sm text-gray-600 mt-4">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                                    <path d="M8 1a2 2 0 0 1 2 2v4H6V3a2 2 0 0 1 2-2zm3 6V3a3 3 0 0 0-6 0v4a2 2 0 0 0-2 2v5a2 2 0 0 0 2 2h6a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2z"/>
                                </svg>
                                <span>Secured by vagaro</span>
                            </div>
                        </form>
                        </div>

                {/* Order Summary Sidebar - Desktop Only */}
                <div className="hidden lg:block w-80 bg-gray-100 p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">Order Summary</h3>

                    {/* Date and Time */}
                    <div className="mb-6">
                        <div className="flex justify-between items-start">
                            <div>
                                <div className="font-medium text-gray-900">
                                    {bookingData.selectedDate ?
                                        new Date(bookingData.selectedDate).toLocaleDateString('en-US', {
                                            weekday: 'short',
                                            month: 'short',
                                            day: 'numeric',
                                            year: 'numeric'
                                        }) : 'Date not selected'
                                    }
                                </div>
                            </div>
                            <div className="text-right">
                                <div className="font-medium text-gray-900">
                                    {bookingData.selectedTime ?
                                        new Date(`2000-01-01 ${bookingData.selectedTime}`).toLocaleTimeString('en-US', {
                                            hour: 'numeric',
                                            minute: '2-digit',
                                            hour12: true
                                        }) : 'Time not selected'
                                    }
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Stylist Info */}
                    <div className="mb-6 pb-4 border-b border-gray-300">
                        <div className="flex items-start space-x-3">
                            <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center flex-shrink-0">
                                <span className="text-sm font-medium text-gray-700">
                                    {(bookingData.selectedEmployee?.display_name || bookingData.selectedEmployee?.name || bookingData.selectedEmployee?.full_name)?.charAt(0) || 'S'}
                                </span>
                            </div>
                            <div className="flex-1">
                                <div className="text-sm text-gray-600">
                                    {bookingData.selectedEmployee?.title || bookingData.selectedEmployee?.role || 'Stylist'}
                                </div>
                                <div className="font-medium text-gray-900">
                                    {bookingData.selectedEmployee?.display_name ||
                                     bookingData.selectedEmployee?.name ||
                                     bookingData.selectedEmployee?.full_name ||
                                     'Stylist'}
                                </div>
                            </div>
                        </div>
                    </div>

                    {/* Services and Total */}
                    <div className="mb-6">
                        {/* Main Service */}
                        <div className="flex justify-between items-center mb-2">
                            <div className="text-sm text-gray-900">
                                {bookingData.selectedService?.name || 'Classic Lash Fullset(C01-C02)'}
                            </div>
                            <div className="font-medium text-gray-900">
                                ${bookingData.selectedService?.price || '199.00'}
                            </div>
                        </div>

                        {/* Add-ons */}
                        {bookingData.selectedAddOns && bookingData.selectedAddOns.length > 0 && (
                            bookingData.selectedAddOns.map((addon, index) => (
                                <div key={index} className="flex justify-between items-center mb-2">
                                    <div className="text-sm text-gray-900">
                                        {addon.name}
                                    </div>
                                    <div className="font-medium text-gray-900">
                                        ${addon.price}
                                    </div>
                                </div>
                            ))
                        )}

                        {/* Total */}
                        <div className="flex justify-between items-center text-lg font-semibold mt-4">
                            <span>Total</span>
                            <span>${totalCost.toFixed(2)}</span>
                        </div>
                    </div>

                    {/* Payment Summary */}
                    <div className="mb-6 pb-4 border-b border-gray-300 pt-4 border-t border-gray-300">
                        <div className="flex justify-between items-center mb-2">
                            <span className="text-sm text-gray-900">Total Due Now</span>
                            <span className="font-medium text-gray-900">$0.00</span>
                        </div>
                        <div className="flex justify-between items-center">
                            <span className="text-sm text-gray-900">Total Due at Business</span>
                            <span className="font-medium text-gray-900">${totalCost.toFixed(2)}</span>
                        </div>
                    </div>
                </div>
                </div>
            </div>
        </div>
    );
};

export default ReviewBookingPage;
