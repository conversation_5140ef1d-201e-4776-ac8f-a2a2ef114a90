const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/index.es-DHhENybw.js","assets/index-L2zCFnCv.js","assets/index-DJe76gaH.css"])))=>i.map(i=>d[i]);
import{_ as gs}from"./index-L2zCFnCv.js";function fe(n){"@babel/helpers - typeof";return fe=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(e){return typeof e}:function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},fe(n)}var tr=Uint8Array,mr=Uint16<PERSON>rray,Ps=Int32Array,go=new tr([0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,0,0,0]),mo=new tr([0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13,0,0]),ms=new tr([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Au=function(n,e){for(var r=new mr(31),a=0;a<31;++a)r[a]=e+=1<<n[a-1];for(var u=new Ps(r[30]),a=1;a<30;++a)for(var o=r[a];o<r[a+1];++o)u[o]=o-r[a]<<5|a;return{b:r,r:u}},xu=Au(go,2),Su=xu.b,vs=xu.r;Su[28]=258,vs[258]=28;var _u=Au(mo,0),Pl=_u.b,nu=_u.r,bs=new mr(32768);for(var we=0;we<32768;++we){var kn=(we&43690)>>1|(we&21845)<<1;kn=(kn&52428)>>2|(kn&13107)<<2,kn=(kn&61680)>>4|(kn&3855)<<4,bs[we]=((kn&65280)>>8|(kn&255)<<8)>>1}var Qr=function(n,e,r){for(var a=n.length,u=0,o=new mr(e);u<a;++u)n[u]&&++o[n[u]-1];var c=new mr(e);for(u=1;u<e;++u)c[u]=c[u-1]+o[u-1]<<1;var h;if(r){h=new mr(1<<e);var f=15-e;for(u=0;u<a;++u)if(n[u])for(var g=u<<4|n[u],y=e-n[u],w=c[n[u]-1]++<<y,S=w|(1<<y)-1;w<=S;++w)h[bs[w]>>f]=g}else for(h=new mr(a),u=0;u<a;++u)n[u]&&(h[u]=bs[c[n[u]-1]++]>>15-n[u]);return h},On=new tr(288);for(var we=0;we<144;++we)On[we]=8;for(var we=144;we<256;++we)On[we]=9;for(var we=256;we<280;++we)On[we]=7;for(var we=280;we<288;++we)On[we]=8;var va=new tr(32);for(var we=0;we<32;++we)va[we]=5;var kl=Qr(On,9,0),Fl=Qr(On,9,1),Il=Qr(va,5,0),Cl=Qr(va,5,1),os=function(n){for(var e=n[0],r=1;r<n.length;++r)n[r]>e&&(e=n[r]);return e},Er=function(n,e,r){var a=e/8|0;return(n[a]|n[a+1]<<8)>>(e&7)&r},ss=function(n,e){var r=e/8|0;return(n[r]|n[r+1]<<8|n[r+2]<<16)>>(e&7)},ks=function(n){return(n+7)/8|0},Pu=function(n,e,r){return(r==null||r>n.length)&&(r=n.length),new tr(n.subarray(e,r))},jl=["unexpected EOF","invalid block type","invalid length/literal","invalid distance","stream finished","no stream handler",,"no callback","invalid UTF-8 data","extra field too long","date not in range 1980-2099","filename too long","stream finishing","invalid zip data"],Dr=function(n,e,r){var a=new Error(e||jl[n]);if(a.code=n,Error.captureStackTrace&&Error.captureStackTrace(a,Dr),!r)throw a;return a},Ol=function(n,e,r,a){var u=n.length,o=0;if(!u||e.f&&!e.l)return r||new tr(0);var c=!r,h=c||e.i!=2,f=e.i;c&&(r=new tr(u*3));var g=function(Nt){var Ft=r.length;if(Nt>Ft){var _t=new tr(Math.max(Ft*2,Nt));_t.set(r),r=_t}},y=e.f||0,w=e.p||0,S=e.b||0,p=e.l,O=e.d,F=e.m,q=e.n,_=u*8;do{if(!p){y=Er(n,w,1);var B=Er(n,w+1,3);if(w+=3,B)if(B==1)p=Fl,O=Cl,F=9,q=5;else if(B==2){var wt=Er(n,w,31)+257,tt=Er(n,w+10,15)+4,z=wt+Er(n,w+5,31)+1;w+=14;for(var nt=new tr(z),pt=new tr(19),P=0;P<tt;++P)pt[ms[P]]=Er(n,w+P*3,7);w+=tt*3;for(var k=os(pt),W=(1<<k)-1,D=Qr(pt,k,1),P=0;P<z;){var st=D[Er(n,w,W)];w+=st&15;var Y=st>>4;if(Y<16)nt[P++]=Y;else{var it=0,ht=0;for(Y==16?(ht=3+Er(n,w,3),w+=2,it=nt[P-1]):Y==17?(ht=3+Er(n,w,7),w+=3):Y==18&&(ht=11+Er(n,w,127),w+=7);ht--;)nt[P++]=it}}var $=nt.subarray(0,wt),lt=nt.subarray(wt);F=os($),q=os(lt),p=Qr($,F,1),O=Qr(lt,q,1)}else Dr(1);else{var Y=ks(w)+4,ot=n[Y-4]|n[Y-3]<<8,ct=Y+ot;if(ct>u){f&&Dr(0);break}h&&g(S+ot),r.set(n.subarray(Y,ct),S),e.b=S+=ot,e.p=w=ct*8,e.f=y;continue}if(w>_){f&&Dr(0);break}}h&&g(S+131072);for(var dt=(1<<F)-1,jt=(1<<q)-1,N=w;;N=w){var it=p[ss(n,w)&dt],C=it>>4;if(w+=it&15,w>_){f&&Dr(0);break}if(it||Dr(2),C<256)r[S++]=C;else if(C==256){N=w,p=null;break}else{var M=C-254;if(C>264){var P=C-257,T=go[P];M=Er(n,w,(1<<T)-1)+Su[P],w+=T}var J=O[ss(n,w)&jt],Q=J>>4;J||Dr(3),w+=J&15;var lt=Pl[Q];if(Q>3){var T=mo[Q];lt+=ss(n,w)&(1<<T)-1,w+=T}if(w>_){f&&Dr(0);break}h&&g(S+131072);var et=S+M;if(S<lt){var rt=o-lt,At=Math.min(lt,et);for(rt+S<0&&Dr(3);S<At;++S)r[S]=a[rt+S]}for(;S<et;++S)r[S]=r[S-lt]}}e.l=p,e.p=N,e.b=S,e.f=y,p&&(y=1,e.m=F,e.d=O,e.n=q)}while(!y);return S!=r.length&&c?Pu(r,0,S):r.subarray(0,S)},dn=function(n,e,r){r<<=e&7;var a=e/8|0;n[a]|=r,n[a+1]|=r>>8},ca=function(n,e,r){r<<=e&7;var a=e/8|0;n[a]|=r,n[a+1]|=r>>8,n[a+2]|=r>>16},us=function(n,e){for(var r=[],a=0;a<n.length;++a)n[a]&&r.push({s:a,f:n[a]});var u=r.length,o=r.slice();if(!u)return{t:Fu,l:0};if(u==1){var c=new tr(r[0].s+1);return c[r[0].s]=1,{t:c,l:1}}r.sort(function(ct,wt){return ct.f-wt.f}),r.push({s:-1,f:25001});var h=r[0],f=r[1],g=0,y=1,w=2;for(r[0]={s:-1,f:h.f+f.f,l:h,r:f};y!=u-1;)h=r[r[g].f<r[w].f?g++:w++],f=r[g!=y&&r[g].f<r[w].f?g++:w++],r[y++]={s:-1,f:h.f+f.f,l:h,r:f};for(var S=o[0].s,a=1;a<u;++a)o[a].s>S&&(S=o[a].s);var p=new mr(S+1),O=ys(r[y-1],p,0);if(O>e){var a=0,F=0,q=O-e,_=1<<q;for(o.sort(function(wt,tt){return p[tt.s]-p[wt.s]||wt.f-tt.f});a<u;++a){var B=o[a].s;if(p[B]>e)F+=_-(1<<O-p[B]),p[B]=e;else break}for(F>>=q;F>0;){var Y=o[a].s;p[Y]<e?F-=1<<e-p[Y]++-1:++a}for(;a>=0&&F;--a){var ot=o[a].s;p[ot]==e&&(--p[ot],++F)}O=e}return{t:new tr(p),l:O}},ys=function(n,e,r){return n.s==-1?Math.max(ys(n.l,e,r+1),ys(n.r,e,r+1)):e[n.s]=r},iu=function(n){for(var e=n.length;e&&!n[--e];);for(var r=new mr(++e),a=0,u=n[0],o=1,c=function(f){r[a++]=f},h=1;h<=e;++h)if(n[h]==u&&h!=e)++o;else{if(!u&&o>2){for(;o>138;o-=138)c(32754);o>2&&(c(o>10?o-11<<5|28690:o-3<<5|12305),o=0)}else if(o>3){for(c(u),--o;o>6;o-=6)c(8304);o>2&&(c(o-3<<5|8208),o=0)}for(;o--;)c(u);o=1,u=n[h]}return{c:r.subarray(0,a),n:e}},ha=function(n,e){for(var r=0,a=0;a<e.length;++a)r+=n[a]*e[a];return r},ku=function(n,e,r){var a=r.length,u=ks(e+2);n[u]=a&255,n[u+1]=a>>8,n[u+2]=n[u]^255,n[u+3]=n[u+1]^255;for(var o=0;o<a;++o)n[u+o+4]=r[o];return(u+4+a)*8},au=function(n,e,r,a,u,o,c,h,f,g,y){dn(e,y++,r),++u[256];for(var w=us(u,15),S=w.t,p=w.l,O=us(o,15),F=O.t,q=O.l,_=iu(S),B=_.c,Y=_.n,ot=iu(F),ct=ot.c,wt=ot.n,tt=new mr(19),z=0;z<B.length;++z)++tt[B[z]&31];for(var z=0;z<ct.length;++z)++tt[ct[z]&31];for(var nt=us(tt,7),pt=nt.t,P=nt.l,k=19;k>4&&!pt[ms[k-1]];--k);var W=g+5<<3,D=ha(u,On)+ha(o,va)+c,st=ha(u,S)+ha(o,F)+c+14+3*k+ha(tt,pt)+2*tt[16]+3*tt[17]+7*tt[18];if(f>=0&&W<=D&&W<=st)return ku(e,y,n.subarray(f,f+g));var it,ht,$,lt;if(dn(e,y,1+(st<D)),y+=2,st<D){it=Qr(S,p,0),ht=S,$=Qr(F,q,0),lt=F;var dt=Qr(pt,P,0);dn(e,y,Y-257),dn(e,y+5,wt-1),dn(e,y+10,k-4),y+=14;for(var z=0;z<k;++z)dn(e,y+3*z,pt[ms[z]]);y+=3*k;for(var jt=[B,ct],N=0;N<2;++N)for(var C=jt[N],z=0;z<C.length;++z){var M=C[z]&31;dn(e,y,dt[M]),y+=pt[M],M>15&&(dn(e,y,C[z]>>5&127),y+=C[z]>>12)}}else it=kl,ht=On,$=Il,lt=va;for(var z=0;z<h;++z){var T=a[z];if(T>255){var M=T>>18&31;ca(e,y,it[M+257]),y+=ht[M+257],M>7&&(dn(e,y,T>>23&31),y+=go[M]);var J=T&31;ca(e,y,$[J]),y+=lt[J],J>3&&(ca(e,y,T>>5&8191),y+=mo[J])}else ca(e,y,it[T]),y+=ht[T]}return ca(e,y,it[256]),y+ht[256]},Bl=new Ps([65540,131080,131088,131104,262176,1048704,1048832,2114560,2117632]),Fu=new tr(0),Ml=function(n,e,r,a,u,o){var c=o.z||n.length,h=new tr(a+c+5*(1+Math.ceil(c/7e3))+u),f=h.subarray(a,h.length-u),g=o.l,y=(o.r||0)&7;if(e){y&&(f[0]=o.r>>3);for(var w=Bl[e-1],S=w>>13,p=w&8191,O=(1<<r)-1,F=o.p||new mr(32768),q=o.h||new mr(O+1),_=Math.ceil(r/3),B=2*_,Y=function(Ut){return(n[Ut]^n[Ut+1]<<_^n[Ut+2]<<B)&O},ot=new Ps(25e3),ct=new mr(288),wt=new mr(32),tt=0,z=0,nt=o.i||0,pt=0,P=o.w||0,k=0;nt+2<c;++nt){var W=Y(nt),D=nt&32767,st=q[W];if(F[D]=st,q[W]=D,P<=nt){var it=c-nt;if((tt>7e3||pt>24576)&&(it>423||!g)){y=au(n,f,0,ot,ct,wt,z,pt,k,nt-k,y),pt=tt=z=0,k=nt;for(var ht=0;ht<286;++ht)ct[ht]=0;for(var ht=0;ht<30;++ht)wt[ht]=0}var $=2,lt=0,dt=p,jt=D-st&32767;if(it>2&&W==Y(nt-jt))for(var N=Math.min(S,it)-1,C=Math.min(32767,nt),M=Math.min(258,it);jt<=C&&--dt&&D!=st;){if(n[nt+$]==n[nt+$-jt]){for(var T=0;T<M&&n[nt+T]==n[nt+T-jt];++T);if(T>$){if($=T,lt=jt,T>N)break;for(var J=Math.min(jt,T-2),Q=0,ht=0;ht<J;++ht){var et=nt-jt+ht&32767,rt=F[et],At=et-rt&32767;At>Q&&(Q=At,st=et)}}}D=st,st=F[D],jt+=D-st&32767}if(lt){ot[pt++]=268435456|vs[$]<<18|nu[lt];var Nt=vs[$]&31,Ft=nu[lt]&31;z+=go[Nt]+mo[Ft],++ct[257+Nt],++wt[Ft],P=nt+$,++tt}else ot[pt++]=n[nt],++ct[n[nt]]}}for(nt=Math.max(nt,P);nt<c;++nt)ot[pt++]=n[nt],++ct[n[nt]];y=au(n,f,g,ot,ct,wt,z,pt,k,nt-k,y),g||(o.r=y&7|f[y/8|0]<<3,y-=7,o.h=q,o.p=F,o.i=nt,o.w=P)}else{for(var nt=o.w||0;nt<c+g;nt+=65535){var _t=nt+65535;_t>=c&&(f[y/8|0]=g,_t=c),y=ku(f,y+1,n.subarray(nt,_t))}o.i=c}return Pu(h,0,a+ks(y)+u)},Iu=function(){var n=1,e=0;return{p:function(r){for(var a=n,u=e,o=r.length|0,c=0;c!=o;){for(var h=Math.min(c+2655,o);c<h;++c)u+=a+=r[c];a=(a&65535)+15*(a>>16),u=(u&65535)+15*(u>>16)}n=a,e=u},d:function(){return n%=65521,e%=65521,(n&255)<<24|(n&65280)<<8|(e&255)<<8|e>>8}}},El=function(n,e,r,a,u){if(!u&&(u={l:1},e.dictionary)){var o=e.dictionary.subarray(-32768),c=new tr(o.length+n.length);c.set(o),c.set(n,o.length),n=c,u.w=o.length}return Ml(n,e.level==null?6:e.level,e.mem==null?u.l?Math.ceil(Math.max(8,Math.min(13,Math.log(n.length)))*1.5):20:12+e.mem,r,a,u)},Cu=function(n,e,r){for(;r;++e)n[e]=r,r>>>=8},ql=function(n,e){var r=e.level,a=r==0?0:r<6?1:r==9?3:2;if(n[0]=120,n[1]=a<<6|(e.dictionary&&32),n[1]|=31-(n[0]<<8|n[1])%31,e.dictionary){var u=Iu();u.p(e.dictionary),Cu(n,2,u.d())}},Dl=function(n,e){return((n[0]&15)!=8||n[0]>>4>7||(n[0]<<8|n[1])%31)&&Dr(6,"invalid zlib data"),(n[1]>>5&1)==1&&Dr(6,"invalid zlib data: "+(n[1]&32?"need":"unexpected")+" dictionary"),(n[1]>>3&4)+2};function ws(n,e){e||(e={});var r=Iu();r.p(n);var a=El(n,e,e.dictionary?6:2,4);return ql(a,e),Cu(a,a.length-4,r.d()),a}function Rl(n,e){return Ol(n.subarray(Dl(n),-4),{i:2},e,e)}var Tl=typeof TextDecoder<"u"&&new TextDecoder,zl=0;try{Tl.decode(Fu,{stream:!0}),zl=1}catch{}var Ht=function(){return typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:this}();function ls(){Ht.console&&typeof Ht.console.log=="function"&&Ht.console.log.apply(Ht.console,arguments)}var ve={log:ls,warn:function(n){Ht.console&&(typeof Ht.console.warn=="function"?Ht.console.warn.apply(Ht.console,arguments):ls.call(null,arguments))},error:function(n){Ht.console&&(typeof Ht.console.error=="function"?Ht.console.error.apply(Ht.console,arguments):ls(n))}};function cs(n,e,r){var a=new XMLHttpRequest;a.open("GET",n),a.responseType="blob",a.onload=function(){Yn(a.response,e,r)},a.onerror=function(){ve.error("could not download file")},a.send()}function ou(n){var e=new XMLHttpRequest;e.open("HEAD",n,!1);try{e.send()}catch{}return e.status>=200&&e.status<=299}function lo(n){try{n.dispatchEvent(new MouseEvent("click"))}catch{var e=document.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),n.dispatchEvent(e)}}var fa,Ls,Yn=Ht.saveAs||((typeof window>"u"?"undefined":fe(window))!=="object"||window!==Ht?function(){}:typeof HTMLAnchorElement<"u"&&"download"in HTMLAnchorElement.prototype?function(n,e,r){var a=Ht.URL||Ht.webkitURL,u=document.createElement("a");e=e||n.name||"download",u.download=e,u.rel="noopener",typeof n=="string"?(u.href=n,u.origin!==location.origin?ou(u.href)?cs(n,e,r):lo(u,u.target="_blank"):lo(u)):(u.href=a.createObjectURL(n),setTimeout(function(){a.revokeObjectURL(u.href)},4e4),setTimeout(function(){lo(u)},0))}:"msSaveOrOpenBlob"in navigator?function(n,e,r){if(e=e||n.name||"download",typeof n=="string")if(ou(n))cs(n,e,r);else{var a=document.createElement("a");a.href=n,a.target="_blank",setTimeout(function(){lo(a)})}else navigator.msSaveOrOpenBlob(function(u,o){return o===void 0?o={autoBom:!1}:fe(o)!=="object"&&(ve.warn("Deprecated: Expected third argument to be a object"),o={autoBom:!o}),o.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(u.type)?new Blob(["\uFEFF",u],{type:u.type}):u}(n,r),e)}:function(n,e,r,a){if((a=a||open("","_blank"))&&(a.document.title=a.document.body.innerText="downloading..."),typeof n=="string")return cs(n,e,r);var u=n.type==="application/octet-stream",o=/constructor/i.test(Ht.HTMLElement)||Ht.safari,c=/CriOS\/[\d]+/.test(navigator.userAgent);if((c||u&&o)&&(typeof FileReader>"u"?"undefined":fe(FileReader))==="object"){var h=new FileReader;h.onloadend=function(){var y=h.result;y=c?y:y.replace(/^data:[^;]*;/,"data:attachment/file;"),a?a.location.href=y:location=y,a=null},h.readAsDataURL(n)}else{var f=Ht.URL||Ht.webkitURL,g=f.createObjectURL(n);a?a.location=g:location.href=g,a=null,setTimeout(function(){f.revokeObjectURL(g)},4e4)}});/**
 * A class to parse color values
 * <AUTHOR> Stefanov <<EMAIL>>
 * {@link   http://www.phpied.com/rgb-color-parser-in-javascript/}
 * @license Use it if you like it
 */function ju(n){var e;n=n||"",this.ok=!1,n.charAt(0)=="#"&&(n=n.substr(1,6)),n={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"}[n=(n=n.replace(/ /g,"")).toLowerCase()]||n;for(var r=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(h){return[parseInt(h[1]),parseInt(h[2]),parseInt(h[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(h){return[parseInt(h[1],16),parseInt(h[2],16),parseInt(h[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(h){return[parseInt(h[1]+h[1],16),parseInt(h[2]+h[2],16),parseInt(h[3]+h[3],16)]}}],a=0;a<r.length;a++){var u=r[a].re,o=r[a].process,c=u.exec(n);c&&(e=o(c),this.r=e[0],this.g=e[1],this.b=e[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var h=this.r.toString(16),f=this.g.toString(16),g=this.b.toString(16);return h.length==1&&(h="0"+h),f.length==1&&(f="0"+f),g.length==1&&(g="0"+g),"#"+h+f+g}}/**
 * @license
 * Joseph Myers does not specify a particular license for his work.
 *
 * Author: Joseph Myers
 * Accessed from: http://www.myersdaily.org/joseph/javascript/md5.js
 *
 * Modified by: Owen Leong
 */function hs(n,e){var r=n[0],a=n[1],u=n[2],o=n[3];r=Ke(r,a,u,o,e[0],7,-680876936),o=Ke(o,r,a,u,e[1],12,-389564586),u=Ke(u,o,r,a,e[2],17,606105819),a=Ke(a,u,o,r,e[3],22,-**********),r=Ke(r,a,u,o,e[4],7,-176418897),o=Ke(o,r,a,u,e[5],12,**********),u=Ke(u,o,r,a,e[6],17,-**********),a=Ke(a,u,o,r,e[7],22,-45705983),r=Ke(r,a,u,o,e[8],7,**********),o=Ke(o,r,a,u,e[9],12,-**********),u=Ke(u,o,r,a,e[10],17,-42063),a=Ke(a,u,o,r,e[11],22,-**********),r=Ke(r,a,u,o,e[12],7,**********),o=Ke(o,r,a,u,e[13],12,-40341101),u=Ke(u,o,r,a,e[14],17,-**********),r=Ze(r,a=Ke(a,u,o,r,e[15],22,**********),u,o,e[1],5,-165796510),o=Ze(o,r,a,u,e[6],9,-**********),u=Ze(u,o,r,a,e[11],14,643717713),a=Ze(a,u,o,r,e[0],20,-373897302),r=Ze(r,a,u,o,e[5],5,-701558691),o=Ze(o,r,a,u,e[10],9,38016083),u=Ze(u,o,r,a,e[15],14,-660478335),a=Ze(a,u,o,r,e[4],20,-405537848),r=Ze(r,a,u,o,e[9],5,568446438),o=Ze(o,r,a,u,e[14],9,-1019803690),u=Ze(u,o,r,a,e[3],14,-187363961),a=Ze(a,u,o,r,e[8],20,1163531501),r=Ze(r,a,u,o,e[13],5,-1444681467),o=Ze(o,r,a,u,e[2],9,-51403784),u=Ze(u,o,r,a,e[7],14,1735328473),r=$e(r,a=Ze(a,u,o,r,e[12],20,-1926607734),u,o,e[5],4,-378558),o=$e(o,r,a,u,e[8],11,-2022574463),u=$e(u,o,r,a,e[11],16,1839030562),a=$e(a,u,o,r,e[14],23,-35309556),r=$e(r,a,u,o,e[1],4,-1530992060),o=$e(o,r,a,u,e[4],11,1272893353),u=$e(u,o,r,a,e[7],16,-155497632),a=$e(a,u,o,r,e[10],23,-1094730640),r=$e(r,a,u,o,e[13],4,681279174),o=$e(o,r,a,u,e[0],11,-358537222),u=$e(u,o,r,a,e[3],16,-722521979),a=$e(a,u,o,r,e[6],23,76029189),r=$e(r,a,u,o,e[9],4,-640364487),o=$e(o,r,a,u,e[12],11,-421815835),u=$e(u,o,r,a,e[15],16,530742520),r=Qe(r,a=$e(a,u,o,r,e[2],23,-995338651),u,o,e[0],6,-198630844),o=Qe(o,r,a,u,e[7],10,1126891415),u=Qe(u,o,r,a,e[14],15,-1416354905),a=Qe(a,u,o,r,e[5],21,-57434055),r=Qe(r,a,u,o,e[12],6,1700485571),o=Qe(o,r,a,u,e[3],10,-1894986606),u=Qe(u,o,r,a,e[10],15,-1051523),a=Qe(a,u,o,r,e[1],21,-2054922799),r=Qe(r,a,u,o,e[8],6,1873313359),o=Qe(o,r,a,u,e[15],10,-30611744),u=Qe(u,o,r,a,e[6],15,-1560198380),a=Qe(a,u,o,r,e[13],21,1309151649),r=Qe(r,a,u,o,e[4],6,-145523070),o=Qe(o,r,a,u,e[11],10,-1120210379),u=Qe(u,o,r,a,e[2],15,718787259),a=Qe(a,u,o,r,e[9],21,-343485551),n[0]=Cn(r,n[0]),n[1]=Cn(a,n[1]),n[2]=Cn(u,n[2]),n[3]=Cn(o,n[3])}function vo(n,e,r,a,u,o){return e=Cn(Cn(e,n),Cn(a,o)),Cn(e<<u|e>>>32-u,r)}function Ke(n,e,r,a,u,o,c){return vo(e&r|~e&a,n,e,u,o,c)}function Ze(n,e,r,a,u,o,c){return vo(e&a|r&~a,n,e,u,o,c)}function $e(n,e,r,a,u,o,c){return vo(e^r^a,n,e,u,o,c)}function Qe(n,e,r,a,u,o,c){return vo(r^(e|~a),n,e,u,o,c)}function Ou(n){var e,r=n.length,a=[1732584193,-271733879,-1732584194,271733878];for(e=64;e<=n.length;e+=64)hs(a,Ul(n.substring(e-64,e)));n=n.substring(e-64);var u=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;e<n.length;e++)u[e>>2]|=n.charCodeAt(e)<<(e%4<<3);if(u[e>>2]|=128<<(e%4<<3),e>55)for(hs(a,u),e=0;e<16;e++)u[e]=0;return u[14]=8*r,hs(a,u),a}function Ul(n){var e,r=[];for(e=0;e<64;e+=4)r[e>>2]=n.charCodeAt(e)+(n.charCodeAt(e+1)<<8)+(n.charCodeAt(e+2)<<16)+(n.charCodeAt(e+3)<<24);return r}fa=Ht.atob.bind(Ht),Ls=Ht.btoa.bind(Ht);var su="0123456789abcdef".split("");function Hl(n){for(var e="",r=0;r<4;r++)e+=su[n>>8*r+4&15]+su[n>>8*r&15];return e}function Wl(n){return String.fromCharCode((255&n)>>0,(65280&n)>>8,(16711680&n)>>16,(**********&n)>>24)}function Ns(n){return Ou(n).map(Wl).join("")}var Vl=function(n){for(var e=0;e<n.length;e++)n[e]=Hl(n[e]);return n.join("")}(Ou("hello"))!="5d41402abc4b2a76b9719d911017c592";function Cn(n,e){if(Vl){var r=(65535&n)+(65535&e);return(n>>16)+(e>>16)+(r>>16)<<16|65535&r}return n+e&**********}/**
 * @license
 * FPDF is released under a permissive license: there is no usage restriction.
 * You may embed it freely in your application (commercial or not), with or
 * without modifications.
 *
 * Reference: http://www.fpdf.org/en/script/script37.php
 */function As(n,e){var r,a,u,o;if(n!==r){for(var c=(u=n,o=1+(256/n.length>>0),new Array(o+1).join(u)),h=[],f=0;f<256;f++)h[f]=f;var g=0;for(f=0;f<256;f++){var y=h[f];g=(g+y+c.charCodeAt(f))%256,h[f]=h[g],h[g]=y}r=n,a=h}else h=a;var w=e.length,S=0,p=0,O="";for(f=0;f<w;f++)p=(p+(y=h[S=(S+1)%256]))%256,h[S]=h[p],h[p]=y,c=h[(h[S]+h[p])%256],O+=String.fromCharCode(e.charCodeAt(f)^c);return O}/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 * Author: Owen Leong (@owenl131)
 * Date: 15 Oct 2020
 * References:
 * https://www.cs.cmu.edu/~dst/Adobe/Gallery/anon21jul01-pdf-encryption.txt
 * https://github.com/foliojs/pdfkit/blob/master/lib/security.js
 * http://www.fpdf.org/en/script/script37.php
 */var uu={print:4,modify:8,copy:16,"annot-forms":32};function ji(n,e,r,a){this.v=1,this.r=2;var u=192;n.forEach(function(h){if(uu.perm!==void 0)throw new Error("Invalid permission: "+h);u+=uu[h]}),this.padding="(¿N^NuAd\0NVÿú\b..\0¶Ðh>/\f©þdSiz";var o=(e+this.padding).substr(0,32),c=(r+this.padding).substr(0,32);this.O=this.processOwnerPassword(o,c),this.P=-(1+(255^u)),this.encryptionKey=Ns(o+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(a)).substr(0,5),this.U=As(this.encryptionKey,this.padding)}function Oi(n){if(/[^\u0000-\u00ff]/.test(n))throw new Error("Invalid PDF Name Object: "+n+", Only accept ASCII characters.");for(var e="",r=n.length,a=0;a<r;a++){var u=n.charCodeAt(a);u<33||u===35||u===37||u===40||u===41||u===47||u===60||u===62||u===91||u===93||u===123||u===125||u>126?e+="#"+("0"+u.toString(16)).slice(-2):e+=n[a]}return e}function lu(n){if(fe(n)!=="object")throw new Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var e={};this.subscribe=function(r,a,u){if(u=u||!1,typeof r!="string"||typeof a!="function"||typeof u!="boolean")throw new Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");e.hasOwnProperty(r)||(e[r]={});var o=Math.random().toString(35);return e[r][o]=[a,!!u],o},this.unsubscribe=function(r){for(var a in e)if(e[a][r])return delete e[a][r],Object.keys(e[a]).length===0&&delete e[a],!0;return!1},this.publish=function(r){if(e.hasOwnProperty(r)){var a=Array.prototype.slice.call(arguments,1),u=[];for(var o in e[r]){var c=e[r][o];try{c[0].apply(n,a)}catch(h){Ht.console&&ve.error("jsPDF PubSub Error",h.message,h)}c[1]&&u.push(o)}u.length&&u.forEach(this.unsubscribe)}},this.getTopics=function(){return e}}function ba(n){if(!(this instanceof ba))return new ba(n);var e="opacity,stroke-opacity".split(",");for(var r in n)n.hasOwnProperty(r)&&e.indexOf(r)>=0&&(this[r]=n[r]);this.id="",this.objectNumber=-1}function Bu(n,e){this.gState=n,this.matrix=e,this.id="",this.objectNumber=-1}function In(n,e,r,a,u){if(!(this instanceof In))return new In(n,e,r,a,u);this.type=n==="axial"?2:3,this.coords=e,this.colors=r,Bu.call(this,a,u)}function Xn(n,e,r,a,u){if(!(this instanceof Xn))return new Xn(n,e,r,a,u);this.boundingBox=n,this.xStep=e,this.yStep=r,this.stream="",this.cloneIndex=0,Bu.call(this,a,u)}function Tt(n){var e,r=typeof arguments[0]=="string"?arguments[0]:"p",a=arguments[1],u=arguments[2],o=arguments[3],c=[],h=1,f=16,g="S",y=null;fe(n=n||{})==="object"&&(r=n.orientation,a=n.unit||a,u=n.format||u,o=n.compress||n.compressPdf||o,(y=n.encryption||null)!==null&&(y.userPassword=y.userPassword||"",y.ownerPassword=y.ownerPassword||"",y.userPermissions=y.userPermissions||[]),h=typeof n.userUnit=="number"?Math.abs(n.userUnit):1,n.precision!==void 0&&(e=n.precision),n.floatPrecision!==void 0&&(f=n.floatPrecision),g=n.defaultPathOperation||"S"),c=n.filters||(o===!0?["FlateEncode"]:c),a=a||"mm",r=(""+(r||"P")).toLowerCase();var w=n.putOnlyUsedFonts||!1,S={},p={internal:{},__private__:{}};p.__private__.PubSub=lu;var O="1.3",F=p.__private__.getPdfVersion=function(){return O};p.__private__.setPdfVersion=function(s){O=s};var q={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};p.__private__.getPageFormats=function(){return q};var _=p.__private__.getPageFormat=function(s){return q[s]};u=u||"a4";var B={COMPAT:"compat",ADVANCED:"advanced"},Y=B.COMPAT;function ot(){this.saveGraphicsState(),E(new zt(Ct,0,0,-Ct,0,mn()*Ct).toString()+" cm"),this.setFontSize(this.getFontSize()/Ct),g="n",Y=B.ADVANCED}function ct(){this.restoreGraphicsState(),g="S",Y=B.COMPAT}var wt=p.__private__.combineFontStyleAndFontWeight=function(s,v){if(s=="bold"&&v=="normal"||s=="bold"&&v==400||s=="normal"&&v=="italic"||s=="bold"&&v=="italic")throw new Error("Invalid Combination of fontweight and fontstyle");return v&&(s=v==400||v==="normal"?s==="italic"?"italic":"normal":v!=700&&v!=="bold"||s!=="normal"?(v==700?"bold":v)+""+s:"bold"),s};p.advancedAPI=function(s){var v=Y===B.COMPAT;return v&&ot.call(this),typeof s!="function"||(s(this),v&&ct.call(this)),this},p.compatAPI=function(s){var v=Y===B.ADVANCED;return v&&ct.call(this),typeof s!="function"||(s(this),v&&ot.call(this)),this},p.isAdvancedAPI=function(){return Y===B.ADVANCED};var tt,z=function(s){if(Y!==B.ADVANCED)throw new Error(s+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},nt=p.roundToPrecision=p.__private__.roundToPrecision=function(s,v){var j=e||v;if(isNaN(s)||isNaN(j))throw new Error("Invalid argument passed to jsPDF.roundToPrecision");return s.toFixed(j).replace(/0+$/,"")};tt=p.hpf=p.__private__.hpf=typeof f=="number"?function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.hpf");return nt(s,f)}:f==="smart"?function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.hpf");return nt(s,s>-1&&s<1?16:5)}:function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.hpf");return nt(s,16)};var pt=p.f2=p.__private__.f2=function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.f2");return nt(s,2)},P=p.__private__.f3=function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.f3");return nt(s,3)},k=p.scale=p.__private__.scale=function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.scale");return Y===B.COMPAT?s*Ct:Y===B.ADVANCED?s:void 0},W=function(s){return Y===B.COMPAT?mn()-s:Y===B.ADVANCED?s:void 0},D=function(s){return k(W(s))};p.__private__.setPrecision=p.setPrecision=function(s){typeof parseInt(s,10)=="number"&&(e=parseInt(s,10))};var st,it="00000000000000000000000000000000",ht=p.__private__.getFileId=function(){return it},$=p.__private__.setFileId=function(s){return it=s!==void 0&&/^[a-fA-F0-9]{32}$/.test(s)?s.toUpperCase():it.split("").map(function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))}).join(""),y!==null&&(Ye=new ji(y.userPermissions,y.userPassword,y.ownerPassword,it)),it};p.setFileId=function(s){return $(s),this},p.getFileId=function(){return ht()};var lt=p.__private__.convertDateToPDFDate=function(s){var v=s.getTimezoneOffset(),j=v<0?"+":"-",R=Math.floor(Math.abs(v/60)),X=Math.abs(v%60),ut=[j,M(R),"'",M(X),"'"].join("");return["D:",s.getFullYear(),M(s.getMonth()+1),M(s.getDate()),M(s.getHours()),M(s.getMinutes()),M(s.getSeconds()),ut].join("")},dt=p.__private__.convertPDFDateToDate=function(s){var v=parseInt(s.substr(2,4),10),j=parseInt(s.substr(6,2),10)-1,R=parseInt(s.substr(8,2),10),X=parseInt(s.substr(10,2),10),ut=parseInt(s.substr(12,2),10),yt=parseInt(s.substr(14,2),10);return new Date(v,j,R,X,ut,yt,0)},jt=p.__private__.setCreationDate=function(s){var v;if(s===void 0&&(s=new Date),s instanceof Date)v=lt(s);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(s))throw new Error("Invalid argument passed to jsPDF.setCreationDate");v=s}return st=v},N=p.__private__.getCreationDate=function(s){var v=st;return s==="jsDate"&&(v=dt(st)),v};p.setCreationDate=function(s){return jt(s),this},p.getCreationDate=function(s){return N(s)};var C,M=p.__private__.padd2=function(s){return("0"+parseInt(s)).slice(-2)},T=p.__private__.padd2Hex=function(s){return("00"+(s=s.toString())).substr(s.length)},J=0,Q=[],et=[],rt=0,At=[],Nt=[],Ft=!1,_t=et,Ut=function(){J=0,rt=0,et=[],Q=[],At=[],rn=Be(),kr=Be()};p.__private__.setCustomOutputDestination=function(s){Ft=!0,_t=s};var ft=function(s){Ft||(_t=s)};p.__private__.resetCustomOutputDestination=function(){Ft=!1,_t=et};var E=p.__private__.out=function(s){return s=s.toString(),rt+=s.length+1,_t.push(s),_t},Kt=p.__private__.write=function(s){return E(arguments.length===1?s.toString():Array.prototype.join.call(arguments," "))},Et=p.__private__.getArrayBuffer=function(s){for(var v=s.length,j=new ArrayBuffer(v),R=new Uint8Array(j);v--;)R[v]=s.charCodeAt(v);return j},Lt=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];p.__private__.getStandardFonts=function(){return Lt};var xt=n.fontSize||16;p.__private__.setFontSize=p.setFontSize=function(s){return xt=Y===B.ADVANCED?s/Ct:s,this};var It,kt=p.__private__.getFontSize=p.getFontSize=function(){return Y===B.COMPAT?xt:xt*Ct},qt=n.R2L||!1;p.__private__.setR2L=p.setR2L=function(s){return qt=s,this},p.__private__.getR2L=p.getR2L=function(){return qt};var Gt,Qt=p.__private__.setZoomMode=function(s){var v=[void 0,null,"fullwidth","fullheight","fullpage","original"];if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(s))It=s;else if(isNaN(s)){if(v.indexOf(s)===-1)throw new Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+s+'" is not recognized.');It=s}else It=parseInt(s,10)};p.__private__.getZoomMode=function(){return It};var te,ie=p.__private__.setPageMode=function(s){if([void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(s)==-1)throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+s+'" is not recognized.');Gt=s};p.__private__.getPageMode=function(){return Gt};var de=p.__private__.setLayoutMode=function(s){if([void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(s)==-1)throw new Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+s+'" is not recognized.');te=s};p.__private__.getLayoutMode=function(){return te},p.__private__.setDisplayMode=p.setDisplayMode=function(s,v,j){return Qt(s),de(v),ie(j),this};var Wt={title:"",subject:"",author:"",keywords:"",creator:""};p.__private__.getDocumentProperty=function(s){if(Object.keys(Wt).indexOf(s)===-1)throw new Error("Invalid argument passed to jsPDF.getDocumentProperty");return Wt[s]},p.__private__.getDocumentProperties=function(){return Wt},p.__private__.setDocumentProperties=p.setProperties=p.setDocumentProperties=function(s){for(var v in Wt)Wt.hasOwnProperty(v)&&s[v]&&(Wt[v]=s[v]);return this},p.__private__.setDocumentProperty=function(s,v){if(Object.keys(Wt).indexOf(s)===-1)throw new Error("Invalid arguments passed to jsPDF.setDocumentProperty");return Wt[s]=v};var ee,Ct,Je,oe,Sr,ge={},Le={},zr=[],ue={},Mn={},Ae={},_r={},en=null,xe=0,Jt=[],le=new lu(p),En=n.hotfixes||[],We={},Ur={},Hr=[],zt=function s(v,j,R,X,ut,yt){if(!(this instanceof s))return new s(v,j,R,X,ut,yt);isNaN(v)&&(v=1),isNaN(j)&&(j=0),isNaN(R)&&(R=0),isNaN(X)&&(X=1),isNaN(ut)&&(ut=0),isNaN(yt)&&(yt=0),this._matrix=[v,j,R,X,ut,yt]};Object.defineProperty(zt.prototype,"sx",{get:function(){return this._matrix[0]},set:function(s){this._matrix[0]=s}}),Object.defineProperty(zt.prototype,"shy",{get:function(){return this._matrix[1]},set:function(s){this._matrix[1]=s}}),Object.defineProperty(zt.prototype,"shx",{get:function(){return this._matrix[2]},set:function(s){this._matrix[2]=s}}),Object.defineProperty(zt.prototype,"sy",{get:function(){return this._matrix[3]},set:function(s){this._matrix[3]=s}}),Object.defineProperty(zt.prototype,"tx",{get:function(){return this._matrix[4]},set:function(s){this._matrix[4]=s}}),Object.defineProperty(zt.prototype,"ty",{get:function(){return this._matrix[5]},set:function(s){this._matrix[5]=s}}),Object.defineProperty(zt.prototype,"a",{get:function(){return this._matrix[0]},set:function(s){this._matrix[0]=s}}),Object.defineProperty(zt.prototype,"b",{get:function(){return this._matrix[1]},set:function(s){this._matrix[1]=s}}),Object.defineProperty(zt.prototype,"c",{get:function(){return this._matrix[2]},set:function(s){this._matrix[2]=s}}),Object.defineProperty(zt.prototype,"d",{get:function(){return this._matrix[3]},set:function(s){this._matrix[3]=s}}),Object.defineProperty(zt.prototype,"e",{get:function(){return this._matrix[4]},set:function(s){this._matrix[4]=s}}),Object.defineProperty(zt.prototype,"f",{get:function(){return this._matrix[5]},set:function(s){this._matrix[5]=s}}),Object.defineProperty(zt.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(zt.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(zt.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(zt.prototype,"isIdentity",{get:function(){return this.sx===1&&this.shy===0&&this.shx===0&&this.sy===1&&this.tx===0&&this.ty===0}}),zt.prototype.join=function(s){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map(tt).join(s)},zt.prototype.multiply=function(s){var v=s.sx*this.sx+s.shy*this.shx,j=s.sx*this.shy+s.shy*this.sy,R=s.shx*this.sx+s.sy*this.shx,X=s.shx*this.shy+s.sy*this.sy,ut=s.tx*this.sx+s.ty*this.shx+this.tx,yt=s.tx*this.shy+s.ty*this.sy+this.ty;return new zt(v,j,R,X,ut,yt)},zt.prototype.decompose=function(){var s=this.sx,v=this.shy,j=this.shx,R=this.sy,X=this.tx,ut=this.ty,yt=Math.sqrt(s*s+v*v),Ot=(s/=yt)*j+(v/=yt)*R;j-=s*Ot,R-=v*Ot;var Dt=Math.sqrt(j*j+R*R);return Ot/=Dt,s*(R/=Dt)<v*(j/=Dt)&&(s=-s,v=-v,Ot=-Ot,yt=-yt),{scale:new zt(yt,0,0,Dt,0,0),translate:new zt(1,0,0,1,X,ut),rotate:new zt(s,v,-v,s,0,0),skew:new zt(1,0,Ot,1,0,0)}},zt.prototype.toString=function(s){return this.join(" ")},zt.prototype.inversed=function(){var s=this.sx,v=this.shy,j=this.shx,R=this.sy,X=this.tx,ut=this.ty,yt=1/(s*R-v*j),Ot=R*yt,Dt=-v*yt,Zt=-j*yt,Yt=s*yt;return new zt(Ot,Dt,Zt,Yt,-Ot*X-Zt*ut,-Dt*X-Yt*ut)},zt.prototype.applyToPoint=function(s){var v=s.x*this.sx+s.y*this.shx+this.tx,j=s.x*this.shy+s.y*this.sy+this.ty;return new pi(v,j)},zt.prototype.applyToRectangle=function(s){var v=this.applyToPoint(s),j=this.applyToPoint(new pi(s.x+s.w,s.y+s.h));return new Wi(v.x,v.y,j.x-v.x,j.y-v.y)},zt.prototype.clone=function(){var s=this.sx,v=this.shy,j=this.shx,R=this.sy,X=this.tx,ut=this.ty;return new zt(s,v,j,R,X,ut)},p.Matrix=zt;var Pr=p.matrixMult=function(s,v){return v.multiply(s)},Wr=new zt(1,0,0,1,0,0);p.unitMatrix=p.identityMatrix=Wr;var ir=function(s,v){if(!Mn[s]){var j=(v instanceof In?"Sh":"P")+(Object.keys(ue).length+1).toString(10);v.id=j,Mn[s]=j,ue[j]=v,le.publish("addPattern",v)}};p.ShadingPattern=In,p.TilingPattern=Xn,p.addShadingPattern=function(s,v){return z("addShadingPattern()"),ir(s,v),this},p.beginTilingPattern=function(s){z("beginTilingPattern()"),qa(s.boundingBox[0],s.boundingBox[1],s.boundingBox[2]-s.boundingBox[0],s.boundingBox[3]-s.boundingBox[1],s.matrix)},p.endTilingPattern=function(s,v){z("endTilingPattern()"),v.stream=Nt[C].join(`
`),ir(s,v),le.publish("endTilingPattern",v),Hr.pop().restore()};var De=p.__private__.newObject=function(){var s=Be();return hr(s,!0),s},Be=p.__private__.newObjectDeferred=function(){return J++,Q[J]=function(){return rt},J},hr=function(s,v){return v=typeof v=="boolean"&&v,Q[s]=rt,v&&E(s+" 0 obj"),s},ei=p.__private__.newAdditionalObject=function(){var s={objId:Be(),content:""};return At.push(s),s},rn=Be(),kr=Be(),Fr=p.__private__.decodeColorString=function(s){var v=s.split(" ");if(v.length!==2||v[1]!=="g"&&v[1]!=="G")v.length===5&&(v[4]==="k"||v[4]==="K")&&(v=[(1-v[0])*(1-v[3]),(1-v[1])*(1-v[3]),(1-v[2])*(1-v[3]),"r"]);else{var j=parseFloat(v[0]);v=[j,j,j,"r"]}for(var R="#",X=0;X<3;X++)R+=("0"+Math.floor(255*parseFloat(v[X])).toString(16)).slice(-2);return R},Ir=p.__private__.encodeColorString=function(s){var v;typeof s=="string"&&(s={ch1:s});var j=s.ch1,R=s.ch2,X=s.ch3,ut=s.ch4,yt=s.pdfColorType==="draw"?["G","RG","K"]:["g","rg","k"];if(typeof j=="string"&&j.charAt(0)!=="#"){var Ot=new ju(j);if(Ot.ok)j=Ot.toHex();else if(!/^\d*\.?\d*$/.test(j))throw new Error('Invalid color "'+j+'" passed to jsPDF.encodeColorString.')}if(typeof j=="string"&&/^#[0-9A-Fa-f]{3}$/.test(j)&&(j="#"+j[1]+j[1]+j[2]+j[2]+j[3]+j[3]),typeof j=="string"&&/^#[0-9A-Fa-f]{6}$/.test(j)){var Dt=parseInt(j.substr(1),16);j=Dt>>16&255,R=Dt>>8&255,X=255&Dt}if(R===void 0||ut===void 0&&j===R&&R===X)if(typeof j=="string")v=j+" "+yt[0];else switch(s.precision){case 2:v=pt(j/255)+" "+yt[0];break;case 3:default:v=P(j/255)+" "+yt[0]}else if(ut===void 0||fe(ut)==="object"){if(ut&&!isNaN(ut.a)&&ut.a===0)return v=["1.","1.","1.",yt[1]].join(" ");if(typeof j=="string")v=[j,R,X,yt[1]].join(" ");else switch(s.precision){case 2:v=[pt(j/255),pt(R/255),pt(X/255),yt[1]].join(" ");break;default:case 3:v=[P(j/255),P(R/255),P(X/255),yt[1]].join(" ")}}else if(typeof j=="string")v=[j,R,X,ut,yt[2]].join(" ");else switch(s.precision){case 2:v=[pt(j),pt(R),pt(X),pt(ut),yt[2]].join(" ");break;case 3:default:v=[P(j),P(R),P(X),P(ut),yt[2]].join(" ")}return v},Vr=p.__private__.getFilters=function(){return c},vr=p.__private__.putStream=function(s){var v=(s=s||{}).data||"",j=s.filters||Vr(),R=s.alreadyAppliedFilters||[],X=s.addLength1||!1,ut=v.length,yt=s.objectId,Ot=function(Xe){return Xe};if(y!==null&&yt===void 0)throw new Error("ObjectId must be passed to putStream for file encryption");y!==null&&(Ot=Ye.encryptor(yt,0));var Dt={};j===!0&&(j=["FlateEncode"]);var Zt=s.additionalKeyValues||[],Yt=(Dt=Tt.API.processDataByFilters!==void 0?Tt.API.processDataByFilters(v,j):{data:v,reverseChain:[]}).reverseChain+(Array.isArray(R)?R.join(" "):R.toString());if(Dt.data.length!==0&&(Zt.push({key:"Length",value:Dt.data.length}),X===!0&&Zt.push({key:"Length1",value:ut})),Yt.length!=0)if(Yt.split("/").length-1==1)Zt.push({key:"Filter",value:Yt});else{Zt.push({key:"Filter",value:"["+Yt+"]"});for(var ne=0;ne<Zt.length;ne+=1)if(Zt[ne].key==="DecodeParms"){for(var Ne=[],Se=0;Se<Dt.reverseChain.split("/").length-1;Se+=1)Ne.push("null");Ne.push(Zt[ne].value),Zt[ne].value="["+Ne.join(" ")+"]"}}E("<<");for(var Me=0;Me<Zt.length;Me++)E("/"+Zt[Me].key+" "+Zt[Me].value);E(">>"),Dt.data.length!==0&&(E("stream"),E(Ot(Dt.data)),E("endstream"))},Gr=p.__private__.putPage=function(s){var v=s.number,j=s.data,R=s.objId,X=s.contentsObjId;hr(R,!0),E("<</Type /Page"),E("/Parent "+s.rootDictionaryObjId+" 0 R"),E("/Resources "+s.resourceDictionaryObjId+" 0 R"),E("/MediaBox ["+parseFloat(tt(s.mediaBox.bottomLeftX))+" "+parseFloat(tt(s.mediaBox.bottomLeftY))+" "+tt(s.mediaBox.topRightX)+" "+tt(s.mediaBox.topRightY)+"]"),s.cropBox!==null&&E("/CropBox ["+tt(s.cropBox.bottomLeftX)+" "+tt(s.cropBox.bottomLeftY)+" "+tt(s.cropBox.topRightX)+" "+tt(s.cropBox.topRightY)+"]"),s.bleedBox!==null&&E("/BleedBox ["+tt(s.bleedBox.bottomLeftX)+" "+tt(s.bleedBox.bottomLeftY)+" "+tt(s.bleedBox.topRightX)+" "+tt(s.bleedBox.topRightY)+"]"),s.trimBox!==null&&E("/TrimBox ["+tt(s.trimBox.bottomLeftX)+" "+tt(s.trimBox.bottomLeftY)+" "+tt(s.trimBox.topRightX)+" "+tt(s.trimBox.topRightY)+"]"),s.artBox!==null&&E("/ArtBox ["+tt(s.artBox.bottomLeftX)+" "+tt(s.artBox.bottomLeftY)+" "+tt(s.artBox.topRightX)+" "+tt(s.artBox.topRightY)+"]"),typeof s.userUnit=="number"&&s.userUnit!==1&&E("/UserUnit "+s.userUnit),le.publish("putPage",{objId:R,pageContext:Jt[v],pageNumber:v,page:j}),E("/Contents "+X+" 0 R"),E(">>"),E("endobj");var ut=j.join(`
`);return Y===B.ADVANCED&&(ut+=`
Q`),hr(X,!0),vr({data:ut,filters:Vr(),objectId:X}),E("endobj"),R},qn=p.__private__.putPages=function(){var s,v,j=[];for(s=1;s<=xe;s++)Jt[s].objId=Be(),Jt[s].contentsObjId=Be();for(s=1;s<=xe;s++)j.push(Gr({number:s,data:Nt[s],objId:Jt[s].objId,contentsObjId:Jt[s].contentsObjId,mediaBox:Jt[s].mediaBox,cropBox:Jt[s].cropBox,bleedBox:Jt[s].bleedBox,trimBox:Jt[s].trimBox,artBox:Jt[s].artBox,userUnit:Jt[s].userUnit,rootDictionaryObjId:rn,resourceDictionaryObjId:kr}));hr(rn,!0),E("<</Type /Pages");var R="/Kids [";for(v=0;v<xe;v++)R+=j[v]+" 0 R ";E(R+"]"),E("/Count "+xe),E(">>"),E("endobj"),le.publish("postPutPages")},ri=function(s){le.publish("putFont",{font:s,out:E,newObject:De,putStream:vr}),s.isAlreadyPutted!==!0&&(s.objectNumber=De(),E("<<"),E("/Type /Font"),E("/BaseFont /"+Oi(s.postScriptName)),E("/Subtype /Type1"),typeof s.encoding=="string"&&E("/Encoding /"+s.encoding),E("/FirstChar 32"),E("/LastChar 255"),E(">>"),E("endobj"))},ni=function(){for(var s in ge)ge.hasOwnProperty(s)&&(w===!1||w===!0&&S.hasOwnProperty(s))&&ri(ge[s])},ii=function(s){s.objectNumber=De();var v=[];v.push({key:"Type",value:"/XObject"}),v.push({key:"Subtype",value:"/Form"}),v.push({key:"BBox",value:"["+[tt(s.x),tt(s.y),tt(s.x+s.width),tt(s.y+s.height)].join(" ")+"]"}),v.push({key:"Matrix",value:"["+s.matrix.toString()+"]"});var j=s.pages[1].join(`
`);vr({data:j,additionalKeyValues:v,objectId:s.objectNumber}),E("endobj")},ai=function(){for(var s in We)We.hasOwnProperty(s)&&ii(We[s])},ya=function(s,v){var j,R=[],X=1/(v-1);for(j=0;j<1;j+=X)R.push(j);if(R.push(1),s[0].offset!=0){var ut={offset:0,color:s[0].color};s.unshift(ut)}if(s[s.length-1].offset!=1){var yt={offset:1,color:s[s.length-1].color};s.push(yt)}for(var Ot="",Dt=0,Zt=0;Zt<R.length;Zt++){for(j=R[Zt];j>s[Dt+1].offset;)Dt++;var Yt=s[Dt].offset,ne=(j-Yt)/(s[Dt+1].offset-Yt),Ne=s[Dt].color,Se=s[Dt+1].color;Ot+=T(Math.round((1-ne)*Ne[0]+ne*Se[0]).toString(16))+T(Math.round((1-ne)*Ne[1]+ne*Se[1]).toString(16))+T(Math.round((1-ne)*Ne[2]+ne*Se[2]).toString(16))}return Ot.trim()},bo=function(s,v){v||(v=21);var j=De(),R=ya(s.colors,v),X=[];X.push({key:"FunctionType",value:"0"}),X.push({key:"Domain",value:"[0.0 1.0]"}),X.push({key:"Size",value:"["+v+"]"}),X.push({key:"BitsPerSample",value:"8"}),X.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),X.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),vr({data:R,additionalKeyValues:X,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:j}),E("endobj"),s.objectNumber=De(),E("<< /ShadingType "+s.type),E("/ColorSpace /DeviceRGB");var ut="/Coords ["+tt(parseFloat(s.coords[0]))+" "+tt(parseFloat(s.coords[1]))+" ";s.type===2?ut+=tt(parseFloat(s.coords[2]))+" "+tt(parseFloat(s.coords[3])):ut+=tt(parseFloat(s.coords[2]))+" "+tt(parseFloat(s.coords[3]))+" "+tt(parseFloat(s.coords[4]))+" "+tt(parseFloat(s.coords[5])),E(ut+="]"),s.matrix&&E("/Matrix ["+s.matrix.toString()+"]"),E("/Function "+j+" 0 R"),E("/Extend [true true]"),E(">>"),E("endobj")},yo=function(s,v){var j=Be(),R=De();v.push({resourcesOid:j,objectOid:R}),s.objectNumber=R;var X=[];X.push({key:"Type",value:"/Pattern"}),X.push({key:"PatternType",value:"1"}),X.push({key:"PaintType",value:"1"}),X.push({key:"TilingType",value:"1"}),X.push({key:"BBox",value:"["+s.boundingBox.map(tt).join(" ")+"]"}),X.push({key:"XStep",value:tt(s.xStep)}),X.push({key:"YStep",value:tt(s.yStep)}),X.push({key:"Resources",value:j+" 0 R"}),s.matrix&&X.push({key:"Matrix",value:"["+s.matrix.toString()+"]"}),vr({data:s.stream,additionalKeyValues:X,objectId:s.objectNumber}),E("endobj")},oi=function(s){var v;for(v in ue)ue.hasOwnProperty(v)&&(ue[v]instanceof In?bo(ue[v]):ue[v]instanceof Xn&&yo(ue[v],s))},wa=function(s){for(var v in s.objectNumber=De(),E("<<"),s)switch(v){case"opacity":E("/ca "+pt(s[v]));break;case"stroke-opacity":E("/CA "+pt(s[v]))}E(">>"),E("endobj")},wo=function(){var s;for(s in Ae)Ae.hasOwnProperty(s)&&wa(Ae[s])},Bi=function(){for(var s in E("/XObject <<"),We)We.hasOwnProperty(s)&&We[s].objectNumber>=0&&E("/"+s+" "+We[s].objectNumber+" 0 R");le.publish("putXobjectDict"),E(">>")},Lo=function(){Ye.oid=De(),E("<<"),E("/Filter /Standard"),E("/V "+Ye.v),E("/R "+Ye.r),E("/U <"+Ye.toHexString(Ye.U)+">"),E("/O <"+Ye.toHexString(Ye.O)+">"),E("/P "+Ye.P),E(">>"),E("endobj")},La=function(){for(var s in E("/Font <<"),ge)ge.hasOwnProperty(s)&&(w===!1||w===!0&&S.hasOwnProperty(s))&&E("/"+s+" "+ge[s].objectNumber+" 0 R");E(">>")},No=function(){if(Object.keys(ue).length>0){for(var s in E("/Shading <<"),ue)ue.hasOwnProperty(s)&&ue[s]instanceof In&&ue[s].objectNumber>=0&&E("/"+s+" "+ue[s].objectNumber+" 0 R");le.publish("putShadingPatternDict"),E(">>")}},si=function(s){if(Object.keys(ue).length>0){for(var v in E("/Pattern <<"),ue)ue.hasOwnProperty(v)&&ue[v]instanceof p.TilingPattern&&ue[v].objectNumber>=0&&ue[v].objectNumber<s&&E("/"+v+" "+ue[v].objectNumber+" 0 R");le.publish("putTilingPatternDict"),E(">>")}},Ao=function(){if(Object.keys(Ae).length>0){var s;for(s in E("/ExtGState <<"),Ae)Ae.hasOwnProperty(s)&&Ae[s].objectNumber>=0&&E("/"+s+" "+Ae[s].objectNumber+" 0 R");le.publish("putGStateDict"),E(">>")}},ke=function(s){hr(s.resourcesOid,!0),E("<<"),E("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),La(),No(),si(s.objectOid),Ao(),Bi(),E(">>"),E("endobj")},Na=function(){var s=[];ni(),wo(),ai(),oi(s),le.publish("putResources"),s.forEach(ke),ke({resourcesOid:kr,objectOid:Number.MAX_SAFE_INTEGER}),le.publish("postPutResources")},Aa=function(){le.publish("putAdditionalObjects");for(var s=0;s<At.length;s++){var v=At[s];hr(v.objId,!0),E(v.content),E("endobj")}le.publish("postPutAdditionalObjects")},xa=function(s){Le[s.fontName]=Le[s.fontName]||{},Le[s.fontName][s.fontStyle]=s.id},Mi=function(s,v,j,R,X){var ut={id:"F"+(Object.keys(ge).length+1).toString(10),postScriptName:s,fontName:v,fontStyle:j,encoding:R,isStandardFont:X||!1,metadata:{}};return le.publish("addFont",{font:ut,instance:this}),ge[ut.id]=ut,xa(ut),ut.id},xo=function(s){for(var v=0,j=Lt.length;v<j;v++){var R=Mi.call(this,s[v][0],s[v][1],s[v][2],Lt[v][3],!0);w===!1&&(S[R]=!0);var X=s[v][0].split("-");xa({id:R,fontName:X[0],fontStyle:X[1]||""})}le.publish("addFonts",{fonts:ge,dictionary:Le})},Cr=function(s){return s.foo=function(){try{return s.apply(this,arguments)}catch(R){var v=R.stack||"";~v.indexOf(" at ")&&(v=v.split(" at ")[1]);var j="Error in function "+v.split(`
`)[0].split("<")[0]+": "+R.message;if(!Ht.console)throw new Error(j);Ht.console.error(j,R),Ht.alert&&alert(j)}},s.foo.bar=s,s.foo},ui=function(s,v){var j,R,X,ut,yt,Ot,Dt,Zt,Yt;if(X=(v=v||{}).sourceEncoding||"Unicode",yt=v.outputEncoding,(v.autoencode||yt)&&ge[ee].metadata&&ge[ee].metadata[X]&&ge[ee].metadata[X].encoding&&(ut=ge[ee].metadata[X].encoding,!yt&&ge[ee].encoding&&(yt=ge[ee].encoding),!yt&&ut.codePages&&(yt=ut.codePages[0]),typeof yt=="string"&&(yt=ut[yt]),yt)){for(Dt=!1,Ot=[],j=0,R=s.length;j<R;j++)(Zt=yt[s.charCodeAt(j)])?Ot.push(String.fromCharCode(Zt)):Ot.push(s[j]),Ot[j].charCodeAt(0)>>8&&(Dt=!0);s=Ot.join("")}for(j=s.length;Dt===void 0&&j!==0;)s.charCodeAt(j-1)>>8&&(Dt=!0),j--;if(!Dt)return s;for(Ot=v.noBOM?[]:[254,255],j=0,R=s.length;j<R;j++){if((Yt=(Zt=s.charCodeAt(j))>>8)>>8)throw new Error("Character at position "+j+" of string '"+s+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");Ot.push(Yt),Ot.push(Zt-(Yt<<8))}return String.fromCharCode.apply(void 0,Ot)},ar=p.__private__.pdfEscape=p.pdfEscape=function(s,v){return ui(s,v).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},Ei=p.__private__.beginPage=function(s){Nt[++xe]=[],Jt[xe]={objId:0,contentsObjId:0,userUnit:Number(h),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(s[0]),topRightY:Number(s[1])}},_a(xe),ft(Nt[C])},Sa=function(s,v){var j,R,X;switch(r=v||r,typeof s=="string"&&(j=_(s.toLowerCase()),Array.isArray(j)&&(R=j[0],X=j[1])),Array.isArray(s)&&(R=s[0]*Ct,X=s[1]*Ct),isNaN(R)&&(R=u[0],X=u[1]),(R>14400||X>14400)&&(ve.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),R=Math.min(14400,R),X=Math.min(14400,X)),u=[R,X],r.substr(0,1)){case"l":X>R&&(u=[X,R]);break;case"p":R>X&&(u=[X,R])}Ei(u),ja(Ti),E(jr),Ui!==0&&E(Ui+" J"),Hi!==0&&E(Hi+" j"),le.publish("addPage",{pageNumber:xe})},So=function(s){s>0&&s<=xe&&(Nt.splice(s,1),Jt.splice(s,1),xe--,C>xe&&(C=xe),this.setPage(C))},_a=function(s){s>0&&s<=xe&&(C=s)},_o=p.__private__.getNumberOfPages=p.getNumberOfPages=function(){return Nt.length-1},Pa=function(s,v,j){var R,X=void 0;return j=j||{},s=s!==void 0?s:ge[ee].fontName,v=v!==void 0?v:ge[ee].fontStyle,R=s.toLowerCase(),Le[R]!==void 0&&Le[R][v]!==void 0?X=Le[R][v]:Le[s]!==void 0&&Le[s][v]!==void 0?X=Le[s][v]:j.disableWarning===!1&&ve.warn("Unable to look up font label for font '"+s+"', '"+v+"'. Refer to getFontList() for available fonts."),X||j.noFallback||(X=Le.times[v])==null&&(X=Le.times.normal),X},Po=p.__private__.putInfo=function(){var s=De(),v=function(R){return R};for(var j in y!==null&&(v=Ye.encryptor(s,0)),E("<<"),E("/Producer ("+ar(v("jsPDF "+Tt.version))+")"),Wt)Wt.hasOwnProperty(j)&&Wt[j]&&E("/"+j.substr(0,1).toUpperCase()+j.substr(1)+" ("+ar(v(Wt[j]))+")");E("/CreationDate ("+ar(v(st))+")"),E(">>"),E("endobj")},qi=p.__private__.putCatalog=function(s){var v=(s=s||{}).rootDictionaryObjId||rn;switch(De(),E("<<"),E("/Type /Catalog"),E("/Pages "+v+" 0 R"),It||(It="fullwidth"),It){case"fullwidth":E("/OpenAction [3 0 R /FitH null]");break;case"fullheight":E("/OpenAction [3 0 R /FitV null]");break;case"fullpage":E("/OpenAction [3 0 R /Fit]");break;case"original":E("/OpenAction [3 0 R /XYZ null null 1]");break;default:var j=""+It;j.substr(j.length-1)==="%"&&(It=parseInt(It)/100),typeof It=="number"&&E("/OpenAction [3 0 R /XYZ null null "+pt(It)+"]")}switch(te||(te="continuous"),te){case"continuous":E("/PageLayout /OneColumn");break;case"single":E("/PageLayout /SinglePage");break;case"two":case"twoleft":E("/PageLayout /TwoColumnLeft");break;case"tworight":E("/PageLayout /TwoColumnRight")}Gt&&E("/PageMode /"+Gt),le.publish("putCatalog"),E(">>"),E("endobj")},ko=p.__private__.putTrailer=function(){E("trailer"),E("<<"),E("/Size "+(J+1)),E("/Root "+J+" 0 R"),E("/Info "+(J-1)+" 0 R"),y!==null&&E("/Encrypt "+Ye.oid+" 0 R"),E("/ID [ <"+it+"> <"+it+"> ]"),E(">>")},Fo=p.__private__.putHeader=function(){E("%PDF-"+O),E("%ºß¬à")},Io=p.__private__.putXRef=function(){var s="0000000000";E("xref"),E("0 "+(J+1)),E("0000000000 65535 f ");for(var v=1;v<=J;v++)typeof Q[v]=="function"?E((s+Q[v]()).slice(-10)+" 00000 n "):Q[v]!==void 0?E((s+Q[v]).slice(-10)+" 00000 n "):E("0000000000 00000 n ")},nn=p.__private__.buildDocument=function(){Ut(),ft(et),le.publish("buildDocument"),Fo(),qn(),Aa(),Na(),y!==null&&Lo(),Po(),qi();var s=rt;return Io(),ko(),E("startxref"),E(""+s),E("%%EOF"),ft(Nt[C]),et.join(`
`)},li=p.__private__.getBlob=function(s){return new Blob([Et(s)],{type:"application/pdf"})},ci=p.output=p.__private__.output=Cr(function(s,v){switch(typeof(v=v||{})=="string"?v={filename:v}:v.filename=v.filename||"generated.pdf",s){case void 0:return nn();case"save":p.save(v.filename);break;case"arraybuffer":return Et(nn());case"blob":return li(nn());case"bloburi":case"bloburl":if(Ht.URL!==void 0&&typeof Ht.URL.createObjectURL=="function")return Ht.URL&&Ht.URL.createObjectURL(li(nn()))||void 0;ve.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var j="",R=nn();try{j=Ls(R)}catch{j=Ls(unescape(encodeURIComponent(R)))}return"data:application/pdf;filename="+v.filename+";base64,"+j;case"pdfobjectnewwindow":if(Object.prototype.toString.call(Ht)==="[object Window]"){var X="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",ut=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';v.pdfObjectUrl&&(X=v.pdfObjectUrl,ut="");var yt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+X+'"'+ut+'><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(v)+");<\/script></body></html>",Ot=Ht.open();return Ot!==null&&Ot.document.write(yt),Ot}throw new Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if(Object.prototype.toString.call(Ht)==="[object Window]"){var Dt='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(v.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+v.filename+'" width="500px" height="400px" /></body></html>',Zt=Ht.open();if(Zt!==null){Zt.document.write(Dt);var Yt=this;Zt.document.documentElement.querySelector("#pdfViewer").onload=function(){Zt.document.title=v.filename,Zt.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(Yt.output("bloburl"))}}return Zt}throw new Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if(Object.prototype.toString.call(Ht)!=="[object Window]")throw new Error("The option dataurlnewwindow just works in a browser-environment.");var ne='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",v)+'"></iframe></body></html>',Ne=Ht.open();if(Ne!==null&&(Ne.document.write(ne),Ne.document.title=v.filename),Ne||typeof safari>"u")return Ne;break;case"datauri":case"dataurl":return Ht.document.location.href=this.output("datauristring",v);default:return null}}),ka=function(s){return Array.isArray(En)===!0&&En.indexOf(s)>-1};switch(a){case"pt":Ct=1;break;case"mm":Ct=72/25.4;break;case"cm":Ct=72/2.54;break;case"in":Ct=72;break;case"px":Ct=ka("px_scaling")==1?.75:96/72;break;case"pc":case"em":Ct=12;break;case"ex":Ct=6;break;default:if(typeof a!="number")throw new Error("Invalid unit: "+a);Ct=a}var Ye=null;jt(),$();var Co=function(s){return y!==null?Ye.encryptor(s,0):function(v){return v}},Fa=p.__private__.getPageInfo=p.getPageInfo=function(s){if(isNaN(s)||s%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:Jt[s].objId,pageNumber:s,pageContext:Jt[s]}},Vt=p.__private__.getPageInfoByObjId=function(s){if(isNaN(s)||s%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var v in Jt)if(Jt[v].objId===s)break;return Fa(v)},jo=p.__private__.getCurrentPageInfo=p.getCurrentPageInfo=function(){return{objId:Jt[C].objId,pageNumber:C,pageContext:Jt[C]}};p.addPage=function(){return Sa.apply(this,arguments),this},p.setPage=function(){return _a.apply(this,arguments),ft.call(this,Nt[C]),this},p.insertPage=function(s){return this.addPage(),this.movePage(C,s),this},p.movePage=function(s,v){var j,R;if(s>v){j=Nt[s],R=Jt[s];for(var X=s;X>v;X--)Nt[X]=Nt[X-1],Jt[X]=Jt[X-1];Nt[v]=j,Jt[v]=R,this.setPage(v)}else if(s<v){j=Nt[s],R=Jt[s];for(var ut=s;ut<v;ut++)Nt[ut]=Nt[ut+1],Jt[ut]=Jt[ut+1];Nt[v]=j,Jt[v]=R,this.setPage(v)}return this},p.deletePage=function(){return So.apply(this,arguments),this},p.__private__.text=p.text=function(s,v,j,R,X){var ut,yt,Ot,Dt,Zt,Yt,ne,Ne,Se,Me=(R=R||{}).scope||this;if(typeof s=="number"&&typeof v=="number"&&(typeof j=="string"||Array.isArray(j))){var Xe=j;j=v,v=s,s=Xe}if(arguments[3]instanceof zt?(z("The transform parameter of text() with a Matrix value"),Se=X):(Ot=arguments[4],Dt=arguments[5],fe(ne=arguments[3])==="object"&&ne!==null||(typeof Ot=="string"&&(Dt=Ot,Ot=null),typeof ne=="string"&&(Dt=ne,ne=null),typeof ne=="number"&&(Ot=ne,ne=null),R={flags:ne,angle:Ot,align:Dt})),isNaN(v)||isNaN(j)||s==null)throw new Error("Invalid arguments passed to jsPDF.text");if(s.length===0)return Me;var ze="",Or=!1,fr=typeof R.lineHeightFactor=="number"?R.lineHeightFactor:Rn,Xr=Me.internal.scaleFactor;function Da(be){return be=be.split("	").join(Array(R.TabLen||9).join(" ")),ar(be,ne)}function Yi(be){for(var ye,Ie=be.concat(),Re=[],ln=Ie.length;ln--;)typeof(ye=Ie.shift())=="string"?Re.push(ye):Array.isArray(be)&&(ye.length===1||ye[1]===void 0&&ye[2]===void 0)?Re.push(ye[0]):Re.push([ye[0],ye[1],ye[2]]);return Re}function Xi(be,ye){var Ie;if(typeof be=="string")Ie=ye(be)[0];else if(Array.isArray(be)){for(var Re,ln,na=be.concat(),Si=[],Ha=na.length;Ha--;)typeof(Re=na.shift())=="string"?Si.push(ye(Re)[0]):Array.isArray(Re)&&typeof Re[0]=="string"&&(ln=ye(Re[0],Re[1],Re[2]),Si.push([ln[0],ln[1],ln[2]]));Ie=Si}return Ie}var mi=!1,Ki=!0;if(typeof s=="string")mi=!0;else if(Array.isArray(s)){var Zi=s.concat();yt=[];for(var vi,Ve=Zi.length;Ve--;)(typeof(vi=Zi.shift())!="string"||Array.isArray(vi)&&typeof vi[0]!="string")&&(Ki=!1);mi=Ki}if(mi===!1)throw new Error('Type of text must be string or Array. "'+s+'" is not recognized.');typeof s=="string"&&(s=s.match(/[\r?\n]/)?s.split(/\r\n|\r|\n/g):[s]);var bi=xt/Me.internal.scaleFactor,yi=bi*(fr-1);switch(R.baseline){case"bottom":j-=yi;break;case"top":j+=bi-yi;break;case"hanging":j+=bi-2*yi;break;case"middle":j+=bi/2-yi}if((Yt=R.maxWidth||0)>0&&(typeof s=="string"?s=Me.splitTextToSize(s,Yt):Object.prototype.toString.call(s)==="[object Array]"&&(s=s.reduce(function(be,ye){return be.concat(Me.splitTextToSize(ye,Yt))},[]))),ut={text:s,x:v,y:j,options:R,mutex:{pdfEscape:ar,activeFontKey:ee,fonts:ge,activeFontSize:xt}},le.publish("preProcessText",ut),s=ut.text,Ot=(R=ut.options).angle,!(Se instanceof zt)&&Ot&&typeof Ot=="number"){Ot*=Math.PI/180,R.rotationDirection===0&&(Ot=-Ot),Y===B.ADVANCED&&(Ot=-Ot);var wi=Math.cos(Ot),$i=Math.sin(Ot);Se=new zt(wi,$i,-$i,wi,0,0)}else Ot&&Ot instanceof zt&&(Se=Ot);Y!==B.ADVANCED||Se||(Se=Wr),(Zt=R.charSpace||di)!==void 0&&(ze+=tt(k(Zt))+` Tc
`,this.setCharSpace(this.getCharSpace()||0)),(Ne=R.horizontalScale)!==void 0&&(ze+=tt(100*Ne)+` Tz
`),R.lang;var or=-1,Uo=R.renderingMode!==void 0?R.renderingMode:R.stroke,Qi=Me.internal.getCurrentPageInfo().pageContext;switch(Uo){case 0:case!1:case"fill":or=0;break;case 1:case!0:case"stroke":or=1;break;case 2:case"fillThenStroke":or=2;break;case 3:case"invisible":or=3;break;case 4:case"fillAndAddForClipping":or=4;break;case 5:case"strokeAndAddPathForClipping":or=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":or=6;break;case 7:case"addToPathForClipping":or=7}var Ra=Qi.usedRenderingMode!==void 0?Qi.usedRenderingMode:-1;or!==-1?ze+=or+` Tr
`:Ra!==-1&&(ze+=`0 Tr
`),or!==-1&&(Qi.usedRenderingMode=or),Dt=R.align||"left";var br,Li=xt*fr,Ta=Me.internal.pageSize.getWidth(),za=ge[ee];Zt=R.charSpace||di,Yt=R.maxWidth||0,ne=Object.assign({autoencode:!0,noBOM:!0},R.flags);var vn=[],Un=function(be){return Me.getStringUnitWidth(be,{font:za,charSpace:Zt,fontSize:xt,doKerning:!1})*xt/Xr};if(Object.prototype.toString.call(s)==="[object Array]"){var sr;yt=Yi(s),Dt!=="left"&&(br=yt.map(Un));var er,bn=0;if(Dt==="right"){v-=br[0],s=[],Ve=yt.length;for(var on=0;on<Ve;on++)on===0?(er=Yr(v),sr=an(j)):(er=k(bn-br[on]),sr=-Li),s.push([yt[on],er,sr]),bn=br[on]}else if(Dt==="center"){v-=br[0]/2,s=[],Ve=yt.length;for(var sn=0;sn<Ve;sn++)sn===0?(er=Yr(v),sr=an(j)):(er=k((bn-br[sn])/2),sr=-Li),s.push([yt[sn],er,sr]),bn=br[sn]}else if(Dt==="left"){s=[],Ve=yt.length;for(var Ni=0;Ni<Ve;Ni++)s.push(yt[Ni])}else if(Dt==="justify"&&za.encoding==="Identity-H"){s=[],Ve=yt.length,Yt=Yt!==0?Yt:Ta;for(var un=0,Fe=0;Fe<Ve;Fe++)if(sr=Fe===0?an(j):-Li,er=Fe===0?Yr(v):un,Fe<Ve-1){var ta=k((Yt-br[Fe])/(yt[Fe].split(" ").length-1)),rr=yt[Fe].split(" ");s.push([rr[0]+" ",er,sr]),un=0;for(var yr=1;yr<rr.length;yr++){var Ai=(Un(rr[yr-1]+" "+rr[yr])-Un(rr[yr]))*Xr+ta;yr==rr.length-1?s.push([rr[yr],Ai,0]):s.push([rr[yr]+" ",Ai,0]),un-=Ai}}else s.push([yt[Fe],er,sr]);s.push(["",un,0])}else{if(Dt!=="justify")throw new Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');for(s=[],Ve=yt.length,Yt=Yt!==0?Yt:Ta,Fe=0;Fe<Ve;Fe++)sr=Fe===0?an(j):-Li,er=Fe===0?Yr(v):0,Fe<Ve-1?vn.push(tt(k((Yt-br[Fe])/(yt[Fe].split(" ").length-1)))):vn.push(0),s.push([yt[Fe],er,sr])}}var Ua=typeof R.R2L=="boolean"?R.R2L:qt;Ua===!0&&(s=Xi(s,function(be,ye,Ie){return[be.split("").reverse().join(""),ye,Ie]})),ut={text:s,x:v,y:j,options:R,mutex:{pdfEscape:ar,activeFontKey:ee,fonts:ge,activeFontSize:xt}},le.publish("postProcessText",ut),s=ut.text,Or=ut.mutex.isHex||!1;var ea=ge[ee].encoding;ea!=="WinAnsiEncoding"&&ea!=="StandardEncoding"||(s=Xi(s,function(be,ye,Ie){return[Da(be),ye,Ie]})),yt=Yi(s),s=[];for(var Hn,Wn,yn,Vn=0,xi=1,Gn=Array.isArray(yt[0])?xi:Vn,wn="",ra=function(be,ye,Ie){var Re="";return Ie instanceof zt?(Ie=typeof R.angle=="number"?Pr(Ie,new zt(1,0,0,1,be,ye)):Pr(new zt(1,0,0,1,be,ye),Ie),Y===B.ADVANCED&&(Ie=Pr(new zt(1,0,0,-1,0,0),Ie)),Re=Ie.join(" ")+` Tm
`):Re=tt(be)+" "+tt(ye)+` Td
`,Re},wr=0;wr<yt.length;wr++){switch(wn="",Gn){case xi:yn=(Or?"<":"(")+yt[wr][0]+(Or?">":")"),Hn=parseFloat(yt[wr][1]),Wn=parseFloat(yt[wr][2]);break;case Vn:yn=(Or?"<":"(")+yt[wr]+(Or?">":")"),Hn=Yr(v),Wn=an(j)}vn!==void 0&&vn[wr]!==void 0&&(wn=vn[wr]+` Tw
`),wr===0?s.push(wn+ra(Hn,Wn,Se)+yn):Gn===Vn?s.push(wn+yn):Gn===xi&&s.push(wn+ra(Hn,Wn,Se)+yn)}s=Gn===Vn?s.join(` Tj
T* `):s.join(` Tj
`),s+=` Tj
`;var Lr=`BT
/`;return Lr+=ee+" "+xt+` Tf
`,Lr+=tt(xt*fr)+` TL
`,Lr+=Tn+`
`,Lr+=ze,Lr+=s,E(Lr+="ET"),S[ee]=!0,Me};var Oo=p.__private__.clip=p.clip=function(s){return E(s==="evenodd"?"W*":"W"),this};p.clipEvenOdd=function(){return Oo("evenodd")},p.__private__.discardPath=p.discardPath=function(){return E("n"),this};var Jr=p.__private__.isValidStyle=function(s){var v=!1;return[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(s)!==-1&&(v=!0),v};p.__private__.setDefaultPathOperation=p.setDefaultPathOperation=function(s){return Jr(s)&&(g=s),this};var Ia=p.__private__.getStyle=p.getStyle=function(s){var v=g;switch(s){case"D":case"S":v="S";break;case"F":v="f";break;case"FD":case"DF":v="B";break;case"f":case"f*":case"B":case"B*":v=s}return v},Ca=p.close=function(){return E("h"),this};p.stroke=function(){return E("S"),this},p.fill=function(s){return hi("f",s),this},p.fillEvenOdd=function(s){return hi("f*",s),this},p.fillStroke=function(s){return hi("B",s),this},p.fillStrokeEvenOdd=function(s){return hi("B*",s),this};var hi=function(s,v){fe(v)==="object"?Mo(v,s):E(s)},Di=function(s){s===null||Y===B.ADVANCED&&s===void 0||(s=Ia(s),E(s))};function Bo(s,v,j,R,X){var ut=new Xn(v||this.boundingBox,j||this.xStep,R||this.yStep,this.gState,X||this.matrix);ut.stream=this.stream;var yt=s+"$$"+this.cloneIndex+++"$$";return ir(yt,ut),ut}var Mo=function(s,v){var j=Mn[s.key],R=ue[j];if(R instanceof In)E("q"),E(Eo(v)),R.gState&&p.setGState(R.gState),E(s.matrix.toString()+" cm"),E("/"+j+" sh"),E("Q");else if(R instanceof Xn){var X=new zt(1,0,0,-1,0,mn());s.matrix&&(X=X.multiply(s.matrix||Wr),j=Bo.call(R,s.key,s.boundingBox,s.xStep,s.yStep,X).id),E("q"),E("/Pattern cs"),E("/"+j+" scn"),R.gState&&p.setGState(R.gState),E(v),E("Q")}},Eo=function(s){switch(s){case"f":case"F":return"W n";case"f*":return"W* n";case"B":return"W S";case"B*":return"W* S";case"S":return"W S";case"n":return"W n"}},Ri=p.moveTo=function(s,v){return E(tt(k(s))+" "+tt(D(v))+" m"),this},Dn=p.lineTo=function(s,v){return E(tt(k(s))+" "+tt(D(v))+" l"),this},pn=p.curveTo=function(s,v,j,R,X,ut){return E([tt(k(s)),tt(D(v)),tt(k(j)),tt(D(R)),tt(k(X)),tt(D(ut)),"c"].join(" ")),this};p.__private__.line=p.line=function(s,v,j,R,X){if(isNaN(s)||isNaN(v)||isNaN(j)||isNaN(R)||!Jr(X))throw new Error("Invalid arguments passed to jsPDF.line");return Y===B.COMPAT?this.lines([[j-s,R-v]],s,v,[1,1],X||"S"):this.lines([[j-s,R-v]],s,v,[1,1]).stroke()},p.__private__.lines=p.lines=function(s,v,j,R,X,ut){var yt,Ot,Dt,Zt,Yt,ne,Ne,Se,Me,Xe,ze,Or;if(typeof s=="number"&&(Or=j,j=v,v=s,s=Or),R=R||[1,1],ut=ut||!1,isNaN(v)||isNaN(j)||!Array.isArray(s)||!Array.isArray(R)||!Jr(X)||typeof ut!="boolean")throw new Error("Invalid arguments passed to jsPDF.lines");for(Ri(v,j),yt=R[0],Ot=R[1],Zt=s.length,Xe=v,ze=j,Dt=0;Dt<Zt;Dt++)(Yt=s[Dt]).length===2?(Xe=Yt[0]*yt+Xe,ze=Yt[1]*Ot+ze,Dn(Xe,ze)):(ne=Yt[0]*yt+Xe,Ne=Yt[1]*Ot+ze,Se=Yt[2]*yt+Xe,Me=Yt[3]*Ot+ze,Xe=Yt[4]*yt+Xe,ze=Yt[5]*Ot+ze,pn(ne,Ne,Se,Me,Xe,ze));return ut&&Ca(),Di(X),this},p.path=function(s){for(var v=0;v<s.length;v++){var j=s[v],R=j.c;switch(j.op){case"m":Ri(R[0],R[1]);break;case"l":Dn(R[0],R[1]);break;case"c":pn.apply(this,R);break;case"h":Ca()}}return this},p.__private__.rect=p.rect=function(s,v,j,R,X){if(isNaN(s)||isNaN(v)||isNaN(j)||isNaN(R)||!Jr(X))throw new Error("Invalid arguments passed to jsPDF.rect");return Y===B.COMPAT&&(R=-R),E([tt(k(s)),tt(D(v)),tt(k(j)),tt(k(R)),"re"].join(" ")),Di(X),this},p.__private__.triangle=p.triangle=function(s,v,j,R,X,ut,yt){if(isNaN(s)||isNaN(v)||isNaN(j)||isNaN(R)||isNaN(X)||isNaN(ut)||!Jr(yt))throw new Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[j-s,R-v],[X-j,ut-R],[s-X,v-ut]],s,v,[1,1],yt,!0),this},p.__private__.roundedRect=p.roundedRect=function(s,v,j,R,X,ut,yt){if(isNaN(s)||isNaN(v)||isNaN(j)||isNaN(R)||isNaN(X)||isNaN(ut)||!Jr(yt))throw new Error("Invalid arguments passed to jsPDF.roundedRect");var Ot=4/3*(Math.SQRT2-1);return X=Math.min(X,.5*j),ut=Math.min(ut,.5*R),this.lines([[j-2*X,0],[X*Ot,0,X,ut-ut*Ot,X,ut],[0,R-2*ut],[0,ut*Ot,-X*Ot,ut,-X,ut],[2*X-j,0],[-X*Ot,0,-X,-ut*Ot,-X,-ut],[0,2*ut-R],[0,-ut*Ot,X*Ot,-ut,X,-ut]],s+X,v,[1,1],yt,!0),this},p.__private__.ellipse=p.ellipse=function(s,v,j,R,X){if(isNaN(s)||isNaN(v)||isNaN(j)||isNaN(R)||!Jr(X))throw new Error("Invalid arguments passed to jsPDF.ellipse");var ut=4/3*(Math.SQRT2-1)*j,yt=4/3*(Math.SQRT2-1)*R;return Ri(s+j,v),pn(s+j,v-yt,s+ut,v-R,s,v-R),pn(s-ut,v-R,s-j,v-yt,s-j,v),pn(s-j,v+yt,s-ut,v+R,s,v+R),pn(s+ut,v+R,s+j,v+yt,s+j,v),Di(X),this},p.__private__.circle=p.circle=function(s,v,j,R){if(isNaN(s)||isNaN(v)||isNaN(j)||!Jr(R))throw new Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(s,v,j,j,R)},p.setFont=function(s,v,j){return j&&(v=wt(v,j)),ee=Pa(s,v,{disableWarning:!1}),this};var qo=p.__private__.getFont=p.getFont=function(){return ge[Pa.apply(p,arguments)]};p.__private__.getFontList=p.getFontList=function(){var s,v,j={};for(s in Le)if(Le.hasOwnProperty(s))for(v in j[s]=[],Le[s])Le[s].hasOwnProperty(v)&&j[s].push(v);return j},p.addFont=function(s,v,j,R,X){var ut=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&ut.indexOf(arguments[3])!==-1?X=arguments[3]:arguments[3]&&ut.indexOf(arguments[3])==-1&&(j=wt(j,R)),X=X||"Identity-H",Mi.call(this,s,v,j,X)};var Rn,Ti=n.lineWidth||.200025,fi=p.__private__.getLineWidth=p.getLineWidth=function(){return Ti},ja=p.__private__.setLineWidth=p.setLineWidth=function(s){return Ti=s,E(tt(k(s))+" w"),this};p.__private__.setLineDash=Tt.API.setLineDash=Tt.API.setLineDashPattern=function(s,v){if(s=s||[],v=v||0,isNaN(v)||!Array.isArray(s))throw new Error("Invalid arguments passed to jsPDF.setLineDash");return s=s.map(function(j){return tt(k(j))}).join(" "),v=tt(k(v)),E("["+s+"] "+v+" d"),this};var Oa=p.__private__.getLineHeight=p.getLineHeight=function(){return xt*Rn};p.__private__.getLineHeight=p.getLineHeight=function(){return xt*Rn};var Ba=p.__private__.setLineHeightFactor=p.setLineHeightFactor=function(s){return typeof(s=s||1.15)=="number"&&(Rn=s),this},Ma=p.__private__.getLineHeightFactor=p.getLineHeightFactor=function(){return Rn};Ba(n.lineHeight);var Yr=p.__private__.getHorizontalCoordinate=function(s){return k(s)},an=p.__private__.getVerticalCoordinate=function(s){return Y===B.ADVANCED?s:Jt[C].mediaBox.topRightY-Jt[C].mediaBox.bottomLeftY-k(s)},Do=p.__private__.getHorizontalCoordinateString=p.getHorizontalCoordinateString=function(s){return tt(Yr(s))},gn=p.__private__.getVerticalCoordinateString=p.getVerticalCoordinateString=function(s){return tt(an(s))},jr=n.strokeColor||"0 G";p.__private__.getStrokeColor=p.getDrawColor=function(){return Fr(jr)},p.__private__.setStrokeColor=p.setDrawColor=function(s,v,j,R){return jr=Ir({ch1:s,ch2:v,ch3:j,ch4:R,pdfColorType:"draw",precision:2}),E(jr),this};var zi=n.fillColor||"0 g";p.__private__.getFillColor=p.getFillColor=function(){return Fr(zi)},p.__private__.setFillColor=p.setFillColor=function(s,v,j,R){return zi=Ir({ch1:s,ch2:v,ch3:j,ch4:R,pdfColorType:"fill",precision:2}),E(zi),this};var Tn=n.textColor||"0 g",Ro=p.__private__.getTextColor=p.getTextColor=function(){return Fr(Tn)};p.__private__.setTextColor=p.setTextColor=function(s,v,j,R){return Tn=Ir({ch1:s,ch2:v,ch3:j,ch4:R,pdfColorType:"text",precision:3}),this};var di=n.charSpace,To=p.__private__.getCharSpace=p.getCharSpace=function(){return parseFloat(di||0)};p.__private__.setCharSpace=p.setCharSpace=function(s){if(isNaN(s))throw new Error("Invalid argument passed to jsPDF.setCharSpace");return di=s,this};var Ui=0;p.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},p.__private__.setLineCap=p.setLineCap=function(s){var v=p.CapJoinStyles[s];if(v===void 0)throw new Error("Line cap style of '"+s+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Ui=v,E(v+" J"),this};var Hi=0;p.__private__.setLineJoin=p.setLineJoin=function(s){var v=p.CapJoinStyles[s];if(v===void 0)throw new Error("Line join style of '"+s+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return Hi=v,E(v+" j"),this},p.__private__.setLineMiterLimit=p.__private__.setMiterLimit=p.setLineMiterLimit=p.setMiterLimit=function(s){if(s=s||0,isNaN(s))throw new Error("Invalid argument passed to jsPDF.setLineMiterLimit");return E(tt(k(s))+" M"),this},p.GState=ba,p.setGState=function(s){(s=typeof s=="string"?Ae[_r[s]]:Ea(null,s)).equals(en)||(E("/"+s.id+" gs"),en=s)};var Ea=function(s,v){if(!s||!_r[s]){var j=!1;for(var R in Ae)if(Ae.hasOwnProperty(R)&&Ae[R].equals(v)){j=!0;break}if(j)v=Ae[R];else{var X="GS"+(Object.keys(Ae).length+1).toString(10);Ae[X]=v,v.id=X}return s&&(_r[s]=v.id),le.publish("addGState",v),v}};p.addGState=function(s,v){return Ea(s,v),this},p.saveGraphicsState=function(){return E("q"),zr.push({key:ee,size:xt,color:Tn}),this},p.restoreGraphicsState=function(){E("Q");var s=zr.pop();return ee=s.key,xt=s.size,Tn=s.color,en=null,this},p.setCurrentTransformationMatrix=function(s){return E(s.toString()+" cm"),this},p.comment=function(s){return E("#"+s),this};var pi=function(s,v){var j=s||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return j},set:function(ut){isNaN(ut)||(j=parseFloat(ut))}});var R=v||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return R},set:function(ut){isNaN(ut)||(R=parseFloat(ut))}});var X="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return X},set:function(ut){X=ut.toString()}}),this},Wi=function(s,v,j,R){pi.call(this,s,v),this.type="rect";var X=j||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return X},set:function(yt){isNaN(yt)||(X=parseFloat(yt))}});var ut=R||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return ut},set:function(yt){isNaN(yt)||(ut=parseFloat(yt))}}),this},Vi=function(){this.page=xe,this.currentPage=C,this.pages=Nt.slice(0),this.pagesContext=Jt.slice(0),this.x=Je,this.y=oe,this.matrix=Sr,this.width=zn(C),this.height=mn(C),this.outputDestination=_t,this.id="",this.objectNumber=-1};Vi.prototype.restore=function(){xe=this.page,C=this.currentPage,Jt=this.pagesContext,Nt=this.pages,Je=this.x,oe=this.y,Sr=this.matrix,Gi(C,this.width),Ji(C,this.height),_t=this.outputDestination};var qa=function(s,v,j,R,X){Hr.push(new Vi),xe=C=0,Nt=[],Je=s,oe=v,Sr=X,Ei([j,R])},zo=function(s){if(Ur[s])Hr.pop().restore();else{var v=new Vi,j="Xo"+(Object.keys(We).length+1).toString(10);v.id=j,Ur[s]=j,We[j]=v,le.publish("addFormObject",v),Hr.pop().restore()}};for(var gi in p.beginFormObject=function(s,v,j,R,X){return qa(s,v,j,R,X),this},p.endFormObject=function(s){return zo(s),this},p.doFormObject=function(s,v){var j=We[Ur[s]];return E("q"),E(v.toString()+" cm"),E("/"+j.id+" Do"),E("Q"),this},p.getFormObject=function(s){var v=We[Ur[s]];return{x:v.x,y:v.y,width:v.width,height:v.height,matrix:v.matrix}},p.save=function(s,v){return s=s||"generated.pdf",(v=v||{}).returnPromise=v.returnPromise||!1,v.returnPromise===!1?(Yn(li(nn()),s),typeof Yn.unload=="function"&&Ht.setTimeout&&setTimeout(Yn.unload,911),this):new Promise(function(j,R){try{var X=Yn(li(nn()),s);typeof Yn.unload=="function"&&Ht.setTimeout&&setTimeout(Yn.unload,911),j(X)}catch(ut){R(ut.message)}})},Tt.API)Tt.API.hasOwnProperty(gi)&&(gi==="events"&&Tt.API.events.length?function(s,v){var j,R,X;for(X=v.length-1;X!==-1;X--)j=v[X][0],R=v[X][1],s.subscribe.apply(s,[j].concat(typeof R=="function"?[R]:R))}(le,Tt.API.events):p[gi]=Tt.API[gi]);var zn=p.getPageWidth=function(s){return(Jt[s=s||C].mediaBox.topRightX-Jt[s].mediaBox.bottomLeftX)/Ct},Gi=p.setPageWidth=function(s,v){Jt[s].mediaBox.topRightX=v*Ct+Jt[s].mediaBox.bottomLeftX},mn=p.getPageHeight=function(s){return(Jt[s=s||C].mediaBox.topRightY-Jt[s].mediaBox.bottomLeftY)/Ct},Ji=p.setPageHeight=function(s,v){Jt[s].mediaBox.topRightY=v*Ct+Jt[s].mediaBox.bottomLeftY};return p.internal={pdfEscape:ar,getStyle:Ia,getFont:qo,getFontSize:kt,getCharSpace:To,getTextColor:Ro,getLineHeight:Oa,getLineHeightFactor:Ma,getLineWidth:fi,write:Kt,getHorizontalCoordinate:Yr,getVerticalCoordinate:an,getCoordinateString:Do,getVerticalCoordinateString:gn,collections:{},newObject:De,newAdditionalObject:ei,newObjectDeferred:Be,newObjectDeferredBegin:hr,getFilters:Vr,putStream:vr,events:le,scaleFactor:Ct,pageSize:{getWidth:function(){return zn(C)},setWidth:function(s){Gi(C,s)},getHeight:function(){return mn(C)},setHeight:function(s){Ji(C,s)}},encryptionOptions:y,encryption:Ye,getEncryptor:Co,output:ci,getNumberOfPages:_o,pages:Nt,out:E,f2:pt,f3:P,getPageInfo:Fa,getPageInfoByObjId:Vt,getCurrentPageInfo:jo,getPDFVersion:F,Point:pi,Rectangle:Wi,Matrix:zt,hasHotfix:ka},Object.defineProperty(p.internal.pageSize,"width",{get:function(){return zn(C)},set:function(s){Gi(C,s)},enumerable:!0,configurable:!0}),Object.defineProperty(p.internal.pageSize,"height",{get:function(){return mn(C)},set:function(s){Ji(C,s)},enumerable:!0,configurable:!0}),xo.call(p,Lt),ee="F1",Sa(u,r),le.publish("initialized"),p}ji.prototype.lsbFirstWord=function(n){return String.fromCharCode(n>>0&255,n>>8&255,n>>16&255,n>>24&255)},ji.prototype.toHexString=function(n){return n.split("").map(function(e){return("0"+(255&e.charCodeAt(0)).toString(16)).slice(-2)}).join("")},ji.prototype.hexToBytes=function(n){for(var e=[],r=0;r<n.length;r+=2)e.push(String.fromCharCode(parseInt(n.substr(r,2),16)));return e.join("")},ji.prototype.processOwnerPassword=function(n,e){return As(Ns(e).substr(0,5),n)},ji.prototype.encryptor=function(n,e){var r=Ns(this.encryptionKey+String.fromCharCode(255&n,n>>8&255,n>>16&255,255&e,e>>8&255)).substr(0,10);return function(a){return As(r,a)}},ba.prototype.equals=function(n){var e,r="id,objectNumber,equals";if(!n||fe(n)!==fe(this))return!1;var a=0;for(e in this)if(!(r.indexOf(e)>=0)){if(this.hasOwnProperty(e)&&!n.hasOwnProperty(e)||this[e]!==n[e])return!1;a++}for(e in n)n.hasOwnProperty(e)&&r.indexOf(e)<0&&a--;return a===0},Tt.API={events:[]},Tt.version="3.0.1";var Pe=Tt.API,Fs=1,ti=function(n){return n.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},Ii=function(n){return n.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},Xt=function(n){return n.toFixed(2)},Fn=function(n){return n.toFixed(5)};Pe.__acroform__={};var cr=function(n,e){n.prototype=Object.create(e.prototype),n.prototype.constructor=n},cu=function(n){return n*Fs},Zr=function(n){var e=new Eu,r=Mt.internal.getHeight(n)||0,a=Mt.internal.getWidth(n)||0;return e.BBox=[0,0,Number(Xt(a)),Number(Xt(r))],e},Gl=Pe.__acroform__.setBit=function(n,e){if(n=n||0,e=e||0,isNaN(n)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return n|=1<<e},Jl=Pe.__acroform__.clearBit=function(n,e){if(n=n||0,e=e||0,isNaN(n)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return n&=~(1<<e)},Yl=Pe.__acroform__.getBit=function(n,e){if(isNaN(n)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return(n&1<<e)==0?0:1},Ce=Pe.__acroform__.getBitForPdf=function(n,e){if(isNaN(n)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return Yl(n,e-1)},je=Pe.__acroform__.setBitForPdf=function(n,e){if(isNaN(n)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return Gl(n,e-1)},Oe=Pe.__acroform__.clearBitForPdf=function(n,e){if(isNaN(n)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return Jl(n,e-1)},Xl=Pe.__acroform__.calculateCoordinates=function(n,e){var r=e.internal.getHorizontalCoordinate,a=e.internal.getVerticalCoordinate,u=n[0],o=n[1],c=n[2],h=n[3],f={};return f.lowerLeft_X=r(u)||0,f.lowerLeft_Y=a(o+h)||0,f.upperRight_X=r(u+c)||0,f.upperRight_Y=a(o)||0,[Number(Xt(f.lowerLeft_X)),Number(Xt(f.lowerLeft_Y)),Number(Xt(f.upperRight_X)),Number(Xt(f.upperRight_Y))]},Kl=function(n){if(n.appearanceStreamContent)return n.appearanceStreamContent;if(n.V||n.DV){var e=[],r=n._V||n.DV,a=xs(n,r),u=n.scope.internal.getFont(n.fontName,n.fontStyle).id;e.push("/Tx BMC"),e.push("q"),e.push("BT"),e.push(n.scope.__private__.encodeColorString(n.color)),e.push("/"+u+" "+Xt(a.fontSize)+" Tf"),e.push("1 0 0 1 0 0 Tm"),e.push(a.text),e.push("ET"),e.push("Q"),e.push("EMC");var o=Zr(n);return o.scope=n.scope,o.stream=e.join(`
`),o}},xs=function(n,e){var r=n.fontSize===0?n.maxFontSize:n.fontSize,a={text:"",fontSize:""},u=(e=(e=e.substr(0,1)=="("?e.substr(1):e).substr(e.length-1)==")"?e.substr(0,e.length-1):e).split(" ");u=n.multiline?u.map(function(P){return P.split(`
`)}):u.map(function(P){return[P]});var o=r,c=Mt.internal.getHeight(n)||0;c=c<0?-c:c;var h=Mt.internal.getWidth(n)||0;h=h<0?-h:h;var f=function(P,k,W){if(P+1<u.length){var D=k+" "+u[P+1][0];return co(D,n,W).width<=h-4}return!1};o++;t:for(;o>0;){e="",o--;var g,y,w=co("3",n,o).height,S=n.multiline?c-o:(c-w)/2,p=S+=2,O=0,F=0,q=0;if(o<=0){e=`(...) Tj
`,e+="% Width of Text: "+co(e,n,o=12).width+", FieldWidth:"+h+`
`;break}for(var _="",B=0,Y=0;Y<u.length;Y++)if(u.hasOwnProperty(Y)){var ot=!1;if(u[Y].length!==1&&q!==u[Y].length-1){if((w+2)*(B+2)+2>c)continue t;_+=u[Y][q],ot=!0,F=Y,Y--}else{_=(_+=u[Y][q]+" ").substr(_.length-1)==" "?_.substr(0,_.length-1):_;var ct=parseInt(Y),wt=f(ct,_,o),tt=Y>=u.length-1;if(wt&&!tt){_+=" ",q=0;continue}if(wt||tt){if(tt)F=ct;else if(n.multiline&&(w+2)*(B+2)+2>c)continue t}else{if(!n.multiline||(w+2)*(B+2)+2>c)continue t;F=ct}}for(var z="",nt=O;nt<=F;nt++){var pt=u[nt];if(n.multiline){if(nt===F){z+=pt[q]+" ",q=(q+1)%pt.length;continue}if(nt===O){z+=pt[pt.length-1]+" ";continue}}z+=pt[0]+" "}switch(z=z.substr(z.length-1)==" "?z.substr(0,z.length-1):z,y=co(z,n,o).width,n.textAlign){case"right":g=h-y-2;break;case"center":g=(h-y)/2;break;case"left":default:g=2}e+=Xt(g)+" "+Xt(p)+` Td
`,e+="("+ti(z)+`) Tj
`,e+=-Xt(g)+` 0 Td
`,p=-(o+2),y=0,O=ot?F:F+1,B++,_=""}break}return a.text=e,a.fontSize=o,a},co=function(n,e,r){var a=e.scope.internal.getFont(e.fontName,e.fontStyle),u=e.scope.getStringUnitWidth(n,{font:a,fontSize:parseFloat(r),charSpace:0})*parseFloat(r);return{height:e.scope.getStringUnitWidth("3",{font:a,fontSize:parseFloat(r),charSpace:0})*parseFloat(r)*1.5,width:u}},Zl={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},$l=function(n,e){var r={type:"reference",object:n};e.internal.getPageInfo(n.page).pageContext.annotations.find(function(a){return a.type===r.type&&a.object===r.object})===void 0&&e.internal.getPageInfo(n.page).pageContext.annotations.push(r)},Ql=function(n,e){for(var r in n)if(n.hasOwnProperty(r)){var a=r,u=n[r];e.internal.newObjectDeferredBegin(u.objId,!0),fe(u)==="object"&&typeof u.putStream=="function"&&u.putStream(),delete n[a]}},tc=function(n,e){if(e.scope=n,n.internal!==void 0&&(n.internal.acroformPlugin===void 0||n.internal.acroformPlugin.isInitialized===!1)){if(Rr.FieldNum=0,n.internal.acroformPlugin=JSON.parse(JSON.stringify(Zl)),n.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("Exception while creating AcroformDictionary");Fs=n.internal.scaleFactor,n.internal.acroformPlugin.acroFormDictionaryRoot=new qu,n.internal.acroformPlugin.acroFormDictionaryRoot.scope=n,n.internal.acroformPlugin.acroFormDictionaryRoot._eventID=n.internal.events.subscribe("postPutResources",function(){(function(r){r.internal.events.unsubscribe(r.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete r.internal.acroformPlugin.acroFormDictionaryRoot._eventID,r.internal.acroformPlugin.printedOut=!0})(n)}),n.internal.events.subscribe("buildDocument",function(){(function(r){r.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var a=r.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var u in a)if(a.hasOwnProperty(u)){var o=a[u];o.objId=void 0,o.hasAnnotation&&$l(o,r)}})(n)}),n.internal.events.subscribe("putCatalog",function(){(function(r){if(r.internal.acroformPlugin.acroFormDictionaryRoot===void 0)throw new Error("putCatalogCallback: Root missing.");r.internal.write("/AcroForm "+r.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")})(n)}),n.internal.events.subscribe("postPutPages",function(r){(function(a,u){var o=!a;for(var c in a||(u.internal.newObjectDeferredBegin(u.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),u.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),a=a||u.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(a.hasOwnProperty(c)){var h=a[c],f=[],g=h.Rect;if(h.Rect&&(h.Rect=Xl(h.Rect,u)),u.internal.newObjectDeferredBegin(h.objId,!0),h.DA=Mt.createDefaultAppearanceStream(h),fe(h)==="object"&&typeof h.getKeyValueListForStream=="function"&&(f=h.getKeyValueListForStream()),h.Rect=g,h.hasAppearanceStream&&!h.appearanceStreamContent){var y=Kl(h);f.push({key:"AP",value:"<</N "+y+">>"}),u.internal.acroformPlugin.xForms.push(y)}if(h.appearanceStreamContent){var w="";for(var S in h.appearanceStreamContent)if(h.appearanceStreamContent.hasOwnProperty(S)){var p=h.appearanceStreamContent[S];if(w+="/"+S+" ",w+="<<",Object.keys(p).length>=1||Array.isArray(p)){for(var c in p)if(p.hasOwnProperty(c)){var O=p[c];typeof O=="function"&&(O=O.call(u,h)),w+="/"+c+" "+O+" ",u.internal.acroformPlugin.xForms.indexOf(O)>=0||u.internal.acroformPlugin.xForms.push(O)}}else typeof(O=p)=="function"&&(O=O.call(u,h)),w+="/"+c+" "+O,u.internal.acroformPlugin.xForms.indexOf(O)>=0||u.internal.acroformPlugin.xForms.push(O);w+=">>"}f.push({key:"AP",value:`<<
`+w+">>"})}u.internal.putStream({additionalKeyValues:f,objectId:h.objId}),u.internal.out("endobj")}o&&Ql(u.internal.acroformPlugin.xForms,u)})(r,n)}),n.internal.acroformPlugin.isInitialized=!0}},Mu=Pe.__acroform__.arrayToPdfArray=function(n,e,r){var a=function(c){return c};if(Array.isArray(n)){for(var u="[",o=0;o<n.length;o++)switch(o!==0&&(u+=" "),fe(n[o])){case"boolean":case"number":case"object":u+=n[o].toString();break;case"string":n[o].substr(0,1)!=="/"?(e!==void 0&&r&&(a=r.internal.getEncryptor(e)),u+="("+ti(a(n[o].toString()))+")"):u+=n[o].toString()}return u+="]"}throw new Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},fs=function(n,e,r){var a=function(u){return u};return e!==void 0&&r&&(a=r.internal.getEncryptor(e)),(n=n||"").toString(),n="("+ti(a(n))+")"},$r=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(this._objId===void 0){if(this.scope===void 0)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(n){this._objId=n}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};$r.prototype.toString=function(){return this.objId+" 0 R"},$r.prototype.putStream=function(){var n=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:n,objectId:this.objId}),this.scope.internal.out("endobj")},$r.prototype.getKeyValueListForStream=function(){var n=[],e=Object.getOwnPropertyNames(this).filter(function(o){return o!="content"&&o!="appearanceStreamContent"&&o!="scope"&&o!="objId"&&o.substring(0,1)!="_"});for(var r in e)if(Object.getOwnPropertyDescriptor(this,e[r]).configurable===!1){var a=e[r],u=this[a];u&&(Array.isArray(u)?n.push({key:a,value:Mu(u,this.objId,this.scope)}):u instanceof $r?(u.scope=this.scope,n.push({key:a,value:u.objId+" 0 R"})):typeof u!="function"&&n.push({key:a,value:u}))}return n};var Eu=function(){$r.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var n,e=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return e},set:function(r){e=r}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(r){n=r.trim()},get:function(){return n||null}})};cr(Eu,$r);var qu=function(){$r.call(this);var n,e=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return e.length>0?e:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return e}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(n){var r=function(a){return a};return this.scope&&(r=this.scope.internal.getEncryptor(this.objId)),"("+ti(r(n))+")"}},set:function(r){n=r}})};cr(qu,$r);var Rr=function n(){$r.call(this);var e=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return e},set:function(_){if(isNaN(_))throw new Error('Invalid value "'+_+'" for attribute F supplied.');e=_}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return!!Ce(e,3)},set:function(_){_?this.F=je(e,3):this.F=Oe(e,3)}});var r=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return r},set:function(_){if(isNaN(_))throw new Error('Invalid value "'+_+'" for attribute Ff supplied.');r=_}});var a=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(a.length!==0)return a},set:function(_){a=_!==void 0?_:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[0])?0:a[0]},set:function(_){a[0]=_}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[1])?0:a[1]},set:function(_){a[1]=_}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[2])?0:a[2]},set:function(_){a[2]=_}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!a||isNaN(a[3])?0:a[3]},set:function(_){a[3]=_}});var u="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return u},set:function(_){switch(_){case"/Btn":case"/Tx":case"/Ch":case"/Sig":u=_;break;default:throw new Error('Invalid value "'+_+'" for attribute FT supplied.')}}});var o=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!o||o.length<1){if(this instanceof po)return;o="FieldObject"+n.FieldNum++}var _=function(B){return B};return this.scope&&(_=this.scope.internal.getEncryptor(this.objId)),"("+ti(_(o))+")"},set:function(_){o=_.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return o},set:function(_){o=_}});var c="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return c},set:function(_){c=_}});var h="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return h},set:function(_){h=_}});var f=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return f},set:function(_){f=_}});var g=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return g===void 0?50/Fs:g},set:function(_){g=_}});var y="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return y},set:function(_){y=_}});var w="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!w||this instanceof po||this instanceof jn))return fs(w,this.objId,this.scope)},set:function(_){_=_.toString(),w=_}});var S=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(S)return this instanceof Te?S:fs(S,this.objId,this.scope)},set:function(_){_=_.toString(),S=this instanceof Te?_:_.substr(0,1)==="("?Ii(_.substr(1,_.length-2)):Ii(_)}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof Te?Ii(S.substr(1,S.length-1)):S},set:function(_){_=_.toString(),S=this instanceof Te?"/"+_:_}});var p=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(p)return p},set:function(_){this.V=_}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(p)return this instanceof Te?p:fs(p,this.objId,this.scope)},set:function(_){_=_.toString(),p=this instanceof Te?_:_.substr(0,1)==="("?Ii(_.substr(1,_.length-2)):Ii(_)}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof Te?Ii(p.substr(1,p.length-1)):p},set:function(_){_=_.toString(),p=this instanceof Te?"/"+_:_}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var O,F=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return F},set:function(_){_=!!_,F=_}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(O)return O},set:function(_){O=_}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,1)},set:function(_){_?this.Ff=je(this.Ff,1):this.Ff=Oe(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,2)},set:function(_){_?this.Ff=je(this.Ff,2):this.Ff=Oe(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,3)},set:function(_){_?this.Ff=je(this.Ff,3):this.Ff=Oe(this.Ff,3)}});var q=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(q!==null)return q},set:function(_){if([0,1,2].indexOf(_)===-1)throw new Error('Invalid value "'+_+'" for attribute Q supplied.');q=_}}),Object.defineProperty(this,"textAlign",{get:function(){var _;switch(q){case 0:default:_="left";break;case 1:_="center";break;case 2:_="right"}return _},configurable:!0,enumerable:!0,set:function(_){switch(_){case"right":case 2:q=2;break;case"center":case 1:q=1;break;case"left":case 0:default:q=0}}})};cr(Rr,$r);var Kn=function(){Rr.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var n=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return n},set:function(r){n=r}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return n},set:function(r){n=r}});var e=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return Mu(e,this.objId,this.scope)},set:function(r){var a,u;u=[],typeof(a=r)=="string"&&(u=function(o,c,h){h||(h=1);for(var f,g=[];f=c.exec(o);)g.push(f[h]);return g}(a,/\((.*?)\)/g)),e=u}}),this.getOptions=function(){return e},this.setOptions=function(r){e=r,this.sort&&e.sort()},this.addOption=function(r){r=(r=r||"").toString(),e.push(r),this.sort&&e.sort()},this.removeOption=function(r,a){for(a=a||!1,r=(r=r||"").toString();e.indexOf(r)!==-1&&(e.splice(e.indexOf(r),1),a!==!1););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,18)},set:function(r){r?this.Ff=je(this.Ff,18):this.Ff=Oe(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,19)},set:function(r){this.combo===!0&&(r?this.Ff=je(this.Ff,19):this.Ff=Oe(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,20)},set:function(r){r?(this.Ff=je(this.Ff,20),e.sort()):this.Ff=Oe(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,22)},set:function(r){r?this.Ff=je(this.Ff,22):this.Ff=Oe(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,23)},set:function(r){r?this.Ff=je(this.Ff,23):this.Ff=Oe(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,27)},set:function(r){r?this.Ff=je(this.Ff,27):this.Ff=Oe(this.Ff,27)}}),this.hasAppearanceStream=!1};cr(Kn,Rr);var Zn=function(){Kn.call(this),this.fontName="helvetica",this.combo=!1};cr(Zn,Kn);var $n=function(){Zn.call(this),this.combo=!0};cr($n,Zn);var da=function(){$n.call(this),this.edit=!0};cr(da,$n);var Te=function(){Rr.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,15)},set:function(r){r?this.Ff=je(this.Ff,15):this.Ff=Oe(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,16)},set:function(r){r?this.Ff=je(this.Ff,16):this.Ff=Oe(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,17)},set:function(r){r?this.Ff=je(this.Ff,17):this.Ff=Oe(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,26)},set:function(r){r?this.Ff=je(this.Ff,26):this.Ff=Oe(this.Ff,26)}});var n,e={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var r=function(o){return o};if(this.scope&&(r=this.scope.internal.getEncryptor(this.objId)),Object.keys(e).length!==0){var a,u=[];for(a in u.push("<<"),e)u.push("/"+a+" ("+ti(r(e[a]))+")");return u.push(">>"),u.join(`
`)}},set:function(r){fe(r)==="object"&&(e=r)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return e.CA||""},set:function(r){typeof r=="string"&&(e.CA=r)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return n},set:function(r){n=r}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return n.substr(1,n.length-1)},set:function(r){n="/"+r}})};cr(Te,Rr);var pa=function(){Te.call(this),this.pushButton=!0};cr(pa,Te);var Qn=function(){Te.call(this),this.radio=!0,this.pushButton=!1;var n=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return n},set:function(e){n=e!==void 0?e:[]}})};cr(Qn,Te);var po=function(){var n,e;Rr.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return n},set:function(u){n=u}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return e},set:function(u){e=u}});var r,a={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var u=function(h){return h};this.scope&&(u=this.scope.internal.getEncryptor(this.objId));var o,c=[];for(o in c.push("<<"),a)c.push("/"+o+" ("+ti(u(a[o]))+")");return c.push(">>"),c.join(`
`)},set:function(u){fe(u)==="object"&&(a=u)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return a.CA||""},set:function(u){typeof u=="string"&&(a.CA=u)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return r},set:function(u){r=u}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return r.substr(1,r.length-1)},set:function(u){r="/"+u}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=Mt.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};cr(po,Rr),Qn.prototype.setAppearance=function(n){if(!("createAppearanceStream"in n)||!("getCA"in n))throw new Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var e in this.Kids)if(this.Kids.hasOwnProperty(e)){var r=this.Kids[e];r.appearanceStreamContent=n.createAppearanceStream(r.optionName),r.caption=n.getCA()}},Qn.prototype.createOption=function(n){var e=new po;return e.Parent=this,e.optionName=n,this.Kids.push(e),ec.call(this.scope,e),e};var ga=function(){Te.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=Mt.CheckBox.createAppearanceStream()};cr(ga,Te);var jn=function(){Rr.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,13)},set:function(e){e?this.Ff=je(this.Ff,13):this.Ff=Oe(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,21)},set:function(e){e?this.Ff=je(this.Ff,21):this.Ff=Oe(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,23)},set:function(e){e?this.Ff=je(this.Ff,23):this.Ff=Oe(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,24)},set:function(e){e?this.Ff=je(this.Ff,24):this.Ff=Oe(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,25)},set:function(e){e?this.Ff=je(this.Ff,25):this.Ff=Oe(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,26)},set:function(e){e?this.Ff=je(this.Ff,26):this.Ff=Oe(this.Ff,26)}});var n=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return n},set:function(e){n=e}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return n},set:function(e){Number.isInteger(e)&&(n=e)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};cr(jn,Rr);var ma=function(){jn.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return!!Ce(this.Ff,14)},set:function(n){n?this.Ff=je(this.Ff,14):this.Ff=Oe(this.Ff,14)}}),this.password=!0};cr(ma,jn);var Mt={CheckBox:{createAppearanceStream:function(){return{N:{On:Mt.CheckBox.YesNormal},D:{On:Mt.CheckBox.YesPushDown,Off:Mt.CheckBox.OffPushDown}}},YesPushDown:function(n){var e=Zr(n);e.scope=n.scope;var r=[],a=n.scope.internal.getFont(n.fontName,n.fontStyle).id,u=n.scope.__private__.encodeColorString(n.color),o=xs(n,n.caption);return r.push("0.749023 g"),r.push("0 0 "+Xt(Mt.internal.getWidth(n))+" "+Xt(Mt.internal.getHeight(n))+" re"),r.push("f"),r.push("BMC"),r.push("q"),r.push("0 0 1 rg"),r.push("/"+a+" "+Xt(o.fontSize)+" Tf "+u),r.push("BT"),r.push(o.text),r.push("ET"),r.push("Q"),r.push("EMC"),e.stream=r.join(`
`),e},YesNormal:function(n){var e=Zr(n);e.scope=n.scope;var r=n.scope.internal.getFont(n.fontName,n.fontStyle).id,a=n.scope.__private__.encodeColorString(n.color),u=[],o=Mt.internal.getHeight(n),c=Mt.internal.getWidth(n),h=xs(n,n.caption);return u.push("1 g"),u.push("0 0 "+Xt(c)+" "+Xt(o)+" re"),u.push("f"),u.push("q"),u.push("0 0 1 rg"),u.push("0 0 "+Xt(c-1)+" "+Xt(o-1)+" re"),u.push("W"),u.push("n"),u.push("0 g"),u.push("BT"),u.push("/"+r+" "+Xt(h.fontSize)+" Tf "+a),u.push(h.text),u.push("ET"),u.push("Q"),e.stream=u.join(`
`),e},OffPushDown:function(n){var e=Zr(n);e.scope=n.scope;var r=[];return r.push("0.749023 g"),r.push("0 0 "+Xt(Mt.internal.getWidth(n))+" "+Xt(Mt.internal.getHeight(n))+" re"),r.push("f"),e.stream=r.join(`
`),e}},RadioButton:{Circle:{createAppearanceStream:function(n){var e={D:{Off:Mt.RadioButton.Circle.OffPushDown},N:{}};return e.N[n]=Mt.RadioButton.Circle.YesNormal,e.D[n]=Mt.RadioButton.Circle.YesPushDown,e},getCA:function(){return"l"},YesNormal:function(n){var e=Zr(n);e.scope=n.scope;var r=[],a=Mt.internal.getWidth(n)<=Mt.internal.getHeight(n)?Mt.internal.getWidth(n)/4:Mt.internal.getHeight(n)/4;a=Number((.9*a).toFixed(5));var u=Mt.internal.Bezier_C,o=Number((a*u).toFixed(5));return r.push("q"),r.push("1 0 0 1 "+Fn(Mt.internal.getWidth(n)/2)+" "+Fn(Mt.internal.getHeight(n)/2)+" cm"),r.push(a+" 0 m"),r.push(a+" "+o+" "+o+" "+a+" 0 "+a+" c"),r.push("-"+o+" "+a+" -"+a+" "+o+" -"+a+" 0 c"),r.push("-"+a+" -"+o+" -"+o+" -"+a+" 0 -"+a+" c"),r.push(o+" -"+a+" "+a+" -"+o+" "+a+" 0 c"),r.push("f"),r.push("Q"),e.stream=r.join(`
`),e},YesPushDown:function(n){var e=Zr(n);e.scope=n.scope;var r=[],a=Mt.internal.getWidth(n)<=Mt.internal.getHeight(n)?Mt.internal.getWidth(n)/4:Mt.internal.getHeight(n)/4;a=Number((.9*a).toFixed(5));var u=Number((2*a).toFixed(5)),o=Number((u*Mt.internal.Bezier_C).toFixed(5)),c=Number((a*Mt.internal.Bezier_C).toFixed(5));return r.push("0.749023 g"),r.push("q"),r.push("1 0 0 1 "+Fn(Mt.internal.getWidth(n)/2)+" "+Fn(Mt.internal.getHeight(n)/2)+" cm"),r.push(u+" 0 m"),r.push(u+" "+o+" "+o+" "+u+" 0 "+u+" c"),r.push("-"+o+" "+u+" -"+u+" "+o+" -"+u+" 0 c"),r.push("-"+u+" -"+o+" -"+o+" -"+u+" 0 -"+u+" c"),r.push(o+" -"+u+" "+u+" -"+o+" "+u+" 0 c"),r.push("f"),r.push("Q"),r.push("0 g"),r.push("q"),r.push("1 0 0 1 "+Fn(Mt.internal.getWidth(n)/2)+" "+Fn(Mt.internal.getHeight(n)/2)+" cm"),r.push(a+" 0 m"),r.push(a+" "+c+" "+c+" "+a+" 0 "+a+" c"),r.push("-"+c+" "+a+" -"+a+" "+c+" -"+a+" 0 c"),r.push("-"+a+" -"+c+" -"+c+" -"+a+" 0 -"+a+" c"),r.push(c+" -"+a+" "+a+" -"+c+" "+a+" 0 c"),r.push("f"),r.push("Q"),e.stream=r.join(`
`),e},OffPushDown:function(n){var e=Zr(n);e.scope=n.scope;var r=[],a=Mt.internal.getWidth(n)<=Mt.internal.getHeight(n)?Mt.internal.getWidth(n)/4:Mt.internal.getHeight(n)/4;a=Number((.9*a).toFixed(5));var u=Number((2*a).toFixed(5)),o=Number((u*Mt.internal.Bezier_C).toFixed(5));return r.push("0.749023 g"),r.push("q"),r.push("1 0 0 1 "+Fn(Mt.internal.getWidth(n)/2)+" "+Fn(Mt.internal.getHeight(n)/2)+" cm"),r.push(u+" 0 m"),r.push(u+" "+o+" "+o+" "+u+" 0 "+u+" c"),r.push("-"+o+" "+u+" -"+u+" "+o+" -"+u+" 0 c"),r.push("-"+u+" -"+o+" -"+o+" -"+u+" 0 -"+u+" c"),r.push(o+" -"+u+" "+u+" -"+o+" "+u+" 0 c"),r.push("f"),r.push("Q"),e.stream=r.join(`
`),e}},Cross:{createAppearanceStream:function(n){var e={D:{Off:Mt.RadioButton.Cross.OffPushDown},N:{}};return e.N[n]=Mt.RadioButton.Cross.YesNormal,e.D[n]=Mt.RadioButton.Cross.YesPushDown,e},getCA:function(){return"8"},YesNormal:function(n){var e=Zr(n);e.scope=n.scope;var r=[],a=Mt.internal.calculateCross(n);return r.push("q"),r.push("1 1 "+Xt(Mt.internal.getWidth(n)-2)+" "+Xt(Mt.internal.getHeight(n)-2)+" re"),r.push("W"),r.push("n"),r.push(Xt(a.x1.x)+" "+Xt(a.x1.y)+" m"),r.push(Xt(a.x2.x)+" "+Xt(a.x2.y)+" l"),r.push(Xt(a.x4.x)+" "+Xt(a.x4.y)+" m"),r.push(Xt(a.x3.x)+" "+Xt(a.x3.y)+" l"),r.push("s"),r.push("Q"),e.stream=r.join(`
`),e},YesPushDown:function(n){var e=Zr(n);e.scope=n.scope;var r=Mt.internal.calculateCross(n),a=[];return a.push("0.749023 g"),a.push("0 0 "+Xt(Mt.internal.getWidth(n))+" "+Xt(Mt.internal.getHeight(n))+" re"),a.push("f"),a.push("q"),a.push("1 1 "+Xt(Mt.internal.getWidth(n)-2)+" "+Xt(Mt.internal.getHeight(n)-2)+" re"),a.push("W"),a.push("n"),a.push(Xt(r.x1.x)+" "+Xt(r.x1.y)+" m"),a.push(Xt(r.x2.x)+" "+Xt(r.x2.y)+" l"),a.push(Xt(r.x4.x)+" "+Xt(r.x4.y)+" m"),a.push(Xt(r.x3.x)+" "+Xt(r.x3.y)+" l"),a.push("s"),a.push("Q"),e.stream=a.join(`
`),e},OffPushDown:function(n){var e=Zr(n);e.scope=n.scope;var r=[];return r.push("0.749023 g"),r.push("0 0 "+Xt(Mt.internal.getWidth(n))+" "+Xt(Mt.internal.getHeight(n))+" re"),r.push("f"),e.stream=r.join(`
`),e}}},createDefaultAppearanceStream:function(n){var e=n.scope.internal.getFont(n.fontName,n.fontStyle).id,r=n.scope.__private__.encodeColorString(n.color);return"/"+e+" "+n.fontSize+" Tf "+r}};Mt.internal={Bezier_C:.551915024494,calculateCross:function(n){var e=Mt.internal.getWidth(n),r=Mt.internal.getHeight(n),a=Math.min(e,r);return{x1:{x:(e-a)/2,y:(r-a)/2+a},x2:{x:(e-a)/2+a,y:(r-a)/2},x3:{x:(e-a)/2,y:(r-a)/2},x4:{x:(e-a)/2+a,y:(r-a)/2+a}}}},Mt.internal.getWidth=function(n){var e=0;return fe(n)==="object"&&(e=cu(n.Rect[2])),e},Mt.internal.getHeight=function(n){var e=0;return fe(n)==="object"&&(e=cu(n.Rect[3])),e};var ec=Pe.addField=function(n){if(tc(this,n),!(n instanceof Rr))throw new Error("Invalid argument passed to jsPDF.addField.");var e;return(e=n).scope.internal.acroformPlugin.printedOut&&(e.scope.internal.acroformPlugin.printedOut=!1,e.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),e.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(e),n.page=n.scope.internal.getCurrentPageInfo().pageNumber,this};Pe.AcroFormChoiceField=Kn,Pe.AcroFormListBox=Zn,Pe.AcroFormComboBox=$n,Pe.AcroFormEditBox=da,Pe.AcroFormButton=Te,Pe.AcroFormPushButton=pa,Pe.AcroFormRadioButton=Qn,Pe.AcroFormCheckBox=ga,Pe.AcroFormTextField=jn,Pe.AcroFormPasswordField=ma,Pe.AcroFormAppearance=Mt,Pe.AcroForm={ChoiceField:Kn,ListBox:Zn,ComboBox:$n,EditBox:da,Button:Te,PushButton:pa,RadioButton:Qn,CheckBox:ga,TextField:jn,PasswordField:ma,Appearance:Mt},Tt.AcroForm={ChoiceField:Kn,ListBox:Zn,ComboBox:$n,EditBox:da,Button:Te,PushButton:pa,RadioButton:Qn,CheckBox:ga,TextField:jn,PasswordField:ma,Appearance:Mt};var rc=Tt.AcroForm;function Du(n){return n.reduce(function(e,r,a){return e[r]=a,e},{})}(function(n){n.__addimage__={};var e="UNKNOWN",r={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},a=n.__addimage__.getImageFileTypeByImageData=function(P,k){var W,D,st,it,ht,$=e;if((k=k||e)==="RGBA"||P.data!==void 0&&P.data instanceof Uint8ClampedArray&&"height"in P&&"width"in P)return"RGBA";if(wt(P))for(ht in r)for(st=r[ht],W=0;W<st.length;W+=1){for(it=!0,D=0;D<st[W].length;D+=1)if(st[W][D]!==void 0&&st[W][D]!==P[D]){it=!1;break}if(it===!0){$=ht;break}}else for(ht in r)for(st=r[ht],W=0;W<st.length;W+=1){for(it=!0,D=0;D<st[W].length;D+=1)if(st[W][D]!==void 0&&st[W][D]!==P.charCodeAt(D)){it=!1;break}if(it===!0){$=ht;break}}return $===e&&k!==e&&($=k),$},u=function P(k){for(var W=this.internal.write,D=this.internal.putStream,st=(0,this.internal.getFilters)();st.indexOf("FlateEncode")!==-1;)st.splice(st.indexOf("FlateEncode"),1);k.objectId=this.internal.newObject();var it=[];if(it.push({key:"Type",value:"/XObject"}),it.push({key:"Subtype",value:"/Image"}),it.push({key:"Width",value:k.width}),it.push({key:"Height",value:k.height}),k.colorSpace===q.INDEXED?it.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(k.palette.length/3-1)+" "+("sMask"in k&&k.sMask!==void 0?k.objectId+2:k.objectId+1)+" 0 R]"}):(it.push({key:"ColorSpace",value:"/"+k.colorSpace}),k.colorSpace===q.DEVICE_CMYK&&it.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),it.push({key:"BitsPerComponent",value:k.bitsPerComponent}),"decodeParameters"in k&&k.decodeParameters!==void 0&&it.push({key:"DecodeParms",value:"<<"+k.decodeParameters+">>"}),"transparency"in k&&Array.isArray(k.transparency)){for(var ht="",$=0,lt=k.transparency.length;$<lt;$++)ht+=k.transparency[$]+" "+k.transparency[$]+" ";it.push({key:"Mask",value:"["+ht+"]"})}k.sMask!==void 0&&it.push({key:"SMask",value:k.objectId+1+" 0 R"});var dt=k.filter!==void 0?["/"+k.filter]:void 0;if(D({data:k.data,additionalKeyValues:it,alreadyAppliedFilters:dt,objectId:k.objectId}),W("endobj"),"sMask"in k&&k.sMask!==void 0){var jt="/Predictor "+k.predictor+" /Colors 1 /BitsPerComponent "+k.bitsPerComponent+" /Columns "+k.width,N={width:k.width,height:k.height,colorSpace:"DeviceGray",bitsPerComponent:k.bitsPerComponent,decodeParameters:jt,data:k.sMask};"filter"in k&&(N.filter=k.filter),P.call(this,N)}if(k.colorSpace===q.INDEXED){var C=this.internal.newObject();D({data:z(new Uint8Array(k.palette)),objectId:C}),W("endobj")}},o=function(){var P=this.internal.collections.addImage_images;for(var k in P)u.call(this,P[k])},c=function(){var P,k=this.internal.collections.addImage_images,W=this.internal.write;for(var D in k)W("/I"+(P=k[D]).index,P.objectId,"0","R")},h=function(){this.internal.collections.addImage_images||(this.internal.collections.addImage_images={},this.internal.events.subscribe("putResources",o),this.internal.events.subscribe("putXobjectDict",c))},f=function(){var P=this.internal.collections.addImage_images;return h.call(this),P},g=function(){return Object.keys(this.internal.collections.addImage_images).length},y=function(P){return typeof n["process"+P.toUpperCase()]=="function"},w=function(P){return fe(P)==="object"&&P.nodeType===1},S=function(P,k){if(P.nodeName==="IMG"&&P.hasAttribute("src")){var W=""+P.getAttribute("src");if(W.indexOf("data:image/")===0)return fa(unescape(W).split("base64,").pop());var D=n.loadFile(W,!0);if(D!==void 0)return D}if(P.nodeName==="CANVAS"){if(P.width===0||P.height===0)throw new Error("Given canvas must have data. Canvas width: "+P.width+", height: "+P.height);var st;switch(k){case"PNG":st="image/png";break;case"WEBP":st="image/webp";break;case"JPEG":case"JPG":default:st="image/jpeg"}return fa(P.toDataURL(st,1).split("base64,").pop())}},p=function(P){var k=this.internal.collections.addImage_images;if(k){for(var W in k)if(P===k[W].alias)return k[W]}},O=function(P,k,W){return P||k||(P=-96,k=-96),P<0&&(P=-1*W.width*72/P/this.internal.scaleFactor),k<0&&(k=-1*W.height*72/k/this.internal.scaleFactor),P===0&&(P=k*W.width/W.height),k===0&&(k=P*W.height/W.width),[P,k]},F=function(P,k,W,D,st,it){var ht=O.call(this,W,D,st),$=this.internal.getCoordinateString,lt=this.internal.getVerticalCoordinateString,dt=f.call(this);if(W=ht[0],D=ht[1],dt[st.index]=st,it){it*=Math.PI/180;var jt=Math.cos(it),N=Math.sin(it),C=function(T){return T.toFixed(4)},M=[C(jt),C(N),C(-1*N),C(jt),0,0,"cm"]}this.internal.write("q"),it?(this.internal.write([1,"0","0",1,$(P),lt(k+D),"cm"].join(" ")),this.internal.write(M.join(" ")),this.internal.write([$(W),"0","0",$(D),"0","0","cm"].join(" "))):this.internal.write([$(W),"0","0",$(D),$(P),lt(k+D),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write([1,0,0,-1,0,0,"cm"].join(" ")),this.internal.write("/I"+st.index+" Do"),this.internal.write("Q")},q=n.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};n.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var _=n.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},B=n.__addimage__.sHashCode=function(P){var k,W,D=0;if(typeof P=="string")for(W=P.length,k=0;k<W;k++)D=(D<<5)-D+P.charCodeAt(k),D|=0;else if(wt(P))for(W=P.byteLength/2,k=0;k<W;k++)D=(D<<5)-D+P[k],D|=0;return D},Y=n.__addimage__.validateStringAsBase64=function(P){(P=P||"").toString().trim();var k=!0;return P.length===0&&(k=!1),P.length%4!=0&&(k=!1),/^[A-Za-z0-9+/]+$/.test(P.substr(0,P.length-2))===!1&&(k=!1),/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(P.substr(-2))===!1&&(k=!1),k},ot=n.__addimage__.extractImageFromDataUrl=function(P){if(P==null||!(P=P.trim()).startsWith("data:"))return null;var k=P.indexOf(",");return k<0?null:P.substring(0,k).trim().endsWith("base64")?P.substring(k+1):null},ct=n.__addimage__.supportsArrayBuffer=function(){return typeof ArrayBuffer<"u"&&typeof Uint8Array<"u"};n.__addimage__.isArrayBuffer=function(P){return ct()&&P instanceof ArrayBuffer};var wt=n.__addimage__.isArrayBufferView=function(P){return ct()&&typeof Uint32Array<"u"&&(P instanceof Int8Array||P instanceof Uint8Array||typeof Uint8ClampedArray<"u"&&P instanceof Uint8ClampedArray||P instanceof Int16Array||P instanceof Uint16Array||P instanceof Int32Array||P instanceof Uint32Array||P instanceof Float32Array||P instanceof Float64Array)},tt=n.__addimage__.binaryStringToUint8Array=function(P){for(var k=P.length,W=new Uint8Array(k),D=0;D<k;D++)W[D]=P.charCodeAt(D);return W},z=n.__addimage__.arrayBufferToBinaryString=function(P){for(var k="",W=wt(P)?P:new Uint8Array(P),D=0;D<W.length;D+=8192)k+=String.fromCharCode.apply(null,W.subarray(D,D+8192));return k};n.addImage=function(){var P,k,W,D,st,it,ht,$,lt;if(typeof arguments[1]=="number"?(k=e,W=arguments[1],D=arguments[2],st=arguments[3],it=arguments[4],ht=arguments[5],$=arguments[6],lt=arguments[7]):(k=arguments[1],W=arguments[2],D=arguments[3],st=arguments[4],it=arguments[5],ht=arguments[6],$=arguments[7],lt=arguments[8]),fe(P=arguments[0])==="object"&&!w(P)&&"imageData"in P){var dt=P;P=dt.imageData,k=dt.format||k||e,W=dt.x||W||0,D=dt.y||D||0,st=dt.w||dt.width||st,it=dt.h||dt.height||it,ht=dt.alias||ht,$=dt.compression||$,lt=dt.rotation||dt.angle||lt}var jt=this.internal.getFilters();if($===void 0&&jt.indexOf("FlateEncode")!==-1&&($="SLOW"),isNaN(W)||isNaN(D))throw new Error("Invalid coordinates passed to jsPDF.addImage");h.call(this);var N=nt.call(this,P,k,ht,$);return F.call(this,W,D,st,it,N,lt),this};var nt=function(P,k,W,D){var st,it,ht;if(typeof P=="string"&&a(P)===e){P=unescape(P);var $=pt(P,!1);($!==""||($=n.loadFile(P,!0))!==void 0)&&(P=$)}if(w(P)&&(P=S(P,k)),k=a(P,k),!y(k))throw new Error("addImage does not support files of type '"+k+"', please ensure that a plugin for '"+k+"' support is added.");if(((ht=W)==null||ht.length===0)&&(W=function(lt){return typeof lt=="string"||wt(lt)?B(lt):wt(lt.data)?B(lt.data):null}(P)),(st=p.call(this,W))||(ct()&&(P instanceof Uint8Array||k==="RGBA"||(it=P,P=tt(P))),st=this["process"+k.toUpperCase()](P,g.call(this),W,function(lt){return lt&&typeof lt=="string"&&(lt=lt.toUpperCase()),lt in n.image_compression?lt:_.NONE}(D),it)),!st)throw new Error("An unknown error occurred whilst processing the image.");return st},pt=n.__addimage__.convertBase64ToBinaryString=function(P,k){k=typeof k!="boolean"||k;var W,D="";if(typeof P=="string"){var st;W=(st=ot(P))!==null&&st!==void 0?st:P;try{D=fa(W)}catch(it){if(k)throw Y(W)?new Error("atob-Error in jsPDF.convertBase64ToBinaryString "+it.message):new Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return D};n.getImageProperties=function(P){var k,W,D="";if(w(P)&&(P=S(P)),typeof P=="string"&&a(P)===e&&((D=pt(P,!1))===""&&(D=n.loadFile(P)||""),P=D),W=a(P),!y(W))throw new Error("addImage does not support files of type '"+W+"', please ensure that a plugin for '"+W+"' support is added.");if(!ct()||P instanceof Uint8Array||(P=tt(P)),!(k=this["process"+W.toUpperCase()](P)))throw new Error("An unknown error occurred whilst processing the image");return k.fileType=W,k}})(Tt.API),function(n){var e=function(r){if(r!==void 0&&r!="")return!0};Tt.API.events.push(["addPage",function(r){this.internal.getPageInfo(r.pageNumber).pageContext.annotations=[]}]),n.events.push(["putPage",function(r){for(var a,u,o,c=this.internal.getCoordinateString,h=this.internal.getVerticalCoordinateString,f=this.internal.getPageInfoByObjId(r.objId),g=r.pageContext.annotations,y=!1,w=0;w<g.length&&!y;w++)switch((a=g[w]).type){case"link":(e(a.options.url)||e(a.options.pageNumber))&&(y=!0);break;case"reference":case"text":case"freetext":y=!0}if(y!=0){this.internal.write("/Annots [");for(var S=0;S<g.length;S++){a=g[S];var p=this.internal.pdfEscape,O=this.internal.getEncryptor(r.objId);switch(a.type){case"reference":this.internal.write(" "+a.object.objId+" 0 R ");break;case"text":var F=this.internal.newAdditionalObject(),q=this.internal.newAdditionalObject(),_=this.internal.getEncryptor(F.objId),B=a.title||"Note";o="<</Type /Annot /Subtype /Text "+(u="/Rect ["+c(a.bounds.x)+" "+h(a.bounds.y+a.bounds.h)+" "+c(a.bounds.x+a.bounds.w)+" "+h(a.bounds.y)+"] ")+"/Contents ("+p(_(a.contents))+")",o+=" /Popup "+q.objId+" 0 R",o+=" /P "+f.objId+" 0 R",o+=" /T ("+p(_(B))+") >>",F.content=o;var Y=F.objId+" 0 R";o="<</Type /Annot /Subtype /Popup "+(u="/Rect ["+c(a.bounds.x+30)+" "+h(a.bounds.y+a.bounds.h)+" "+c(a.bounds.x+a.bounds.w+30)+" "+h(a.bounds.y)+"] ")+" /Parent "+Y,a.open&&(o+=" /Open true"),o+=" >>",q.content=o,this.internal.write(F.objId,"0 R",q.objId,"0 R");break;case"freetext":u="/Rect ["+c(a.bounds.x)+" "+h(a.bounds.y)+" "+c(a.bounds.x+a.bounds.w)+" "+h(a.bounds.y+a.bounds.h)+"] ";var ot=a.color||"#000000";o="<</Type /Annot /Subtype /FreeText "+u+"/Contents ("+p(O(a.contents))+")",o+=" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+ot+")",o+=" /Border [0 0 0]",o+=" >>",this.internal.write(o);break;case"link":if(a.options.name){var ct=this.annotations._nameMap[a.options.name];a.options.pageNumber=ct.page,a.options.top=ct.y}else a.options.top||(a.options.top=0);if(u="/Rect ["+a.finalBounds.x+" "+a.finalBounds.y+" "+a.finalBounds.w+" "+a.finalBounds.h+"] ",o="",a.options.url)o="<</Type /Annot /Subtype /Link "+u+"/Border [0 0 0] /A <</S /URI /URI ("+p(O(a.options.url))+") >>";else if(a.options.pageNumber)switch(o="<</Type /Annot /Subtype /Link "+u+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(a.options.pageNumber).objId+" 0 R",a.options.magFactor=a.options.magFactor||"XYZ",a.options.magFactor){case"Fit":o+=" /Fit]";break;case"FitH":o+=" /FitH "+a.options.top+"]";break;case"FitV":a.options.left=a.options.left||0,o+=" /FitV "+a.options.left+"]";break;case"XYZ":default:var wt=h(a.options.top);a.options.left=a.options.left||0,a.options.zoom===void 0&&(a.options.zoom=0),o+=" /XYZ "+a.options.left+" "+wt+" "+a.options.zoom+"]"}o!=""&&(o+=" >>",this.internal.write(o))}}this.internal.write("]")}}]),n.createAnnotation=function(r){var a=this.internal.getCurrentPageInfo();switch(r.type){case"link":this.link(r.bounds.x,r.bounds.y,r.bounds.w,r.bounds.h,r);break;case"text":case"freetext":a.pageContext.annotations.push(r)}},n.link=function(r,a,u,o,c){var h=this.internal.getCurrentPageInfo(),f=this.internal.getCoordinateString,g=this.internal.getVerticalCoordinateString;h.pageContext.annotations.push({finalBounds:{x:f(r),y:g(a),w:f(r+u),h:g(a+o)},options:c,type:"link"})},n.textWithLink=function(r,a,u,o){var c,h,f=this.getTextWidth(r),g=this.internal.getLineHeight()/this.internal.scaleFactor;if(o.maxWidth!==void 0){h=o.maxWidth;var y=this.splitTextToSize(r,h).length;c=Math.ceil(g*y)}else h=f,c=g;return this.text(r,a,u,o),u+=.2*g,o.align==="center"&&(a-=f/2),o.align==="right"&&(a-=f),this.link(a,u-g,h,c,o),f},n.getTextWidth=function(r){var a=this.internal.getFontSize();return this.getStringUnitWidth(r)*a/this.internal.scaleFactor}}(Tt.API),function(n){var e={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},r={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},a={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},u=[1570,1571,1573,1575];n.__arabicParser__={};var o=n.__arabicParser__.isInArabicSubstitutionA=function(F){return e[F.charCodeAt(0)]!==void 0},c=n.__arabicParser__.isArabicLetter=function(F){return typeof F=="string"&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(F)},h=n.__arabicParser__.isArabicEndLetter=function(F){return c(F)&&o(F)&&e[F.charCodeAt(0)].length<=2},f=n.__arabicParser__.isArabicAlfLetter=function(F){return c(F)&&u.indexOf(F.charCodeAt(0))>=0};n.__arabicParser__.arabicLetterHasIsolatedForm=function(F){return c(F)&&o(F)&&e[F.charCodeAt(0)].length>=1};var g=n.__arabicParser__.arabicLetterHasFinalForm=function(F){return c(F)&&o(F)&&e[F.charCodeAt(0)].length>=2};n.__arabicParser__.arabicLetterHasInitialForm=function(F){return c(F)&&o(F)&&e[F.charCodeAt(0)].length>=3};var y=n.__arabicParser__.arabicLetterHasMedialForm=function(F){return c(F)&&o(F)&&e[F.charCodeAt(0)].length==4},w=n.__arabicParser__.resolveLigatures=function(F){var q=0,_=r,B="",Y=0;for(q=0;q<F.length;q+=1)_[F.charCodeAt(q)]!==void 0?(Y++,typeof(_=_[F.charCodeAt(q)])=="number"&&(B+=String.fromCharCode(_),_=r,Y=0),q===F.length-1&&(_=r,B+=F.charAt(q-(Y-1)),q-=Y-1,Y=0)):(_=r,B+=F.charAt(q-Y),q-=Y,Y=0);return B};n.__arabicParser__.isArabicDiacritic=function(F){return F!==void 0&&a[F.charCodeAt(0)]!==void 0};var S=n.__arabicParser__.getCorrectForm=function(F,q,_){return c(F)?o(F)===!1?-1:!g(F)||!c(q)&&!c(_)||!c(_)&&h(q)||h(F)&&!c(q)||h(F)&&f(q)||h(F)&&h(q)?0:y(F)&&c(q)&&!h(q)&&c(_)&&g(_)?3:h(F)||!c(_)?1:2:-1},p=function(F){var q=0,_=0,B=0,Y="",ot="",ct="",wt=(F=F||"").split("\\s+"),tt=[];for(q=0;q<wt.length;q+=1){for(tt.push(""),_=0;_<wt[q].length;_+=1)Y=wt[q][_],ot=wt[q][_-1],ct=wt[q][_+1],c(Y)?(B=S(Y,ot,ct),tt[q]+=B!==-1?String.fromCharCode(e[Y.charCodeAt(0)][B]):Y):tt[q]+=Y;tt[q]=w(tt[q])}return tt.join(" ")},O=n.__arabicParser__.processArabic=n.processArabic=function(){var F,q=typeof arguments[0]=="string"?arguments[0]:arguments[0].text,_=[];if(Array.isArray(q)){var B=0;for(_=[],B=0;B<q.length;B+=1)Array.isArray(q[B])?_.push([p(q[B][0]),q[B][1],q[B][2]]):_.push([p(q[B])]);F=_}else F=p(q);return typeof arguments[0]=="string"?F:(arguments[0].text=F,arguments[0])};n.events.push(["preProcessText",O])}(Tt.API),Tt.API.autoPrint=function(n){var e;switch((n=n||{}).variant=n.variant||"non-conform",n.variant){case"javascript":this.addJS("print({});");break;case"non-conform":default:this.internal.events.subscribe("postPutResources",function(){e=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){this.internal.out("/OpenAction "+e+" 0 R")})}return this},function(n){var e=function(){var r=void 0;Object.defineProperty(this,"pdf",{get:function(){return r},set:function(h){r=h}});var a=150;Object.defineProperty(this,"width",{get:function(){return a},set:function(h){a=isNaN(h)||Number.isInteger(h)===!1||h<0?150:h,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=a+1)}});var u=300;Object.defineProperty(this,"height",{get:function(){return u},set:function(h){u=isNaN(h)||Number.isInteger(h)===!1||h<0?300:h,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=u+1)}});var o=[];Object.defineProperty(this,"childNodes",{get:function(){return o},set:function(h){o=h}});var c={};Object.defineProperty(this,"style",{get:function(){return c},set:function(h){c=h}}),Object.defineProperty(this,"parentNode",{})};e.prototype.getContext=function(r,a){var u;if((r=r||"2d")!=="2d")return null;for(u in a)this.pdf.context2d.hasOwnProperty(u)&&(this.pdf.context2d[u]=a[u]);return this.pdf.context2d._canvas=this,this.pdf.context2d},e.prototype.toDataURL=function(){throw new Error("toDataURL is not implemented.")},n.events.push(["initialized",function(){this.canvas=new e,this.canvas.pdf=this}])}(Tt.API),function(n){var e={left:0,top:0,bottom:0,right:0},r=!1,a=function(){this.internal.__cell__===void 0&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},e),this.internal.__cell__.margins.width=this.getPageWidth(),u.call(this))},u=function(){this.internal.__cell__.lastCell=new o,this.internal.__cell__.pages=1},o=function(){var f=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return f},set:function(F){f=F}});var g=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return g},set:function(F){g=F}});var y=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return y},set:function(F){y=F}});var w=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return w},set:function(F){w=F}});var S=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return S},set:function(F){S=F}});var p=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return p},set:function(F){p=F}});var O=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return O},set:function(F){O=F}}),this};o.prototype.clone=function(){return new o(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},o.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},n.setHeaderFunction=function(f){return a.call(this),this.internal.__cell__.headerFunction=typeof f=="function"?f:void 0,this},n.getTextDimensions=function(f,g){a.call(this);var y=(g=g||{}).fontSize||this.getFontSize(),w=g.font||this.getFont(),S=g.scaleFactor||this.internal.scaleFactor,p=0,O=0,F=0,q=this;if(!Array.isArray(f)&&typeof f!="string"){if(typeof f!="number")throw new Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");f=String(f)}var _=g.maxWidth;_>0?typeof f=="string"?f=this.splitTextToSize(f,_):Object.prototype.toString.call(f)==="[object Array]"&&(f=f.reduce(function(Y,ot){return Y.concat(q.splitTextToSize(ot,_))},[])):f=Array.isArray(f)?f:[f];for(var B=0;B<f.length;B++)p<(F=this.getStringUnitWidth(f[B],{font:w})*y)&&(p=F);return p!==0&&(O=f.length),{w:p/=S,h:Math.max((O*y*this.getLineHeightFactor()-y*(this.getLineHeightFactor()-1))/S,0)}},n.cellAddPage=function(){a.call(this),this.addPage();var f=this.internal.__cell__.margins||e;return this.internal.__cell__.lastCell=new o(f.left,f.top,void 0,void 0),this.internal.__cell__.pages+=1,this};var c=n.cell=function(){var f;f=arguments[0]instanceof o?arguments[0]:new o(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),a.call(this);var g=this.internal.__cell__.lastCell,y=this.internal.__cell__.padding,w=this.internal.__cell__.margins||e,S=this.internal.__cell__.tableHeaderRow,p=this.internal.__cell__.printHeaders;return g.lineNumber!==void 0&&(g.lineNumber===f.lineNumber?(f.x=(g.x||0)+(g.width||0),f.y=g.y||0):g.y+g.height+f.height+w.bottom>this.getPageHeight()?(this.cellAddPage(),f.y=w.top,p&&S&&(this.printHeaderRow(f.lineNumber,!0),f.y+=S[0].height)):f.y=g.y+g.height||f.y),f.text[0]!==void 0&&(this.rect(f.x,f.y,f.width,f.height,r===!0?"FD":void 0),f.align==="right"?this.text(f.text,f.x+f.width-y,f.y+y,{align:"right",baseline:"top"}):f.align==="center"?this.text(f.text,f.x+f.width/2,f.y+y,{align:"center",baseline:"top",maxWidth:f.width-y-y}):this.text(f.text,f.x+y,f.y+y,{align:"left",baseline:"top",maxWidth:f.width-y-y})),this.internal.__cell__.lastCell=f,this};n.table=function(f,g,y,w,S){if(a.call(this),!y)throw new Error("No data for PDF table.");var p,O,F,q,_=[],B=[],Y=[],ot={},ct={},wt=[],tt=[],z=(S=S||{}).autoSize||!1,nt=S.printHeaders!==!1,pt=S.css&&S.css["font-size"]!==void 0?16*S.css["font-size"]:S.fontSize||12,P=S.margins||Object.assign({width:this.getPageWidth()},e),k=typeof S.padding=="number"?S.padding:3,W=S.headerBackgroundColor||"#c8c8c8",D=S.headerTextColor||"#000";if(u.call(this),this.internal.__cell__.printHeaders=nt,this.internal.__cell__.margins=P,this.internal.__cell__.table_font_size=pt,this.internal.__cell__.padding=k,this.internal.__cell__.headerBackgroundColor=W,this.internal.__cell__.headerTextColor=D,this.setFontSize(pt),w==null)B=_=Object.keys(y[0]),Y=_.map(function(){return"left"});else if(Array.isArray(w)&&fe(w[0])==="object")for(_=w.map(function(dt){return dt.name}),B=w.map(function(dt){return dt.prompt||dt.name||""}),Y=w.map(function(dt){return dt.align||"left"}),p=0;p<w.length;p+=1)ct[w[p].name]=w[p].width*(19.049976/25.4);else Array.isArray(w)&&typeof w[0]=="string"&&(B=_=w,Y=_.map(function(){return"left"}));if(z||Array.isArray(w)&&typeof w[0]=="string")for(p=0;p<_.length;p+=1){for(ot[q=_[p]]=y.map(function(dt){return dt[q]}),this.setFont(void 0,"bold"),wt.push(this.getTextDimensions(B[p],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),O=ot[q],this.setFont(void 0,"normal"),F=0;F<O.length;F+=1)wt.push(this.getTextDimensions(O[F],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);ct[q]=Math.max.apply(null,wt)+k+k,wt=[]}if(nt){var st={};for(p=0;p<_.length;p+=1)st[_[p]]={},st[_[p]].text=B[p],st[_[p]].align=Y[p];var it=h.call(this,st,ct);tt=_.map(function(dt){return new o(f,g,ct[dt],it,st[dt].text,void 0,st[dt].align)}),this.setTableHeaderRow(tt),this.printHeaderRow(1,!1)}var ht=w.reduce(function(dt,jt){return dt[jt.name]=jt.align,dt},{});for(p=0;p<y.length;p+=1){"rowStart"in S&&S.rowStart instanceof Function&&S.rowStart({row:p,data:y[p]},this);var $=h.call(this,y[p],ct);for(F=0;F<_.length;F+=1){var lt=y[p][_[F]];"cellStart"in S&&S.cellStart instanceof Function&&S.cellStart({row:p,col:F,data:lt},this),c.call(this,new o(f,g,ct[_[F]],$,lt,p+2,ht[_[F]]))}}return this.internal.__cell__.table_x=f,this.internal.__cell__.table_y=g,this};var h=function(f,g){var y=this.internal.__cell__.padding,w=this.internal.__cell__.table_font_size,S=this.internal.scaleFactor;return Object.keys(f).map(function(p){var O=f[p];return this.splitTextToSize(O.hasOwnProperty("text")?O.text:O,g[p]-y-y)},this).map(function(p){return this.getLineHeightFactor()*p.length*w/S+y+y},this).reduce(function(p,O){return Math.max(p,O)},0)};n.setTableHeaderRow=function(f){a.call(this),this.internal.__cell__.tableHeaderRow=f},n.printHeaderRow=function(f,g){if(a.call(this),!this.internal.__cell__.tableHeaderRow)throw new Error("Property tableHeaderRow does not exist.");var y;if(r=!0,typeof this.internal.__cell__.headerFunction=="function"){var w=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new o(w[0],w[1],w[2],w[3],void 0,-1)}this.setFont(void 0,"bold");for(var S=[],p=0;p<this.internal.__cell__.tableHeaderRow.length;p+=1){y=this.internal.__cell__.tableHeaderRow[p].clone(),g&&(y.y=this.internal.__cell__.margins.top||0,S.push(y)),y.lineNumber=f;var O=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),c.call(this,y),this.setTextColor(O)}S.length>0&&this.setTableHeaderRow(S),this.setFont(void 0,"normal"),r=!1}}(Tt.API);var Ru={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},Tu=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],Ss=Du(Tu),zu=[100,200,300,400,500,600,700,800,900],nc=Du(zu);function _s(n){var e=n.family.replace(/"|'/g,"").toLowerCase(),r=function(o){return Ru[o=o||"normal"]?o:"normal"}(n.style),a=function(o){if(!o)return 400;if(typeof o=="number")return o>=100&&o<=900&&o%100==0?o:400;if(/^\d00$/.test(o))return parseInt(o);switch(o){case"bold":return 700;case"normal":default:return 400}}(n.weight),u=function(o){return typeof Ss[o=o||"normal"]=="number"?o:"normal"}(n.stretch);return{family:e,style:r,weight:a,stretch:u,src:n.src||[],ref:n.ref||{name:e,style:[u,r,a].join(" ")}}}function hu(n,e,r,a){var u;for(u=r;u>=0&&u<e.length;u+=a)if(n[e[u]])return n[e[u]];for(u=r;u>=0&&u<e.length;u-=a)if(n[e[u]])return n[e[u]]}var ic={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},fu={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};function du(n){return[n.stretch,n.style,n.weight,n.family].join(" ")}function ac(n,e,r){for(var a=(r=r||{}).defaultFontFamily||"times",u=Object.assign({},ic,r.genericFontFamilies||{}),o=null,c=null,h=0;h<e.length;++h)if(u[(o=_s(e[h])).family]&&(o.family=u[o.family]),n.hasOwnProperty(o.family)){c=n[o.family];break}if(!(c=c||n[a]))throw new Error("Could not find a font-family for the rule '"+du(o)+"' and default family '"+a+"'.");if(c=function(f,g){if(g[f])return g[f];var y=Ss[f],w=y<=Ss.normal?-1:1,S=hu(g,Tu,y,w);if(!S)throw new Error("Could not find a matching font-stretch value for "+f);return S}(o.stretch,c),c=function(f,g){if(g[f])return g[f];for(var y=Ru[f],w=0;w<y.length;++w)if(g[y[w]])return g[y[w]];throw new Error("Could not find a matching font-style for "+f)}(o.style,c),!(c=function(f,g){if(g[f])return g[f];if(f===400&&g[500])return g[500];if(f===500&&g[400])return g[400];var y=nc[f],w=hu(g,zu,y,f<400?-1:1);if(!w)throw new Error("Could not find a matching font-weight for value "+f);return w}(o.weight,c)))throw new Error("Failed to resolve a font for the rule '"+du(o)+"'.");return c}function pu(n){return n.trimLeft()}function oc(n,e){for(var r=0;r<n.length;){if(n.charAt(r)===e)return[n.substring(0,r),n.substring(r+1)];r+=1}return null}function sc(n){var e=n.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return e===null?null:[e[0],n.substring(e[0].length)]}var ho,gu,mu,ds=["times"];(function(n){var e,r,a,u,o,c,h,f,g,y=function(N){return N=N||{},this.isStrokeTransparent=N.isStrokeTransparent||!1,this.strokeOpacity=N.strokeOpacity||1,this.strokeStyle=N.strokeStyle||"#000000",this.fillStyle=N.fillStyle||"#000000",this.isFillTransparent=N.isFillTransparent||!1,this.fillOpacity=N.fillOpacity||1,this.font=N.font||"10px sans-serif",this.textBaseline=N.textBaseline||"alphabetic",this.textAlign=N.textAlign||"left",this.lineWidth=N.lineWidth||1,this.lineJoin=N.lineJoin||"miter",this.lineCap=N.lineCap||"butt",this.path=N.path||[],this.transform=N.transform!==void 0?N.transform.clone():new f,this.globalCompositeOperation=N.globalCompositeOperation||"normal",this.globalAlpha=N.globalAlpha||1,this.clip_path=N.clip_path||[],this.currentPoint=N.currentPoint||new c,this.miterLimit=N.miterLimit||10,this.lastPoint=N.lastPoint||new c,this.lineDashOffset=N.lineDashOffset||0,this.lineDash=N.lineDash||[],this.margin=N.margin||[0,0,0,0],this.prevPageLastElemOffset=N.prevPageLastElemOffset||0,this.ignoreClearRect=typeof N.ignoreClearRect!="boolean"||N.ignoreClearRect,this};n.events.push(["initialized",function(){this.context2d=new w(this),e=this.internal.f2,r=this.internal.getCoordinateString,a=this.internal.getVerticalCoordinateString,u=this.internal.getHorizontalCoordinate,o=this.internal.getVerticalCoordinate,c=this.internal.Point,h=this.internal.Rectangle,f=this.internal.Matrix,g=new y}]);var w=function(N){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}});var C=N;Object.defineProperty(this,"pdf",{get:function(){return C}});var M=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return M},set:function(ft){M=!!ft}});var T=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return T},set:function(ft){T=!!ft}});var J=0;Object.defineProperty(this,"posX",{get:function(){return J},set:function(ft){isNaN(ft)||(J=ft)}});var Q=0;Object.defineProperty(this,"posY",{get:function(){return Q},set:function(ft){isNaN(ft)||(Q=ft)}}),Object.defineProperty(this,"margin",{get:function(){return g.margin},set:function(ft){var E;typeof ft=="number"?E=[ft,ft,ft,ft]:((E=new Array(4))[0]=ft[0],E[1]=ft.length>=2?ft[1]:E[0],E[2]=ft.length>=3?ft[2]:E[0],E[3]=ft.length>=4?ft[3]:E[1]),g.margin=E}});var et=!1;Object.defineProperty(this,"autoPaging",{get:function(){return et},set:function(ft){et=ft}});var rt=0;Object.defineProperty(this,"lastBreak",{get:function(){return rt},set:function(ft){rt=ft}});var At=[];Object.defineProperty(this,"pageBreaks",{get:function(){return At},set:function(ft){At=ft}}),Object.defineProperty(this,"ctx",{get:function(){return g},set:function(ft){ft instanceof y&&(g=ft)}}),Object.defineProperty(this,"path",{get:function(){return g.path},set:function(ft){g.path=ft}});var Nt=[];Object.defineProperty(this,"ctxStack",{get:function(){return Nt},set:function(ft){Nt=ft}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(ft){var E;E=S(ft),this.ctx.fillStyle=E.style,this.ctx.isFillTransparent=E.a===0,this.ctx.fillOpacity=E.a,this.pdf.setFillColor(E.r,E.g,E.b,{a:E.a}),this.pdf.setTextColor(E.r,E.g,E.b,{a:E.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(ft){var E=S(ft);this.ctx.strokeStyle=E.style,this.ctx.isStrokeTransparent=E.a===0,this.ctx.strokeOpacity=E.a,E.a===0?this.pdf.setDrawColor(255,255,255):(E.a,this.pdf.setDrawColor(E.r,E.g,E.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(ft){["butt","round","square"].indexOf(ft)!==-1&&(this.ctx.lineCap=ft,this.pdf.setLineCap(ft))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(ft){isNaN(ft)||(this.ctx.lineWidth=ft,this.pdf.setLineWidth(ft))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(ft){["bevel","round","miter"].indexOf(ft)!==-1&&(this.ctx.lineJoin=ft,this.pdf.setLineJoin(ft))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(ft){isNaN(ft)||(this.ctx.miterLimit=ft,this.pdf.setMiterLimit(ft))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(ft){this.ctx.textBaseline=ft}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(ft){["right","end","center","left","start"].indexOf(ft)!==-1&&(this.ctx.textAlign=ft)}});var Ft=null;function _t(ft,E){if(Ft===null){var Kt=function(Et){var Lt=[];return Object.keys(Et).forEach(function(xt){Et[xt].forEach(function(It){var kt=null;switch(It){case"bold":kt={family:xt,weight:"bold"};break;case"italic":kt={family:xt,style:"italic"};break;case"bolditalic":kt={family:xt,weight:"bold",style:"italic"};break;case"":case"normal":kt={family:xt}}kt!==null&&(kt.ref={name:xt,style:It},Lt.push(kt))})}),Lt}(ft.getFontList());Ft=function(Et){for(var Lt={},xt=0;xt<Et.length;++xt){var It=_s(Et[xt]),kt=It.family,qt=It.stretch,Gt=It.style,Qt=It.weight;Lt[kt]=Lt[kt]||{},Lt[kt][qt]=Lt[kt][qt]||{},Lt[kt][qt][Gt]=Lt[kt][qt][Gt]||{},Lt[kt][qt][Gt][Qt]=It}return Lt}(Kt.concat(E))}return Ft}var Ut=null;Object.defineProperty(this,"fontFaces",{get:function(){return Ut},set:function(ft){Ft=null,Ut=ft}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(ft){var E;if(this.ctx.font=ft,(E=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(ft))!==null){var Kt=E[1];E[2];var Et=E[3],Lt=E[4];E[5];var xt=E[6],It=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(Lt)[2];Lt=Math.floor(It==="px"?parseFloat(Lt)*this.pdf.internal.scaleFactor:It==="em"?parseFloat(Lt)*this.pdf.getFontSize():parseFloat(Lt)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(Lt);var kt=function(Wt){var ee,Ct,Je=[],oe=Wt.trim();if(oe==="")return ds;if(oe in fu)return[fu[oe]];for(;oe!=="";){switch(Ct=null,ee=(oe=pu(oe)).charAt(0)){case'"':case"'":Ct=oc(oe.substring(1),ee);break;default:Ct=sc(oe)}if(Ct===null||(Je.push(Ct[0]),(oe=pu(Ct[1]))!==""&&oe.charAt(0)!==","))return ds;oe=oe.replace(/^,/,"")}return Je}(xt);if(this.fontFaces){var qt=ac(_t(this.pdf,this.fontFaces),kt.map(function(Wt){return{family:Wt,stretch:"normal",weight:Et,style:Kt}}));this.pdf.setFont(qt.ref.name,qt.ref.style)}else{var Gt="";(Et==="bold"||parseInt(Et,10)>=700||Kt==="bold")&&(Gt="bold"),Kt==="italic"&&(Gt+="italic"),Gt.length===0&&(Gt="normal");for(var Qt="",te={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},ie=0;ie<kt.length;ie++){if(this.pdf.internal.getFont(kt[ie],Gt,{noFallback:!0,disableWarning:!0})!==void 0){Qt=kt[ie];break}if(Gt==="bolditalic"&&this.pdf.internal.getFont(kt[ie],"bold",{noFallback:!0,disableWarning:!0})!==void 0)Qt=kt[ie],Gt="bold";else if(this.pdf.internal.getFont(kt[ie],"normal",{noFallback:!0,disableWarning:!0})!==void 0){Qt=kt[ie],Gt="normal";break}}if(Qt===""){for(var de=0;de<kt.length;de++)if(te[kt[de]]){Qt=te[kt[de]];break}}Qt=Qt===""?"Times":Qt,this.pdf.setFont(Qt,Gt)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(ft){this.ctx.globalCompositeOperation=ft}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(ft){this.ctx.globalAlpha=ft}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(ft){this.ctx.lineDashOffset=ft,jt.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(ft){this.ctx.lineDash=ft,jt.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(ft){this.ctx.ignoreClearRect=!!ft}})};w.prototype.setLineDash=function(N){this.lineDash=N},w.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},w.prototype.fill=function(){ot.call(this,"fill",!1)},w.prototype.stroke=function(){ot.call(this,"stroke",!1)},w.prototype.beginPath=function(){this.path=[{type:"begin"}]},w.prototype.moveTo=function(N,C){if(isNaN(N)||isNaN(C))throw ve.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.moveTo");var M=this.ctx.transform.applyToPoint(new c(N,C));this.path.push({type:"mt",x:M.x,y:M.y}),this.ctx.lastPoint=new c(N,C)},w.prototype.closePath=function(){var N=new c(0,0),C=0;for(C=this.path.length-1;C!==-1;C--)if(this.path[C].type==="begin"&&fe(this.path[C+1])==="object"&&typeof this.path[C+1].x=="number"){N=new c(this.path[C+1].x,this.path[C+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new c(N.x,N.y)},w.prototype.lineTo=function(N,C){if(isNaN(N)||isNaN(C))throw ve.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.lineTo");var M=this.ctx.transform.applyToPoint(new c(N,C));this.path.push({type:"lt",x:M.x,y:M.y}),this.ctx.lastPoint=new c(M.x,M.y)},w.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),ot.call(this,null,!0)},w.prototype.quadraticCurveTo=function(N,C,M,T){if(isNaN(M)||isNaN(T)||isNaN(N)||isNaN(C))throw ve.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var J=this.ctx.transform.applyToPoint(new c(M,T)),Q=this.ctx.transform.applyToPoint(new c(N,C));this.path.push({type:"qct",x1:Q.x,y1:Q.y,x:J.x,y:J.y}),this.ctx.lastPoint=new c(J.x,J.y)},w.prototype.bezierCurveTo=function(N,C,M,T,J,Q){if(isNaN(J)||isNaN(Q)||isNaN(N)||isNaN(C)||isNaN(M)||isNaN(T))throw ve.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var et=this.ctx.transform.applyToPoint(new c(J,Q)),rt=this.ctx.transform.applyToPoint(new c(N,C)),At=this.ctx.transform.applyToPoint(new c(M,T));this.path.push({type:"bct",x1:rt.x,y1:rt.y,x2:At.x,y2:At.y,x:et.x,y:et.y}),this.ctx.lastPoint=new c(et.x,et.y)},w.prototype.arc=function(N,C,M,T,J,Q){if(isNaN(N)||isNaN(C)||isNaN(M)||isNaN(T)||isNaN(J))throw ve.error("jsPDF.context2d.arc: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.arc");if(Q=!!Q,!this.ctx.transform.isIdentity){var et=this.ctx.transform.applyToPoint(new c(N,C));N=et.x,C=et.y;var rt=this.ctx.transform.applyToPoint(new c(0,M)),At=this.ctx.transform.applyToPoint(new c(0,0));M=Math.sqrt(Math.pow(rt.x-At.x,2)+Math.pow(rt.y-At.y,2))}Math.abs(J-T)>=2*Math.PI&&(T=0,J=2*Math.PI),this.path.push({type:"arc",x:N,y:C,radius:M,startAngle:T,endAngle:J,counterclockwise:Q})},w.prototype.arcTo=function(N,C,M,T,J){throw new Error("arcTo not implemented.")},w.prototype.rect=function(N,C,M,T){if(isNaN(N)||isNaN(C)||isNaN(M)||isNaN(T))throw ve.error("jsPDF.context2d.rect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(N,C),this.lineTo(N+M,C),this.lineTo(N+M,C+T),this.lineTo(N,C+T),this.lineTo(N,C),this.lineTo(N+M,C),this.lineTo(N,C)},w.prototype.fillRect=function(N,C,M,T){if(isNaN(N)||isNaN(C)||isNaN(M)||isNaN(T))throw ve.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!p.call(this)){var J={};this.lineCap!=="butt"&&(J.lineCap=this.lineCap,this.lineCap="butt"),this.lineJoin!=="miter"&&(J.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(N,C,M,T),this.fill(),J.hasOwnProperty("lineCap")&&(this.lineCap=J.lineCap),J.hasOwnProperty("lineJoin")&&(this.lineJoin=J.lineJoin)}},w.prototype.strokeRect=function(N,C,M,T){if(isNaN(N)||isNaN(C)||isNaN(M)||isNaN(T))throw ve.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeRect");O.call(this)||(this.beginPath(),this.rect(N,C,M,T),this.stroke())},w.prototype.clearRect=function(N,C,M,T){if(isNaN(N)||isNaN(C)||isNaN(M)||isNaN(T))throw ve.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(N,C,M,T))},w.prototype.save=function(N){N=typeof N!="boolean"||N;for(var C=this.pdf.internal.getCurrentPageInfo().pageNumber,M=0;M<this.pdf.internal.getNumberOfPages();M++)this.pdf.setPage(M+1),this.pdf.internal.out("q");if(this.pdf.setPage(C),N){this.ctx.fontSize=this.pdf.internal.getFontSize();var T=new y(this.ctx);this.ctxStack.push(this.ctx),this.ctx=T}},w.prototype.restore=function(N){N=typeof N!="boolean"||N;for(var C=this.pdf.internal.getCurrentPageInfo().pageNumber,M=0;M<this.pdf.internal.getNumberOfPages();M++)this.pdf.setPage(M+1),this.pdf.internal.out("Q");this.pdf.setPage(C),N&&this.ctxStack.length!==0&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},w.prototype.toDataURL=function(){throw new Error("toDataUrl not implemented.")};var S=function(N){var C,M,T,J;if(N.isCanvasGradient===!0&&(N=N.getColor()),!N)return{r:0,g:0,b:0,a:0,style:N};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(N))C=0,M=0,T=0,J=0;else{var Q=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(N);if(Q!==null)C=parseInt(Q[1]),M=parseInt(Q[2]),T=parseInt(Q[3]),J=1;else if((Q=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(N))!==null)C=parseInt(Q[1]),M=parseInt(Q[2]),T=parseInt(Q[3]),J=parseFloat(Q[4]);else{if(J=1,typeof N=="string"&&N.charAt(0)!=="#"){var et=new ju(N);N=et.ok?et.toHex():"#000000"}N.length===4?(C=N.substring(1,2),C+=C,M=N.substring(2,3),M+=M,T=N.substring(3,4),T+=T):(C=N.substring(1,3),M=N.substring(3,5),T=N.substring(5,7)),C=parseInt(C,16),M=parseInt(M,16),T=parseInt(T,16)}}return{r:C,g:M,b:T,a:J,style:N}},p=function(){return this.ctx.isFillTransparent||this.globalAlpha==0},O=function(){return!!(this.ctx.isStrokeTransparent||this.globalAlpha==0)};w.prototype.fillText=function(N,C,M,T){if(isNaN(C)||isNaN(M)||typeof N!="string")throw ve.error("jsPDF.context2d.fillText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillText");if(T=isNaN(T)?void 0:T,!p.call(this)){var J=$(this.ctx.transform.rotation),Q=this.ctx.transform.scaleX;k.call(this,{text:N,x:C,y:M,scale:Q,angle:J,align:this.textAlign,maxWidth:T})}},w.prototype.strokeText=function(N,C,M,T){if(isNaN(C)||isNaN(M)||typeof N!="string")throw ve.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!O.call(this)){T=isNaN(T)?void 0:T;var J=$(this.ctx.transform.rotation),Q=this.ctx.transform.scaleX;k.call(this,{text:N,x:C,y:M,scale:Q,renderingMode:"stroke",angle:J,align:this.textAlign,maxWidth:T})}},w.prototype.measureText=function(N){if(typeof N!="string")throw ve.error("jsPDF.context2d.measureText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.measureText");var C=this.pdf,M=this.pdf.internal.scaleFactor,T=C.internal.getFontSize(),J=C.getStringUnitWidth(N)*T/C.internal.scaleFactor,Q=function(et){var rt=(et=et||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return rt}}),this};return new Q({width:J*=Math.round(96*M/72*1e4)/1e4})},w.prototype.scale=function(N,C){if(isNaN(N)||isNaN(C))throw ve.error("jsPDF.context2d.scale: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.scale");var M=new f(N,0,0,C,0,0);this.ctx.transform=this.ctx.transform.multiply(M)},w.prototype.rotate=function(N){if(isNaN(N))throw ve.error("jsPDF.context2d.rotate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rotate");var C=new f(Math.cos(N),Math.sin(N),-Math.sin(N),Math.cos(N),0,0);this.ctx.transform=this.ctx.transform.multiply(C)},w.prototype.translate=function(N,C){if(isNaN(N)||isNaN(C))throw ve.error("jsPDF.context2d.translate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.translate");var M=new f(1,0,0,1,N,C);this.ctx.transform=this.ctx.transform.multiply(M)},w.prototype.transform=function(N,C,M,T,J,Q){if(isNaN(N)||isNaN(C)||isNaN(M)||isNaN(T)||isNaN(J)||isNaN(Q))throw ve.error("jsPDF.context2d.transform: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.transform");var et=new f(N,C,M,T,J,Q);this.ctx.transform=this.ctx.transform.multiply(et)},w.prototype.setTransform=function(N,C,M,T,J,Q){N=isNaN(N)?1:N,C=isNaN(C)?0:C,M=isNaN(M)?0:M,T=isNaN(T)?1:T,J=isNaN(J)?0:J,Q=isNaN(Q)?0:Q,this.ctx.transform=new f(N,C,M,T,J,Q)};var F=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};w.prototype.drawImage=function(N,C,M,T,J,Q,et,rt,At){var Nt=this.pdf.getImageProperties(N),Ft=1,_t=1,Ut=1,ft=1;T!==void 0&&rt!==void 0&&(Ut=rt/T,ft=At/J,Ft=Nt.width/T*rt/T,_t=Nt.height/J*At/J),Q===void 0&&(Q=C,et=M,C=0,M=0),T!==void 0&&rt===void 0&&(rt=T,At=J),T===void 0&&rt===void 0&&(rt=Nt.width,At=Nt.height);for(var E,Kt=this.ctx.transform.decompose(),Et=$(Kt.rotate.shx),Lt=new f,xt=(Lt=(Lt=(Lt=Lt.multiply(Kt.translate)).multiply(Kt.skew)).multiply(Kt.scale)).applyToRectangle(new h(Q-C*Ut,et-M*ft,T*Ft,J*_t)),It=q.call(this,xt),kt=[],qt=0;qt<It.length;qt+=1)kt.indexOf(It[qt])===-1&&kt.push(It[qt]);if(Y(kt),this.autoPaging)for(var Gt=kt[0],Qt=kt[kt.length-1],te=Gt;te<Qt+1;te++){this.pdf.setPage(te);var ie=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],de=te===1?this.posY+this.margin[0]:this.margin[0],Wt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],ee=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],Ct=te===1?0:Wt+(te-2)*ee;if(this.ctx.clip_path.length!==0){var Je=this.path;E=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=B(E,this.posX+this.margin[3],-Ct+de+this.ctx.prevPageLastElemOffset),ct.call(this,"fill",!0),this.path=Je}var oe=JSON.parse(JSON.stringify(xt));oe=B([oe],this.posX+this.margin[3],-Ct+de+this.ctx.prevPageLastElemOffset)[0];var Sr=(te>Gt||te<Qt)&&F.call(this);Sr&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],ie,ee,null).clip().discardPath()),this.pdf.addImage(N,"JPEG",oe.x,oe.y,oe.w,oe.h,null,null,Et),Sr&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(N,"JPEG",xt.x,xt.y,xt.w,xt.h,null,null,Et)};var q=function(N,C,M){var T=[];C=C||this.pdf.internal.pageSize.width,M=M||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var J=this.posY+this.ctx.prevPageLastElemOffset;switch(N.type){default:case"mt":case"lt":T.push(Math.floor((N.y+J)/M)+1);break;case"arc":T.push(Math.floor((N.y+J-N.radius)/M)+1),T.push(Math.floor((N.y+J+N.radius)/M)+1);break;case"qct":var Q=lt(this.ctx.lastPoint.x,this.ctx.lastPoint.y,N.x1,N.y1,N.x,N.y);T.push(Math.floor((Q.y+J)/M)+1),T.push(Math.floor((Q.y+Q.h+J)/M)+1);break;case"bct":var et=dt(this.ctx.lastPoint.x,this.ctx.lastPoint.y,N.x1,N.y1,N.x2,N.y2,N.x,N.y);T.push(Math.floor((et.y+J)/M)+1),T.push(Math.floor((et.y+et.h+J)/M)+1);break;case"rect":T.push(Math.floor((N.y+J)/M)+1),T.push(Math.floor((N.y+N.h+J)/M)+1)}for(var rt=0;rt<T.length;rt+=1)for(;this.pdf.internal.getNumberOfPages()<T[rt];)_.call(this);return T},_=function(){var N=this.fillStyle,C=this.strokeStyle,M=this.font,T=this.lineCap,J=this.lineWidth,Q=this.lineJoin;this.pdf.addPage(),this.fillStyle=N,this.strokeStyle=C,this.font=M,this.lineCap=T,this.lineWidth=J,this.lineJoin=Q},B=function(N,C,M){for(var T=0;T<N.length;T++)switch(N[T].type){case"bct":N[T].x2+=C,N[T].y2+=M;case"qct":N[T].x1+=C,N[T].y1+=M;case"mt":case"lt":case"arc":default:N[T].x+=C,N[T].y+=M}return N},Y=function(N){return N.sort(function(C,M){return C-M})},ot=function(N,C){for(var M,T,J=this.fillStyle,Q=this.strokeStyle,et=this.lineCap,rt=this.lineWidth,At=Math.abs(rt*this.ctx.transform.scaleX),Nt=this.lineJoin,Ft=JSON.parse(JSON.stringify(this.path)),_t=JSON.parse(JSON.stringify(this.path)),Ut=[],ft=0;ft<_t.length;ft++)if(_t[ft].x!==void 0)for(var E=q.call(this,_t[ft]),Kt=0;Kt<E.length;Kt+=1)Ut.indexOf(E[Kt])===-1&&Ut.push(E[Kt]);for(var Et=0;Et<Ut.length;Et++)for(;this.pdf.internal.getNumberOfPages()<Ut[Et];)_.call(this);if(Y(Ut),this.autoPaging)for(var Lt=Ut[0],xt=Ut[Ut.length-1],It=Lt;It<xt+1;It++){this.pdf.setPage(It),this.fillStyle=J,this.strokeStyle=Q,this.lineCap=et,this.lineWidth=At,this.lineJoin=Nt;var kt=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],qt=It===1?this.posY+this.margin[0]:this.margin[0],Gt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],Qt=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],te=It===1?0:Gt+(It-2)*Qt;if(this.ctx.clip_path.length!==0){var ie=this.path;M=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=B(M,this.posX+this.margin[3],-te+qt+this.ctx.prevPageLastElemOffset),ct.call(this,N,!0),this.path=ie}if(T=JSON.parse(JSON.stringify(Ft)),this.path=B(T,this.posX+this.margin[3],-te+qt+this.ctx.prevPageLastElemOffset),C===!1||It===0){var de=(It>Lt||It<xt)&&F.call(this);de&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],kt,Qt,null).clip().discardPath()),ct.call(this,N,C),de&&this.pdf.restoreGraphicsState()}this.lineWidth=rt}else this.lineWidth=At,ct.call(this,N,C),this.lineWidth=rt;this.path=Ft},ct=function(N,C){if((N!=="stroke"||C||!O.call(this))&&(N==="stroke"||C||!p.call(this))){for(var M,T,J=[],Q=this.path,et=0;et<Q.length;et++){var rt=Q[et];switch(rt.type){case"begin":J.push({begin:!0});break;case"close":J.push({close:!0});break;case"mt":J.push({start:rt,deltas:[],abs:[]});break;case"lt":var At=J.length;if(Q[et-1]&&!isNaN(Q[et-1].x)&&(M=[rt.x-Q[et-1].x,rt.y-Q[et-1].y],At>0)){for(;At>=0;At--)if(J[At-1].close!==!0&&J[At-1].begin!==!0){J[At-1].deltas.push(M),J[At-1].abs.push(rt);break}}break;case"bct":M=[rt.x1-Q[et-1].x,rt.y1-Q[et-1].y,rt.x2-Q[et-1].x,rt.y2-Q[et-1].y,rt.x-Q[et-1].x,rt.y-Q[et-1].y],J[J.length-1].deltas.push(M);break;case"qct":var Nt=Q[et-1].x+2/3*(rt.x1-Q[et-1].x),Ft=Q[et-1].y+2/3*(rt.y1-Q[et-1].y),_t=rt.x+2/3*(rt.x1-rt.x),Ut=rt.y+2/3*(rt.y1-rt.y),ft=rt.x,E=rt.y;M=[Nt-Q[et-1].x,Ft-Q[et-1].y,_t-Q[et-1].x,Ut-Q[et-1].y,ft-Q[et-1].x,E-Q[et-1].y],J[J.length-1].deltas.push(M);break;case"arc":J.push({deltas:[],abs:[],arc:!0}),Array.isArray(J[J.length-1].abs)&&J[J.length-1].abs.push(rt)}}T=C?null:N==="stroke"?"stroke":"fill";for(var Kt=!1,Et=0;Et<J.length;Et++)if(J[Et].arc)for(var Lt=J[Et].abs,xt=0;xt<Lt.length;xt++){var It=Lt[xt];It.type==="arc"?z.call(this,It.x,It.y,It.radius,It.startAngle,It.endAngle,It.counterclockwise,void 0,C,!Kt):W.call(this,It.x,It.y),Kt=!0}else if(J[Et].close===!0)this.pdf.internal.out("h"),Kt=!1;else if(J[Et].begin!==!0){var kt=J[Et].start.x,qt=J[Et].start.y;D.call(this,J[Et].deltas,kt,qt),Kt=!0}T&&nt.call(this,T),C&&pt.call(this)}},wt=function(N){var C=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,M=C*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return N-M;case"top":return N+C-M;case"hanging":return N+C-2*M;case"middle":return N+C/2-M;case"ideographic":return N;case"alphabetic":default:return N}},tt=function(N){return N+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};w.prototype.createLinearGradient=function(){var N=function(){};return N.colorStops=[],N.addColorStop=function(C,M){this.colorStops.push([C,M])},N.getColor=function(){return this.colorStops.length===0?"#000000":this.colorStops[0][1]},N.isCanvasGradient=!0,N},w.prototype.createPattern=function(){return this.createLinearGradient()},w.prototype.createRadialGradient=function(){return this.createLinearGradient()};var z=function(N,C,M,T,J,Q,et,rt,At){for(var Nt=it.call(this,M,T,J,Q),Ft=0;Ft<Nt.length;Ft++){var _t=Nt[Ft];Ft===0&&(At?P.call(this,_t.x1+N,_t.y1+C):W.call(this,_t.x1+N,_t.y1+C)),st.call(this,N,C,_t.x2,_t.y2,_t.x3,_t.y3,_t.x4,_t.y4)}rt?pt.call(this):nt.call(this,et)},nt=function(N){switch(N){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},pt=function(){this.pdf.clip(),this.pdf.discardPath()},P=function(N,C){this.pdf.internal.out(r(N)+" "+a(C)+" m")},k=function(N){var C;switch(N.align){case"right":case"end":C="right";break;case"center":C="center";break;case"left":case"start":default:C="left"}var M=this.pdf.getTextDimensions(N.text),T=wt.call(this,N.y),J=tt.call(this,T)-M.h,Q=this.ctx.transform.applyToPoint(new c(N.x,T)),et=this.ctx.transform.decompose(),rt=new f;rt=(rt=(rt=rt.multiply(et.translate)).multiply(et.skew)).multiply(et.scale);for(var At,Nt,Ft,_t=this.ctx.transform.applyToRectangle(new h(N.x,T,M.w,M.h)),Ut=rt.applyToRectangle(new h(N.x,J,M.w,M.h)),ft=q.call(this,Ut),E=[],Kt=0;Kt<ft.length;Kt+=1)E.indexOf(ft[Kt])===-1&&E.push(ft[Kt]);if(Y(E),this.autoPaging)for(var Et=E[0],Lt=E[E.length-1],xt=Et;xt<Lt+1;xt++){this.pdf.setPage(xt);var It=xt===1?this.posY+this.margin[0]:this.margin[0],kt=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],qt=this.pdf.internal.pageSize.height-this.margin[2],Gt=qt-this.margin[0],Qt=this.pdf.internal.pageSize.width-this.margin[1],te=Qt-this.margin[3],ie=xt===1?0:kt+(xt-2)*Gt;if(this.ctx.clip_path.length!==0){var de=this.path;At=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=B(At,this.posX+this.margin[3],-1*ie+It),ct.call(this,"fill",!0),this.path=de}var Wt=B([JSON.parse(JSON.stringify(Ut))],this.posX+this.margin[3],-ie+It+this.ctx.prevPageLastElemOffset)[0];N.scale>=.01&&(Nt=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Nt*N.scale),Ft=this.lineWidth,this.lineWidth=Ft*N.scale);var ee=this.autoPaging!=="text";if(ee||Wt.y+Wt.h<=qt){if(ee||Wt.y>=It&&Wt.x<=Qt){var Ct=ee?N.text:this.pdf.splitTextToSize(N.text,N.maxWidth||Qt-Wt.x)[0],Je=B([JSON.parse(JSON.stringify(_t))],this.posX+this.margin[3],-ie+It+this.ctx.prevPageLastElemOffset)[0],oe=ee&&(xt>Et||xt<Lt)&&F.call(this);oe&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],te,Gt,null).clip().discardPath()),this.pdf.text(Ct,Je.x,Je.y,{angle:N.angle,align:C,renderingMode:N.renderingMode}),oe&&this.pdf.restoreGraphicsState()}}else Wt.y<qt&&(this.ctx.prevPageLastElemOffset+=qt-Wt.y);N.scale>=.01&&(this.pdf.setFontSize(Nt),this.lineWidth=Ft)}else N.scale>=.01&&(Nt=this.pdf.internal.getFontSize(),this.pdf.setFontSize(Nt*N.scale),Ft=this.lineWidth,this.lineWidth=Ft*N.scale),this.pdf.text(N.text,Q.x+this.posX,Q.y+this.posY,{angle:N.angle,align:C,renderingMode:N.renderingMode,maxWidth:N.maxWidth}),N.scale>=.01&&(this.pdf.setFontSize(Nt),this.lineWidth=Ft)},W=function(N,C,M,T){M=M||0,T=T||0,this.pdf.internal.out(r(N+M)+" "+a(C+T)+" l")},D=function(N,C,M){return this.pdf.lines(N,C,M,null,null)},st=function(N,C,M,T,J,Q,et,rt){this.pdf.internal.out([e(u(M+N)),e(o(T+C)),e(u(J+N)),e(o(Q+C)),e(u(et+N)),e(o(rt+C)),"c"].join(" "))},it=function(N,C,M,T){for(var J=2*Math.PI,Q=Math.PI/2;C>M;)C-=J;var et=Math.abs(M-C);et<J&&T&&(et=J-et);for(var rt=[],At=T?-1:1,Nt=C;et>1e-5;){var Ft=Nt+At*Math.min(et,Q);rt.push(ht.call(this,N,Nt,Ft)),et-=Math.abs(Ft-Nt),Nt=Ft}return rt},ht=function(N,C,M){var T=(M-C)/2,J=N*Math.cos(T),Q=N*Math.sin(T),et=J,rt=-Q,At=et*et+rt*rt,Nt=At+et*J+rt*Q,Ft=4/3*(Math.sqrt(2*At*Nt)-Nt)/(et*Q-rt*J),_t=et-Ft*rt,Ut=rt+Ft*et,ft=_t,E=-Ut,Kt=T+C,Et=Math.cos(Kt),Lt=Math.sin(Kt);return{x1:N*Math.cos(C),y1:N*Math.sin(C),x2:_t*Et-Ut*Lt,y2:_t*Lt+Ut*Et,x3:ft*Et-E*Lt,y3:ft*Lt+E*Et,x4:N*Math.cos(M),y4:N*Math.sin(M)}},$=function(N){return 180*N/Math.PI},lt=function(N,C,M,T,J,Q){var et=N+.5*(M-N),rt=C+.5*(T-C),At=J+.5*(M-J),Nt=Q+.5*(T-Q),Ft=Math.min(N,J,et,At),_t=Math.max(N,J,et,At),Ut=Math.min(C,Q,rt,Nt),ft=Math.max(C,Q,rt,Nt);return new h(Ft,Ut,_t-Ft,ft-Ut)},dt=function(N,C,M,T,J,Q,et,rt){var At,Nt,Ft,_t,Ut,ft,E,Kt,Et,Lt,xt,It,kt,qt,Gt=M-N,Qt=T-C,te=J-M,ie=Q-T,de=et-J,Wt=rt-Q;for(Nt=0;Nt<41;Nt++)Et=(E=(Ft=N+(At=Nt/40)*Gt)+At*((Ut=M+At*te)-Ft))+At*(Ut+At*(J+At*de-Ut)-E),Lt=(Kt=(_t=C+At*Qt)+At*((ft=T+At*ie)-_t))+At*(ft+At*(Q+At*Wt-ft)-Kt),Nt==0?(xt=Et,It=Lt,kt=Et,qt=Lt):(xt=Math.min(xt,Et),It=Math.min(It,Lt),kt=Math.max(kt,Et),qt=Math.max(qt,Lt));return new h(Math.round(xt),Math.round(It),Math.round(kt-xt),Math.round(qt-It))},jt=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var N,C,M=(N=this.ctx.lineDash,C=this.ctx.lineDashOffset,JSON.stringify({lineDash:N,lineDashOffset:C}));this.prevLineDash!==M&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=M)}}})(Tt.API),function(n){var e=function(o){var c,h,f,g,y,w,S,p,O,F;for(h=[],f=0,g=(o+=c="\0\0\0\0".slice(o.length%4||4)).length;g>f;f+=4)(y=(o.charCodeAt(f)<<24)+(o.charCodeAt(f+1)<<16)+(o.charCodeAt(f+2)<<8)+o.charCodeAt(f+3))!==0?(w=(y=((y=((y=((y=(y-(F=y%85))/85)-(O=y%85))/85)-(p=y%85))/85)-(S=y%85))/85)%85,h.push(w+33,S+33,p+33,O+33,F+33)):h.push(122);return function(q,_){for(var B=_;B>0;B--)q.pop()}(h,c.length),String.fromCharCode.apply(String,h)+"~>"},r=function(o){var c,h,f,g,y,w=String,S="length",p=255,O="charCodeAt",F="slice",q="replace";for(o[F](-2),o=o[F](0,-2)[q](/\s/g,"")[q]("z","!!!!!"),f=[],g=0,y=(o+=c="uuuuu"[F](o[S]%5||5))[S];y>g;g+=5)h=52200625*(o[O](g)-33)+614125*(o[O](g+1)-33)+7225*(o[O](g+2)-33)+85*(o[O](g+3)-33)+(o[O](g+4)-33),f.push(p&h>>24,p&h>>16,p&h>>8,p&h);return function(_,B){for(var Y=B;Y>0;Y--)_.pop()}(f,c[S]),w.fromCharCode.apply(w,f)},a=function(o){var c=new RegExp(/^([0-9A-Fa-f]{2})+$/);if((o=o.replace(/\s/g,"")).indexOf(">")!==-1&&(o=o.substr(0,o.indexOf(">"))),o.length%2&&(o+="0"),c.test(o)===!1)return"";for(var h="",f=0;f<o.length;f+=2)h+=String.fromCharCode("0x"+(o[f]+o[f+1]));return h},u=function(o){for(var c=new Uint8Array(o.length),h=o.length;h--;)c[h]=o.charCodeAt(h);return o=(c=ws(c)).reduce(function(f,g){return f+String.fromCharCode(g)},"")};n.processDataByFilters=function(o,c){var h=0,f=o||"",g=[];for(typeof(c=c||[])=="string"&&(c=[c]),h=0;h<c.length;h+=1)switch(c[h]){case"ASCII85Decode":case"/ASCII85Decode":f=r(f),g.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":f=e(f),g.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":f=a(f),g.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":f=f.split("").map(function(y){return("0"+y.charCodeAt().toString(16)).slice(-2)}).join("")+">",g.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":f=u(f),g.push("/FlateDecode");break;default:throw new Error('The filter: "'+c[h]+'" is not implemented')}return{data:f,reverseChain:g.reverse().join(" ")}}}(Tt.API),function(n){n.loadFile=function(e,r,a){return function(u,o,c){o=o!==!1,c=typeof c=="function"?c:function(){};var h=void 0;try{h=function(f,g,y){var w=new XMLHttpRequest,S=0,p=function(O){var F=O.length,q=[],_=String.fromCharCode;for(S=0;S<F;S+=1)q.push(_(255&O.charCodeAt(S)));return q.join("")};if(w.open("GET",f,!g),w.overrideMimeType("text/plain; charset=x-user-defined"),g===!1&&(w.onload=function(){w.status===200?y(p(this.responseText)):y(void 0)}),w.send(null),g&&w.status===200)return p(w.responseText)}(u,o,c)}catch{}return h}(e,r,a)},n.loadImageFile=n.loadFile}(Tt.API),function(n){function e(){return(Ht.html2canvas?Promise.resolve(Ht.html2canvas):gs(()=>import("./html2canvas.esm-CBrSDip1.js"),[])).catch(function(c){return Promise.reject(new Error("Could not load html2canvas: "+c))}).then(function(c){return c.default?c.default:c})}function r(){return(Ht.DOMPurify?Promise.resolve(Ht.DOMPurify):gs(()=>import("./purify.es-CQJ0hv7W.js"),[])).catch(function(c){return Promise.reject(new Error("Could not load dompurify: "+c))}).then(function(c){return c.default?c.default:c})}var a=function(c){var h=fe(c);return h==="undefined"?"undefined":h==="string"||c instanceof String?"string":h==="number"||c instanceof Number?"number":h==="function"||c instanceof Function?"function":c&&c.constructor===Array?"array":c&&c.nodeType===1?"element":h==="object"?"object":"unknown"},u=function(c,h){var f=document.createElement(c);for(var g in h.className&&(f.className=h.className),h.innerHTML&&h.dompurify&&(f.innerHTML=h.dompurify.sanitize(h.innerHTML)),h.style)f.style[g]=h.style[g];return f},o=function c(h){var f=Object.assign(c.convert(Promise.resolve()),JSON.parse(JSON.stringify(c.template))),g=c.convert(Promise.resolve(),f);return g=(g=g.setProgress(1,c,1,[c])).set(h)};(o.prototype=Object.create(Promise.prototype)).constructor=o,o.convert=function(c,h){return c.__proto__=h||o.prototype,c},o.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},o.prototype.from=function(c,h){return this.then(function(){switch(h=h||function(f){switch(a(f)){case"string":return"string";case"element":return f.nodeName.toLowerCase()==="canvas"?"canvas":"element";default:return"unknown"}}(c)){case"string":return this.then(r).then(function(f){return this.set({src:u("div",{innerHTML:c,dompurify:f})})});case"element":return this.set({src:c});case"canvas":return this.set({canvas:c});case"img":return this.set({img:c});default:return this.error("Unknown source type.")}})},o.prototype.to=function(c){switch(c){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},o.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then(function(){var c={position:"relative",display:"inline-block",width:(typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},h=function f(g,y){for(var w=g.nodeType===3?document.createTextNode(g.nodeValue):g.cloneNode(!1),S=g.firstChild;S;S=S.nextSibling)y!==!0&&S.nodeType===1&&S.nodeName==="SCRIPT"||w.appendChild(f(S,y));return g.nodeType===1&&(g.nodeName==="CANVAS"?(w.width=g.width,w.height=g.height,w.getContext("2d").drawImage(g,0,0)):g.nodeName!=="TEXTAREA"&&g.nodeName!=="SELECT"||(w.value=g.value),w.addEventListener("load",function(){w.scrollTop=g.scrollTop,w.scrollLeft=g.scrollLeft},!0)),w}(this.prop.src,this.opt.html2canvas.javascriptEnabled);h.tagName==="BODY"&&(c.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=u("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=u("div",{className:"html2pdf__container",style:c}),this.prop.container.appendChild(h),this.prop.container.firstChild.appendChild(u("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"})},o.prototype.toCanvas=function(){var c=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(c).then(e).then(function(h){var f=Object.assign({},this.opt.html2canvas);return delete f.onrendered,h(this.prop.container,f)}).then(function(h){(this.opt.html2canvas.onrendered||function(){})(h),this.prop.canvas=h,document.body.removeChild(this.prop.overlay)})},o.prototype.toContext2d=function(){var c=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(c).then(e).then(function(h){var f=this.opt.jsPDF,g=this.opt.fontFaces,y=typeof this.opt.width!="number"||isNaN(this.opt.width)||typeof this.opt.windowWidth!="number"||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,w=Object.assign({async:!0,allowTaint:!0,scale:y,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete w.onrendered,f.context2d.autoPaging=this.opt.autoPaging===void 0||this.opt.autoPaging,f.context2d.posX=this.opt.x,f.context2d.posY=this.opt.y,f.context2d.margin=this.opt.margin,f.context2d.fontFaces=g,g)for(var S=0;S<g.length;++S){var p=g[S],O=p.src.find(function(F){return F.format==="truetype"});O&&f.addFont(O.url,p.ref.name,p.ref.style)}return w.windowHeight=w.windowHeight||0,w.windowHeight=w.windowHeight==0?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):w.windowHeight,f.context2d.save(!0),h(this.prop.container,w)}).then(function(h){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(h),this.prop.canvas=h,document.body.removeChild(this.prop.overlay)})},o.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then(function(){var c=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=c})},o.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then(function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF})},o.prototype.output=function(c,h,f){return(f=f||"pdf").toLowerCase()==="img"||f.toLowerCase()==="image"?this.outputImg(c,h):this.outputPdf(c,h)},o.prototype.outputPdf=function(c,h){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){return this.prop.pdf.output(c,h)})},o.prototype.outputImg=function(c){return this.thenList([function(){return this.prop.img||this.toImg()}]).then(function(){switch(c){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+c+'" is not supported.'}})},o.prototype.save=function(c){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(c?{filename:c}:null).then(function(){this.prop.pdf.save(this.opt.filename)})},o.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then(function(){this.prop.callback(this.prop.pdf)})},o.prototype.set=function(c){if(a(c)!=="object")return this;var h=Object.keys(c||{}).map(function(f){if(f in o.template.prop)return function(){this.prop[f]=c[f]};switch(f){case"margin":return this.setMargin.bind(this,c.margin);case"jsPDF":return function(){return this.opt.jsPDF=c.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,c.pageSize);default:return function(){this.opt[f]=c[f]}}},this);return this.then(function(){return this.thenList(h)})},o.prototype.get=function(c,h){return this.then(function(){var f=c in o.template.prop?this.prop[c]:this.opt[c];return h?h(f):f})},o.prototype.setMargin=function(c){return this.then(function(){switch(a(c)){case"number":c=[c,c,c,c];case"array":if(c.length===2&&(c=[c[0],c[1],c[0],c[1]]),c.length===4)break;default:return this.error("Invalid margin array.")}this.opt.margin=c}).then(this.setPageSize)},o.prototype.setPageSize=function(c){function h(f,g){return Math.floor(f*g/72*96)}return this.then(function(){(c=c||Tt.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(c.inner={width:c.width-this.opt.margin[1]-this.opt.margin[3],height:c.height-this.opt.margin[0]-this.opt.margin[2]},c.inner.px={width:h(c.inner.width,c.k),height:h(c.inner.height,c.k)},c.inner.ratio=c.inner.height/c.inner.width),this.prop.pageSize=c})},o.prototype.setProgress=function(c,h,f,g){return c!=null&&(this.progress.val=c),h!=null&&(this.progress.state=h),f!=null&&(this.progress.n=f),g!=null&&(this.progress.stack=g),this.progress.ratio=this.progress.val/this.progress.state,this},o.prototype.updateProgress=function(c,h,f,g){return this.setProgress(c?this.progress.val+c:null,h||null,f?this.progress.n+f:null,g?this.progress.stack.concat(g):null)},o.prototype.then=function(c,h){var f=this;return this.thenCore(c,h,function(g,y){return f.updateProgress(null,null,1,[g]),Promise.prototype.then.call(this,function(w){return f.updateProgress(null,g),w}).then(g,y).then(function(w){return f.updateProgress(1),w})})},o.prototype.thenCore=function(c,h,f){f=f||Promise.prototype.then,c&&(c=c.bind(this)),h&&(h=h.bind(this));var g=Promise.toString().indexOf("[native code]")!==-1&&Promise.name==="Promise"?this:o.convert(Object.assign({},this),Promise.prototype),y=f.call(g,c,h);return o.convert(y,this.__proto__)},o.prototype.thenExternal=function(c,h){return Promise.prototype.then.call(this,c,h)},o.prototype.thenList=function(c){var h=this;return c.forEach(function(f){h=h.thenCore(f)}),h},o.prototype.catch=function(c){c&&(c=c.bind(this));var h=Promise.prototype.catch.call(this,c);return o.convert(h,this)},o.prototype.catchExternal=function(c){return Promise.prototype.catch.call(this,c)},o.prototype.error=function(c){return this.then(function(){throw new Error(c)})},o.prototype.using=o.prototype.set,o.prototype.saveAs=o.prototype.save,o.prototype.export=o.prototype.output,o.prototype.run=o.prototype.then,Tt.getPageSize=function(c,h,f){if(fe(c)==="object"){var g=c;c=g.orientation,h=g.unit||h,f=g.format||f}h=h||"mm",f=f||"a4",c=(""+(c||"P")).toLowerCase();var y,w=(""+f).toLowerCase(),S={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(h){case"pt":y=1;break;case"mm":y=72/25.4;break;case"cm":y=72/2.54;break;case"in":y=72;break;case"px":y=.75;break;case"pc":case"em":y=12;break;case"ex":y=6;break;default:throw"Invalid unit: "+h}var p,O=0,F=0;if(S.hasOwnProperty(w))O=S[w][1]/y,F=S[w][0]/y;else try{O=f[1],F=f[0]}catch{throw new Error("Invalid format: "+f)}if(c==="p"||c==="portrait")c="p",F>O&&(p=F,F=O,O=p);else{if(c!=="l"&&c!=="landscape")throw"Invalid orientation: "+c;c="l",O>F&&(p=F,F=O,O=p)}return{width:F,height:O,unit:h,k:y,orientation:c}},n.html=function(c,h){(h=h||{}).callback=h.callback||function(){},h.html2canvas=h.html2canvas||{},h.html2canvas.canvas=h.html2canvas.canvas||this.canvas,h.jsPDF=h.jsPDF||this,h.fontFaces=h.fontFaces?h.fontFaces.map(_s):null;var f=new o(h);return h.worker?f:f.from(c).doCallback()}}(Tt.API),Tt.API.addJS=function(n){return mu=n,this.internal.events.subscribe("postPutResources",function(){ho=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(ho+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),gu=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+mu+")"),this.internal.out(">>"),this.internal.out("endobj")}),this.internal.events.subscribe("putCatalog",function(){ho!==void 0&&gu!==void 0&&this.internal.out("/Names <</JavaScript "+ho+" 0 R>>")}),this},function(n){var e;n.events.push(["postPutResources",function(){var r=this,a=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var u=r.outline.render().split(/\r\n/),o=0;o<u.length;o++){var c=u[o],h=a.exec(c);if(h!=null){var f=h[1];r.internal.newObjectDeferredBegin(f,!1)}r.internal.write(c)}if(this.outline.createNamedDestinations){var g=this.internal.pages.length,y=[];for(o=0;o<g;o++){var w=r.internal.newObject();y.push(w);var S=r.internal.getPageInfo(o+1);r.internal.write("<< /D["+S.objId+" 0 R /XYZ null null null]>> endobj")}var p=r.internal.newObject();for(r.internal.write("<< /Names [ "),o=0;o<y.length;o++)r.internal.write("(page_"+(o+1)+")"+y[o]+" 0 R");r.internal.write(" ] >>","endobj"),e=r.internal.newObject(),r.internal.write("<< /Dests "+p+" 0 R"),r.internal.write(">>","endobj")}}]),n.events.push(["putCatalog",function(){this.outline.root.children.length>0&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+e+" 0 R"))}]),n.events.push(["initialized",function(){var r=this;r.outline={createNamedDestinations:!1,root:{children:[]}},r.outline.add=function(a,u,o){var c={title:u,options:o,children:[]};return a==null&&(a=this.root),a.children.push(c),c},r.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=r,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},r.outline.genIds_r=function(a){a.id=r.internal.newObjectDeferred();for(var u=0;u<a.children.length;u++)this.genIds_r(a.children[u])},r.outline.renderRoot=function(a){this.objStart(a),this.line("/Type /Outlines"),a.children.length>0&&(this.line("/First "+this.makeRef(a.children[0])),this.line("/Last "+this.makeRef(a.children[a.children.length-1]))),this.line("/Count "+this.count_r({count:0},a)),this.objEnd()},r.outline.renderItems=function(a){for(var u=this.ctx.pdf.internal.getVerticalCoordinateString,o=0;o<a.children.length;o++){var c=a.children[o];this.objStart(c),this.line("/Title "+this.makeString(c.title)),this.line("/Parent "+this.makeRef(a)),o>0&&this.line("/Prev "+this.makeRef(a.children[o-1])),o<a.children.length-1&&this.line("/Next "+this.makeRef(a.children[o+1])),c.children.length>0&&(this.line("/First "+this.makeRef(c.children[0])),this.line("/Last "+this.makeRef(c.children[c.children.length-1])));var h=this.count=this.count_r({count:0},c);if(h>0&&this.line("/Count "+h),c.options&&c.options.pageNumber){var f=r.internal.getPageInfo(c.options.pageNumber);this.line("/Dest ["+f.objId+" 0 R /XYZ 0 "+u(0)+" 0]")}this.objEnd()}for(var g=0;g<a.children.length;g++)this.renderItems(a.children[g])},r.outline.line=function(a){this.ctx.val+=a+`\r
`},r.outline.makeRef=function(a){return a.id+" 0 R"},r.outline.makeString=function(a){return"("+r.internal.pdfEscape(a)+")"},r.outline.objStart=function(a){this.ctx.val+=`\r
`+a.id+` 0 obj\r
<<\r
`},r.outline.objEnd=function(){this.ctx.val+=`>> \r
endobj\r
`},r.outline.count_r=function(a,u){for(var o=0;o<u.children.length;o++)a.count++,this.count_r(a,u.children[o]);return a.count}}])}(Tt.API),function(n){var e=[192,193,194,195,196,197,198,199];n.processJPEG=function(r,a,u,o,c,h){var f,g=this.decode.DCT_DECODE,y=null;if(typeof r=="string"||this.__addimage__.isArrayBuffer(r)||this.__addimage__.isArrayBufferView(r)){switch(r=c||r,r=this.__addimage__.isArrayBuffer(r)?new Uint8Array(r):r,(f=function(w){for(var S,p=256*w.charCodeAt(4)+w.charCodeAt(5),O=w.length,F={width:0,height:0,numcomponents:1},q=4;q<O;q+=2){if(q+=p,e.indexOf(w.charCodeAt(q+1))!==-1){S=256*w.charCodeAt(q+5)+w.charCodeAt(q+6),F={width:256*w.charCodeAt(q+7)+w.charCodeAt(q+8),height:S,numcomponents:w.charCodeAt(q+9)};break}p=256*w.charCodeAt(q+2)+w.charCodeAt(q+3)}return F}(r=this.__addimage__.isArrayBufferView(r)?this.__addimage__.arrayBufferToBinaryString(r):r)).numcomponents){case 1:h=this.color_spaces.DEVICE_GRAY;break;case 4:h=this.color_spaces.DEVICE_CMYK;break;case 3:h=this.color_spaces.DEVICE_RGB}y={data:r,width:f.width,height:f.height,colorSpace:h,bitsPerComponent:8,filter:g,index:a,alias:u}}return y}}(Tt.API);var Ci,fo,vu,bu,yu,uc=function(){var n,e,r;function a(o){var c,h,f,g,y,w,S,p,O,F,q,_,B,Y;for(this.data=o,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},w=null;;){switch(c=this.readUInt32(),O=(function(){var ot,ct;for(ct=[],ot=0;ot<4;++ot)ct.push(String.fromCharCode(this.data[this.pos++]));return ct}).call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(c);break;case"fcTL":w&&this.animation.frames.push(w),this.pos+=4,w={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},y=this.readUInt16(),g=this.readUInt16()||100,w.delay=1e3*y/g,w.disposeOp=this.data[this.pos++],w.blendOp=this.data[this.pos++],w.data=[];break;case"IDAT":case"fdAT":for(O==="fdAT"&&(this.pos+=4,c-=4),o=(w!=null?w.data:void 0)||this.imgData,_=0;0<=c?_<c:_>c;0<=c?++_:--_)o.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(f=this.palette.length/3,this.transparency.indexed=this.read(c),this.transparency.indexed.length>f)throw new Error("More transparent colors than palette size");if((F=f-this.transparency.indexed.length)>0)for(B=0;0<=F?B<F:B>F;0<=F?++B:--B)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(c)[0];break;case 2:this.transparency.rgb=this.read(c)}break;case"tEXt":S=(q=this.read(c)).indexOf(0),p=String.fromCharCode.apply(String,q.slice(0,S)),this.text[p]=String.fromCharCode.apply(String,q.slice(S+1));break;case"IEND":return w&&this.animation.frames.push(w),this.colors=(function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}).call(this),this.hasAlphaChannel=(Y=this.colorType)===4||Y===6,h=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*h,this.colorSpace=(function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}).call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=c}if(this.pos+=4,this.pos>this.data.length)throw new Error("Incomplete or corrupt PNG file")}}a.prototype.read=function(o){var c,h;for(h=[],c=0;0<=o?c<o:c>o;0<=o?++c:--c)h.push(this.data[this.pos++]);return h},a.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},a.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},a.prototype.decodePixels=function(o){var c=this.pixelBitlength/8,h=new Uint8Array(this.width*this.height*c),f=0,g=this;if(o==null&&(o=this.imgData),o.length===0)return new Uint8Array(0);function y(w,S,p,O){var F,q,_,B,Y,ot,ct,wt,tt,z,nt,pt,P,k,W,D,st,it,ht,$,lt,dt=Math.ceil((g.width-w)/p),jt=Math.ceil((g.height-S)/O),N=g.width==dt&&g.height==jt;for(k=c*dt,pt=N?h:new Uint8Array(k*jt),ot=o.length,P=0,q=0;P<jt&&f<ot;){switch(o[f++]){case 0:for(B=st=0;st<k;B=st+=1)pt[q++]=o[f++];break;case 1:for(B=it=0;it<k;B=it+=1)F=o[f++],Y=B<c?0:pt[q-c],pt[q++]=(F+Y)%256;break;case 2:for(B=ht=0;ht<k;B=ht+=1)F=o[f++],_=(B-B%c)/c,W=P&&pt[(P-1)*k+_*c+B%c],pt[q++]=(W+F)%256;break;case 3:for(B=$=0;$<k;B=$+=1)F=o[f++],_=(B-B%c)/c,Y=B<c?0:pt[q-c],W=P&&pt[(P-1)*k+_*c+B%c],pt[q++]=(F+Math.floor((Y+W)/2))%256;break;case 4:for(B=lt=0;lt<k;B=lt+=1)F=o[f++],_=(B-B%c)/c,Y=B<c?0:pt[q-c],P===0?W=D=0:(W=pt[(P-1)*k+_*c+B%c],D=_&&pt[(P-1)*k+(_-1)*c+B%c]),ct=Y+W-D,wt=Math.abs(ct-Y),z=Math.abs(ct-W),nt=Math.abs(ct-D),tt=wt<=z&&wt<=nt?Y:z<=nt?W:D,pt[q++]=(F+tt)%256;break;default:throw new Error("Invalid filter algorithm: "+o[f-1])}if(!N){var C=((S+P*O)*g.width+w)*c,M=P*k;for(B=0;B<dt;B+=1){for(var T=0;T<c;T+=1)h[C++]=pt[M++];C+=(p-1)*c}}P++}}return o=Rl(o),g.interlaceMethod==1?(y(0,0,8,8),y(4,0,8,8),y(0,4,4,8),y(2,0,4,4),y(0,2,2,4),y(1,0,2,2),y(0,1,1,2)):y(0,0,1,1),h},a.prototype.decodePalette=function(){var o,c,h,f,g,y,w,S,p;for(h=this.palette,y=this.transparency.indexed||[],g=new Uint8Array((y.length||0)+h.length),f=0,o=0,c=w=0,S=h.length;w<S;c=w+=3)g[f++]=h[c],g[f++]=h[c+1],g[f++]=h[c+2],g[f++]=(p=y[o++])!=null?p:255;return g},a.prototype.copyToImageData=function(o,c){var h,f,g,y,w,S,p,O,F,q,_;if(f=this.colors,F=null,h=this.hasAlphaChannel,this.palette.length&&(F=(_=this._decodedPalette)!=null?_:this._decodedPalette=this.decodePalette(),f=4,h=!0),O=(g=o.data||o).length,w=F||c,y=S=0,f===1)for(;y<O;)p=F?4*c[y/4]:S,q=w[p++],g[y++]=q,g[y++]=q,g[y++]=q,g[y++]=h?w[p++]:255,S=p;else for(;y<O;)p=F?4*c[y/4]:S,g[y++]=w[p++],g[y++]=w[p++],g[y++]=w[p++],g[y++]=h?w[p++]:255,S=p},a.prototype.decode=function(){var o;return o=new Uint8Array(this.width*this.height*4),this.copyToImageData(o,this.decodePixels()),o};var u=function(){if(Object.prototype.toString.call(Ht)==="[object Window]"){try{e=Ht.document.createElement("canvas"),r=e.getContext("2d")}catch{return!1}return!0}return!1};return u(),n=function(o){var c;if(u()===!0)return r.width=o.width,r.height=o.height,r.clearRect(0,0,o.width,o.height),r.putImageData(o,0,0),(c=new Image).src=e.toDataURL(),c;throw new Error("This method requires a Browser with Canvas-capability.")},a.prototype.decodeFrames=function(o){var c,h,f,g,y,w,S,p;if(this.animation){for(p=[],h=y=0,w=(S=this.animation.frames).length;y<w;h=++y)c=S[h],f=o.createImageData(c.width,c.height),g=this.decodePixels(new Uint8Array(c.data)),this.copyToImageData(f,g),c.imageData=f,p.push(c.image=n(f));return p}},a.prototype.renderFrame=function(o,c){var h,f,g;return h=(f=this.animation.frames)[c],g=f[c-1],c===0&&o.clearRect(0,0,this.width,this.height),(g!=null?g.disposeOp:void 0)===1?o.clearRect(g.xOffset,g.yOffset,g.width,g.height):(g!=null?g.disposeOp:void 0)===2&&o.putImageData(g.imageData,g.xOffset,g.yOffset),h.blendOp===0&&o.clearRect(h.xOffset,h.yOffset,h.width,h.height),o.drawImage(h.image,h.xOffset,h.yOffset)},a.prototype.animate=function(o){var c,h,f,g,y,w,S=this;return h=0,w=this.animation,g=w.numFrames,f=w.frames,y=w.numPlays,(c=function(){var p,O;if(p=h++%g,O=f[p],S.renderFrame(o,p),g>1&&h/g<y)return S.animation._timeout=setTimeout(c,O.delay)})()},a.prototype.stopAnimation=function(){var o;return clearTimeout((o=this.animation)!=null?o._timeout:void 0)},a.prototype.render=function(o){var c,h;return o._png&&o._png.stopAnimation(),o._png=this,o.width=this.width,o.height=this.height,c=o.getContext("2d"),this.animation?(this.decodeFrames(c),this.animate(c)):(h=c.createImageData(this.width,this.height),this.copyToImageData(h,this.decodePixels()),c.putImageData(h,0,0))},a}();/**
 * @license
 *
 * Copyright (c) 2014 James Robb, https://github.com/jamesbrobb
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 *//**
 * @license
 * (c) Dean McNamee <<EMAIL>>, 2013.
 *
 * https://github.com/deanm/omggif
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 *
 * omggif is a JavaScript implementation of a GIF 89a encoder and decoder,
 * including animation and compression.  It does not rely on any specific
 * underlying system, so should run in the browser, Node, or Plask.
 */function lc(n){var e=0;if(n[e++]!==71||n[e++]!==73||n[e++]!==70||n[e++]!==56||(n[e++]+1&253)!=56||n[e++]!==97)throw new Error("Invalid GIF 87a/89a header.");var r=n[e++]|n[e++]<<8,a=n[e++]|n[e++]<<8,u=n[e++],o=u>>7,c=1<<(7&u)+1;n[e++],n[e++];var h=null,f=null;o&&(h=e,f=c,e+=3*c);var g=!0,y=[],w=0,S=null,p=0,O=null;for(this.width=r,this.height=a;g&&e<n.length;)switch(n[e++]){case 33:switch(n[e++]){case 255:if(n[e]!==11||n[e+1]==78&&n[e+2]==69&&n[e+3]==84&&n[e+4]==83&&n[e+5]==67&&n[e+6]==65&&n[e+7]==80&&n[e+8]==69&&n[e+9]==50&&n[e+10]==46&&n[e+11]==48&&n[e+12]==3&&n[e+13]==1&&n[e+16]==0)e+=14,O=n[e++]|n[e++]<<8,e++;else for(e+=12;;){if(!((P=n[e++])>=0))throw Error("Invalid block size");if(P===0)break;e+=P}break;case 249:if(n[e++]!==4||n[e+4]!==0)throw new Error("Invalid graphics extension block.");var F=n[e++];w=n[e++]|n[e++]<<8,S=n[e++],(1&F)==0&&(S=null),p=F>>2&7,e++;break;case 254:for(;;){if(!((P=n[e++])>=0))throw Error("Invalid block size");if(P===0)break;e+=P}break;default:throw new Error("Unknown graphic control label: 0x"+n[e-1].toString(16))}break;case 44:var q=n[e++]|n[e++]<<8,_=n[e++]|n[e++]<<8,B=n[e++]|n[e++]<<8,Y=n[e++]|n[e++]<<8,ot=n[e++],ct=ot>>6&1,wt=1<<(7&ot)+1,tt=h,z=f,nt=!1;ot>>7&&(nt=!0,tt=e,z=wt,e+=3*wt);var pt=e;for(e++;;){var P;if(!((P=n[e++])>=0))throw Error("Invalid block size");if(P===0)break;e+=P}y.push({x:q,y:_,width:B,height:Y,has_local_palette:nt,palette_offset:tt,palette_size:z,data_offset:pt,data_length:e-pt,transparent_index:S,interlaced:!!ct,delay:w,disposal:p});break;case 59:g=!1;break;default:throw new Error("Unknown gif block: 0x"+n[e-1].toString(16))}this.numFrames=function(){return y.length},this.loopCount=function(){return O},this.frameInfo=function(k){if(k<0||k>=y.length)throw new Error("Frame index out of range.");return y[k]},this.decodeAndBlitFrameBGRA=function(k,W){var D=this.frameInfo(k),st=D.width*D.height,it=new Uint8Array(st);wu(n,D.data_offset,it,st);var ht=D.palette_offset,$=D.transparent_index;$===null&&($=256);var lt=D.width,dt=r-lt,jt=lt,N=4*(D.y*r+D.x),C=4*((D.y+D.height)*r+D.x),M=N,T=4*dt;D.interlaced===!0&&(T+=4*r*7);for(var J=8,Q=0,et=it.length;Q<et;++Q){var rt=it[Q];if(jt===0&&(jt=lt,(M+=T)>=C&&(T=4*dt+4*r*(J-1),M=N+(lt+dt)*(J<<1),J>>=1)),rt===$)M+=4;else{var At=n[ht+3*rt],Nt=n[ht+3*rt+1],Ft=n[ht+3*rt+2];W[M++]=Ft,W[M++]=Nt,W[M++]=At,W[M++]=255}--jt}},this.decodeAndBlitFrameRGBA=function(k,W){var D=this.frameInfo(k),st=D.width*D.height,it=new Uint8Array(st);wu(n,D.data_offset,it,st);var ht=D.palette_offset,$=D.transparent_index;$===null&&($=256);var lt=D.width,dt=r-lt,jt=lt,N=4*(D.y*r+D.x),C=4*((D.y+D.height)*r+D.x),M=N,T=4*dt;D.interlaced===!0&&(T+=4*r*7);for(var J=8,Q=0,et=it.length;Q<et;++Q){var rt=it[Q];if(jt===0&&(jt=lt,(M+=T)>=C&&(T=4*dt+4*r*(J-1),M=N+(lt+dt)*(J<<1),J>>=1)),rt===$)M+=4;else{var At=n[ht+3*rt],Nt=n[ht+3*rt+1],Ft=n[ht+3*rt+2];W[M++]=At,W[M++]=Nt,W[M++]=Ft,W[M++]=255}--jt}}}function wu(n,e,r,a){for(var u=n[e++],o=1<<u,c=o+1,h=c+1,f=u+1,g=(1<<f)-1,y=0,w=0,S=0,p=n[e++],O=new Int32Array(4096),F=null;;){for(;y<16&&p!==0;)w|=n[e++]<<y,y+=8,p===1?p=n[e++]:--p;if(y<f)break;var q=w&g;if(w>>=f,y-=f,q!==o){if(q===c)break;for(var _=q<h?q:F,B=0,Y=_;Y>o;)Y=O[Y]>>8,++B;var ot=Y;if(S+B+(_!==q?1:0)>a)return void ve.log("Warning, gif stream longer than expected.");r[S++]=ot;var ct=S+=B;for(_!==q&&(r[S++]=ot),Y=_;B--;)Y=O[Y],r[--ct]=255&Y,Y>>=8;F!==null&&h<4096&&(O[h++]=F<<8|ot,h>=g+1&&f<12&&(++f,g=g<<1|1)),F=q}else h=c+1,g=(1<<(f=u+1))-1,F=null}return S!==a&&ve.log("Warning, gif stream shorter than expected."),r}/**
 * @license
  Copyright (c) 2008, Adobe Systems Incorporated
  All rights reserved.

  Redistribution and use in source and binary forms, with or without 
  modification, are permitted provided that the following conditions are
  met:

  * Redistributions of source code must retain the above copyright notice, 
    this list of conditions and the following disclaimer.
  
  * Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the 
    documentation and/or other materials provided with the distribution.
  
  * Neither the name of Adobe Systems Incorporated nor the names of its 
    contributors may be used to endorse or promote products derived from 
    this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
  IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/function ps(n){var e,r,a,u,o,c=Math.floor,h=new Array(64),f=new Array(64),g=new Array(64),y=new Array(64),w=new Array(65535),S=new Array(65535),p=new Array(64),O=new Array(64),F=[],q=0,_=7,B=new Array(64),Y=new Array(64),ot=new Array(64),ct=new Array(256),wt=new Array(2048),tt=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],z=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],nt=[0,1,2,3,4,5,6,7,8,9,10,11],pt=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],P=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],k=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],W=[0,1,2,3,4,5,6,7,8,9,10,11],D=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],st=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function it(N,C){for(var M=0,T=0,J=new Array,Q=1;Q<=16;Q++){for(var et=1;et<=N[Q];et++)J[C[T]]=[],J[C[T]][0]=M,J[C[T]][1]=Q,T++,M++;M*=2}return J}function ht(N){for(var C=N[0],M=N[1]-1;M>=0;)C&1<<M&&(q|=1<<_),M--,--_<0&&(q==255?($(255),$(0)):$(q),_=7,q=0)}function $(N){F.push(N)}function lt(N){$(N>>8&255),$(255&N)}function dt(N,C,M,T,J){for(var Q,et=J[0],rt=J[240],At=function(Lt,xt){var It,kt,qt,Gt,Qt,te,ie,de,Wt,ee,Ct=0;for(Wt=0;Wt<8;++Wt){It=Lt[Ct],kt=Lt[Ct+1],qt=Lt[Ct+2],Gt=Lt[Ct+3],Qt=Lt[Ct+4],te=Lt[Ct+5],ie=Lt[Ct+6];var Je=It+(de=Lt[Ct+7]),oe=It-de,Sr=kt+ie,ge=kt-ie,Le=qt+te,zr=qt-te,ue=Gt+Qt,Mn=Gt-Qt,Ae=Je+ue,_r=Je-ue,en=Sr+Le,xe=Sr-Le;Lt[Ct]=Ae+en,Lt[Ct+4]=Ae-en;var Jt=.707106781*(xe+_r);Lt[Ct+2]=_r+Jt,Lt[Ct+6]=_r-Jt;var le=.382683433*((Ae=Mn+zr)-(xe=ge+oe)),En=.5411961*Ae+le,We=1.306562965*xe+le,Ur=.707106781*(en=zr+ge),Hr=oe+Ur,zt=oe-Ur;Lt[Ct+5]=zt+En,Lt[Ct+3]=zt-En,Lt[Ct+1]=Hr+We,Lt[Ct+7]=Hr-We,Ct+=8}for(Ct=0,Wt=0;Wt<8;++Wt){It=Lt[Ct],kt=Lt[Ct+8],qt=Lt[Ct+16],Gt=Lt[Ct+24],Qt=Lt[Ct+32],te=Lt[Ct+40],ie=Lt[Ct+48];var Pr=It+(de=Lt[Ct+56]),Wr=It-de,ir=kt+ie,De=kt-ie,Be=qt+te,hr=qt-te,ei=Gt+Qt,rn=Gt-Qt,kr=Pr+ei,Fr=Pr-ei,Ir=ir+Be,Vr=ir-Be;Lt[Ct]=kr+Ir,Lt[Ct+32]=kr-Ir;var vr=.707106781*(Vr+Fr);Lt[Ct+16]=Fr+vr,Lt[Ct+48]=Fr-vr;var Gr=.382683433*((kr=rn+hr)-(Vr=De+Wr)),qn=.5411961*kr+Gr,ri=1.306562965*Vr+Gr,ni=.707106781*(Ir=hr+De),ii=Wr+ni,ai=Wr-ni;Lt[Ct+40]=ai+qn,Lt[Ct+24]=ai-qn,Lt[Ct+8]=ii+ri,Lt[Ct+56]=ii-ri,Ct++}for(Wt=0;Wt<64;++Wt)ee=Lt[Wt]*xt[Wt],p[Wt]=ee>0?ee+.5|0:ee-.5|0;return p}(N,C),Nt=0;Nt<64;++Nt)O[tt[Nt]]=At[Nt];var Ft=O[0]-M;M=O[0],Ft==0?ht(T[0]):(ht(T[S[Q=32767+Ft]]),ht(w[Q]));for(var _t=63;_t>0&&O[_t]==0;)_t--;if(_t==0)return ht(et),M;for(var Ut,ft=1;ft<=_t;){for(var E=ft;O[ft]==0&&ft<=_t;)++ft;var Kt=ft-E;if(Kt>=16){Ut=Kt>>4;for(var Et=1;Et<=Ut;++Et)ht(rt);Kt&=15}Q=32767+O[ft],ht(J[(Kt<<4)+S[Q]]),ht(w[Q]),ft++}return _t!=63&&ht(et),M}function jt(N){N=Math.min(Math.max(N,1),100),o!=N&&(function(C){for(var M=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],T=0;T<64;T++){var J=c((M[T]*C+50)/100);J=Math.min(Math.max(J,1),255),h[tt[T]]=J}for(var Q=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],et=0;et<64;et++){var rt=c((Q[et]*C+50)/100);rt=Math.min(Math.max(rt,1),255),f[tt[et]]=rt}for(var At=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],Nt=0,Ft=0;Ft<8;Ft++)for(var _t=0;_t<8;_t++)g[Nt]=1/(h[tt[Nt]]*At[Ft]*At[_t]*8),y[Nt]=1/(f[tt[Nt]]*At[Ft]*At[_t]*8),Nt++}(N<50?Math.floor(5e3/N):Math.floor(200-2*N)),o=N)}this.encode=function(N,C){C&&jt(C),F=new Array,q=0,_=7,lt(65496),lt(65504),lt(16),$(74),$(70),$(73),$(70),$(0),$(1),$(1),$(0),lt(1),lt(1),$(0),$(0),function(){lt(65499),lt(132),$(0);for(var kt=0;kt<64;kt++)$(h[kt]);$(1);for(var qt=0;qt<64;qt++)$(f[qt])}(),function(kt,qt){lt(65472),lt(17),$(8),lt(qt),lt(kt),$(3),$(1),$(17),$(0),$(2),$(17),$(1),$(3),$(17),$(1)}(N.width,N.height),function(){lt(65476),lt(418),$(0);for(var kt=0;kt<16;kt++)$(z[kt+1]);for(var qt=0;qt<=11;qt++)$(nt[qt]);$(16);for(var Gt=0;Gt<16;Gt++)$(pt[Gt+1]);for(var Qt=0;Qt<=161;Qt++)$(P[Qt]);$(1);for(var te=0;te<16;te++)$(k[te+1]);for(var ie=0;ie<=11;ie++)$(W[ie]);$(17);for(var de=0;de<16;de++)$(D[de+1]);for(var Wt=0;Wt<=161;Wt++)$(st[Wt])}(),lt(65498),lt(12),$(3),$(1),$(0),$(2),$(17),$(3),$(17),$(0),$(63),$(0);var M=0,T=0,J=0;q=0,_=7,this.encode.displayName="_encode_";for(var Q,et,rt,At,Nt,Ft,_t,Ut,ft,E=N.data,Kt=N.width,Et=N.height,Lt=4*Kt,xt=0;xt<Et;){for(Q=0;Q<Lt;){for(Nt=Lt*xt+Q,_t=-1,Ut=0,ft=0;ft<64;ft++)Ft=Nt+(Ut=ft>>3)*Lt+(_t=4*(7&ft)),xt+Ut>=Et&&(Ft-=Lt*(xt+1+Ut-Et)),Q+_t>=Lt&&(Ft-=Q+_t-Lt+4),et=E[Ft++],rt=E[Ft++],At=E[Ft++],B[ft]=(wt[et]+wt[rt+256>>0]+wt[At+512>>0]>>16)-128,Y[ft]=(wt[et+768>>0]+wt[rt+1024>>0]+wt[At+1280>>0]>>16)-128,ot[ft]=(wt[et+1280>>0]+wt[rt+1536>>0]+wt[At+1792>>0]>>16)-128;M=dt(B,g,M,e,a),T=dt(Y,y,T,r,u),J=dt(ot,y,J,r,u),Q+=32}xt+=8}if(_>=0){var It=[];It[1]=_+1,It[0]=(1<<_+1)-1,ht(It)}return lt(65497),new Uint8Array(F)},n=n||50,function(){for(var N=String.fromCharCode,C=0;C<256;C++)ct[C]=N(C)}(),e=it(z,nt),r=it(k,W),a=it(pt,P),u=it(D,st),function(){for(var N=1,C=2,M=1;M<=15;M++){for(var T=N;T<C;T++)S[32767+T]=M,w[32767+T]=[],w[32767+T][1]=M,w[32767+T][0]=T;for(var J=-(C-1);J<=-N;J++)S[32767+J]=M,w[32767+J]=[],w[32767+J][1]=M,w[32767+J][0]=C-1+J;N<<=1,C<<=1}}(),function(){for(var N=0;N<256;N++)wt[N]=19595*N,wt[N+256>>0]=38470*N,wt[N+512>>0]=7471*N+32768,wt[N+768>>0]=-11059*N,wt[N+1024>>0]=-21709*N,wt[N+1280>>0]=32768*N+8421375,wt[N+1536>>0]=-27439*N,wt[N+1792>>0]=-5329*N}(),jt(n)}/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */function qr(n,e){if(this.pos=0,this.buffer=n,this.datav=new DataView(n.buffer),this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,["BM","BA","CI","CP","IC","PT"].indexOf(this.flag)===-1)throw new Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function Lu(n){function e(z){if(!z)throw Error("assert :P")}function r(z,nt,pt){for(var P=0;4>P;P++)if(z[nt+P]!=pt.charCodeAt(P))return!0;return!1}function a(z,nt,pt,P,k){for(var W=0;W<k;W++)z[nt+W]=pt[P+W]}function u(z,nt,pt,P){for(var k=0;k<P;k++)z[nt+k]=pt}function o(z){return new Int32Array(z)}function c(z,nt){for(var pt=[],P=0;P<z;P++)pt.push(new nt);return pt}function h(z,nt){var pt=[];return function P(k,W,D){for(var st=D[W],it=0;it<st&&(k.push(D.length>W+1?[]:new nt),!(D.length<W+1));it++)P(k[it],W+1,D)}(pt,0,z),pt}var f=function(){var z=this;function nt(t,i){for(var l=1<<i-1>>>0;t&l;)l>>>=1;return l?(t&l-1)+l:t}function pt(t,i,l,d,m){e(!(d%l));do t[i+(d-=l)]=m;while(0<d)}function P(t,i,l,d,m){if(e(2328>=m),512>=m)var b=o(512);else if((b=o(m))==null)return 0;return function(L,A,x,I,U,K){var Z,G,vt=A,at=1<<x,H=o(16),V=o(16);for(e(U!=0),e(I!=null),e(L!=null),e(0<x),G=0;G<U;++G){if(15<I[G])return 0;++H[I[G]]}if(H[0]==U)return 0;for(V[1]=0,Z=1;15>Z;++Z){if(H[Z]>1<<Z)return 0;V[Z+1]=V[Z]+H[Z]}for(G=0;G<U;++G)Z=I[G],0<I[G]&&(K[V[Z]++]=G);if(V[15]==1)return(I=new k).g=0,I.value=K[0],pt(L,vt,1,at,I),at;var gt,bt=-1,mt=at-1,Bt=0,St=1,Rt=1,Pt=1<<x;for(G=0,Z=1,U=2;Z<=x;++Z,U<<=1){if(St+=Rt<<=1,0>(Rt-=H[Z]))return 0;for(;0<H[Z];--H[Z])(I=new k).g=Z,I.value=K[G++],pt(L,vt+Bt,U,Pt,I),Bt=nt(Bt,Z)}for(Z=x+1,U=2;15>=Z;++Z,U<<=1){if(St+=Rt<<=1,0>(Rt-=H[Z]))return 0;for(;0<H[Z];--H[Z]){if(I=new k,(Bt&mt)!=bt){for(vt+=Pt,gt=1<<(bt=Z)-x;15>bt&&!(0>=(gt-=H[bt]));)++bt,gt<<=1;at+=Pt=1<<(gt=bt-x),L[A+(bt=Bt&mt)].g=gt+x,L[A+bt].value=vt-A-bt}I.g=Z-x,I.value=K[G++],pt(L,vt+(Bt>>x),U,Pt,I),Bt=nt(Bt,Z)}}return St!=2*V[15]-1?0:at}(t,i,l,d,m,b)}function k(){this.value=this.g=0}function W(){this.value=this.g=0}function D(){this.G=c(5,k),this.H=o(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=c(Ve,W)}function st(t,i,l,d){e(t!=null),e(i!=null),e(2147483648>d),t.Ca=254,t.I=0,t.b=-8,t.Ka=0,t.oa=i,t.pa=l,t.Jd=i,t.Yc=l+d,t.Zc=4<=d?l+d-4+1:l,Q(t)}function it(t,i){for(var l=0;0<i--;)l|=rt(t,128)<<i;return l}function ht(t,i){var l=it(t,i);return et(t)?-l:l}function $(t,i,l,d){var m,b=0;for(e(t!=null),e(i!=null),e(4294967288>d),t.Sb=d,t.Ra=0,t.u=0,t.h=0,4<d&&(d=4),m=0;m<d;++m)b+=i[l+m]<<8*m;t.Ra=b,t.bb=d,t.oa=i,t.pa=l}function lt(t){for(;8<=t.u&&t.bb<t.Sb;)t.Ra>>>=8,t.Ra+=t.oa[t.pa+t.bb]<<wi-8>>>0,++t.bb,t.u-=8;M(t)&&(t.h=1,t.u=0)}function dt(t,i){if(e(0<=i),!t.h&&i<=yi){var l=C(t)&bi[i];return t.u+=i,lt(t),l}return t.h=1,t.u=0}function jt(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function N(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function C(t){return t.Ra>>>(t.u&wi-1)>>>0}function M(t){return e(t.bb<=t.Sb),t.h||t.bb==t.Sb&&t.u>wi}function T(t,i){t.u=i,t.h=M(t)}function J(t){t.u>=$i&&(e(t.u>=$i),lt(t))}function Q(t){e(t!=null&&t.oa!=null),t.pa<t.Zc?(t.I=(t.oa[t.pa++]|t.I<<8)>>>0,t.b+=8):(e(t!=null&&t.oa!=null),t.pa<t.Yc?(t.b+=8,t.I=t.oa[t.pa++]|t.I<<8):t.Ka?t.b=0:(t.I<<=8,t.b+=8,t.Ka=1))}function et(t){return it(t,1)}function rt(t,i){var l=t.Ca;0>t.b&&Q(t);var d=t.b,m=l*i>>>8,b=(t.I>>>d>m)+0;for(b?(l-=m,t.I-=m+1<<d>>>0):l=m+1,d=l,m=0;256<=d;)m+=8,d>>=8;return d=7^m+or[d],t.b-=d,t.Ca=(l<<d)-1,b}function At(t,i,l){t[i+0]=l>>24&255,t[i+1]=l>>16&255,t[i+2]=l>>8&255,t[i+3]=l>>0&255}function Nt(t,i){return t[i+0]<<0|t[i+1]<<8}function Ft(t,i){return Nt(t,i)|t[i+2]<<16}function _t(t,i){return Nt(t,i)|Nt(t,i+2)<<16}function Ut(t,i){var l=1<<i;return e(t!=null),e(0<i),t.X=o(l),t.X==null?0:(t.Mb=32-i,t.Xa=i,1)}function ft(t,i){e(t!=null),e(i!=null),e(t.Xa==i.Xa),a(i.X,0,t.X,0,1<<i.Xa)}function E(){this.X=[],this.Xa=this.Mb=0}function Kt(t,i,l,d){e(l!=null),e(d!=null);var m=l[0],b=d[0];return m==0&&(m=(t*b+i/2)/i),b==0&&(b=(i*m+t/2)/t),0>=m||0>=b?0:(l[0]=m,d[0]=b,1)}function Et(t,i){return t+(1<<i)-1>>>i}function Lt(t,i){return((4278255360&t)+(4278255360&i)>>>0&4278255360)+((16711935&t)+(16711935&i)>>>0&16711935)>>>0}function xt(t,i){z[i]=function(l,d,m,b,L,A,x){var I;for(I=0;I<L;++I){var U=z[t](A[x+I-1],m,b+I);A[x+I]=Lt(l[d+I],U)}}}function It(){this.ud=this.hd=this.jd=0}function kt(t,i){return((4278124286&(t^i))>>>1)+(t&i)>>>0}function qt(t){return 0<=t&&256>t?t:0>t?0:255<t?255:void 0}function Gt(t,i){return qt(t+(t-i+.5>>1))}function Qt(t,i,l){return Math.abs(i-l)-Math.abs(t-l)}function te(t,i,l,d,m,b,L){for(d=b[L-1],l=0;l<m;++l)b[L+l]=d=Lt(t[i+l],d)}function ie(t,i,l,d,m){var b;for(b=0;b<l;++b){var L=t[i+b],A=L>>8&255,x=16711935&(x=(x=16711935&L)+((A<<16)+A));d[m+b]=(4278255360&L)+x>>>0}}function de(t,i){i.jd=t>>0&255,i.hd=t>>8&255,i.ud=t>>16&255}function Wt(t,i,l,d,m,b){var L;for(L=0;L<d;++L){var A=i[l+L],x=A>>>8,I=A,U=255&(U=(U=A>>>16)+((t.jd<<24>>24)*(x<<24>>24)>>>5));I=255&(I=(I=I+((t.hd<<24>>24)*(x<<24>>24)>>>5))+((t.ud<<24>>24)*(U<<24>>24)>>>5)),m[b+L]=(4278255360&A)+(U<<16)+I}}function ee(t,i,l,d,m){z[i]=function(b,L,A,x,I,U,K,Z,G){for(x=K;x<Z;++x)for(K=0;K<G;++K)I[U++]=m(A[d(b[L++])])},z[t]=function(b,L,A,x,I,U,K){var Z=8>>b.b,G=b.Ea,vt=b.K[0],at=b.w;if(8>Z)for(b=(1<<b.b)-1,at=(1<<Z)-1;L<A;++L){var H,V=0;for(H=0;H<G;++H)H&b||(V=d(x[I++])),U[K++]=m(vt[V&at]),V>>=Z}else z["VP8LMapColor"+l](x,I,vt,at,U,K,L,A,G)}}function Ct(t,i,l,d,m){for(l=i+l;i<l;){var b=t[i++];d[m++]=b>>16&255,d[m++]=b>>8&255,d[m++]=b>>0&255}}function Je(t,i,l,d,m){for(l=i+l;i<l;){var b=t[i++];d[m++]=b>>16&255,d[m++]=b>>8&255,d[m++]=b>>0&255,d[m++]=b>>24&255}}function oe(t,i,l,d,m){for(l=i+l;i<l;){var b=(L=t[i++])>>16&240|L>>12&15,L=L>>0&240|L>>28&15;d[m++]=b,d[m++]=L}}function Sr(t,i,l,d,m){for(l=i+l;i<l;){var b=(L=t[i++])>>16&248|L>>13&7,L=L>>5&224|L>>3&31;d[m++]=b,d[m++]=L}}function ge(t,i,l,d,m){for(l=i+l;i<l;){var b=t[i++];d[m++]=b>>0&255,d[m++]=b>>8&255,d[m++]=b>>16&255}}function Le(t,i,l,d,m,b){if(b==0)for(l=i+l;i<l;)At(d,((b=t[i++])[0]>>24|b[1]>>8&65280|b[2]<<8&16711680|b[3]<<24)>>>0),m+=32;else a(d,m,t,i,l)}function zr(t,i){z[i][0]=z[t+"0"],z[i][1]=z[t+"1"],z[i][2]=z[t+"2"],z[i][3]=z[t+"3"],z[i][4]=z[t+"4"],z[i][5]=z[t+"5"],z[i][6]=z[t+"6"],z[i][7]=z[t+"7"],z[i][8]=z[t+"8"],z[i][9]=z[t+"9"],z[i][10]=z[t+"10"],z[i][11]=z[t+"11"],z[i][12]=z[t+"12"],z[i][13]=z[t+"13"],z[i][14]=z[t+"0"],z[i][15]=z[t+"0"]}function ue(t){return t==Vo||t==Go||t==Xa||t==Jo}function Mn(){this.eb=[],this.size=this.A=this.fb=0}function Ae(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function _r(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new Mn,this.f.kb=new Ae,this.sd=null}function en(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function xe(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function Jt(t){return alert("todo:WebPSamplerProcessPlane"),t.T}function le(t,i){var l=t.T,d=i.ba.f.RGBA,m=d.eb,b=d.fb+t.ka*d.A,L=Ar[i.ba.S],A=t.y,x=t.O,I=t.f,U=t.N,K=t.ea,Z=t.W,G=i.cc,vt=i.dc,at=i.Mc,H=i.Nc,V=t.ka,gt=t.ka+t.T,bt=t.U,mt=bt+1>>1;for(V==0?L(A,x,null,null,I,U,K,Z,I,U,K,Z,m,b,null,null,bt):(L(i.ec,i.fc,A,x,G,vt,at,H,I,U,K,Z,m,b-d.A,m,b,bt),++l);V+2<gt;V+=2)G=I,vt=U,at=K,H=Z,U+=t.Rc,Z+=t.Rc,b+=2*d.A,L(A,(x+=2*t.fa)-t.fa,A,x,G,vt,at,H,I,U,K,Z,m,b-d.A,m,b,bt);return x+=t.fa,t.j+gt<t.o?(a(i.ec,i.fc,A,x,bt),a(i.cc,i.dc,I,U,mt),a(i.Mc,i.Nc,K,Z,mt),l--):1&gt||L(A,x,null,null,I,U,K,Z,I,U,K,Z,m,b+d.A,null,null,bt),l}function En(t,i,l){var d=t.F,m=[t.J];if(d!=null){var b=t.U,L=i.ba.S,A=L==Ya||L==Xa;i=i.ba.f.RGBA;var x=[0],I=t.ka;x[0]=t.T,t.Kb&&(I==0?--x[0]:(--I,m[0]-=t.width),t.j+t.ka+t.T==t.o&&(x[0]=t.o-t.j-I));var U=i.eb;I=i.fb+I*i.A,t=ye(d,m[0],t.width,b,x,U,I+(A?0:3),i.A),e(l==x),t&&ue(L)&&Lr(U,I,A,b,x,i.A)}return 0}function We(t){var i=t.ma,l=i.ba.S,d=11>l,m=l==Ga||l==Ja||l==Ya||l==Wo||l==12||ue(l);if(i.memory=null,i.Ib=null,i.Jb=null,i.Nd=null,!Ki(i.Oa,t,m?11:12))return 0;if(m&&ue(l)&&yt(),t.da)alert("todo:use_scaling");else{if(d){if(i.Ib=Jt,t.Kb){if(l=t.U+1>>1,i.memory=o(t.U+2*l),i.memory==null)return 0;i.ec=i.memory,i.fc=0,i.cc=i.ec,i.dc=i.fc+t.U,i.Mc=i.cc,i.Nc=i.dc+l,i.Ib=le,yt()}}else alert("todo:EmitYUV");m&&(i.Jb=En,d&&X())}if(d&&!Rs){for(t=0;256>t;++t)ul[t]=89858*(t-128)+Za>>Ka,hl[t]=-22014*(t-128)+Za,cl[t]=-45773*(t-128),ll[t]=113618*(t-128)+Za>>Ka;for(t=aa;t<Ko;++t)i=76283*(t-16)+Za>>Ka,fl[t-aa]=fr(i,255),dl[t-aa]=fr(i+8>>4,15);Rs=1}return 1}function Ur(t){var i=t.ma,l=t.U,d=t.T;return e(!(1&t.ka)),0>=l||0>=d?0:(l=i.Ib(t,i),i.Jb!=null&&i.Jb(t,i,l),i.Dc+=l,1)}function Hr(t){t.ma.memory=null}function zt(t,i,l,d){return dt(t,8)!=47?0:(i[0]=dt(t,14)+1,l[0]=dt(t,14)+1,d[0]=dt(t,1),dt(t,3)!=0?0:!t.h)}function Pr(t,i){if(4>t)return t+1;var l=t-2>>1;return(2+(1&t)<<l)+dt(i,l)+1}function Wr(t,i){return 120<i?i-120:1<=(l=((l=Yu[i-1])>>4)*t+(8-(15&l)))?l:1;var l}function ir(t,i,l){var d=C(l),m=t[i+=255&d].g-8;return 0<m&&(T(l,l.u+8),d=C(l),i+=t[i].value,i+=d&(1<<m)-1),T(l,l.u+t[i].g),t[i].value}function De(t,i,l){return l.g+=t.g,l.value+=t.value<<i>>>0,e(8>=l.g),t.g}function Be(t,i,l){var d=t.xc;return e((i=d==0?0:t.vc[t.md*(l>>d)+(i>>d)])<t.Wb),t.Ya[i]}function hr(t,i,l,d){var m=t.ab,b=t.c*i,L=t.C;i=L+i;var A=l,x=d;for(d=t.Ta,l=t.Ua;0<m--;){var I=t.gc[m],U=L,K=i,Z=A,G=x,vt=(x=d,A=l,I.Ea);switch(e(U<K),e(K<=I.nc),I.hc){case 2:Ra(Z,G,(K-U)*vt,x,A);break;case 0:var at=U,H=K,V=x,gt=A,bt=(Pt=I).Ea;at==0&&(Uo(Z,G,null,null,1,V,gt),te(Z,G+1,0,0,bt-1,V,gt+1),G+=bt,gt+=bt,++at);for(var mt=1<<Pt.b,Bt=mt-1,St=Et(bt,Pt.b),Rt=Pt.K,Pt=Pt.w+(at>>Pt.b)*St;at<H;){var se=Rt,ce=Pt,ae=1;for(Qi(Z,G,V,gt-bt,1,V,gt);ae<bt;){var re=(ae&~Bt)+mt;re>bt&&(re=bt),(0,vn[se[ce++]>>8&15])(Z,G+ +ae,V,gt+ae-bt,re-ae,V,gt+ae),ae=re}G+=bt,gt+=bt,++at&Bt||(Pt+=St)}K!=I.nc&&a(x,A-vt,x,A+(K-U-1)*vt,vt);break;case 1:for(vt=Z,H=G,bt=(Z=I.Ea)-(gt=Z&~(V=(G=1<<I.b)-1)),at=Et(Z,I.b),mt=I.K,I=I.w+(U>>I.b)*at;U<K;){for(Bt=mt,St=I,Rt=new It,Pt=H+gt,se=H+Z;H<Pt;)de(Bt[St++],Rt),Un(Rt,vt,H,G,x,A),H+=G,A+=G;H<se&&(de(Bt[St++],Rt),Un(Rt,vt,H,bt,x,A),H+=bt,A+=bt),++U&V||(I+=at)}break;case 3:if(Z==x&&G==A&&0<I.b){for(H=x,Z=vt=A+(K-U)*vt-(gt=(K-U)*Et(I.Ea,I.b)),G=x,V=A,at=[],gt=(bt=gt)-1;0<=gt;--gt)at[gt]=G[V+gt];for(gt=bt-1;0<=gt;--gt)H[Z+gt]=at[gt];br(I,U,K,x,vt,x,A)}else br(I,U,K,Z,G,x,A)}A=d,x=l}x!=l&&a(d,l,A,x,b)}function ei(t,i){var l=t.V,d=t.Ba+t.c*t.C,m=i-t.C;if(e(i<=t.l.o),e(16>=m),0<m){var b=t.l,L=t.Ta,A=t.Ua,x=b.width;if(hr(t,m,l,d),m=A=[A],e((l=t.C)<(d=i)),e(b.v<b.va),d>b.o&&(d=b.o),l<b.j){var I=b.j-l;l=b.j,m[0]+=I*x}if(l>=d?l=0:(m[0]+=4*b.v,b.ka=l-b.j,b.U=b.va-b.v,b.T=d-l,l=1),l){if(A=A[0],11>(l=t.ca).S){var U=l.f.RGBA,K=(d=l.S,m=b.U,b=b.T,I=U.eb,U.A),Z=b;for(U=U.fb+t.Ma*U.A;0<Z--;){var G=L,vt=A,at=m,H=I,V=U;switch(d){case Va:sr(G,vt,at,H,V);break;case Ga:er(G,vt,at,H,V);break;case Vo:er(G,vt,at,H,V),Lr(H,V,0,at,1,0);break;case Is:sn(G,vt,at,H,V);break;case Ja:Le(G,vt,at,H,V,1);break;case Go:Le(G,vt,at,H,V,1),Lr(H,V,0,at,1,0);break;case Ya:Le(G,vt,at,H,V,0);break;case Xa:Le(G,vt,at,H,V,0),Lr(H,V,1,at,1,0);break;case Wo:bn(G,vt,at,H,V);break;case Jo:bn(G,vt,at,H,V),be(H,V,at,1,0);break;case Cs:on(G,vt,at,H,V);break;default:e(0)}A+=x,U+=K}t.Ma+=b}else alert("todo:EmitRescaledRowsYUVA");e(t.Ma<=l.height)}}t.C=i,e(t.C<=t.i)}function rn(t){var i;if(0<t.ua)return 0;for(i=0;i<t.Wb;++i){var l=t.Ya[i].G,d=t.Ya[i].H;if(0<l[1][d[1]+0].g||0<l[2][d[2]+0].g||0<l[3][d[3]+0].g)return 0}return 1}function kr(t,i,l,d,m,b){if(t.Z!=0){var L=t.qd,A=t.rd;for(e(Nn[t.Z]!=null);i<l;++i)Nn[t.Z](L,A,d,m,d,m,b),L=d,A=m,m+=b;t.qd=L,t.rd=A}}function Fr(t,i){var l=t.l.ma,d=l.Z==0||l.Z==1?t.l.j:t.C;if(d=t.C<d?d:t.C,e(i<=t.l.o),i>d){var m=t.l.width,b=l.ca,L=l.tb+m*d,A=t.V,x=t.Ba+t.c*d,I=t.gc;e(t.ab==1),e(I[0].hc==3),Ta(I[0],d,i,A,x,b,L),kr(l,d,i,b,L,m)}t.C=t.Ma=i}function Ir(t,i,l,d,m,b,L){var A=t.$/d,x=t.$%d,I=t.m,U=t.s,K=l+t.$,Z=K;m=l+d*m;var G=l+d*b,vt=280+U.ua,at=t.Pb?A:16777216,H=0<U.ua?U.Wa:null,V=U.wc,gt=K<G?Be(U,x,A):null;e(t.C<b),e(G<=m);var bt=!1;t:for(;;){for(;bt||K<G;){var mt=0;if(A>=at){var Bt=K-l;e((at=t).Pb),at.wd=at.m,at.xd=Bt,0<at.s.ua&&ft(at.s.Wa,at.s.vb),at=A+Ku}if(x&V||(gt=Be(U,x,A)),e(gt!=null),gt.Qb&&(i[K]=gt.qb,bt=!0),!bt)if(J(I),gt.jc){mt=I,Bt=i;var St=K,Rt=gt.pd[C(mt)&Ve-1];e(gt.jc),256>Rt.g?(T(mt,mt.u+Rt.g),Bt[St]=Rt.value,mt=0):(T(mt,mt.u+Rt.g-256),e(256<=Rt.value),mt=Rt.value),mt==0&&(bt=!0)}else mt=ir(gt.G[0],gt.H[0],I);if(I.h)break;if(bt||256>mt){if(!bt)if(gt.nd)i[K]=(gt.qb|mt<<8)>>>0;else{if(J(I),bt=ir(gt.G[1],gt.H[1],I),J(I),Bt=ir(gt.G[2],gt.H[2],I),St=ir(gt.G[3],gt.H[3],I),I.h)break;i[K]=(St<<24|bt<<16|mt<<8|Bt)>>>0}if(bt=!1,++K,++x>=d&&(x=0,++A,L!=null&&A<=b&&!(A%16)&&L(t,A),H!=null))for(;Z<K;)mt=i[Z++],H.X[(506832829*mt&**********)>>>H.Mb]=mt}else if(280>mt){if(mt=Pr(mt-256,I),Bt=ir(gt.G[4],gt.H[4],I),J(I),Bt=Wr(d,Bt=Pr(Bt,I)),I.h)break;if(K-l<Bt||m-K<mt)break t;for(St=0;St<mt;++St)i[K+St]=i[K+St-Bt];for(K+=mt,x+=mt;x>=d;)x-=d,++A,L!=null&&A<=b&&!(A%16)&&L(t,A);if(e(K<=m),x&V&&(gt=Be(U,x,A)),H!=null)for(;Z<K;)mt=i[Z++],H.X[(506832829*mt&**********)>>>H.Mb]=mt}else{if(!(mt<vt))break t;for(bt=mt-280,e(H!=null);Z<K;)mt=i[Z++],H.X[(506832829*mt&**********)>>>H.Mb]=mt;mt=K,e(!(bt>>>(Bt=H).Xa)),i[mt]=Bt.X[bt],bt=!0}bt||e(I.h==M(I))}if(t.Pb&&I.h&&K<m)e(t.m.h),t.a=5,t.m=t.wd,t.$=t.xd,0<t.s.ua&&ft(t.s.vb,t.s.Wa);else{if(I.h)break t;L!=null&&L(t,A>b?b:A),t.a=0,t.$=K-l}return 1}return t.a=3,0}function Vr(t){e(t!=null),t.vc=null,t.yc=null,t.Ya=null;var i=t.Wa;i!=null&&(i.X=null),t.vb=null,e(t!=null)}function vr(){var t=new zo;return t==null?null:(t.a=0,t.xb=Bs,zr("Predictor","VP8LPredictors"),zr("Predictor","VP8LPredictors_C"),zr("PredictorAdd","VP8LPredictorsAdd"),zr("PredictorAdd","VP8LPredictorsAdd_C"),Ra=ie,Un=Wt,sr=Ct,er=Je,bn=oe,on=Sr,sn=ge,z.VP8LMapColor32b=Li,z.VP8LMapColor8b=za,t)}function Gr(t,i,l,d,m){var b=1,L=[t],A=[i],x=d.m,I=d.s,U=null,K=0;t:for(;;){if(l)for(;b&&dt(x,1);){var Z=L,G=A,vt=d,at=1,H=vt.m,V=vt.gc[vt.ab],gt=dt(H,2);if(vt.Oc&1<<gt)b=0;else{switch(vt.Oc|=1<<gt,V.hc=gt,V.Ea=Z[0],V.nc=G[0],V.K=[null],++vt.ab,e(4>=vt.ab),gt){case 0:case 1:V.b=dt(H,3)+2,at=Gr(Et(V.Ea,V.b),Et(V.nc,V.b),0,vt,V.K),V.K=V.K[0];break;case 3:var bt,mt=dt(H,8)+1,Bt=16<mt?0:4<mt?1:2<mt?2:3;if(Z[0]=Et(V.Ea,Bt),V.b=Bt,bt=at=Gr(mt,1,0,vt,V.K)){var St,Rt=mt,Pt=V,se=1<<(8>>Pt.b),ce=o(se);if(ce==null)bt=0;else{var ae=Pt.K[0],re=Pt.w;for(ce[0]=Pt.K[0][0],St=1;St<1*Rt;++St)ce[St]=Lt(ae[re+St],ce[St-1]);for(;St<4*se;++St)ce[St]=0;Pt.K[0]=null,Pt.K[0]=ce,bt=1}}at=bt;break;case 2:break;default:e(0)}b=at}}if(L=L[0],A=A[0],b&&dt(x,1)&&!(b=1<=(K=dt(x,4))&&11>=K)){d.a=3;break t}var me;if(me=b)e:{var pe,$t,Ee,ur=d,qe=L,lr=A,he=K,pr=l,gr=ur.m,Ue=ur.s,Ge=[null],nr=1,xr=0,Kr=Xu[he];r:for(;;){if(pr&&dt(gr,1)){var He=dt(gr,3)+2,hn=Et(qe,He),Jn=Et(lr,He),_i=hn*Jn;if(!Gr(hn,Jn,0,ur,Ge))break r;for(Ge=Ge[0],Ue.xc=He,pe=0;pe<_i;++pe){var An=Ge[pe]>>8&65535;Ge[pe]=An,An>=nr&&(nr=An+1)}}if(gr.h)break r;for($t=0;5>$t;++$t){var _e=js[$t];!$t&&0<he&&(_e+=1<<he),xr<_e&&(xr=_e)}var Zo=c(nr*Kr,k),Us=nr,Hs=c(Us,D);if(Hs==null)var Qa=null;else e(65536>=Us),Qa=Hs;var oa=o(xr);if(Qa==null||oa==null||Zo==null){ur.a=1;break r}var to=Zo;for(pe=Ee=0;pe<nr;++pe){var Mr=Qa[pe],Pi=Mr.G,ki=Mr.H,Ws=0,eo=1,Vs=0;for($t=0;5>$t;++$t){_e=js[$t],Pi[$t]=to,ki[$t]=Ee,!$t&&0<he&&(_e+=1<<he);i:{var ro,$o=_e,no=ur,sa=oa,ml=to,vl=Ee,Qo=0,xn=no.m,bl=dt(xn,1);if(u(sa,0,0,$o),bl){var yl=dt(xn,1)+1,wl=dt(xn,1),Gs=dt(xn,wl==0?1:8);sa[Gs]=1,yl==2&&(sa[Gs=dt(xn,8)]=1);var io=1}else{var Js=o(19),Ys=dt(xn,4)+4;if(19<Ys){no.a=3;var ao=0;break i}for(ro=0;ro<Ys;++ro)Js[Ju[ro]]=dt(xn,3);var ts=void 0,ua=void 0,Xs=no,Ll=Js,oo=$o,Ks=sa,es=0,Sn=Xs.m,Zs=8,$s=c(128,k);n:for(;P($s,0,7,Ll,19);){if(dt(Sn,1)){var Nl=2+2*dt(Sn,3);if((ts=2+dt(Sn,Nl))>oo)break n}else ts=oo;for(ua=0;ua<oo&&ts--;){J(Sn);var Qs=$s[0+(127&C(Sn))];T(Sn,Sn.u+Qs.g);var Fi=Qs.value;if(16>Fi)Ks[ua++]=Fi,Fi!=0&&(Zs=Fi);else{var Al=Fi==16,tu=Fi-16,xl=Vu[tu],eu=dt(Sn,Wu[tu])+xl;if(ua+eu>oo)break n;for(var Sl=Al?Zs:0;0<eu--;)Ks[ua++]=Sl}}es=1;break n}es||(Xs.a=3),io=es}(io=io&&!xn.h)&&(Qo=P(ml,vl,8,sa,$o)),io&&Qo!=0?ao=Qo:(no.a=3,ao=0)}if(ao==0)break r;if(eo&&Gu[$t]==1&&(eo=to[Ee].g==0),Ws+=to[Ee].g,Ee+=ao,3>=$t){var la,rs=oa[0];for(la=1;la<_e;++la)oa[la]>rs&&(rs=oa[la]);Vs+=rs}}if(Mr.nd=eo,Mr.Qb=0,eo&&(Mr.qb=(Pi[3][ki[3]+0].value<<24|Pi[1][ki[1]+0].value<<16|Pi[2][ki[2]+0].value)>>>0,Ws==0&&256>Pi[0][ki[0]+0].value&&(Mr.Qb=1,Mr.qb+=Pi[0][ki[0]+0].value<<8)),Mr.jc=!Mr.Qb&&6>Vs,Mr.jc){var so,fn=Mr;for(so=0;so<Ve;++so){var _n=so,Pn=fn.pd[_n],uo=fn.G[0][fn.H[0]+_n];256<=uo.value?(Pn.g=uo.g+256,Pn.value=uo.value):(Pn.g=0,Pn.value=0,_n>>=De(uo,8,Pn),_n>>=De(fn.G[1][fn.H[1]+_n],16,Pn),_n>>=De(fn.G[2][fn.H[2]+_n],0,Pn),De(fn.G[3][fn.H[3]+_n],24,Pn))}}}Ue.vc=Ge,Ue.Wb=nr,Ue.Ya=Qa,Ue.yc=Zo,me=1;break e}me=0}if(!(b=me)){d.a=3;break t}if(0<K){if(I.ua=1<<K,!Ut(I.Wa,K)){d.a=1,b=0;break t}}else I.ua=0;var ns=d,ru=L,_l=A,is=ns.s,as=is.xc;if(ns.c=ru,ns.i=_l,is.md=Et(ru,as),is.wc=as==0?-1:(1<<as)-1,l){d.xb=nl;break t}if((U=o(L*A))==null){d.a=1,b=0;break t}b=(b=Ir(d,U,0,L,A,A,null))&&!x.h;break t}return b?(m!=null?m[0]=U:(e(U==null),e(l)),d.$=0,l||Vr(I)):Vr(I),b}function qn(t,i){var l=t.c*t.i,d=l+i+16*i;return e(t.c<=i),t.V=o(d),t.V==null?(t.Ta=null,t.Ua=0,t.a=1,0):(t.Ta=t.V,t.Ua=t.Ba+l+i,1)}function ri(t,i){var l=t.C,d=i-l,m=t.V,b=t.Ba+t.c*l;for(e(i<=t.l.o);0<d;){var L=16<d?16:d,A=t.l.ma,x=t.l.width,I=x*L,U=A.ca,K=A.tb+x*l,Z=t.Ta,G=t.Ua;hr(t,L,m,b),Ie(Z,G,U,K,I),kr(A,l,l+L,U,K,x),d-=L,m+=L*t.c,l+=L}e(l==i),t.C=t.Ma=i}function ni(){this.ub=this.yd=this.td=this.Rb=0}function ii(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function ai(){this.Fb=this.Bb=this.Cb=0,this.Zb=o(4),this.Lb=o(4)}function ya(){this.Yb=function(){var t=[];return function i(l,d,m){for(var b=m[d],L=0;L<b&&(l.push(m.length>d+1?[]:0),!(m.length<d+1));L++)i(l[L],d+1,m)}(t,0,[3,11]),t}()}function bo(){this.jb=o(3),this.Wc=h([4,8],ya),this.Xc=h([4,17],ya)}function yo(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new o(4),this.od=new o(4)}function oi(){this.ld=this.La=this.dd=this.tc=0}function wa(){this.Na=this.la=0}function wo(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function Bi(){this.ad=o(384),this.Za=0,this.Ob=o(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function Lo(){this.uc=this.M=this.Nb=0,this.wa=Array(new oi),this.Y=0,this.ya=Array(new Bi),this.aa=0,this.l=new si}function La(){this.y=o(16),this.f=o(8),this.ea=o(8)}function No(){this.cb=this.a=0,this.sc="",this.m=new jt,this.Od=new ni,this.Kc=new ii,this.ed=new yo,this.Qa=new ai,this.Ic=this.$c=this.Aa=0,this.D=new Lo,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=c(8,jt),this.ia=0,this.pb=c(4,wo),this.Pa=new bo,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new La),this.Hd=0,this.rb=Array(new wa),this.sb=0,this.wa=Array(new oi),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new Bi),this.L=this.aa=0,this.gd=h([4,2],oi),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function si(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function Ao(){var t=new No;return t!=null&&(t.a=0,t.sc="OK",t.cb=0,t.Xb=0,ia||(ia=xa)),t}function ke(t,i,l){return t.a==0&&(t.a=i,t.sc=l,t.cb=0),0}function Na(t,i,l){return 3<=l&&t[i+0]==157&&t[i+1]==1&&t[i+2]==42}function Aa(t,i){if(t==null)return 0;if(t.a=0,t.sc="OK",i==null)return ke(t,2,"null VP8Io passed to VP8GetHeaders()");var l=i.data,d=i.w,m=i.ha;if(4>m)return ke(t,7,"Truncated header.");var b=l[d+0]|l[d+1]<<8|l[d+2]<<16,L=t.Od;if(L.Rb=!(1&b),L.td=b>>1&7,L.yd=b>>4&1,L.ub=b>>5,3<L.td)return ke(t,3,"Incorrect keyframe parameters.");if(!L.yd)return ke(t,4,"Frame not displayable.");d+=3,m-=3;var A=t.Kc;if(L.Rb){if(7>m)return ke(t,7,"cannot parse picture header");if(!Na(l,d,m))return ke(t,3,"Bad code word");A.c=16383&(l[d+4]<<8|l[d+3]),A.Td=l[d+4]>>6,A.i=16383&(l[d+6]<<8|l[d+5]),A.Ud=l[d+6]>>6,d+=7,m-=7,t.za=A.c+15>>4,t.Ub=A.i+15>>4,i.width=A.c,i.height=A.i,i.Da=0,i.j=0,i.v=0,i.va=i.width,i.o=i.height,i.da=0,i.ib=i.width,i.hb=i.height,i.U=i.width,i.T=i.height,u((b=t.Pa).jb,0,255,b.jb.length),e((b=t.Qa)!=null),b.Cb=0,b.Bb=0,b.Fb=1,u(b.Zb,0,0,b.Zb.length),u(b.Lb,0,0,b.Lb)}if(L.ub>m)return ke(t,7,"bad partition length");st(b=t.m,l,d,L.ub),d+=L.ub,m-=L.ub,L.Rb&&(A.Ld=et(b),A.Kd=et(b)),A=t.Qa;var x,I=t.Pa;if(e(b!=null),e(A!=null),A.Cb=et(b),A.Cb){if(A.Bb=et(b),et(b)){for(A.Fb=et(b),x=0;4>x;++x)A.Zb[x]=et(b)?ht(b,7):0;for(x=0;4>x;++x)A.Lb[x]=et(b)?ht(b,6):0}if(A.Bb)for(x=0;3>x;++x)I.jb[x]=et(b)?it(b,8):255}else A.Bb=0;if(b.Ka)return ke(t,3,"cannot parse segment header");if((A=t.ed).zd=et(b),A.Tb=it(b,6),A.wb=it(b,3),A.Pc=et(b),A.Pc&&et(b)){for(I=0;4>I;++I)et(b)&&(A.vd[I]=ht(b,6));for(I=0;4>I;++I)et(b)&&(A.od[I]=ht(b,6))}if(t.L=A.Tb==0?0:A.zd?1:2,b.Ka)return ke(t,3,"cannot parse filter header");var U=m;if(m=x=d,d=x+U,A=U,t.Xb=(1<<it(t.m,2))-1,U<3*(I=t.Xb))l=7;else{for(x+=3*I,A-=3*I,U=0;U<I;++U){var K=l[m+0]|l[m+1]<<8|l[m+2]<<16;K>A&&(K=A),st(t.Jc[+U],l,x,K),x+=K,A-=K,m+=3}st(t.Jc[+I],l,x,A),l=x<d?0:5}if(l!=0)return ke(t,l,"cannot parse partitions");for(l=it(x=t.m,7),m=et(x)?ht(x,4):0,d=et(x)?ht(x,4):0,A=et(x)?ht(x,4):0,I=et(x)?ht(x,4):0,x=et(x)?ht(x,4):0,U=t.Qa,K=0;4>K;++K){if(U.Cb){var Z=U.Zb[K];U.Fb||(Z+=l)}else{if(0<K){t.pb[K]=t.pb[0];continue}Z=l}var G=t.pb[K];G.Sc[0]=Yo[fr(Z+m,127)],G.Sc[1]=Xo[fr(Z+0,127)],G.Eb[0]=2*Yo[fr(Z+d,127)],G.Eb[1]=101581*Xo[fr(Z+A,127)]>>16,8>G.Eb[1]&&(G.Eb[1]=8),G.Qc[0]=Yo[fr(Z+I,117)],G.Qc[1]=Xo[fr(Z+x,127)],G.lc=Z+x}if(!L.Rb)return ke(t,4,"Not a key frame.");for(et(b),L=t.Pa,l=0;4>l;++l){for(m=0;8>m;++m)for(d=0;3>d;++d)for(A=0;11>A;++A)I=rt(b,el[l][m][d][A])?it(b,8):Qu[l][m][d][A],L.Wc[l][m].Yb[d][A]=I;for(m=0;17>m;++m)L.Xc[l][m]=L.Wc[l][rl[m]]}return t.kc=et(b),t.kc&&(t.Bd=it(b,8)),t.cb=1}function xa(t,i,l,d,m,b,L){var A=i[m].Yb[l];for(l=0;16>m;++m){if(!rt(t,A[l+0]))return m;for(;!rt(t,A[l+1]);)if(A=i[++m].Yb[0],l=0,m==16)return 16;var x=i[m+1].Yb;if(rt(t,A[l+2])){var I=t,U=0;if(rt(I,(Z=A)[(K=l)+3]))if(rt(I,Z[K+6])){for(A=0,K=2*(U=rt(I,Z[K+8]))+(Z=rt(I,Z[K+9+U])),U=0,Z=Zu[K];Z[A];++A)U+=U+rt(I,Z[A]);U+=3+(8<<K)}else rt(I,Z[K+7])?(U=7+2*rt(I,165),U+=rt(I,145)):U=5+rt(I,159);else U=rt(I,Z[K+4])?3+rt(I,Z[K+5]):2;A=x[2]}else U=1,A=x[1];x=L+$u[m],0>(I=t).b&&Q(I);var K,Z=I.b,G=(K=I.Ca>>1)-(I.I>>Z)>>31;--I.b,I.Ca+=G,I.Ca|=1,I.I-=(K+1&G)<<Z,b[x]=((U^G)-G)*d[(0<m)+0]}return 16}function Mi(t){var i=t.rb[t.sb-1];i.la=0,i.Na=0,u(t.zc,0,0,t.zc.length),t.ja=0}function xo(t,i){if(t==null)return 0;if(i==null)return ke(t,2,"NULL VP8Io parameter in VP8Decode().");if(!t.cb&&!Aa(t,i))return 0;if(e(t.cb),i.ac==null||i.ac(i)){i.ob&&(t.L=0);var l=$a[t.L];if(t.L==2?(t.yb=0,t.zb=0):(t.yb=i.v-l>>4,t.zb=i.j-l>>4,0>t.yb&&(t.yb=0),0>t.zb&&(t.zb=0)),t.Va=i.o+15+l>>4,t.Hb=i.va+15+l>>4,t.Hb>t.za&&(t.Hb=t.za),t.Va>t.Ub&&(t.Va=t.Ub),0<t.L){var d=t.ed;for(l=0;4>l;++l){var m;if(t.Qa.Cb){var b=t.Qa.Lb[l];t.Qa.Fb||(b+=d.Tb)}else b=d.Tb;for(m=0;1>=m;++m){var L=t.gd[l][m],A=b;if(d.Pc&&(A+=d.vd[0],m&&(A+=d.od[0])),0<(A=0>A?0:63<A?63:A)){var x=A;0<d.wb&&(x=4<d.wb?x>>2:x>>1)>9-d.wb&&(x=9-d.wb),1>x&&(x=1),L.dd=x,L.tc=2*A+x,L.ld=40<=A?2:15<=A?1:0}else L.tc=0;L.La=m}}}l=0}else ke(t,6,"Frame setup failed"),l=t.a;if(l=l==0){if(l){t.$c=0,0<t.Aa||(t.Ic=gl);t:{l=t.Ic,d=4*(x=t.za);var I=32*x,U=x+1,K=0<t.L?x*(0<t.Aa?2:1):0,Z=(t.Aa==2?2:1)*x;if((L=d+832+(m=3*(16*l+$a[t.L])/2*I)+(b=t.Fa!=null&&0<t.Fa.length?t.Kc.c*t.Kc.i:0))!=L)l=0;else{if(L>t.Vb){if(t.Vb=0,t.Ec=o(L),t.Fc=0,t.Ec==null){l=ke(t,1,"no memory during frame initialization.");break t}t.Vb=L}L=t.Ec,A=t.Fc,t.Ac=L,t.Bc=A,A+=d,t.Gd=c(I,La),t.Hd=0,t.rb=c(U+1,wa),t.sb=1,t.wa=K?c(K,oi):null,t.Y=0,t.D.Nb=0,t.D.wa=t.wa,t.D.Y=t.Y,0<t.Aa&&(t.D.Y+=x),e(!0),t.oc=L,t.pc=A,A+=832,t.ya=c(Z,Bi),t.aa=0,t.D.ya=t.ya,t.D.aa=t.aa,t.Aa==2&&(t.D.aa+=x),t.R=16*x,t.B=8*x,x=(I=$a[t.L])*t.R,I=I/2*t.B,t.sa=L,t.ta=A+x,t.qa=t.sa,t.ra=t.ta+16*l*t.R+I,t.Ha=t.qa,t.Ia=t.ra+8*l*t.B+I,t.$c=0,A+=m,t.mb=b?L:null,t.nb=b?A:null,e(A+b<=t.Fc+t.Vb),Mi(t),u(t.Ac,t.Bc,0,d),l=1}}if(l){if(i.ka=0,i.y=t.sa,i.O=t.ta,i.f=t.qa,i.N=t.ra,i.ea=t.Ha,i.Vd=t.Ia,i.fa=t.R,i.Rc=t.B,i.F=null,i.J=0,!Ha){for(l=-255;255>=l;++l)Re[255+l]=0>l?-l:l;for(l=-1020;1020>=l;++l)ln[1020+l]=-128>l?-128:127<l?127:l;for(l=-112;112>=l;++l)na[112+l]=-16>l?-16:15<l?15:l;for(l=-255;510>=l;++l)Si[255+l]=0>l?0:255<l?255:l;Ha=1}Ni=Po,un=So,ta=_a,rr=_o,yr=Pa,Fe=Sa,Ai=zi,Ua=Tn,ea=To,Hn=Ui,Wn=Ro,yn=di,Vn=Hi,xi=Ea,Gn=Ma,wn=Yr,ra=an,wr=Do,Br[0]=Jr,Br[1]=ko,Br[2]=jo,Br[3]=Oo,Br[4]=Ia,Br[5]=hi,Br[6]=Ca,Br[7]=Di,Br[8]=Mo,Br[9]=Bo,Ln[0]=ka,Ln[1]=Io,Ln[2]=nn,Ln[3]=li,Ln[4]=Ye,Ln[5]=Co,Ln[6]=Fa,cn[0]=pn,cn[1]=Fo,cn[2]=Eo,cn[3]=Ri,cn[4]=Rn,cn[5]=qo,cn[6]=Ti,l=1}else l=0}l&&(l=function(G,vt){for(G.M=0;G.M<G.Va;++G.M){var at,H=G.Jc[G.M&G.Xb],V=G.m,gt=G;for(at=0;at<gt.za;++at){var bt=V,mt=gt,Bt=mt.Ac,St=mt.Bc+4*at,Rt=mt.zc,Pt=mt.ya[mt.aa+at];if(mt.Qa.Bb?Pt.$b=rt(bt,mt.Pa.jb[0])?2+rt(bt,mt.Pa.jb[2]):rt(bt,mt.Pa.jb[1]):Pt.$b=0,mt.kc&&(Pt.Ad=rt(bt,mt.Bd)),Pt.Za=!rt(bt,145)+0,Pt.Za){var se=Pt.Ob,ce=0;for(mt=0;4>mt;++mt){var ae,re=Rt[0+mt];for(ae=0;4>ae;++ae){re=tl[Bt[St+ae]][re];for(var me=Os[rt(bt,re[0])];0<me;)me=Os[2*me+rt(bt,re[me])];re=-me,Bt[St+ae]=re}a(se,ce,Bt,St,4),ce+=4,Rt[0+mt]=re}}else re=rt(bt,156)?rt(bt,128)?1:3:rt(bt,163)?2:0,Pt.Ob[0]=re,u(Bt,St,re,4),u(Rt,0,re,4);Pt.Dd=rt(bt,142)?rt(bt,114)?rt(bt,183)?1:3:2:0}if(gt.m.Ka)return ke(G,7,"Premature end-of-partition0 encountered.");for(;G.ja<G.za;++G.ja){if(gt=H,bt=(V=G).rb[V.sb-1],Bt=V.rb[V.sb+V.ja],at=V.ya[V.aa+V.ja],St=V.kc?at.Ad:0)bt.la=Bt.la=0,at.Za||(bt.Na=Bt.Na=0),at.Hc=0,at.Gc=0,at.ia=0;else{var pe,$t;if(bt=Bt,Bt=gt,St=V.Pa.Xc,Rt=V.ya[V.aa+V.ja],Pt=V.pb[Rt.$b],mt=Rt.ad,se=0,ce=V.rb[V.sb-1],re=ae=0,u(mt,se,0,384),Rt.Za)var Ee=0,ur=St[3];else{me=o(16);var qe=bt.Na+ce.Na;if(qe=ia(Bt,St[1],qe,Pt.Eb,0,me,0),bt.Na=ce.Na=(0<qe)+0,1<qe)Ni(me,0,mt,se);else{var lr=me[0]+3>>3;for(me=0;256>me;me+=16)mt[se+me]=lr}Ee=1,ur=St[0]}var he=15&bt.la,pr=15&ce.la;for(me=0;4>me;++me){var gr=1&pr;for(lr=$t=0;4>lr;++lr)he=he>>1|(gr=(qe=ia(Bt,ur,qe=gr+(1&he),Pt.Sc,Ee,mt,se))>Ee)<<7,$t=$t<<2|(3<qe?3:1<qe?2:mt[se+0]!=0),se+=16;he>>=4,pr=pr>>1|gr<<7,ae=(ae<<8|$t)>>>0}for(ur=he,Ee=pr>>4,pe=0;4>pe;pe+=2){for($t=0,he=bt.la>>4+pe,pr=ce.la>>4+pe,me=0;2>me;++me){for(gr=1&pr,lr=0;2>lr;++lr)qe=gr+(1&he),he=he>>1|(gr=0<(qe=ia(Bt,St[2],qe,Pt.Qc,0,mt,se)))<<3,$t=$t<<2|(3<qe?3:1<qe?2:mt[se+0]!=0),se+=16;he>>=2,pr=pr>>1|gr<<5}re|=$t<<4*pe,ur|=he<<4<<pe,Ee|=(240&pr)<<pe}bt.la=ur,ce.la=Ee,Rt.Hc=ae,Rt.Gc=re,Rt.ia=43690&re?0:Pt.ia,St=!(ae|re)}if(0<V.L&&(V.wa[V.Y+V.ja]=V.gd[at.$b][at.Za],V.wa[V.Y+V.ja].La|=!St),gt.Ka)return ke(G,7,"Premature end-of-file encountered.")}if(Mi(G),V=vt,gt=1,at=(H=G).D,bt=0<H.L&&H.M>=H.zb&&H.M<=H.Va,H.Aa==0)t:{if(at.M=H.M,at.uc=bt,Xi(H,at),gt=1,at=($t=H.D).Nb,bt=(re=$a[H.L])*H.R,Bt=re/2*H.B,me=16*at*H.R,lr=8*at*H.B,St=H.sa,Rt=H.ta-bt+me,Pt=H.qa,mt=H.ra-Bt+lr,se=H.Ha,ce=H.Ia-Bt+lr,pr=(he=$t.M)==0,ae=he>=H.Va-1,H.Aa==2&&Xi(H,$t),$t.uc)for(gr=(qe=H).D.M,e(qe.D.uc),$t=qe.yb;$t<qe.Hb;++$t){Ee=$t,ur=gr;var Ue=(Ge=(_e=qe).D).Nb;pe=_e.R;var Ge=Ge.wa[Ge.Y+Ee],nr=_e.sa,xr=_e.ta+16*Ue*pe+16*Ee,Kr=Ge.dd,He=Ge.tc;if(He!=0)if(e(3<=He),_e.L==1)0<Ee&&wn(nr,xr,pe,He+4),Ge.La&&wr(nr,xr,pe,He),0<ur&&Gn(nr,xr,pe,He+4),Ge.La&&ra(nr,xr,pe,He);else{var hn=_e.B,Jn=_e.qa,_i=_e.ra+8*Ue*hn+8*Ee,An=_e.Ha,_e=_e.Ia+8*Ue*hn+8*Ee;Ue=Ge.ld,0<Ee&&(Ua(nr,xr,pe,He+4,Kr,Ue),Hn(Jn,_i,An,_e,hn,He+4,Kr,Ue)),Ge.La&&(yn(nr,xr,pe,He,Kr,Ue),xi(Jn,_i,An,_e,hn,He,Kr,Ue)),0<ur&&(Ai(nr,xr,pe,He+4,Kr,Ue),ea(Jn,_i,An,_e,hn,He+4,Kr,Ue)),Ge.La&&(Wn(nr,xr,pe,He,Kr,Ue),Vn(Jn,_i,An,_e,hn,He,Kr,Ue))}}if(H.ia&&alert("todo:DitherRow"),V.put!=null){if($t=16*he,he=16*(he+1),pr?(V.y=H.sa,V.O=H.ta+me,V.f=H.qa,V.N=H.ra+lr,V.ea=H.Ha,V.W=H.Ia+lr):($t-=re,V.y=St,V.O=Rt,V.f=Pt,V.N=mt,V.ea=se,V.W=ce),ae||(he-=re),he>V.o&&(he=V.o),V.F=null,V.J=null,H.Fa!=null&&0<H.Fa.length&&$t<he&&(V.J=Ji(H,V,$t,he-$t),V.F=H.mb,V.F==null&&V.F.length==0)){gt=ke(H,3,"Could not decode alpha data.");break t}$t<V.j&&(re=V.j-$t,$t=V.j,e(!(1&re)),V.O+=H.R*re,V.N+=H.B*(re>>1),V.W+=H.B*(re>>1),V.F!=null&&(V.J+=V.width*re)),$t<he&&(V.O+=V.v,V.N+=V.v>>1,V.W+=V.v>>1,V.F!=null&&(V.J+=V.v),V.ka=$t-V.j,V.U=V.va-V.v,V.T=he-$t,gt=V.put(V))}at+1!=H.Ic||ae||(a(H.sa,H.ta-bt,St,Rt+16*H.R,bt),a(H.qa,H.ra-Bt,Pt,mt+8*H.B,Bt),a(H.Ha,H.Ia-Bt,se,ce+8*H.B,Bt))}if(!gt)return ke(G,6,"Output aborted.")}return 1}(t,i)),i.bc!=null&&i.bc(i),l&=1}return l?(t.cb=0,l):0}function Cr(t,i,l,d,m){m=t[i+l+32*d]+(m>>3),t[i+l+32*d]=-256&m?0>m?0:255:m}function ui(t,i,l,d,m,b){Cr(t,i,0,l,d+m),Cr(t,i,1,l,d+b),Cr(t,i,2,l,d-b),Cr(t,i,3,l,d-m)}function ar(t){return(20091*t>>16)+t}function Ei(t,i,l,d){var m,b=0,L=o(16);for(m=0;4>m;++m){var A=t[i+0]+t[i+8],x=t[i+0]-t[i+8],I=(35468*t[i+4]>>16)-ar(t[i+12]),U=ar(t[i+4])+(35468*t[i+12]>>16);L[b+0]=A+U,L[b+1]=x+I,L[b+2]=x-I,L[b+3]=A-U,b+=4,i++}for(m=b=0;4>m;++m)A=(t=L[b+0]+4)+L[b+8],x=t-L[b+8],I=(35468*L[b+4]>>16)-ar(L[b+12]),Cr(l,d,0,0,A+(U=ar(L[b+4])+(35468*L[b+12]>>16))),Cr(l,d,1,0,x+I),Cr(l,d,2,0,x-I),Cr(l,d,3,0,A-U),b++,d+=32}function Sa(t,i,l,d){var m=t[i+0]+4,b=35468*t[i+4]>>16,L=ar(t[i+4]),A=35468*t[i+1]>>16;ui(l,d,0,m+L,t=ar(t[i+1]),A),ui(l,d,1,m+b,t,A),ui(l,d,2,m-b,t,A),ui(l,d,3,m-L,t,A)}function So(t,i,l,d,m){Ei(t,i,l,d),m&&Ei(t,i+16,l,d+4)}function _a(t,i,l,d){un(t,i+0,l,d,1),un(t,i+32,l,d+128,1)}function _o(t,i,l,d){var m;for(t=t[i+0]+4,m=0;4>m;++m)for(i=0;4>i;++i)Cr(l,d,i,m,t)}function Pa(t,i,l,d){t[i+0]&&rr(t,i+0,l,d),t[i+16]&&rr(t,i+16,l,d+4),t[i+32]&&rr(t,i+32,l,d+128),t[i+48]&&rr(t,i+48,l,d+128+4)}function Po(t,i,l,d){var m,b=o(16);for(m=0;4>m;++m){var L=t[i+0+m]+t[i+12+m],A=t[i+4+m]+t[i+8+m],x=t[i+4+m]-t[i+8+m],I=t[i+0+m]-t[i+12+m];b[0+m]=L+A,b[8+m]=L-A,b[4+m]=I+x,b[12+m]=I-x}for(m=0;4>m;++m)L=(t=b[0+4*m]+3)+b[3+4*m],A=b[1+4*m]+b[2+4*m],x=b[1+4*m]-b[2+4*m],I=t-b[3+4*m],l[d+0]=L+A>>3,l[d+16]=I+x>>3,l[d+32]=L-A>>3,l[d+48]=I-x>>3,d+=64}function qi(t,i,l){var d,m=i-32,b=dr,L=255-t[m-1];for(d=0;d<l;++d){var A,x=b,I=L+t[i-1];for(A=0;A<l;++A)t[i+A]=x[I+t[m+A]];i+=32}}function ko(t,i){qi(t,i,4)}function Fo(t,i){qi(t,i,8)}function Io(t,i){qi(t,i,16)}function nn(t,i){var l;for(l=0;16>l;++l)a(t,i+32*l,t,i-32,16)}function li(t,i){var l;for(l=16;0<l;--l)u(t,i,t[i-1],16),i+=32}function ci(t,i,l){var d;for(d=0;16>d;++d)u(i,l+32*d,t,16)}function ka(t,i){var l,d=16;for(l=0;16>l;++l)d+=t[i-1+32*l]+t[i+l-32];ci(d>>5,t,i)}function Ye(t,i){var l,d=8;for(l=0;16>l;++l)d+=t[i-1+32*l];ci(d>>4,t,i)}function Co(t,i){var l,d=8;for(l=0;16>l;++l)d+=t[i+l-32];ci(d>>4,t,i)}function Fa(t,i){ci(128,t,i)}function Vt(t,i,l){return t+2*i+l+2>>2}function jo(t,i){var l,d=i-32;for(d=new Uint8Array([Vt(t[d-1],t[d+0],t[d+1]),Vt(t[d+0],t[d+1],t[d+2]),Vt(t[d+1],t[d+2],t[d+3]),Vt(t[d+2],t[d+3],t[d+4])]),l=0;4>l;++l)a(t,i+32*l,d,0,d.length)}function Oo(t,i){var l=t[i-1],d=t[i-1+32],m=t[i-1+64],b=t[i-1+96];At(t,i+0,16843009*Vt(t[i-1-32],l,d)),At(t,i+32,16843009*Vt(l,d,m)),At(t,i+64,16843009*Vt(d,m,b)),At(t,i+96,16843009*Vt(m,b,b))}function Jr(t,i){var l,d=4;for(l=0;4>l;++l)d+=t[i+l-32]+t[i-1+32*l];for(d>>=3,l=0;4>l;++l)u(t,i+32*l,d,4)}function Ia(t,i){var l=t[i-1+0],d=t[i-1+32],m=t[i-1+64],b=t[i-1-32],L=t[i+0-32],A=t[i+1-32],x=t[i+2-32],I=t[i+3-32];t[i+0+96]=Vt(d,m,t[i-1+96]),t[i+1+96]=t[i+0+64]=Vt(l,d,m),t[i+2+96]=t[i+1+64]=t[i+0+32]=Vt(b,l,d),t[i+3+96]=t[i+2+64]=t[i+1+32]=t[i+0+0]=Vt(L,b,l),t[i+3+64]=t[i+2+32]=t[i+1+0]=Vt(A,L,b),t[i+3+32]=t[i+2+0]=Vt(x,A,L),t[i+3+0]=Vt(I,x,A)}function Ca(t,i){var l=t[i+1-32],d=t[i+2-32],m=t[i+3-32],b=t[i+4-32],L=t[i+5-32],A=t[i+6-32],x=t[i+7-32];t[i+0+0]=Vt(t[i+0-32],l,d),t[i+1+0]=t[i+0+32]=Vt(l,d,m),t[i+2+0]=t[i+1+32]=t[i+0+64]=Vt(d,m,b),t[i+3+0]=t[i+2+32]=t[i+1+64]=t[i+0+96]=Vt(m,b,L),t[i+3+32]=t[i+2+64]=t[i+1+96]=Vt(b,L,A),t[i+3+64]=t[i+2+96]=Vt(L,A,x),t[i+3+96]=Vt(A,x,x)}function hi(t,i){var l=t[i-1+0],d=t[i-1+32],m=t[i-1+64],b=t[i-1-32],L=t[i+0-32],A=t[i+1-32],x=t[i+2-32],I=t[i+3-32];t[i+0+0]=t[i+1+64]=b+L+1>>1,t[i+1+0]=t[i+2+64]=L+A+1>>1,t[i+2+0]=t[i+3+64]=A+x+1>>1,t[i+3+0]=x+I+1>>1,t[i+0+96]=Vt(m,d,l),t[i+0+64]=Vt(d,l,b),t[i+0+32]=t[i+1+96]=Vt(l,b,L),t[i+1+32]=t[i+2+96]=Vt(b,L,A),t[i+2+32]=t[i+3+96]=Vt(L,A,x),t[i+3+32]=Vt(A,x,I)}function Di(t,i){var l=t[i+0-32],d=t[i+1-32],m=t[i+2-32],b=t[i+3-32],L=t[i+4-32],A=t[i+5-32],x=t[i+6-32],I=t[i+7-32];t[i+0+0]=l+d+1>>1,t[i+1+0]=t[i+0+64]=d+m+1>>1,t[i+2+0]=t[i+1+64]=m+b+1>>1,t[i+3+0]=t[i+2+64]=b+L+1>>1,t[i+0+32]=Vt(l,d,m),t[i+1+32]=t[i+0+96]=Vt(d,m,b),t[i+2+32]=t[i+1+96]=Vt(m,b,L),t[i+3+32]=t[i+2+96]=Vt(b,L,A),t[i+3+64]=Vt(L,A,x),t[i+3+96]=Vt(A,x,I)}function Bo(t,i){var l=t[i-1+0],d=t[i-1+32],m=t[i-1+64],b=t[i-1+96];t[i+0+0]=l+d+1>>1,t[i+2+0]=t[i+0+32]=d+m+1>>1,t[i+2+32]=t[i+0+64]=m+b+1>>1,t[i+1+0]=Vt(l,d,m),t[i+3+0]=t[i+1+32]=Vt(d,m,b),t[i+3+32]=t[i+1+64]=Vt(m,b,b),t[i+3+64]=t[i+2+64]=t[i+0+96]=t[i+1+96]=t[i+2+96]=t[i+3+96]=b}function Mo(t,i){var l=t[i-1+0],d=t[i-1+32],m=t[i-1+64],b=t[i-1+96],L=t[i-1-32],A=t[i+0-32],x=t[i+1-32],I=t[i+2-32];t[i+0+0]=t[i+2+32]=l+L+1>>1,t[i+0+32]=t[i+2+64]=d+l+1>>1,t[i+0+64]=t[i+2+96]=m+d+1>>1,t[i+0+96]=b+m+1>>1,t[i+3+0]=Vt(A,x,I),t[i+2+0]=Vt(L,A,x),t[i+1+0]=t[i+3+32]=Vt(l,L,A),t[i+1+32]=t[i+3+64]=Vt(d,l,L),t[i+1+64]=t[i+3+96]=Vt(m,d,l),t[i+1+96]=Vt(b,m,d)}function Eo(t,i){var l;for(l=0;8>l;++l)a(t,i+32*l,t,i-32,8)}function Ri(t,i){var l;for(l=0;8>l;++l)u(t,i,t[i-1],8),i+=32}function Dn(t,i,l){var d;for(d=0;8>d;++d)u(i,l+32*d,t,8)}function pn(t,i){var l,d=8;for(l=0;8>l;++l)d+=t[i+l-32]+t[i-1+32*l];Dn(d>>4,t,i)}function qo(t,i){var l,d=4;for(l=0;8>l;++l)d+=t[i+l-32];Dn(d>>3,t,i)}function Rn(t,i){var l,d=4;for(l=0;8>l;++l)d+=t[i-1+32*l];Dn(d>>3,t,i)}function Ti(t,i){Dn(128,t,i)}function fi(t,i,l){var d=t[i-l],m=t[i+0],b=3*(m-d)+Ho[1020+t[i-2*l]-t[i+l]],L=Wa[112+(b+4>>3)];t[i-l]=dr[255+d+Wa[112+(b+3>>3)]],t[i+0]=dr[255+m-L]}function ja(t,i,l,d){var m=t[i+0],b=t[i+l];return Nr[255+t[i-2*l]-t[i-l]]>d||Nr[255+b-m]>d}function Oa(t,i,l,d){return 4*Nr[255+t[i-l]-t[i+0]]+Nr[255+t[i-2*l]-t[i+l]]<=d}function Ba(t,i,l,d,m){var b=t[i-3*l],L=t[i-2*l],A=t[i-l],x=t[i+0],I=t[i+l],U=t[i+2*l],K=t[i+3*l];return 4*Nr[255+A-x]+Nr[255+L-I]>d?0:Nr[255+t[i-4*l]-b]<=m&&Nr[255+b-L]<=m&&Nr[255+L-A]<=m&&Nr[255+K-U]<=m&&Nr[255+U-I]<=m&&Nr[255+I-x]<=m}function Ma(t,i,l,d){var m=2*d+1;for(d=0;16>d;++d)Oa(t,i+d,l,m)&&fi(t,i+d,l)}function Yr(t,i,l,d){var m=2*d+1;for(d=0;16>d;++d)Oa(t,i+d*l,1,m)&&fi(t,i+d*l,1)}function an(t,i,l,d){var m;for(m=3;0<m;--m)Ma(t,i+=4*l,l,d)}function Do(t,i,l,d){var m;for(m=3;0<m;--m)Yr(t,i+=4,l,d)}function gn(t,i,l,d,m,b,L,A){for(b=2*b+1;0<m--;){if(Ba(t,i,l,b,L))if(ja(t,i,l,A))fi(t,i,l);else{var x=t,I=i,U=l,K=x[I-2*U],Z=x[I-U],G=x[I+0],vt=x[I+U],at=x[I+2*U],H=27*(gt=Ho[1020+3*(G-Z)+Ho[1020+K-vt]])+63>>7,V=18*gt+63>>7,gt=9*gt+63>>7;x[I-3*U]=dr[255+x[I-3*U]+gt],x[I-2*U]=dr[255+K+V],x[I-U]=dr[255+Z+H],x[I+0]=dr[255+G-H],x[I+U]=dr[255+vt-V],x[I+2*U]=dr[255+at-gt]}i+=d}}function jr(t,i,l,d,m,b,L,A){for(b=2*b+1;0<m--;){if(Ba(t,i,l,b,L))if(ja(t,i,l,A))fi(t,i,l);else{var x=t,I=i,U=l,K=x[I-U],Z=x[I+0],G=x[I+U],vt=Wa[112+((at=3*(Z-K))+4>>3)],at=Wa[112+(at+3>>3)],H=vt+1>>1;x[I-2*U]=dr[255+x[I-2*U]+H],x[I-U]=dr[255+K+at],x[I+0]=dr[255+Z-vt],x[I+U]=dr[255+G-H]}i+=d}}function zi(t,i,l,d,m,b){gn(t,i,l,1,16,d,m,b)}function Tn(t,i,l,d,m,b){gn(t,i,1,l,16,d,m,b)}function Ro(t,i,l,d,m,b){var L;for(L=3;0<L;--L)jr(t,i+=4*l,l,1,16,d,m,b)}function di(t,i,l,d,m,b){var L;for(L=3;0<L;--L)jr(t,i+=4,1,l,16,d,m,b)}function To(t,i,l,d,m,b,L,A){gn(t,i,m,1,8,b,L,A),gn(l,d,m,1,8,b,L,A)}function Ui(t,i,l,d,m,b,L,A){gn(t,i,1,m,8,b,L,A),gn(l,d,1,m,8,b,L,A)}function Hi(t,i,l,d,m,b,L,A){jr(t,i+4*m,m,1,8,b,L,A),jr(l,d+4*m,m,1,8,b,L,A)}function Ea(t,i,l,d,m,b,L,A){jr(t,i+4,1,m,8,b,L,A),jr(l,d+4,1,m,8,b,L,A)}function pi(){this.ba=new _r,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new xe,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function Wi(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function Vi(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function qa(){this.ua=0,this.Wa=new E,this.vb=new E,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new D,this.yc=new k}function zo(){this.xb=this.a=0,this.l=new si,this.ca=new _r,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new N,this.Pb=0,this.wd=new N,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new qa,this.ab=0,this.gc=c(4,Vi),this.Oc=0}function gi(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new si,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function zn(t,i,l,d,m,b,L){for(t=t==null?0:t[i+0],i=0;i<L;++i)m[b+i]=t+l[d+i]&255,t=m[b+i]}function Gi(t,i,l,d,m,b,L){var A;if(t==null)zn(null,null,l,d,m,b,L);else for(A=0;A<L;++A)m[b+A]=t[i+A]+l[d+A]&255}function mn(t,i,l,d,m,b,L){if(t==null)zn(null,null,l,d,m,b,L);else{var A,x=t[i+0],I=x,U=x;for(A=0;A<L;++A)I=U+(x=t[i+A])-I,U=l[d+A]+(-256&I?0>I?0:255:I)&255,I=x,m[b+A]=U}}function Ji(t,i,l,d){var m=i.width,b=i.o;if(e(t!=null&&i!=null),0>l||0>=d||l+d>b)return null;if(!t.Cc){if(t.ga==null){var L;if(t.ga=new gi,(L=t.ga==null)||(L=i.width*i.o,e(t.Gb.length==0),t.Gb=o(L),t.Uc=0,t.Gb==null?L=0:(t.mb=t.Gb,t.nb=t.Uc,t.rc=null,L=1),L=!L),!L){L=t.ga;var A=t.Fa,x=t.P,I=t.qc,U=t.mb,K=t.nb,Z=x+1,G=I-1,vt=L.l;if(e(A!=null&&U!=null&&i!=null),Nn[0]=null,Nn[1]=zn,Nn[2]=Gi,Nn[3]=mn,L.ca=U,L.tb=K,L.c=i.width,L.i=i.height,e(0<L.c&&0<L.i),1>=I)i=0;else if(L.$a=A[x+0]>>0&3,L.Z=A[x+0]>>2&3,L.Lc=A[x+0]>>4&3,x=A[x+0]>>6&3,0>L.$a||1<L.$a||4<=L.Z||1<L.Lc||x)i=0;else if(vt.put=Ur,vt.ac=We,vt.bc=Hr,vt.ma=L,vt.width=i.width,vt.height=i.height,vt.Da=i.Da,vt.v=i.v,vt.va=i.va,vt.j=i.j,vt.o=i.o,L.$a)t:{e(L.$a==1),i=vr();e:for(;;){if(i==null){i=0;break t}if(e(L!=null),L.mc=i,i.c=L.c,i.i=L.i,i.l=L.l,i.l.ma=L,i.l.width=L.c,i.l.height=L.i,i.a=0,$(i.m,A,Z,G),!Gr(L.c,L.i,1,i,null)||(i.ab==1&&i.gc[0].hc==3&&rn(i.s)?(L.ic=1,A=i.c*i.i,i.Ta=null,i.Ua=0,i.V=o(A),i.Ba=0,i.V==null?(i.a=1,i=0):i=1):(L.ic=0,i=qn(i,L.c)),!i))break e;i=1;break t}L.mc=null,i=0}else i=G>=L.c*L.i;L=!i}if(L)return null;t.ga.Lc!=1?t.Ga=0:d=b-l}e(t.ga!=null),e(l+d<=b);t:{if(i=(A=t.ga).c,b=A.l.o,A.$a==0){if(Z=t.rc,G=t.Vc,vt=t.Fa,x=t.P+1+l*i,I=t.mb,U=t.nb+l*i,e(x<=t.P+t.qc),A.Z!=0)for(e(Nn[A.Z]!=null),L=0;L<d;++L)Nn[A.Z](Z,G,vt,x,I,U,i),Z=I,G=U,U+=i,x+=i;else for(L=0;L<d;++L)a(I,U,vt,x,i),Z=I,G=U,U+=i,x+=i;t.rc=Z,t.Vc=G}else{if(e(A.mc!=null),i=l+d,e((L=A.mc)!=null),e(i<=L.i),L.C>=i)i=1;else if(A.ic||X(),A.ic){A=L.V,Z=L.Ba,G=L.c;var at=L.i,H=(vt=1,x=L.$/G,I=L.$%G,U=L.m,K=L.s,L.$),V=G*at,gt=G*i,bt=K.wc,mt=H<gt?Be(K,I,x):null;e(H<=V),e(i<=at),e(rn(K));e:for(;;){for(;!U.h&&H<gt;){if(I&bt||(mt=Be(K,I,x)),e(mt!=null),J(U),256>(at=ir(mt.G[0],mt.H[0],U)))A[Z+H]=at,++H,++I>=G&&(I=0,++x<=i&&!(x%16)&&Fr(L,x));else{if(!(280>at)){vt=0;break e}at=Pr(at-256,U);var Bt,St=ir(mt.G[4],mt.H[4],U);if(J(U),!(H>=(St=Wr(G,St=Pr(St,U)))&&V-H>=at)){vt=0;break e}for(Bt=0;Bt<at;++Bt)A[Z+H+Bt]=A[Z+H+Bt-St];for(H+=at,I+=at;I>=G;)I-=G,++x<=i&&!(x%16)&&Fr(L,x);H<gt&&I&bt&&(mt=Be(K,I,x))}e(U.h==M(U))}Fr(L,x>i?i:x);break e}!vt||U.h&&H<V?(vt=0,L.a=U.h?5:3):L.$=H,i=vt}else i=Ir(L,L.V,L.Ba,L.c,L.i,i,ri);if(!i){d=0;break t}}l+d>=b&&(t.Cc=1),d=1}if(!d)return null;if(t.Cc&&((d=t.ga)!=null&&(d.mc=null),t.ga=null,0<t.Ga))return alert("todo:WebPDequantizeLevels"),null}return t.nb+l*m}function s(t,i,l,d,m,b){for(;0<m--;){var L,A=t,x=i+(l?1:0),I=t,U=i+(l?0:3);for(L=0;L<d;++L){var K=I[U+4*L];K!=255&&(K*=32897,A[x+4*L+0]=A[x+4*L+0]*K>>23,A[x+4*L+1]=A[x+4*L+1]*K>>23,A[x+4*L+2]=A[x+4*L+2]*K>>23)}i+=b}}function v(t,i,l,d,m){for(;0<d--;){var b;for(b=0;b<l;++b){var L=t[i+2*b+0],A=15&(I=t[i+2*b+1]),x=4369*A,I=(240&I|I>>4)*x>>16;t[i+2*b+0]=(240&L|L>>4)*x>>16&240|(15&L|L<<4)*x>>16>>4&15,t[i+2*b+1]=240&I|A}i+=m}}function j(t,i,l,d,m,b,L,A){var x,I,U=255;for(I=0;I<m;++I){for(x=0;x<d;++x){var K=t[i+x];b[L+4*x]=K,U&=K}i+=l,L+=A}return U!=255}function R(t,i,l,d,m){var b;for(b=0;b<m;++b)l[d+b]=t[i+b]>>8}function X(){Lr=s,be=v,ye=j,Ie=R}function ut(t,i,l){z[t]=function(d,m,b,L,A,x,I,U,K,Z,G,vt,at,H,V,gt,bt){var mt,Bt=bt-1>>1,St=A[x+0]|I[U+0]<<16,Rt=K[Z+0]|G[vt+0]<<16;e(d!=null);var Pt=3*St+Rt+131074>>2;for(i(d[m+0],255&Pt,Pt>>16,at,H),b!=null&&(Pt=3*Rt+St+131074>>2,i(b[L+0],255&Pt,Pt>>16,V,gt)),mt=1;mt<=Bt;++mt){var se=A[x+mt]|I[U+mt]<<16,ce=K[Z+mt]|G[vt+mt]<<16,ae=St+se+Rt+ce+524296,re=ae+2*(se+Rt)>>3;Pt=re+St>>1,St=(ae=ae+2*(St+ce)>>3)+se>>1,i(d[m+2*mt-1],255&Pt,Pt>>16,at,H+(2*mt-1)*l),i(d[m+2*mt-0],255&St,St>>16,at,H+(2*mt-0)*l),b!=null&&(Pt=ae+Rt>>1,St=re+ce>>1,i(b[L+2*mt-1],255&Pt,Pt>>16,V,gt+(2*mt-1)*l),i(b[L+2*mt+0],255&St,St>>16,V,gt+(2*mt+0)*l)),St=se,Rt=ce}1&bt||(Pt=3*St+Rt+131074>>2,i(d[m+bt-1],255&Pt,Pt>>16,at,H+(bt-1)*l),b!=null&&(Pt=3*Rt+St+131074>>2,i(b[L+bt-1],255&Pt,Pt>>16,V,gt+(bt-1)*l)))}}function yt(){Ar[Va]=il,Ar[Ga]=Ms,Ar[Is]=al,Ar[Ja]=Es,Ar[Ya]=qs,Ar[Wo]=Ds,Ar[Cs]=ol,Ar[Vo]=Ms,Ar[Go]=Es,Ar[Xa]=qs,Ar[Jo]=Ds}function Ot(t){return t&-16384?0>t?0:255:t>>sl}function Dt(t,i){return Ot((19077*t>>8)+(26149*i>>8)-14234)}function Zt(t,i,l){return Ot((19077*t>>8)-(6419*i>>8)-(13320*l>>8)+8708)}function Yt(t,i){return Ot((19077*t>>8)+(33050*i>>8)-17685)}function ne(t,i,l,d,m){d[m+0]=Dt(t,l),d[m+1]=Zt(t,i,l),d[m+2]=Yt(t,i)}function Ne(t,i,l,d,m){d[m+0]=Yt(t,i),d[m+1]=Zt(t,i,l),d[m+2]=Dt(t,l)}function Se(t,i,l,d,m){var b=Zt(t,i,l);i=b<<3&224|Yt(t,i)>>3,d[m+0]=248&Dt(t,l)|b>>5,d[m+1]=i}function Me(t,i,l,d,m){var b=240&Yt(t,i)|15;d[m+0]=240&Dt(t,l)|Zt(t,i,l)>>4,d[m+1]=b}function Xe(t,i,l,d,m){d[m+0]=255,ne(t,i,l,d,m+1)}function ze(t,i,l,d,m){Ne(t,i,l,d,m),d[m+3]=255}function Or(t,i,l,d,m){ne(t,i,l,d,m),d[m+3]=255}function fr(t,i){return 0>t?0:t>i?i:t}function Xr(t,i,l){z[t]=function(d,m,b,L,A,x,I,U,K){for(var Z=U+(-2&K)*l;U!=Z;)i(d[m+0],b[L+0],A[x+0],I,U),i(d[m+1],b[L+0],A[x+0],I,U+l),m+=2,++L,++x,U+=2*l;1&K&&i(d[m+0],b[L+0],A[x+0],I,U)}}function Da(t,i,l){return l==0?t==0?i==0?6:5:i==0?4:0:l}function Yi(t,i,l,d,m){switch(t>>>30){case 3:un(i,l,d,m,0);break;case 2:Fe(i,l,d,m);break;case 1:rr(i,l,d,m)}}function Xi(t,i){var l,d,m=i.M,b=i.Nb,L=t.oc,A=t.pc+40,x=t.oc,I=t.pc+584,U=t.oc,K=t.pc+600;for(l=0;16>l;++l)L[A+32*l-1]=129;for(l=0;8>l;++l)x[I+32*l-1]=129,U[K+32*l-1]=129;for(0<m?L[A-1-32]=x[I-1-32]=U[K-1-32]=129:(u(L,A-32-1,127,21),u(x,I-32-1,127,9),u(U,K-32-1,127,9)),d=0;d<t.za;++d){var Z=i.ya[i.aa+d];if(0<d){for(l=-1;16>l;++l)a(L,A+32*l-4,L,A+32*l+12,4);for(l=-1;8>l;++l)a(x,I+32*l-4,x,I+32*l+4,4),a(U,K+32*l-4,U,K+32*l+4,4)}var G=t.Gd,vt=t.Hd+d,at=Z.ad,H=Z.Hc;if(0<m&&(a(L,A-32,G[vt].y,0,16),a(x,I-32,G[vt].f,0,8),a(U,K-32,G[vt].ea,0,8)),Z.Za){var V=L,gt=A-32+16;for(0<m&&(d>=t.za-1?u(V,gt,G[vt].y[15],4):a(V,gt,G[vt+1].y,0,4)),l=0;4>l;l++)V[gt+128+l]=V[gt+256+l]=V[gt+384+l]=V[gt+0+l];for(l=0;16>l;++l,H<<=2)V=L,gt=A+Ts[l],Br[Z.Ob[l]](V,gt),Yi(H,at,16*+l,V,gt)}else if(V=Da(d,m,Z.Ob[0]),Ln[V](L,A),H!=0)for(l=0;16>l;++l,H<<=2)Yi(H,at,16*+l,L,A+Ts[l]);for(l=Z.Gc,V=Da(d,m,Z.Dd),cn[V](x,I),cn[V](U,K),H=at,V=x,gt=I,255&(Z=l>>0)&&(170&Z?ta(H,256,V,gt):yr(H,256,V,gt)),Z=U,H=K,255&(l>>=8)&&(170&l?ta(at,320,Z,H):yr(at,320,Z,H)),m<t.Ub-1&&(a(G[vt].y,0,L,A+480,16),a(G[vt].f,0,x,I+224,8),a(G[vt].ea,0,U,K+224,8)),l=8*b*t.B,G=t.sa,vt=t.ta+16*d+16*b*t.R,at=t.qa,Z=t.ra+8*d+l,H=t.Ha,V=t.Ia+8*d+l,l=0;16>l;++l)a(G,vt+l*t.R,L,A+32*l,16);for(l=0;8>l;++l)a(at,Z+l*t.B,x,I+32*l,8),a(H,V+l*t.B,U,K+32*l,8)}}function mi(t,i,l,d,m,b,L,A,x){var I=[0],U=[0],K=0,Z=x!=null?x.kd:0,G=x??new Wi;if(t==null||12>l)return 7;G.data=t,G.w=i,G.ha=l,i=[i],l=[l],G.gb=[G.gb];t:{var vt=i,at=l,H=G.gb;if(e(t!=null),e(at!=null),e(H!=null),H[0]=0,12<=at[0]&&!r(t,vt[0],"RIFF")){if(r(t,vt[0]+8,"WEBP")){H=3;break t}var V=_t(t,vt[0]+4);if(12>V||4294967286<V){H=3;break t}if(Z&&V>at[0]-8){H=7;break t}H[0]=V,vt[0]+=12,at[0]-=12}H=0}if(H!=0)return H;for(V=0<G.gb[0],l=l[0];;){t:{var gt=t;at=i,H=l;var bt=I,mt=U,Bt=vt=[0];if((Pt=K=[K])[0]=0,8>H[0])H=7;else{if(!r(gt,at[0],"VP8X")){if(_t(gt,at[0]+4)!=10){H=3;break t}if(18>H[0]){H=7;break t}var St=_t(gt,at[0]+8),Rt=1+Ft(gt,at[0]+12);if(2147483648<=Rt*(gt=1+Ft(gt,at[0]+15))){H=3;break t}Bt!=null&&(Bt[0]=St),bt!=null&&(bt[0]=Rt),mt!=null&&(mt[0]=gt),at[0]+=18,H[0]-=18,Pt[0]=1}H=0}}if(K=K[0],vt=vt[0],H!=0)return H;if(at=!!(2&vt),!V&&K)return 3;if(b!=null&&(b[0]=!!(16&vt)),L!=null&&(L[0]=at),A!=null&&(A[0]=0),L=I[0],vt=U[0],K&&at&&x==null){H=0;break}if(4>l){H=7;break}if(V&&K||!V&&!K&&!r(t,i[0],"ALPH")){l=[l],G.na=[G.na],G.P=[G.P],G.Sa=[G.Sa];t:{St=t,H=i,V=l;var Pt=G.gb;bt=G.na,mt=G.P,Bt=G.Sa,Rt=22,e(St!=null),e(V!=null),gt=H[0];var se=V[0];for(e(bt!=null),e(Bt!=null),bt[0]=null,mt[0]=null,Bt[0]=0;;){if(H[0]=gt,V[0]=se,8>se){H=7;break t}var ce=_t(St,gt+4);if(4294967286<ce){H=3;break t}var ae=8+ce+1&-2;if(Rt+=ae,0<Pt&&Rt>Pt){H=3;break t}if(!r(St,gt,"VP8 ")||!r(St,gt,"VP8L")){H=0;break t}if(se[0]<ae){H=7;break t}r(St,gt,"ALPH")||(bt[0]=St,mt[0]=gt+8,Bt[0]=ce),gt+=ae,se-=ae}}if(l=l[0],G.na=G.na[0],G.P=G.P[0],G.Sa=G.Sa[0],H!=0)break}l=[l],G.Ja=[G.Ja],G.xa=[G.xa];t:if(Pt=t,H=i,V=l,bt=G.gb[0],mt=G.Ja,Bt=G.xa,St=H[0],gt=!r(Pt,St,"VP8 "),Rt=!r(Pt,St,"VP8L"),e(Pt!=null),e(V!=null),e(mt!=null),e(Bt!=null),8>V[0])H=7;else{if(gt||Rt){if(Pt=_t(Pt,St+4),12<=bt&&Pt>bt-12){H=3;break t}if(Z&&Pt>V[0]-8){H=7;break t}mt[0]=Pt,H[0]+=8,V[0]-=8,Bt[0]=Rt}else Bt[0]=5<=V[0]&&Pt[St+0]==47&&!(Pt[St+4]>>5),mt[0]=V[0];H=0}if(l=l[0],G.Ja=G.Ja[0],G.xa=G.xa[0],i=i[0],H!=0)break;if(4294967286<G.Ja)return 3;if(A==null||at||(A[0]=G.xa?2:1),L=[L],vt=[vt],G.xa){if(5>l){H=7;break}A=L,Z=vt,at=b,t==null||5>l?t=0:5<=l&&t[i+0]==47&&!(t[i+4]>>5)?(V=[0],Pt=[0],bt=[0],$(mt=new N,t,i,l),zt(mt,V,Pt,bt)?(A!=null&&(A[0]=V[0]),Z!=null&&(Z[0]=Pt[0]),at!=null&&(at[0]=bt[0]),t=1):t=0):t=0}else{if(10>l){H=7;break}A=vt,t==null||10>l||!Na(t,i+3,l-3)?t=0:(Z=t[i+0]|t[i+1]<<8|t[i+2]<<16,at=16383&(t[i+7]<<8|t[i+6]),t=16383&(t[i+9]<<8|t[i+8]),1&Z||3<(Z>>1&7)||!(Z>>4&1)||Z>>5>=G.Ja||!at||!t?t=0:(L&&(L[0]=at),A&&(A[0]=t),t=1))}if(!t||(L=L[0],vt=vt[0],K&&(I[0]!=L||U[0]!=vt)))return 3;x!=null&&(x[0]=G,x.offset=i-x.w,e(4294967286>i-x.w),e(x.offset==x.ha-l));break}return H==0||H==7&&K&&x==null?(b!=null&&(b[0]|=G.na!=null&&0<G.na.length),d!=null&&(d[0]=L),m!=null&&(m[0]=vt),0):H}function Ki(t,i,l){var d=i.width,m=i.height,b=0,L=0,A=d,x=m;if(i.Da=t!=null&&0<t.Da,i.Da&&(A=t.cd,x=t.bd,b=t.v,L=t.j,11>l||(b&=-2,L&=-2),0>b||0>L||0>=A||0>=x||b+A>d||L+x>m))return 0;if(i.v=b,i.j=L,i.va=b+A,i.o=L+x,i.U=A,i.T=x,i.da=t!=null&&0<t.da,i.da){if(!Kt(A,x,l=[t.ib],b=[t.hb]))return 0;i.ib=l[0],i.hb=b[0]}return i.ob=t!=null&&t.ob,i.Kb=t==null||!t.Sd,i.da&&(i.ob=i.ib<3*d/4&&i.hb<3*m/4,i.Kb=0),1}function Zi(t){if(t==null)return 2;if(11>t.S){var i=t.f.RGBA;i.fb+=(t.height-1)*i.A,i.A=-i.A}else i=t.f.kb,t=t.height,i.O+=(t-1)*i.fa,i.fa=-i.fa,i.N+=(t-1>>1)*i.Ab,i.Ab=-i.Ab,i.W+=(t-1>>1)*i.Db,i.Db=-i.Db,i.F!=null&&(i.J+=(t-1)*i.lb,i.lb=-i.lb);return 0}function vi(t,i,l,d){if(d==null||0>=t||0>=i)return 2;if(l!=null){if(l.Da){var m=l.cd,b=l.bd,L=-2&l.v,A=-2&l.j;if(0>L||0>A||0>=m||0>=b||L+m>t||A+b>i)return 2;t=m,i=b}if(l.da){if(!Kt(t,i,m=[l.ib],b=[l.hb]))return 2;t=m[0],i=b[0]}}d.width=t,d.height=i;t:{var x=d.width,I=d.height;if(t=d.S,0>=x||0>=I||!(t>=Va&&13>t))t=2;else{if(0>=d.Rd&&d.sd==null){L=b=m=i=0;var U=(A=x*zs[t])*I;if(11>t||(b=(I+1)/2*(i=(x+1)/2),t==12&&(L=(m=x)*I)),(I=o(U+2*b+L))==null){t=1;break t}d.sd=I,11>t?((x=d.f.RGBA).eb=I,x.fb=0,x.A=A,x.size=U):((x=d.f.kb).y=I,x.O=0,x.fa=A,x.Fd=U,x.f=I,x.N=0+U,x.Ab=i,x.Cd=b,x.ea=I,x.W=0+U+b,x.Db=i,x.Ed=b,t==12&&(x.F=I,x.J=0+U+2*b),x.Tc=L,x.lb=m)}if(i=1,m=d.S,b=d.width,L=d.height,m>=Va&&13>m)if(11>m)t=d.f.RGBA,i&=(A=Math.abs(t.A))*(L-1)+b<=t.size,i&=A>=b*zs[m],i&=t.eb!=null;else{t=d.f.kb,A=(b+1)/2,U=(L+1)/2,x=Math.abs(t.fa),I=Math.abs(t.Ab);var K=Math.abs(t.Db),Z=Math.abs(t.lb),G=Z*(L-1)+b;i&=x*(L-1)+b<=t.Fd,i&=I*(U-1)+A<=t.Cd,i=(i&=K*(U-1)+A<=t.Ed)&x>=b&I>=A&K>=A,i&=t.y!=null,i&=t.f!=null,i&=t.ea!=null,m==12&&(i&=Z>=b,i&=G<=t.Tc,i&=t.F!=null)}else i=0;t=i?0:2}}return t!=0||l!=null&&l.fd&&(t=Zi(d)),t}var Ve=64,bi=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,16777215],yi=24,wi=32,$i=8,or=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];xt("Predictor0","PredictorAdd0"),z.Predictor0=function(){return **********},z.Predictor1=function(t){return t},z.Predictor2=function(t,i,l){return i[l+0]},z.Predictor3=function(t,i,l){return i[l+1]},z.Predictor4=function(t,i,l){return i[l-1]},z.Predictor5=function(t,i,l){return kt(kt(t,i[l+1]),i[l+0])},z.Predictor6=function(t,i,l){return kt(t,i[l-1])},z.Predictor7=function(t,i,l){return kt(t,i[l+0])},z.Predictor8=function(t,i,l){return kt(i[l-1],i[l+0])},z.Predictor9=function(t,i,l){return kt(i[l+0],i[l+1])},z.Predictor10=function(t,i,l){return kt(kt(t,i[l-1]),kt(i[l+0],i[l+1]))},z.Predictor11=function(t,i,l){var d=i[l+0];return 0>=Qt(d>>24&255,t>>24&255,(i=i[l-1])>>24&255)+Qt(d>>16&255,t>>16&255,i>>16&255)+Qt(d>>8&255,t>>8&255,i>>8&255)+Qt(255&d,255&t,255&i)?d:t},z.Predictor12=function(t,i,l){var d=i[l+0];return(qt((t>>24&255)+(d>>24&255)-((i=i[l-1])>>24&255))<<24|qt((t>>16&255)+(d>>16&255)-(i>>16&255))<<16|qt((t>>8&255)+(d>>8&255)-(i>>8&255))<<8|qt((255&t)+(255&d)-(255&i)))>>>0},z.Predictor13=function(t,i,l){var d=i[l-1];return(Gt((t=kt(t,i[l+0]))>>24&255,d>>24&255)<<24|Gt(t>>16&255,d>>16&255)<<16|Gt(t>>8&255,d>>8&255)<<8|Gt(t>>0&255,d>>0&255))>>>0};var Uo=z.PredictorAdd0;z.PredictorAdd1=te,xt("Predictor2","PredictorAdd2"),xt("Predictor3","PredictorAdd3"),xt("Predictor4","PredictorAdd4"),xt("Predictor5","PredictorAdd5"),xt("Predictor6","PredictorAdd6"),xt("Predictor7","PredictorAdd7"),xt("Predictor8","PredictorAdd8"),xt("Predictor9","PredictorAdd9"),xt("Predictor10","PredictorAdd10"),xt("Predictor11","PredictorAdd11"),xt("Predictor12","PredictorAdd12"),xt("Predictor13","PredictorAdd13");var Qi=z.PredictorAdd2;ee("ColorIndexInverseTransform","MapARGB","32b",function(t){return t>>8&255},function(t){return t}),ee("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",function(t){return t},function(t){return t>>8&255});var Ra,br=z.ColorIndexInverseTransform,Li=z.MapARGB,Ta=z.VP8LColorIndexInverseTransformAlpha,za=z.MapAlpha,vn=z.VP8LPredictorsAdd=[];vn.length=16,(z.VP8LPredictors=[]).length=16,(z.VP8LPredictorsAdd_C=[]).length=16,(z.VP8LPredictors_C=[]).length=16;var Un,sr,er,bn,on,sn,Ni,un,Fe,ta,rr,yr,Ai,Ua,ea,Hn,Wn,yn,Vn,xi,Gn,wn,ra,wr,Lr,be,ye,Ie,Re=o(511),ln=o(2041),na=o(225),Si=o(767),Ha=0,Ho=ln,Wa=na,dr=Si,Nr=Re,Va=0,Ga=1,Is=2,Ja=3,Ya=4,Wo=5,Cs=6,Vo=7,Go=8,Xa=9,Jo=10,Wu=[2,3,7],Vu=[3,3,11],js=[280,256,256,256,40],Gu=[0,1,1,1,0],Ju=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],Yu=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],Xu=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],Ku=8,Yo=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],Xo=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],ia=null,Zu=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],$u=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],Os=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],Qu=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],tl=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],el=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],rl=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],Ln=[],Br=[],cn=[],nl=1,Bs=2,Nn=[],Ar=[];ut("UpsampleRgbLinePair",ne,3),ut("UpsampleBgrLinePair",Ne,3),ut("UpsampleRgbaLinePair",Or,4),ut("UpsampleBgraLinePair",ze,4),ut("UpsampleArgbLinePair",Xe,4),ut("UpsampleRgba4444LinePair",Me,2),ut("UpsampleRgb565LinePair",Se,2);var il=z.UpsampleRgbLinePair,al=z.UpsampleBgrLinePair,Ms=z.UpsampleRgbaLinePair,Es=z.UpsampleBgraLinePair,qs=z.UpsampleArgbLinePair,Ds=z.UpsampleRgba4444LinePair,ol=z.UpsampleRgb565LinePair,Ka=16,Za=1<<Ka-1,aa=-227,Ko=482,sl=6,Rs=0,ul=o(256),ll=o(256),cl=o(256),hl=o(256),fl=o(Ko-aa),dl=o(Ko-aa);Xr("YuvToRgbRow",ne,3),Xr("YuvToBgrRow",Ne,3),Xr("YuvToRgbaRow",Or,4),Xr("YuvToBgraRow",ze,4),Xr("YuvToArgbRow",Xe,4),Xr("YuvToRgba4444Row",Me,2),Xr("YuvToRgb565Row",Se,2);var Ts=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],$a=[0,2,8],pl=[8,7,6,4,4,2,2,2,1,1,1,1],gl=1;this.WebPDecodeRGBA=function(t,i,l,d,m){var b=Ga,L=new pi,A=new _r;L.ba=A,A.S=b,A.width=[A.width],A.height=[A.height];var x=A.width,I=A.height,U=new en;if(U==null||t==null)var K=2;else e(U!=null),K=mi(t,i,l,U.width,U.height,U.Pd,U.Qd,U.format,null);if(K!=0?x=0:(x!=null&&(x[0]=U.width[0]),I!=null&&(I[0]=U.height[0]),x=1),x){A.width=A.width[0],A.height=A.height[0],d!=null&&(d[0]=A.width),m!=null&&(m[0]=A.height);t:{if(d=new si,(m=new Wi).data=t,m.w=i,m.ha=l,m.kd=1,i=[0],e(m!=null),((t=mi(m.data,m.w,m.ha,null,null,null,i,null,m))==0||t==7)&&i[0]&&(t=4),(i=t)==0){if(e(L!=null),d.data=m.data,d.w=m.w+m.offset,d.ha=m.ha-m.offset,d.put=Ur,d.ac=We,d.bc=Hr,d.ma=L,m.xa){if((t=vr())==null){L=1;break t}if(function(Z,G){var vt=[0],at=[0],H=[0];e:for(;;){if(Z==null)return 0;if(G==null)return Z.a=2,0;if(Z.l=G,Z.a=0,$(Z.m,G.data,G.w,G.ha),!zt(Z.m,vt,at,H)){Z.a=3;break e}if(Z.xb=Bs,G.width=vt[0],G.height=at[0],!Gr(vt[0],at[0],1,Z,null))break e;return 1}return e(Z.a!=0),0}(t,d)){if(d=(i=vi(d.width,d.height,L.Oa,L.ba))==0){e:{d=t;r:for(;;){if(d==null){d=0;break e}if(e(d.s.yc!=null),e(d.s.Ya!=null),e(0<d.s.Wb),e((l=d.l)!=null),e((m=l.ma)!=null),d.xb!=0){if(d.ca=m.ba,d.tb=m.tb,e(d.ca!=null),!Ki(m.Oa,l,Ja)){d.a=2;break r}if(!qn(d,l.width)||l.da)break r;if((l.da||ue(d.ca.S))&&X(),11>d.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),d.ca.f.kb.F!=null&&X()),d.Pb&&0<d.s.ua&&d.s.vb.X==null&&!Ut(d.s.vb,d.s.Wa.Xa)){d.a=1;break r}d.xb=0}if(!Ir(d,d.V,d.Ba,d.c,d.i,l.o,ei))break r;m.Dc=d.Ma,d=1;break e}e(d.a!=0),d=0}d=!d}d&&(i=t.a)}else i=t.a}else{if((t=new Ao)==null){L=1;break t}if(t.Fa=m.na,t.P=m.P,t.qc=m.Sa,Aa(t,d)){if((i=vi(d.width,d.height,L.Oa,L.ba))==0){if(t.Aa=0,l=L.Oa,e((m=t)!=null),l!=null){if(0<(x=0>(x=l.Md)?0:100<x?255:255*x/100)){for(I=U=0;4>I;++I)12>(K=m.pb[I]).lc&&(K.ia=x*pl[0>K.lc?0:K.lc]>>3),U|=K.ia;U&&(alert("todo:VP8InitRandom"),m.ia=1)}m.Ga=l.Id,100<m.Ga?m.Ga=100:0>m.Ga&&(m.Ga=0)}xo(t,d)||(i=t.a)}}else i=t.a}i==0&&L.Oa!=null&&L.Oa.fd&&(i=Zi(L.ba))}L=i}b=L!=0?null:11>b?A.f.RGBA.eb:A.f.kb.y}else b=null;return b};var zs=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function g(z,nt){for(var pt="",P=0;P<4;P++)pt+=String.fromCharCode(z[nt++]);return pt}function y(z,nt){return(z[nt+0]<<0|z[nt+1]<<8|z[nt+2]<<16)>>>0}function w(z,nt){return(z[nt+0]<<0|z[nt+1]<<8|z[nt+2]<<16|z[nt+3]<<24)>>>0}new f;var S=[0],p=[0],O=[],F=new f,q=n,_=function(z,nt){var pt={},P=0,k=!1,W=0,D=0;if(pt.frames=[],!function(C,M,T,J){for(var Q=0;Q<J;Q++)if(C[M+Q]!=T.charCodeAt(Q))return!0;return!1}(z,nt,"RIFF",4)){for(w(z,nt+=4),nt+=8;nt<z.length;){var st=g(z,nt),it=w(z,nt+=4);nt+=4;var ht=it+(1&it);switch(st){case"VP8 ":case"VP8L":pt.frames[P]===void 0&&(pt.frames[P]={}),(dt=pt.frames[P]).src_off=k?D:nt-8,dt.src_size=W+it+8,P++,k&&(k=!1,W=0,D=0);break;case"VP8X":(dt=pt.header={}).feature_flags=z[nt];var $=nt+4;dt.canvas_width=1+y(z,$),$+=3,dt.canvas_height=1+y(z,$),$+=3;break;case"ALPH":k=!0,W=ht+8,D=nt-8;break;case"ANIM":(dt=pt.header).bgcolor=w(z,nt),$=nt+4,dt.loop_count=(jt=z)[(N=$)+0]<<0|jt[N+1]<<8,$+=2;break;case"ANMF":var lt,dt;(dt=pt.frames[P]={}).offset_x=2*y(z,nt),nt+=3,dt.offset_y=2*y(z,nt),nt+=3,dt.width=1+y(z,nt),nt+=3,dt.height=1+y(z,nt),nt+=3,dt.duration=y(z,nt),nt+=3,lt=z[nt++],dt.dispose=1&lt,dt.blend=lt>>1&1}st!="ANMF"&&(nt+=ht)}var jt,N;return pt}}(q,0);_.response=q,_.rgbaoutput=!0,_.dataurl=!1;var B=_.header?_.header:null,Y=_.frames?_.frames:null;if(B){B.loop_counter=B.loop_count,S=[B.canvas_height],p=[B.canvas_width];for(var ot=0;ot<Y.length&&Y[ot].blend!=0;ot++);}var ct=Y[0],wt=F.WebPDecodeRGBA(q,ct.src_off,ct.src_size,p,S);ct.rgba=wt,ct.imgwidth=p[0],ct.imgheight=S[0];for(var tt=0;tt<p[0]*S[0]*4;tt++)O[tt]=wt[tt];return this.width=p,this.height=S,this.data=O,this}(function(n){var e=function(){return typeof ws=="function"},r=function(S,p,O,F){var q=4,_=c;switch(F){case n.image_compression.FAST:q=1,_=o;break;case n.image_compression.MEDIUM:q=6,_=h;break;case n.image_compression.SLOW:q=9,_=f}S=a(S,p,O,_);var B=ws(S,{level:q});return n.__addimage__.arrayBufferToBinaryString(B)},a=function(S,p,O,F){for(var q,_,B,Y=S.length/p,ot=new Uint8Array(S.length+Y),ct=y(),wt=0;wt<Y;wt+=1){if(B=wt*p,q=S.subarray(B,B+p),F)ot.set(F(q,O,_),B+wt);else{for(var tt,z=ct.length,nt=[];tt<z;tt+=1)nt[tt]=ct[tt](q,O,_);var pt=w(nt.concat());ot.set(nt[pt],B+wt)}_=q}return ot},u=function(S){var p=Array.apply([],S);return p.unshift(0),p},o=function(S,p){var O,F=[],q=S.length;F[0]=1;for(var _=0;_<q;_+=1)O=S[_-p]||0,F[_+1]=S[_]-O+256&255;return F},c=function(S,p,O){var F,q=[],_=S.length;q[0]=2;for(var B=0;B<_;B+=1)F=O&&O[B]||0,q[B+1]=S[B]-F+256&255;return q},h=function(S,p,O){var F,q,_=[],B=S.length;_[0]=3;for(var Y=0;Y<B;Y+=1)F=S[Y-p]||0,q=O&&O[Y]||0,_[Y+1]=S[Y]+256-(F+q>>>1)&255;return _},f=function(S,p,O){var F,q,_,B,Y=[],ot=S.length;Y[0]=4;for(var ct=0;ct<ot;ct+=1)F=S[ct-p]||0,q=O&&O[ct]||0,_=O&&O[ct-p]||0,B=g(F,q,_),Y[ct+1]=S[ct]-B+256&255;return Y},g=function(S,p,O){if(S===p&&p===O)return S;var F=Math.abs(p-O),q=Math.abs(S-O),_=Math.abs(S+p-O-O);return F<=q&&F<=_?S:q<=_?p:O},y=function(){return[u,o,c,h,f]},w=function(S){var p=S.map(function(O){return O.reduce(function(F,q){return F+Math.abs(q)},0)});return p.indexOf(Math.min.apply(null,p))};n.processPNG=function(S,p,O,F){var q,_,B,Y,ot,ct,wt,tt,z,nt,pt,P,k,W,D,st=this.decode.FLATE_DECODE,it="";if(this.__addimage__.isArrayBuffer(S)&&(S=new Uint8Array(S)),this.__addimage__.isArrayBufferView(S)){if(S=(B=new uc(S)).imgData,_=B.bits,q=B.colorSpace,ot=B.colors,[4,6].indexOf(B.colorType)!==-1){if(B.bits===8){z=(tt=B.pixelBitlength==32?new Uint32Array(B.decodePixels().buffer):B.pixelBitlength==16?new Uint16Array(B.decodePixels().buffer):new Uint8Array(B.decodePixels().buffer)).length,pt=new Uint8Array(z*B.colors),nt=new Uint8Array(z);var ht,$=B.pixelBitlength-B.bits;for(W=0,D=0;W<z;W++){for(k=tt[W],ht=0;ht<$;)pt[D++]=k>>>ht&255,ht+=B.bits;nt[W]=k>>>ht&255}}if(B.bits===16){z=(tt=new Uint32Array(B.decodePixels().buffer)).length,pt=new Uint8Array(z*(32/B.pixelBitlength)*B.colors),nt=new Uint8Array(z*(32/B.pixelBitlength)),P=B.colors>1,W=0,D=0;for(var lt=0;W<z;)k=tt[W++],pt[D++]=k>>>0&255,P&&(pt[D++]=k>>>16&255,k=tt[W++],pt[D++]=k>>>0&255),nt[lt++]=k>>>16&255;_=8}F!==n.image_compression.NONE&&e()?(S=r(pt,B.width*B.colors,B.colors,F),wt=r(nt,B.width,1,F)):(S=pt,wt=nt,st=void 0)}if(B.colorType===3&&(q=this.color_spaces.INDEXED,ct=B.palette,B.transparency.indexed)){var dt=B.transparency.indexed,jt=0;for(W=0,z=dt.length;W<z;++W)jt+=dt[W];if((jt/=255)===z-1&&dt.indexOf(0)!==-1)Y=[dt.indexOf(0)];else if(jt!==z){for(tt=B.decodePixels(),nt=new Uint8Array(tt.length),W=0,z=tt.length;W<z;W++)nt[W]=dt[tt[W]];wt=r(nt,B.width,1)}}var N=function(C){var M;switch(C){case n.image_compression.FAST:M=11;break;case n.image_compression.MEDIUM:M=13;break;case n.image_compression.SLOW:M=14;break;default:M=12}return M}(F);return st===this.decode.FLATE_DECODE&&(it="/Predictor "+N+" "),it+="/Colors "+ot+" /BitsPerComponent "+_+" /Columns "+B.width,(this.__addimage__.isArrayBuffer(S)||this.__addimage__.isArrayBufferView(S))&&(S=this.__addimage__.arrayBufferToBinaryString(S)),(wt&&this.__addimage__.isArrayBuffer(wt)||this.__addimage__.isArrayBufferView(wt))&&(wt=this.__addimage__.arrayBufferToBinaryString(wt)),{alias:O,data:S,index:p,filter:st,decodeParameters:it,transparency:Y,palette:ct,sMask:wt,predictor:N,width:B.width,height:B.height,bitsPerComponent:_,colorSpace:q}}}})(Tt.API),function(n){n.processGIF89A=function(e,r,a,u){var o=new lc(e),c=o.width,h=o.height,f=[];o.decodeAndBlitFrameRGBA(0,f);var g={data:f,width:c,height:h},y=new ps(100).encode(g,100);return n.processJPEG.call(this,y,r,a,u)},n.processGIF87A=n.processGIF89A}(Tt.API),qr.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.bitPP===16&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var n=this.colors===0?1<<this.bitPP:this.colors;this.palette=new Array(n);for(var e=0;e<n;e++){var r=this.datav.getUint8(this.pos++,!0),a=this.datav.getUint8(this.pos++,!0),u=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0);this.palette[e]={red:u,green:a,blue:r,quad:o}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},qr.prototype.parseBGR=function(){this.pos=this.offset;try{var n="bit"+this.bitPP,e=this.width*this.height*4;this.data=new Uint8Array(e),this[n]()}catch(r){ve.log("bit decode error:"+r)}},qr.prototype.bit1=function(){var n,e=Math.ceil(this.width/8),r=e%4;for(n=this.height-1;n>=0;n--){for(var a=this.bottom_up?n:this.height-1-n,u=0;u<e;u++)for(var o=this.datav.getUint8(this.pos++,!0),c=a*this.width*4+8*u*4,h=0;h<8&&8*u+h<this.width;h++){var f=this.palette[o>>7-h&1];this.data[c+4*h]=f.blue,this.data[c+4*h+1]=f.green,this.data[c+4*h+2]=f.red,this.data[c+4*h+3]=255}r!==0&&(this.pos+=4-r)}},qr.prototype.bit4=function(){for(var n=Math.ceil(this.width/2),e=n%4,r=this.height-1;r>=0;r--){for(var a=this.bottom_up?r:this.height-1-r,u=0;u<n;u++){var o=this.datav.getUint8(this.pos++,!0),c=a*this.width*4+2*u*4,h=o>>4,f=15&o,g=this.palette[h];if(this.data[c]=g.blue,this.data[c+1]=g.green,this.data[c+2]=g.red,this.data[c+3]=255,2*u+1>=this.width)break;g=this.palette[f],this.data[c+4]=g.blue,this.data[c+4+1]=g.green,this.data[c+4+2]=g.red,this.data[c+4+3]=255}e!==0&&(this.pos+=4-e)}},qr.prototype.bit8=function(){for(var n=this.width%4,e=this.height-1;e>=0;e--){for(var r=this.bottom_up?e:this.height-1-e,a=0;a<this.width;a++){var u=this.datav.getUint8(this.pos++,!0),o=r*this.width*4+4*a;if(u<this.palette.length){var c=this.palette[u];this.data[o]=c.red,this.data[o+1]=c.green,this.data[o+2]=c.blue,this.data[o+3]=255}else this.data[o]=255,this.data[o+1]=255,this.data[o+2]=255,this.data[o+3]=255}n!==0&&(this.pos+=4-n)}},qr.prototype.bit15=function(){for(var n=this.width%3,e=parseInt("11111",2),r=this.height-1;r>=0;r--){for(var a=this.bottom_up?r:this.height-1-r,u=0;u<this.width;u++){var o=this.datav.getUint16(this.pos,!0);this.pos+=2;var c=(o&e)/e*255|0,h=(o>>5&e)/e*255|0,f=(o>>10&e)/e*255|0,g=o>>15?255:0,y=a*this.width*4+4*u;this.data[y]=f,this.data[y+1]=h,this.data[y+2]=c,this.data[y+3]=g}this.pos+=n}},qr.prototype.bit16=function(){for(var n=this.width%3,e=parseInt("11111",2),r=parseInt("111111",2),a=this.height-1;a>=0;a--){for(var u=this.bottom_up?a:this.height-1-a,o=0;o<this.width;o++){var c=this.datav.getUint16(this.pos,!0);this.pos+=2;var h=(c&e)/e*255|0,f=(c>>5&r)/r*255|0,g=(c>>11)/e*255|0,y=u*this.width*4+4*o;this.data[y]=g,this.data[y+1]=f,this.data[y+2]=h,this.data[y+3]=255}this.pos+=n}},qr.prototype.bit24=function(){for(var n=this.height-1;n>=0;n--){for(var e=this.bottom_up?n:this.height-1-n,r=0;r<this.width;r++){var a=this.datav.getUint8(this.pos++,!0),u=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),c=e*this.width*4+4*r;this.data[c]=o,this.data[c+1]=u,this.data[c+2]=a,this.data[c+3]=255}this.pos+=this.width%4}},qr.prototype.bit32=function(){for(var n=this.height-1;n>=0;n--)for(var e=this.bottom_up?n:this.height-1-n,r=0;r<this.width;r++){var a=this.datav.getUint8(this.pos++,!0),u=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),c=this.datav.getUint8(this.pos++,!0),h=e*this.width*4+4*r;this.data[h]=o,this.data[h+1]=u,this.data[h+2]=a,this.data[h+3]=c}},qr.prototype.getData=function(){return this.data},function(n){n.processBMP=function(e,r,a,u){var o=new qr(e,!1),c=o.width,h=o.height,f={data:o.getData(),width:c,height:h},g=new ps(100).encode(f,100);return n.processJPEG.call(this,g,r,a,u)}}(Tt.API),Lu.prototype.getData=function(){return this.data},function(n){n.processWEBP=function(e,r,a,u){var o=new Lu(e),c=o.width,h=o.height,f={data:o.getData(),width:c,height:h},g=new ps(100).encode(f,100);return n.processJPEG.call(this,g,r,a,u)}}(Tt.API),Tt.API.processRGBA=function(n,e,r){for(var a=n.data,u=a.length,o=new Uint8Array(u/4*3),c=new Uint8Array(u/4),h=0,f=0,g=0;g<u;g+=4){var y=a[g],w=a[g+1],S=a[g+2],p=a[g+3];o[h++]=y,o[h++]=w,o[h++]=S,c[f++]=p}var O=this.__addimage__.arrayBufferToBinaryString(o);return{alpha:this.__addimage__.arrayBufferToBinaryString(c),data:O,index:e,alias:r,colorSpace:"DeviceRGB",bitsPerComponent:8,width:n.width,height:n.height}},Tt.API.setLanguage=function(n){return this.internal.languageSettings===void 0&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),{af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"}[n]!==void 0&&(this.internal.languageSettings.languageCode=n,this.internal.languageSettings.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")}),this.internal.languageSettings.isSubscribed=!0)),this},Ci=Tt.API,fo=Ci.getCharWidthsArray=function(n,e){var r,a,u=(e=e||{}).font||this.internal.getFont(),o=e.fontSize||this.internal.getFontSize(),c=e.charSpace||this.internal.getCharSpace(),h=e.widths?e.widths:u.metadata.Unicode.widths,f=h.fof?h.fof:1,g=e.kerning?e.kerning:u.metadata.Unicode.kerning,y=g.fof?g.fof:1,w=e.doKerning!==!1,S=0,p=n.length,O=0,F=h[0]||f,q=[];for(r=0;r<p;r++)a=n.charCodeAt(r),typeof u.metadata.widthOfString=="function"?q.push((u.metadata.widthOfGlyph(u.metadata.characterToGlyph(a))+c*(1e3/o)||0)/1e3):(S=w&&fe(g[a])==="object"&&!isNaN(parseInt(g[a][O],10))?g[a][O]/y:0,q.push((h[a]||F)/f+S)),O=a;return q},vu=Ci.getStringUnitWidth=function(n,e){var r=(e=e||{}).fontSize||this.internal.getFontSize(),a=e.font||this.internal.getFont(),u=e.charSpace||this.internal.getCharSpace();return Ci.processArabic&&(n=Ci.processArabic(n)),typeof a.metadata.widthOfString=="function"?a.metadata.widthOfString(n,r,u)/r:fo.apply(this,arguments).reduce(function(o,c){return o+c},0)},bu=function(n,e,r,a){for(var u=[],o=0,c=n.length,h=0;o!==c&&h+e[o]<r;)h+=e[o],o++;u.push(n.slice(0,o));var f=o;for(h=0;o!==c;)h+e[o]>a&&(u.push(n.slice(f,o)),h=0,f=o),h+=e[o],o++;return f!==o&&u.push(n.slice(f,o)),u},yu=function(n,e,r){r||(r={});var a,u,o,c,h,f,g,y=[],w=[y],S=r.textIndent||0,p=0,O=0,F=n.split(" "),q=fo.apply(this,[" ",r])[0];if(f=r.lineIndent===-1?F[0].length+2:r.lineIndent||0){var _=Array(f).join(" "),B=[];F.map(function(ot){(ot=ot.split(/\s*\n/)).length>1?B=B.concat(ot.map(function(ct,wt){return(wt&&ct.length?`
`:"")+ct})):B.push(ot[0])}),F=B,f=vu.apply(this,[_,r])}for(o=0,c=F.length;o<c;o++){var Y=0;if(a=F[o],f&&a[0]==`
`&&(a=a.substr(1),Y=1),S+p+(O=(u=fo.apply(this,[a,r])).reduce(function(ot,ct){return ot+ct},0))>e||Y){if(O>e){for(h=bu.apply(this,[a,u,e-(S+p),e]),y.push(h.shift()),y=[h.pop()];h.length;)w.push([h.shift()]);O=u.slice(a.length-(y[0]?y[0].length:0)).reduce(function(ot,ct){return ot+ct},0)}else y=[a];w.push(y),S=O+f,p=q}else y.push(a),S+=p+O,p=q}return g=f?function(ot,ct){return(ct?_:"")+ot.join(" ")}:function(ot){return ot.join(" ")},w.map(g)},Ci.splitTextToSize=function(n,e,r){var a,u=(r=r||{}).fontSize||this.internal.getFontSize(),o=(function(y){if(y.widths&&y.kerning)return{widths:y.widths,kerning:y.kerning};var w=this.internal.getFont(y.fontName,y.fontStyle);return w.metadata.Unicode?{widths:w.metadata.Unicode.widths||{0:1},kerning:w.metadata.Unicode.kerning||{}}:{font:w.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}).call(this,r);a=Array.isArray(n)?n:String(n).split(/\r?\n/);var c=1*this.internal.scaleFactor*e/u;o.textIndent=r.textIndent?1*r.textIndent*this.internal.scaleFactor/u:0,o.lineIndent=r.lineIndent;var h,f,g=[];for(h=0,f=a.length;h<f;h++)g=g.concat(yu.apply(this,[a[h],c,o]));return g},function(n){n.__fontmetrics__=n.__fontmetrics__||{};for(var e="klmnopqrstuvwxyz",r={},a={},u=0;u<e.length;u++)r[e[u]]="0123456789abcdef"[u],a["0123456789abcdef"[u]]=e[u];var o=function(w){return"0x"+parseInt(w,10).toString(16)},c=n.__fontmetrics__.compress=function(w){var S,p,O,F,q=["{"];for(var _ in w){if(S=w[_],isNaN(parseInt(_,10))?p="'"+_+"'":(_=parseInt(_,10),p=(p=o(_).slice(2)).slice(0,-1)+a[p.slice(-1)]),typeof S=="number")S<0?(O=o(S).slice(3),F="-"):(O=o(S).slice(2),F=""),O=F+O.slice(0,-1)+a[O.slice(-1)];else{if(fe(S)!=="object")throw new Error("Don't know what to do with value type "+fe(S)+".");O=c(S)}q.push(p+O)}return q.push("}"),q.join("")},h=n.__fontmetrics__.uncompress=function(w){if(typeof w!="string")throw new Error("Invalid argument passed to uncompress.");for(var S,p,O,F,q={},_=1,B=q,Y=[],ot="",ct="",wt=w.length-1,tt=1;tt<wt;tt+=1)(F=w[tt])=="'"?S?(O=S.join(""),S=void 0):S=[]:S?S.push(F):F=="{"?(Y.push([B,O]),B={},O=void 0):F=="}"?((p=Y.pop())[0][p[1]]=B,O=void 0,B=p[0]):F=="-"?_=-1:O===void 0?r.hasOwnProperty(F)?(ot+=r[F],O=parseInt(ot,16)*_,_=1,ot=""):ot+=F:r.hasOwnProperty(F)?(ct+=r[F],B[O]=parseInt(ct,16)*_,_=1,O=void 0,ct=""):ct+=F;return q},f={codePages:["WinAnsiEncoding"],WinAnsiEncoding:h("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},g={Unicode:{Courier:f,"Courier-Bold":f,"Courier-BoldOblique":f,"Courier-Oblique":f,Helvetica:f,"Helvetica-Bold":f,"Helvetica-BoldOblique":f,"Helvetica-Oblique":f,"Times-Roman":f,"Times-Bold":f,"Times-BoldItalic":f,"Times-Italic":f}},y={Unicode:{"Courier-Oblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":h("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":h("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:h("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":h("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:h("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":h("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":h("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":h("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":h("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};n.events.push(["addFont",function(w){var S=w.font,p=y.Unicode[S.postScriptName];p&&(S.metadata.Unicode={},S.metadata.Unicode.widths=p.widths,S.metadata.Unicode.kerning=p.kerning);var O=g.Unicode[S.postScriptName];O&&(S.metadata.Unicode.encoding=O,S.encoding=O.codePages[0])}])}(Tt.API),function(n){var e=function(r){for(var a=r.length,u=new Uint8Array(a),o=0;o<a;o++)u[o]=r.charCodeAt(o);return u};n.API.events.push(["addFont",function(r){var a=void 0,u=r.font,o=r.instance;if(!u.isStandardFont){if(o===void 0)throw new Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+u.postScriptName+"').");if(typeof(a=o.existsFileInVFS(u.postScriptName)===!1?o.loadFile(u.postScriptName):o.getFileFromVFS(u.postScriptName))!="string")throw new Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+u.postScriptName+"').");(function(c,h){h=/^\x00\x01\x00\x00/.test(h)?e(h):e(fa(h)),c.metadata=n.API.TTFFont.open(h),c.metadata.Unicode=c.metadata.Unicode||{encoding:{},kerning:{},widths:[]},c.metadata.glyIdsUsed=[0]})(u,a)}}])}(Tt),function(n){function e(){return(Ht.canvg?Promise.resolve(Ht.canvg):gs(()=>import("./index.es-DHhENybw.js"),__vite__mapDeps([0,1,2]))).catch(function(r){return Promise.reject(new Error("Could not load canvg: "+r))}).then(function(r){return r.default?r.default:r})}Tt.API.addSvgAsImage=function(r,a,u,o,c,h,f,g){if(isNaN(a)||isNaN(u))throw ve.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(o)||isNaN(c))throw ve.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),new Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var y=document.createElement("canvas");y.width=o,y.height=c;var w=y.getContext("2d");w.fillStyle="#fff",w.fillRect(0,0,y.width,y.height);var S={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},p=this;return e().then(function(O){return O.fromString(w,r,S)},function(){return Promise.reject(new Error("Could not load canvg."))}).then(function(O){return O.render(S)}).then(function(){p.addImage(y.toDataURL("image/jpeg",1),a,u,o,c,f,g)})}}(),Tt.API.putTotalPages=function(n){var e,r=0;parseInt(this.internal.getFont().id.substr(1),10)<15?(e=new RegExp(n,"g"),r=this.internal.getNumberOfPages()):(e=new RegExp(this.pdfEscape16(n,this.internal.getFont()),"g"),r=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var a=1;a<=this.internal.getNumberOfPages();a++)for(var u=0;u<this.internal.pages[a].length;u++)this.internal.pages[a][u]=this.internal.pages[a][u].replace(e,r);return this},Tt.API.viewerPreferences=function(n,e){var r;n=n||{},e=e||!1;var a,u,o,c={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},h=Object.keys(c),f=[],g=0,y=0,w=0;function S(O,F){var q,_=!1;for(q=0;q<O.length;q+=1)O[q]===F&&(_=!0);return _}if(this.internal.viewerpreferences===void 0&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(c)),this.internal.viewerpreferences.isSubscribed=!1),r=this.internal.viewerpreferences.configuration,n==="reset"||e===!0){var p=h.length;for(w=0;w<p;w+=1)r[h[w]].value=r[h[w]].defaultValue,r[h[w]].explicitSet=!1}if(fe(n)==="object"){for(u in n)if(o=n[u],S(h,u)&&o!==void 0){if(r[u].type==="boolean"&&typeof o=="boolean")r[u].value=o;else if(r[u].type==="name"&&S(r[u].valueSet,o))r[u].value=o;else if(r[u].type==="integer"&&Number.isInteger(o))r[u].value=o;else if(r[u].type==="array"){for(g=0;g<o.length;g+=1)if(a=!0,o[g].length===1&&typeof o[g][0]=="number")f.push(String(o[g]-1));else if(o[g].length>1){for(y=0;y<o[g].length;y+=1)typeof o[g][y]!="number"&&(a=!1);a===!0&&f.push([o[g][0]-1,o[g][1]-1].join(" "))}r[u].value="["+f.join(" ")+"]"}else r[u].value=r[u].defaultValue;r[u].explicitSet=!0}}return this.internal.viewerpreferences.isSubscribed===!1&&(this.internal.events.subscribe("putCatalog",function(){var O,F=[];for(O in r)r[O].explicitSet===!0&&(r[O].type==="name"?F.push("/"+O+" /"+r[O].value):F.push("/"+O+" "+r[O].value));F.length!==0&&this.internal.write(`/ViewerPreferences
<<
`+F.join(`
`)+`
>>`)}),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=r,this},function(n){var e=function(){var a='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',u=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),o=unescape(encodeURIComponent(a)),c=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),h=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),f=unescape(encodeURIComponent("</x:xmpmeta>")),g=o.length+c.length+h.length+u.length+f.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+g+" >>"),this.internal.write("stream"),this.internal.write(u+o+c+h+f),this.internal.write("endstream"),this.internal.write("endobj")},r=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")};n.addMetadata=function(a,u){return this.internal.__metadata__===void 0&&(this.internal.__metadata__={metadata:a,namespaceuri:u||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",r),this.internal.events.subscribe("postPutResources",e)),this}}(Tt.API),function(n){var e=n.API,r=e.pdfEscape16=function(o,c){for(var h,f=c.metadata.Unicode.widths,g=["","0","00","000","0000"],y=[""],w=0,S=o.length;w<S;++w){if(h=c.metadata.characterToGlyph(o.charCodeAt(w)),c.metadata.glyIdsUsed.push(h),c.metadata.toUnicode[h]=o.charCodeAt(w),f.indexOf(h)==-1&&(f.push(h),f.push([parseInt(c.metadata.widthOfGlyph(h),10)])),h=="0")return y.join("");h=h.toString(16),y.push(g[4-h.length],h)}return y.join("")},a=function(o){var c,h,f,g,y,w,S;for(y=`/CIDInit /ProcSet findresource begin
12 dict begin
begincmap
/CIDSystemInfo <<
  /Registry (Adobe)
  /Ordering (UCS)
  /Supplement 0
>> def
/CMapName /Adobe-Identity-UCS def
/CMapType 2 def
1 begincodespacerange
<0000><ffff>
endcodespacerange`,f=[],w=0,S=(h=Object.keys(o).sort(function(p,O){return p-O})).length;w<S;w++)c=h[w],f.length>=100&&(y+=`
`+f.length+` beginbfchar
`+f.join(`
`)+`
endbfchar`,f=[]),o[c]!==void 0&&o[c]!==null&&typeof o[c].toString=="function"&&(g=("0000"+o[c].toString(16)).slice(-4),c=("0000"+(+c).toString(16)).slice(-4),f.push("<"+c+"><"+g+">"));return f.length&&(y+=`
`+f.length+` beginbfchar
`+f.join(`
`)+`
endbfchar
`),y+=`endcmap
CMapName currentdict /CMap defineresource pop
end
end`};e.events.push(["putFont",function(o){(function(c){var h=c.font,f=c.out,g=c.newObject,y=c.putStream;if(h.metadata instanceof n.API.TTFFont&&h.encoding==="Identity-H"){for(var w=h.metadata.Unicode.widths,S=h.metadata.subset.encode(h.metadata.glyIdsUsed,1),p="",O=0;O<S.length;O++)p+=String.fromCharCode(S[O]);var F=g();y({data:p,addLength1:!0,objectId:F}),f("endobj");var q=g();y({data:a(h.metadata.toUnicode),addLength1:!0,objectId:q}),f("endobj");var _=g();f("<<"),f("/Type /FontDescriptor"),f("/FontName /"+Oi(h.fontName)),f("/FontFile2 "+F+" 0 R"),f("/FontBBox "+n.API.PDFObject.convert(h.metadata.bbox)),f("/Flags "+h.metadata.flags),f("/StemV "+h.metadata.stemV),f("/ItalicAngle "+h.metadata.italicAngle),f("/Ascent "+h.metadata.ascender),f("/Descent "+h.metadata.decender),f("/CapHeight "+h.metadata.capHeight),f(">>"),f("endobj");var B=g();f("<<"),f("/Type /Font"),f("/BaseFont /"+Oi(h.fontName)),f("/FontDescriptor "+_+" 0 R"),f("/W "+n.API.PDFObject.convert(w)),f("/CIDToGIDMap /Identity"),f("/DW 1000"),f("/Subtype /CIDFontType2"),f("/CIDSystemInfo"),f("<<"),f("/Supplement 0"),f("/Registry (Adobe)"),f("/Ordering ("+h.encoding+")"),f(">>"),f(">>"),f("endobj"),h.objectNumber=g(),f("<<"),f("/Type /Font"),f("/Subtype /Type0"),f("/ToUnicode "+q+" 0 R"),f("/BaseFont /"+Oi(h.fontName)),f("/Encoding /"+h.encoding),f("/DescendantFonts ["+B+" 0 R]"),f(">>"),f("endobj"),h.isAlreadyPutted=!0}})(o)}]),e.events.push(["putFont",function(o){(function(c){var h=c.font,f=c.out,g=c.newObject,y=c.putStream;if(h.metadata instanceof n.API.TTFFont&&h.encoding==="WinAnsiEncoding"){for(var w=h.metadata.rawData,S="",p=0;p<w.length;p++)S+=String.fromCharCode(w[p]);var O=g();y({data:S,addLength1:!0,objectId:O}),f("endobj");var F=g();y({data:a(h.metadata.toUnicode),addLength1:!0,objectId:F}),f("endobj");var q=g();f("<<"),f("/Descent "+h.metadata.decender),f("/CapHeight "+h.metadata.capHeight),f("/StemV "+h.metadata.stemV),f("/Type /FontDescriptor"),f("/FontFile2 "+O+" 0 R"),f("/Flags 96"),f("/FontBBox "+n.API.PDFObject.convert(h.metadata.bbox)),f("/FontName /"+Oi(h.fontName)),f("/ItalicAngle "+h.metadata.italicAngle),f("/Ascent "+h.metadata.ascender),f(">>"),f("endobj"),h.objectNumber=g();for(var _=0;_<h.metadata.hmtx.widths.length;_++)h.metadata.hmtx.widths[_]=parseInt(h.metadata.hmtx.widths[_]*(1e3/h.metadata.head.unitsPerEm));f("<</Subtype/TrueType/Type/Font/ToUnicode "+F+" 0 R/BaseFont/"+Oi(h.fontName)+"/FontDescriptor "+q+" 0 R/Encoding/"+h.encoding+" /FirstChar 29 /LastChar 255 /Widths "+n.API.PDFObject.convert(h.metadata.hmtx.widths)+">>"),f("endobj"),h.isAlreadyPutted=!0}})(o)}]);var u=function(o){var c,h=o.text||"",f=o.x,g=o.y,y=o.options||{},w=o.mutex||{},S=w.pdfEscape,p=w.activeFontKey,O=w.fonts,F=p,q="",_=0,B="",Y=O[F].encoding;if(O[F].encoding!=="Identity-H")return{text:h,x:f,y:g,options:y,mutex:w};for(B=h,F=p,Array.isArray(h)&&(B=h[0]),_=0;_<B.length;_+=1)O[F].metadata.hasOwnProperty("cmap")&&(c=O[F].metadata.cmap.unicode.codeMap[B[_].charCodeAt(0)]),c||B[_].charCodeAt(0)<256&&O[F].metadata.hasOwnProperty("Unicode")?q+=B[_]:q+="";var ot="";return parseInt(F.slice(1))<14||Y==="WinAnsiEncoding"?ot=S(q,F).split("").map(function(ct){return ct.charCodeAt(0).toString(16)}).join(""):Y==="Identity-H"&&(ot=r(q,O[F])),w.isHex=!0,{text:ot,x:f,y:g,options:y,mutex:w}};e.events.push(["postProcessText",function(o){var c=o.text||"",h=[],f={text:c,x:o.x,y:o.y,options:o.options,mutex:o.mutex};if(Array.isArray(c)){var g=0;for(g=0;g<c.length;g+=1)Array.isArray(c[g])&&c[g].length===3?h.push([u(Object.assign({},f,{text:c[g][0]})).text,c[g][1],c[g][2]]):h.push(u(Object.assign({},f,{text:c[g]})).text);o.text=h}else o.text=u(Object.assign({},f,{text:c})).text}])}(Tt),function(n){var e=function(){return this.internal.vFS===void 0&&(this.internal.vFS={}),!0};n.existsFileInVFS=function(r){return e.call(this),this.internal.vFS[r]!==void 0},n.addFileToVFS=function(r,a){return e.call(this),this.internal.vFS[r]=a,this},n.getFileFromVFS=function(r){return e.call(this),this.internal.vFS[r]!==void 0?this.internal.vFS[r]:null}}(Tt.API),function(n){n.__bidiEngine__=n.prototype.__bidiEngine__=function(a){var u,o,c,h,f,g,y,w=e,S=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],p=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],O={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},F={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},q=["(",")","(","<",">","<","[","]","[","{","}","{","«","»","«","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],_=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),B=!1,Y=0;this.__bidiEngine__={};var ot=function(P){var k=P.charCodeAt(),W=k>>8,D=F[W];return D!==void 0?w[256*D+(255&k)]:W===252||W===253?"AL":_.test(W)?"L":W===8?"R":"N"},ct=function(P){for(var k,W=0;W<P.length;W++){if((k=ot(P.charAt(W)))==="L")return!1;if(k==="R")return!0}return!1},wt=function(P,k,W,D){var st,it,ht,$,lt=k[D];switch(lt){case"L":case"R":B=!1;break;case"N":case"AN":break;case"EN":B&&(lt="AN");break;case"AL":B=!0,lt="R";break;case"WS":lt="N";break;case"CS":D<1||D+1>=k.length||(st=W[D-1])!=="EN"&&st!=="AN"||(it=k[D+1])!=="EN"&&it!=="AN"?lt="N":B&&(it="AN"),lt=it===st?it:"N";break;case"ES":lt=(st=D>0?W[D-1]:"B")==="EN"&&D+1<k.length&&k[D+1]==="EN"?"EN":"N";break;case"ET":if(D>0&&W[D-1]==="EN"){lt="EN";break}if(B){lt="N";break}for(ht=D+1,$=k.length;ht<$&&k[ht]==="ET";)ht++;lt=ht<$&&k[ht]==="EN"?"EN":"N";break;case"NSM":if(c&&!h){for($=k.length,ht=D+1;ht<$&&k[ht]==="NSM";)ht++;if(ht<$){var dt=P[D],jt=dt>=1425&&dt<=2303||dt===64286;if(st=k[ht],jt&&(st==="R"||st==="AL")){lt="R";break}}}lt=D<1||(st=k[D-1])==="B"?"N":W[D-1];break;case"B":B=!1,u=!0,lt=Y;break;case"S":o=!0,lt="N";break;case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":B=!1;break;case"BN":lt="N"}return lt},tt=function(P,k,W){var D=P.split("");return W&&z(D,W,{hiLevel:Y}),D.reverse(),k&&k.reverse(),D.join("")},z=function(P,k,W){var D,st,it,ht,$,lt=-1,dt=P.length,jt=0,N=[],C=Y?p:S,M=[];for(B=!1,u=!1,o=!1,st=0;st<dt;st++)M[st]=ot(P[st]);for(it=0;it<dt;it++){if($=jt,N[it]=wt(P,M,N,it),D=240&(jt=C[$][O[N[it]]]),jt&=15,k[it]=ht=C[jt][5],D>0)if(D===16){for(st=lt;st<it;st++)k[st]=1;lt=-1}else lt=-1;if(C[jt][6])lt===-1&&(lt=it);else if(lt>-1){for(st=lt;st<it;st++)k[st]=ht;lt=-1}M[it]==="B"&&(k[it]=0),W.hiLevel|=ht}o&&function(T,J,Q){for(var et=0;et<Q;et++)if(T[et]==="S"){J[et]=Y;for(var rt=et-1;rt>=0&&T[rt]==="WS";rt--)J[rt]=Y}}(M,k,dt)},nt=function(P,k,W,D,st){if(!(st.hiLevel<P)){if(P===1&&Y===1&&!u)return k.reverse(),void(W&&W.reverse());for(var it,ht,$,lt,dt=k.length,jt=0;jt<dt;){if(D[jt]>=P){for($=jt+1;$<dt&&D[$]>=P;)$++;for(lt=jt,ht=$-1;lt<ht;lt++,ht--)it=k[lt],k[lt]=k[ht],k[ht]=it,W&&(it=W[lt],W[lt]=W[ht],W[ht]=it);jt=$}jt++}}},pt=function(P,k,W){var D=P.split(""),st={hiLevel:Y};return W||(W=[]),z(D,W,st),function(it,ht,$){if($.hiLevel!==0&&y)for(var lt,dt=0;dt<it.length;dt++)ht[dt]===1&&(lt=q.indexOf(it[dt]))>=0&&(it[dt]=q[lt+1])}(D,W,st),nt(2,D,k,W,st),nt(1,D,k,W,st),D.join("")};return this.__bidiEngine__.doBidiReorder=function(P,k,W){if(function(st,it){if(it)for(var ht=0;ht<st.length;ht++)it[ht]=ht;h===void 0&&(h=ct(st)),g===void 0&&(g=ct(st))}(P,k),c||!f||g)if(c&&f&&h^g)Y=h?1:0,P=tt(P,k,W);else if(!c&&f&&g)Y=h?1:0,P=pt(P,k,W),P=tt(P,k);else if(!c||h||f||g){if(c&&!f&&h^g)P=tt(P,k),h?(Y=0,P=pt(P,k,W)):(Y=1,P=pt(P,k,W),P=tt(P,k));else if(c&&h&&!f&&g)Y=1,P=pt(P,k,W),P=tt(P,k);else if(!c&&!f&&h^g){var D=y;h?(Y=1,P=pt(P,k,W),Y=0,y=!1,P=pt(P,k,W),y=D):(Y=0,P=pt(P,k,W),P=tt(P,k),Y=1,y=!1,P=pt(P,k,W),y=D,P=tt(P,k))}}else Y=0,P=pt(P,k,W);else Y=h?1:0,P=pt(P,k,W);return P},this.__bidiEngine__.setOptions=function(P){P&&(c=P.isInputVisual,f=P.isOutputVisual,h=P.isInputRtl,g=P.isOutputRtl,y=P.isSymmetricSwapping)},this.__bidiEngine__.setOptions(a),this.__bidiEngine__};var e=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],r=new n.__bidiEngine__({isInputVisual:!0});n.API.events.push(["postProcessText",function(a){var u=a.text;a.x,a.y;var o=a.options||{};a.mutex,o.lang;var c=[];if(o.isInputVisual=typeof o.isInputVisual!="boolean"||o.isInputVisual,r.setOptions(o),Object.prototype.toString.call(u)==="[object Array]"){var h=0;for(c=[],h=0;h<u.length;h+=1)Object.prototype.toString.call(u[h])==="[object Array]"?c.push([r.doBidiReorder(u[h][0]),u[h][1],u[h][2]]):c.push([r.doBidiReorder(u[h])]);a.text=c}else a.text=r.doBidiReorder(u);r.setOptions({isInputVisual:!0})}])}(Tt),Tt.API.TTFFont=function(){function n(e){var r;if(this.rawData=e,r=this.contents=new Bn(e),this.contents.pos=4,r.readString(4)==="ttcf")throw new Error("TTCF not supported.");r.pos=0,this.parse(),this.subset=new xc(this),this.registerTTF()}return n.open=function(e){return new n(e)},n.prototype.parse=function(){return this.directory=new cc(this.contents),this.head=new fc(this),this.name=new vc(this),this.cmap=new Uu(this),this.toUnicode={},this.hhea=new dc(this),this.maxp=new bc(this),this.hmtx=new yc(this),this.post=new gc(this),this.os2=new pc(this),this.loca=new Ac(this),this.glyf=new wc(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},n.prototype.registerTTF=function(){var e,r,a,u,o;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=(function(){var c,h,f,g;for(g=[],c=0,h=(f=this.bbox).length;c<h;c++)e=f[c],g.push(Math.round(e*this.scaleFactor));return g}).call(this),this.stemV=0,this.post.exists?(a=255&(u=this.post.italic_angle),(32768&(r=u>>16))!=0&&(r=-(1+(65535^r))),this.italicAngle=+(r+"."+a)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=(o=this.familyClass)===1||o===2||o===3||o===4||o===5||o===7,this.isScript=this.familyClass===10,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),this.italicAngle!==0&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw new Error("No unicode cmap for font")},n.prototype.characterToGlyph=function(e){var r;return((r=this.cmap.unicode)!=null?r.codeMap[e]:void 0)||0},n.prototype.widthOfGlyph=function(e){var r;return r=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(e).advance*r},n.prototype.widthOfString=function(e,r,a){var u,o,c,h;for(c=0,o=0,h=(e=""+e).length;0<=h?o<h:o>h;o=0<=h?++o:--o)u=e.charCodeAt(o),c+=this.widthOfGlyph(this.characterToGlyph(u))+a*(1e3/r)||0;return c*(r/1e3)},n.prototype.lineHeight=function(e,r){var a;return r==null&&(r=!1),a=r?this.lineGap:0,(this.ascender+a-this.decender)/1e3*e},n}();var Tr,Bn=function(){function n(e){this.data=e??[],this.pos=0,this.length=this.data.length}return n.prototype.readByte=function(){return this.data[this.pos++]},n.prototype.writeByte=function(e){return this.data[this.pos++]=e},n.prototype.readUInt32=function(){return 16777216*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},n.prototype.writeUInt32=function(e){return this.writeByte(e>>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e)},n.prototype.readInt32=function(){var e;return(e=this.readUInt32())>=2147483648?e-4294967296:e},n.prototype.writeInt32=function(e){return e<0&&(e+=4294967296),this.writeUInt32(e)},n.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},n.prototype.writeUInt16=function(e){return this.writeByte(e>>8&255),this.writeByte(255&e)},n.prototype.readInt16=function(){var e;return(e=this.readUInt16())>=32768?e-65536:e},n.prototype.writeInt16=function(e){return e<0&&(e+=65536),this.writeUInt16(e)},n.prototype.readString=function(e){var r,a;for(a=[],r=0;0<=e?r<e:r>e;r=0<=e?++r:--r)a[r]=String.fromCharCode(this.readByte());return a.join("")},n.prototype.writeString=function(e){var r,a,u;for(u=[],r=0,a=e.length;0<=a?r<a:r>a;r=0<=a?++r:--r)u.push(this.writeByte(e.charCodeAt(r)));return u},n.prototype.readShort=function(){return this.readInt16()},n.prototype.writeShort=function(e){return this.writeInt16(e)},n.prototype.readLongLong=function(){var e,r,a,u,o,c,h,f;return e=this.readByte(),r=this.readByte(),a=this.readByte(),u=this.readByte(),o=this.readByte(),c=this.readByte(),h=this.readByte(),f=this.readByte(),128&e?-1*(72057594037927940*(255^e)+281474976710656*(255^r)+1099511627776*(255^a)+4294967296*(255^u)+16777216*(255^o)+65536*(255^c)+256*(255^h)+(255^f)+1):72057594037927940*e+281474976710656*r+1099511627776*a+4294967296*u+16777216*o+65536*c+256*h+f},n.prototype.writeLongLong=function(e){var r,a;return r=Math.floor(e/4294967296),a=**********&e,this.writeByte(r>>24&255),this.writeByte(r>>16&255),this.writeByte(r>>8&255),this.writeByte(255&r),this.writeByte(a>>24&255),this.writeByte(a>>16&255),this.writeByte(a>>8&255),this.writeByte(255&a)},n.prototype.readInt=function(){return this.readInt32()},n.prototype.writeInt=function(e){return this.writeInt32(e)},n.prototype.read=function(e){var r,a;for(r=[],a=0;0<=e?a<e:a>e;a=0<=e?++a:--a)r.push(this.readByte());return r},n.prototype.write=function(e){var r,a,u,o;for(o=[],a=0,u=e.length;a<u;a++)r=e[a],o.push(this.writeByte(r));return o},n}(),cc=function(){var n;function e(r){var a,u,o;for(this.scalarType=r.readInt(),this.tableCount=r.readShort(),this.searchRange=r.readShort(),this.entrySelector=r.readShort(),this.rangeShift=r.readShort(),this.tables={},u=0,o=this.tableCount;0<=o?u<o:u>o;u=0<=o?++u:--u)a={tag:r.readString(4),checksum:r.readInt(),offset:r.readInt(),length:r.readInt()},this.tables[a.tag]=a}return e.prototype.encode=function(r){var a,u,o,c,h,f,g,y,w,S,p,O,F;for(F in p=Object.keys(r).length,f=Math.log(2),w=16*Math.floor(Math.log(p)/f),c=Math.floor(w/f),y=16*p-w,(u=new Bn).writeInt(this.scalarType),u.writeShort(p),u.writeShort(w),u.writeShort(c),u.writeShort(y),o=16*p,g=u.pos+o,h=null,O=[],r)for(S=r[F],u.writeString(F),u.writeInt(n(S)),u.writeInt(g),u.writeInt(S.length),O=O.concat(S),F==="head"&&(h=g),g+=S.length;g%4;)O.push(0),g++;return u.write(O),a=2981146554-n(u.data),u.pos=h+8,u.writeUInt32(a),u.data},n=function(r){var a,u,o,c;for(r=Hu.call(r);r.length%4;)r.push(0);for(o=new Bn(r),u=0,a=0,c=r.length;a<c;a=a+=4)u+=o.readUInt32();return **********&u},e}(),hc={}.hasOwnProperty,tn=function(n,e){for(var r in e)hc.call(e,r)&&(n[r]=e[r]);function a(){this.constructor=n}return a.prototype=e.prototype,n.prototype=new a,n.__super__=e.prototype,n};Tr=function(){function n(e){var r;this.file=e,r=this.file.directory.tables[this.tag],this.exists=!!r,r&&(this.offset=r.offset,this.length=r.length,this.parse(this.file.contents))}return n.prototype.parse=function(){},n.prototype.encode=function(){},n.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},n}();var fc=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return tn(e,Tr),e.prototype.tag="head",e.prototype.parse=function(r){return r.pos=this.offset,this.version=r.readInt(),this.revision=r.readInt(),this.checkSumAdjustment=r.readInt(),this.magicNumber=r.readInt(),this.flags=r.readShort(),this.unitsPerEm=r.readShort(),this.created=r.readLongLong(),this.modified=r.readLongLong(),this.xMin=r.readShort(),this.yMin=r.readShort(),this.xMax=r.readShort(),this.yMax=r.readShort(),this.macStyle=r.readShort(),this.lowestRecPPEM=r.readShort(),this.fontDirectionHint=r.readShort(),this.indexToLocFormat=r.readShort(),this.glyphDataFormat=r.readShort()},e.prototype.encode=function(r){var a;return(a=new Bn).writeInt(this.version),a.writeInt(this.revision),a.writeInt(this.checkSumAdjustment),a.writeInt(this.magicNumber),a.writeShort(this.flags),a.writeShort(this.unitsPerEm),a.writeLongLong(this.created),a.writeLongLong(this.modified),a.writeShort(this.xMin),a.writeShort(this.yMin),a.writeShort(this.xMax),a.writeShort(this.yMax),a.writeShort(this.macStyle),a.writeShort(this.lowestRecPPEM),a.writeShort(this.fontDirectionHint),a.writeShort(r),a.writeShort(this.glyphDataFormat),a.data},e}(),Nu=function(){function n(e,r){var a,u,o,c,h,f,g,y,w,S,p,O,F,q,_,B,Y;switch(this.platformID=e.readUInt16(),this.encodingID=e.readShort(),this.offset=r+e.readInt(),w=e.pos,e.pos=this.offset,this.format=e.readUInt16(),this.length=e.readUInt16(),this.language=e.readUInt16(),this.isUnicode=this.platformID===3&&this.encodingID===1&&this.format===4||this.platformID===0&&this.format===4,this.codeMap={},this.format){case 0:for(f=0;f<256;++f)this.codeMap[f]=e.readByte();break;case 4:for(p=e.readUInt16(),S=p/2,e.pos+=6,o=function(){var ot,ct;for(ct=[],f=ot=0;0<=S?ot<S:ot>S;f=0<=S?++ot:--ot)ct.push(e.readUInt16());return ct}(),e.pos+=2,F=function(){var ot,ct;for(ct=[],f=ot=0;0<=S?ot<S:ot>S;f=0<=S?++ot:--ot)ct.push(e.readUInt16());return ct}(),g=function(){var ot,ct;for(ct=[],f=ot=0;0<=S?ot<S:ot>S;f=0<=S?++ot:--ot)ct.push(e.readUInt16());return ct}(),y=function(){var ot,ct;for(ct=[],f=ot=0;0<=S?ot<S:ot>S;f=0<=S?++ot:--ot)ct.push(e.readUInt16());return ct}(),u=(this.length-e.pos+this.offset)/2,h=function(){var ot,ct;for(ct=[],f=ot=0;0<=u?ot<u:ot>u;f=0<=u?++ot:--ot)ct.push(e.readUInt16());return ct}(),f=_=0,Y=o.length;_<Y;f=++_)for(q=o[f],a=B=O=F[f];O<=q?B<=q:B>=q;a=O<=q?++B:--B)y[f]===0?c=a+g[f]:(c=h[y[f]/2+(a-O)-(S-f)]||0)!==0&&(c+=g[f]),this.codeMap[a]=65535&c}e.pos=w}return n.encode=function(e,r){var a,u,o,c,h,f,g,y,w,S,p,O,F,q,_,B,Y,ot,ct,wt,tt,z,nt,pt,P,k,W,D,st,it,ht,$,lt,dt,jt,N,C,M,T,J,Q,et,rt,At,Nt,Ft;switch(D=new Bn,c=Object.keys(e).sort(function(_t,Ut){return _t-Ut}),r){case"macroman":for(F=0,q=function(){var _t=[];for(O=0;O<256;++O)_t.push(0);return _t}(),B={0:0},o={},st=0,lt=c.length;st<lt;st++)B[rt=e[u=c[st]]]==null&&(B[rt]=++F),o[u]={old:e[u],new:B[e[u]]},q[u]=B[e[u]];return D.writeUInt16(1),D.writeUInt16(0),D.writeUInt32(12),D.writeUInt16(0),D.writeUInt16(262),D.writeUInt16(0),D.write(q),{charMap:o,subtable:D.data,maxGlyphID:F+1};case"unicode":for(k=[],w=[],Y=0,B={},a={},_=g=null,it=0,dt=c.length;it<dt;it++)B[ct=e[u=c[it]]]==null&&(B[ct]=++Y),a[u]={old:ct,new:B[ct]},h=B[ct]-u,_!=null&&h===g||(_&&w.push(_),k.push(u),g=h),_=u;for(_&&w.push(_),w.push(65535),k.push(65535),pt=2*(nt=k.length),z=2*Math.pow(Math.log(nt)/Math.LN2,2),S=Math.log(z/2)/Math.LN2,tt=2*nt-z,f=[],wt=[],p=[],O=ht=0,jt=k.length;ht<jt;O=++ht){if(P=k[O],y=w[O],P===65535){f.push(0),wt.push(0);break}if(P-(W=a[P].new)>=32768)for(f.push(0),wt.push(2*(p.length+nt-O)),u=$=P;P<=y?$<=y:$>=y;u=P<=y?++$:--$)p.push(a[u].new);else f.push(W-P),wt.push(0)}for(D.writeUInt16(3),D.writeUInt16(1),D.writeUInt32(12),D.writeUInt16(4),D.writeUInt16(16+8*nt+2*p.length),D.writeUInt16(0),D.writeUInt16(pt),D.writeUInt16(z),D.writeUInt16(S),D.writeUInt16(tt),Q=0,N=w.length;Q<N;Q++)u=w[Q],D.writeUInt16(u);for(D.writeUInt16(0),et=0,C=k.length;et<C;et++)u=k[et],D.writeUInt16(u);for(At=0,M=f.length;At<M;At++)h=f[At],D.writeUInt16(h);for(Nt=0,T=wt.length;Nt<T;Nt++)ot=wt[Nt],D.writeUInt16(ot);for(Ft=0,J=p.length;Ft<J;Ft++)F=p[Ft],D.writeUInt16(F);return{charMap:a,subtable:D.data,maxGlyphID:Y+1}}},n}(),Uu=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return tn(e,Tr),e.prototype.tag="cmap",e.prototype.parse=function(r){var a,u,o;for(r.pos=this.offset,this.version=r.readUInt16(),o=r.readUInt16(),this.tables=[],this.unicode=null,u=0;0<=o?u<o:u>o;u=0<=o?++u:--u)a=new Nu(r,this.offset),this.tables.push(a),a.isUnicode&&this.unicode==null&&(this.unicode=a);return!0},e.encode=function(r,a){var u,o;return a==null&&(a="macroman"),u=Nu.encode(r,a),(o=new Bn).writeUInt16(0),o.writeUInt16(1),u.table=o.data.concat(u.subtable),u},e}(),dc=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return tn(e,Tr),e.prototype.tag="hhea",e.prototype.parse=function(r){return r.pos=this.offset,this.version=r.readInt(),this.ascender=r.readShort(),this.decender=r.readShort(),this.lineGap=r.readShort(),this.advanceWidthMax=r.readShort(),this.minLeftSideBearing=r.readShort(),this.minRightSideBearing=r.readShort(),this.xMaxExtent=r.readShort(),this.caretSlopeRise=r.readShort(),this.caretSlopeRun=r.readShort(),this.caretOffset=r.readShort(),r.pos+=8,this.metricDataFormat=r.readShort(),this.numberOfMetrics=r.readUInt16()},e}(),pc=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return tn(e,Tr),e.prototype.tag="OS/2",e.prototype.parse=function(r){if(r.pos=this.offset,this.version=r.readUInt16(),this.averageCharWidth=r.readShort(),this.weightClass=r.readUInt16(),this.widthClass=r.readUInt16(),this.type=r.readShort(),this.ySubscriptXSize=r.readShort(),this.ySubscriptYSize=r.readShort(),this.ySubscriptXOffset=r.readShort(),this.ySubscriptYOffset=r.readShort(),this.ySuperscriptXSize=r.readShort(),this.ySuperscriptYSize=r.readShort(),this.ySuperscriptXOffset=r.readShort(),this.ySuperscriptYOffset=r.readShort(),this.yStrikeoutSize=r.readShort(),this.yStrikeoutPosition=r.readShort(),this.familyClass=r.readShort(),this.panose=function(){var a,u;for(u=[],a=0;a<10;++a)u.push(r.readByte());return u}(),this.charRange=function(){var a,u;for(u=[],a=0;a<4;++a)u.push(r.readInt());return u}(),this.vendorID=r.readString(4),this.selection=r.readShort(),this.firstCharIndex=r.readShort(),this.lastCharIndex=r.readShort(),this.version>0&&(this.ascent=r.readShort(),this.descent=r.readShort(),this.lineGap=r.readShort(),this.winAscent=r.readShort(),this.winDescent=r.readShort(),this.codePageRange=function(){var a,u;for(u=[],a=0;a<2;a=++a)u.push(r.readInt());return u}(),this.version>1))return this.xHeight=r.readShort(),this.capHeight=r.readShort(),this.defaultChar=r.readShort(),this.breakChar=r.readShort(),this.maxContext=r.readShort()},e}(),gc=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return tn(e,Tr),e.prototype.tag="post",e.prototype.parse=function(r){var a,u,o;switch(r.pos=this.offset,this.format=r.readInt(),this.italicAngle=r.readInt(),this.underlinePosition=r.readShort(),this.underlineThickness=r.readShort(),this.isFixedPitch=r.readInt(),this.minMemType42=r.readInt(),this.maxMemType42=r.readInt(),this.minMemType1=r.readInt(),this.maxMemType1=r.readInt(),this.format){case 65536:break;case 131072:var c;for(u=r.readUInt16(),this.glyphNameIndex=[],c=0;0<=u?c<u:c>u;c=0<=u?++c:--c)this.glyphNameIndex.push(r.readUInt16());for(this.names=[],o=[];r.pos<this.offset+this.length;)a=r.readByte(),o.push(this.names.push(r.readString(a)));return o;case 151552:return u=r.readUInt16(),this.offsets=r.read(u);case 196608:break;case 262144:return this.map=(function(){var h,f,g;for(g=[],c=h=0,f=this.file.maxp.numGlyphs;0<=f?h<f:h>f;c=0<=f?++h:--h)g.push(r.readUInt32());return g}).call(this)}},e}(),mc=function(n,e){this.raw=n,this.length=n.length,this.platformID=e.platformID,this.encodingID=e.encodingID,this.languageID=e.languageID},vc=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return tn(e,Tr),e.prototype.tag="name",e.prototype.parse=function(r){var a,u,o,c,h,f,g,y,w,S,p;for(r.pos=this.offset,r.readShort(),a=r.readShort(),f=r.readShort(),u=[],c=0;0<=a?c<a:c>a;c=0<=a?++c:--c)u.push({platformID:r.readShort(),encodingID:r.readShort(),languageID:r.readShort(),nameID:r.readShort(),length:r.readShort(),offset:this.offset+f+r.readShort()});for(g={},c=w=0,S=u.length;w<S;c=++w)o=u[c],r.pos=o.offset,y=r.readString(o.length),h=new mc(y,o),g[p=o.nameID]==null&&(g[p]=[]),g[o.nameID].push(h);this.strings=g,this.copyright=g[0],this.fontFamily=g[1],this.fontSubfamily=g[2],this.uniqueSubfamily=g[3],this.fontName=g[4],this.version=g[5];try{this.postscriptName=g[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch{this.postscriptName=g[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=g[7],this.manufacturer=g[8],this.designer=g[9],this.description=g[10],this.vendorUrl=g[11],this.designerUrl=g[12],this.license=g[13],this.licenseUrl=g[14],this.preferredFamily=g[15],this.preferredSubfamily=g[17],this.compatibleFull=g[18],this.sampleText=g[19]},e}(),bc=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return tn(e,Tr),e.prototype.tag="maxp",e.prototype.parse=function(r){return r.pos=this.offset,this.version=r.readInt(),this.numGlyphs=r.readUInt16(),this.maxPoints=r.readUInt16(),this.maxContours=r.readUInt16(),this.maxCompositePoints=r.readUInt16(),this.maxComponentContours=r.readUInt16(),this.maxZones=r.readUInt16(),this.maxTwilightPoints=r.readUInt16(),this.maxStorage=r.readUInt16(),this.maxFunctionDefs=r.readUInt16(),this.maxInstructionDefs=r.readUInt16(),this.maxStackElements=r.readUInt16(),this.maxSizeOfInstructions=r.readUInt16(),this.maxComponentElements=r.readUInt16(),this.maxComponentDepth=r.readUInt16()},e}(),yc=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return tn(e,Tr),e.prototype.tag="hmtx",e.prototype.parse=function(r){var a,u,o,c,h,f,g;for(r.pos=this.offset,this.metrics=[],a=0,f=this.file.hhea.numberOfMetrics;0<=f?a<f:a>f;a=0<=f?++a:--a)this.metrics.push({advance:r.readUInt16(),lsb:r.readInt16()});for(o=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var y,w;for(w=[],a=y=0;0<=o?y<o:y>o;a=0<=o?++y:--y)w.push(r.readInt16());return w}(),this.widths=(function(){var y,w,S,p;for(p=[],y=0,w=(S=this.metrics).length;y<w;y++)c=S[y],p.push(c.advance);return p}).call(this),u=this.widths[this.widths.length-1],g=[],a=h=0;0<=o?h<o:h>o;a=0<=o?++h:--h)g.push(this.widths.push(u));return g},e.prototype.forGlyph=function(r){return r in this.metrics?this.metrics[r]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[r-this.metrics.length]}},e}(),Hu=[].slice,wc=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return tn(e,Tr),e.prototype.tag="glyf",e.prototype.parse=function(){return this.cache={}},e.prototype.glyphFor=function(r){var a,u,o,c,h,f,g,y,w,S;return r in this.cache?this.cache[r]:(c=this.file.loca,a=this.file.contents,u=c.indexOf(r),(o=c.lengthOf(r))===0?this.cache[r]=null:(a.pos=this.offset+u,h=(f=new Bn(a.read(o))).readShort(),y=f.readShort(),S=f.readShort(),g=f.readShort(),w=f.readShort(),this.cache[r]=h===-1?new Nc(f,y,S,g,w):new Lc(f,h,y,S,g,w),this.cache[r]))},e.prototype.encode=function(r,a,u){var o,c,h,f,g;for(h=[],c=[],f=0,g=a.length;f<g;f++)o=r[a[f]],c.push(h.length),o&&(h=h.concat(o.encode(u)));return c.push(h.length),{table:h,offsets:c}},e}(),Lc=function(){function n(e,r,a,u,o,c){this.raw=e,this.numberOfContours=r,this.xMin=a,this.yMin=u,this.xMax=o,this.yMax=c,this.compound=!1}return n.prototype.encode=function(){return this.raw.data},n}(),Nc=function(){function n(e,r,a,u,o){var c,h;for(this.raw=e,this.xMin=r,this.yMin=a,this.xMax=u,this.yMax=o,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],c=this.raw;h=c.readShort(),this.glyphOffsets.push(c.pos),this.glyphIDs.push(c.readUInt16()),32&h;)c.pos+=1&h?4:2,128&h?c.pos+=8:64&h?c.pos+=4:8&h&&(c.pos+=2)}return n.prototype.encode=function(){var e,r,a;for(r=new Bn(Hu.call(this.raw.data)),e=0,a=this.glyphIDs.length;e<a;++e)r.pos=this.glyphOffsets[e];return r.data},n}(),Ac=function(n){function e(){return e.__super__.constructor.apply(this,arguments)}return tn(e,Tr),e.prototype.tag="loca",e.prototype.parse=function(r){var a,u;return r.pos=this.offset,a=this.file.head.indexToLocFormat,this.offsets=a===0?(function(){var o,c;for(c=[],u=0,o=this.length;u<o;u+=2)c.push(2*r.readUInt16());return c}).call(this):(function(){var o,c;for(c=[],u=0,o=this.length;u<o;u+=4)c.push(r.readUInt32());return c}).call(this)},e.prototype.indexOf=function(r){return this.offsets[r]},e.prototype.lengthOf=function(r){return this.offsets[r+1]-this.offsets[r]},e.prototype.encode=function(r,a){for(var u=new Uint32Array(this.offsets.length),o=0,c=0,h=0;h<u.length;++h)if(u[h]=o,c<a.length&&a[c]==h){++c,u[h]=o;var f=this.offsets[h],g=this.offsets[h+1]-f;g>0&&(o+=g)}for(var y=new Array(4*u.length),w=0;w<u.length;++w)y[4*w+3]=255&u[w],y[4*w+2]=(65280&u[w])>>8,y[4*w+1]=(16711680&u[w])>>16,y[4*w]=(**********&u[w])>>24;return y},e}(),xc=function(){function n(e){this.font=e,this.subset={},this.unicodes={},this.next=33}return n.prototype.generateCmap=function(){var e,r,a,u,o;for(r in u=this.font.cmap.tables[0].codeMap,e={},o=this.subset)a=o[r],e[r]=u[a];return e},n.prototype.glyphsFor=function(e){var r,a,u,o,c,h,f;for(u={},c=0,h=e.length;c<h;c++)u[o=e[c]]=this.font.glyf.glyphFor(o);for(o in r=[],u)(a=u[o])!=null&&a.compound&&r.push.apply(r,a.glyphIDs);if(r.length>0)for(o in f=this.glyphsFor(r))a=f[o],u[o]=a;return u},n.prototype.encode=function(e,r){var a,u,o,c,h,f,g,y,w,S,p,O,F,q,_;for(u in a=Uu.encode(this.generateCmap(),"unicode"),c=this.glyphsFor(e),p={0:0},_=a.charMap)p[(f=_[u]).old]=f.new;for(O in S=a.maxGlyphID,c)O in p||(p[O]=S++);return y=function(B){var Y,ot;for(Y in ot={},B)ot[B[Y]]=Y;return ot}(p),w=Object.keys(y).sort(function(B,Y){return B-Y}),F=function(){var B,Y,ot;for(ot=[],B=0,Y=w.length;B<Y;B++)h=w[B],ot.push(y[h]);return ot}(),o=this.font.glyf.encode(c,F,p),g=this.font.loca.encode(o.offsets,F),q={cmap:this.font.cmap.raw(),glyf:o.table,loca:g,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(r)},this.font.os2.exists&&(q["OS/2"]=this.font.os2.raw()),this.font.directory.encode(q)},n}();Tt.API.PDFObject=function(){var n;function e(){}return n=function(r,a){return(Array(a+1).join("0")+r).slice(-a)},e.convert=function(r){var a,u,o,c;if(Array.isArray(r))return"["+function(){var h,f,g;for(g=[],h=0,f=r.length;h<f;h++)a=r[h],g.push(e.convert(a));return g}().join(" ")+"]";if(typeof r=="string")return"/"+r;if(r!=null&&r.isString)return"("+r+")";if(r instanceof Date)return"(D:"+n(r.getUTCFullYear(),4)+n(r.getUTCMonth(),2)+n(r.getUTCDate(),2)+n(r.getUTCHours(),2)+n(r.getUTCMinutes(),2)+n(r.getUTCSeconds(),2)+"Z)";if({}.toString.call(r)==="[object Object]"){for(u in o=["<<"],r)c=r[u],o.push("/"+u+" "+e.convert(c));return o.push(">>"),o.join(`
`)}return""+r},e}();const _c=Object.freeze(Object.defineProperty({__proto__:null,AcroForm:rc,AcroFormAppearance:Mt,AcroFormButton:Te,AcroFormCheckBox:ga,AcroFormChoiceField:Kn,AcroFormComboBox:$n,AcroFormEditBox:da,AcroFormListBox:Zn,AcroFormPasswordField:ma,AcroFormPushButton:pa,AcroFormRadioButton:Qn,AcroFormTextField:jn,GState:ba,ShadingPattern:In,TilingPattern:Xn,default:Tt,jsPDF:Tt},Symbol.toStringTag,{value:"Module"}));export{fe as _,_c as j};
//# sourceMappingURL=jspdf.es.min-DFZ4OdAQ.js.map
