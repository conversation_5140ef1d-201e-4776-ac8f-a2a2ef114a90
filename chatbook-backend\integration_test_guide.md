# Integration Test Guide - Complete Notification Pipeline

This guide will help you test the complete notification pipeline from Django to your email inbox.

## 🎯 **Test Flow Overview**

```
Django Action → Outbox Event → Background Worker → SNS → SQS → Lambda → Email
```

## 🖥️ **Terminal Setup (3 Terminals Required)**

### Terminal 1: Django Development Server
```bash
cd /path/to/chatbook-backend
source venv/bin/activate
AWS_PROFILE=sbx01 python manage.py runserver 0.0.0.0:8000
```

### Terminal 2: Outbox Background Worker
```bash
cd /path/to/chatbook-backend
source venv/bin/activate
AWS_PROFILE=sbx01 python manage.py process_outbox --daemon --interval=5 --batch-size=10
```

### Terminal 3: Test Actions & Monitoring
```bash
cd /path/to/chatbook-backend
source venv/bin/activate
# We'll run test scripts from here
```

## 📋 **Pre-Test Checklist**

### 1. AWS Infrastructure Verification
```bash
# Terminal 3: Verify AWS connectivity
aws sts get-caller-identity --profile sbx01

# Check SNS topic exists
aws sns get-topic-attributes \
  --topic-arn arn:aws:sns:us-west-2:524752462460:chatbook-user-notifications-sbx \
  --profile sbx01

# Check if your email is subscribed to SNS topic
aws sns list-subscriptions-by-topic \
  --topic-arn arn:aws:sns:us-west-2:524752462460:chatbook-user-notifications-sbx \
  --profile sbx01
```

### 2. Database State Check
```bash
# Terminal 3: Check current outbox events
AWS_PROFILE=sbx01 python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()
from notifications.models import OutboxEvent
print(f'Total outbox events: {OutboxEvent.objects.count()}')
print(f'Pending events: {OutboxEvent.objects.filter(status=\"pending\").count()}')
print(f'Published events: {OutboxEvent.objects.filter(status=\"published\").count()}')
print(f'Failed events: {OutboxEvent.objects.filter(status=\"failed\").count()}')
"
```

### 3. Device Token Setup
```bash
# Terminal 3: Ensure test device token exists
AWS_PROFILE=sbx01 python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()
from django.contrib.auth import get_user_model
from notifications.models import DeviceToken

User = get_user_model()
user, _ = User.objects.get_or_create(
    email='<EMAIL>',
    defaults={
        'phone_number': '+1234567890',
        'first_name': 'Test',
        'last_name': 'User',
        'is_active': True
    }
)

token, created = DeviceToken.objects.get_or_create(
    user=user,
    token='integration_test_device_token_2024',
    defaults={'is_active': True}
)

print(f'User: {user.email}')
print(f'Device token: {token.token}')
print(f'Token created: {created}')
"
```

## 🧪 **Test Scenarios**

### Test 1: Direct Notification
```bash
# Terminal 3: Send direct notification
AWS_PROFILE=sbx01 python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()
from django.contrib.auth import get_user_model
from notifications.outbox import OutboxService
from django.utils import timezone

User = get_user_model()
user = User.objects.get(email='<EMAIL>')

# Create direct notification event
event = OutboxService.create_direct_notification_event(
    user=user,
    message=f'🧪 Integration Test - Direct Notification at {timezone.now().strftime(\"%H:%M:%S\")}',
    notification_type='integration_test',
    additional_data={
        'test_type': 'direct_notification',
        'timestamp': timezone.now().isoformat()
    }
)

print(f'✅ Created direct notification event: {event.id}')
print(f'   Status: {event.status}')
print(f'   Sequence: {event.sequence_number}')
print(f'   Message: {event.payload[\"message\"]}')
print()
print('👀 Watch Terminal 2 for processing...')
print('📧 Check your email inbox for notification!')
"
```

### Test 2: Appointment Creation
```bash
# Terminal 3: Create appointment (triggers outbox event via signal)
AWS_PROFILE=sbx01 python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from business.models import Business, BusinessCustomer
from employees.models import Employee
from customers.models import CustomerProfile
from appointments.models import Appointment
from notifications.models import OutboxEvent

User = get_user_model()
user = User.objects.get(email='<EMAIL>')

# Get business and employee
business = Business.objects.first()
if not business:
    print('❌ No business found - cannot test appointment')
    exit()

employee = business.employees.filter(is_active=True).first()
if not employee:
    print('❌ No active employee found - cannot test appointment')
    exit()

# Get or create customer profile
customer_profile, _ = CustomerProfile.objects.get_or_create(
    user=user,
    defaults={'card_on_file': False}
)

# Get or create business customer
business_customer, _ = BusinessCustomer.objects.get_or_create(
    business=business,
    customer=customer_profile,
    defaults={'loyalty_points': 0}
)

# Count events before
events_before = OutboxEvent.objects.count()

# Create appointment
appointment = Appointment.objects.create(
    customer=business_customer,
    employee=employee,
    start_time=timezone.now() + timedelta(days=1),
    status='requested',
    source='integration_test',
    notes_from_customer='Integration test appointment'
)

# Count events after
events_after = OutboxEvent.objects.count()

print(f'✅ Created appointment: {appointment.id}')
print(f'   Customer: {user.email}')
print(f'   Employee: {employee.user.email if employee.user else \"No user\"}')
print(f'   Time: {appointment.start_time}')
print(f'   Events before: {events_before}')
print(f'   Events after: {events_after}')
print(f'   New events: {events_after - events_before}')
print()
print('👀 Watch Terminal 2 for processing...')
print('📧 Check your email inbox for appointment notification!')
"
```

### Test 3: Appointment Status Change
```bash
# Terminal 3: Update appointment status (triggers outbox event)
AWS_PROFILE=sbx01 python -c "
import os, django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()
from appointments.models import Appointment
from notifications.models import OutboxEvent

# Get the most recent appointment
appointment = Appointment.objects.order_by('-created_at').first()
if not appointment:
    print('❌ No appointment found - create one first')
    exit()

# Count events before
events_before = OutboxEvent.objects.count()

# Update appointment status
old_status = appointment.status
appointment.status = 'confirmed'
appointment.save()

# Count events after
events_after = OutboxEvent.objects.count()

print(f'✅ Updated appointment {appointment.id}')
print(f'   Status changed: {old_status} → {appointment.status}')
print(f'   Events before: {events_before}')
print(f'   Events after: {events_after}')
print(f'   New events: {events_after - events_before}')
print()
print('👀 Watch Terminal 2 for processing...')
print('📧 Check your email inbox for status change notification!')
"
```

## 📊 **Monitoring Commands**

### Real-time Event Monitoring
```bash
# Terminal 3: Watch outbox events in real-time
watch -n 2 'AWS_PROFILE=sbx01 python -c "
import os, django
os.environ.setdefault(\"DJANGO_SETTINGS_MODULE\", \"settings\")
django.setup()
from notifications.models import OutboxEvent
from django.utils import timezone

print(f\"=== Outbox Events Status - {timezone.now().strftime(\"%H:%M:%S\")} ===\")
print(f\"Pending:   {OutboxEvent.objects.filter(status=\"pending\").count()}\")
print(f\"Processing: {OutboxEvent.objects.filter(status=\"processing\").count()}\")
print(f\"Published: {OutboxEvent.objects.filter(status=\"published\").count()}\")
print(f\"Failed:    {OutboxEvent.objects.filter(status=\"failed\").count()}\")
print(f\"Dead Letter: {OutboxEvent.objects.filter(status=\"dead_letter\").count()}\")
print()

# Show recent events
recent_events = OutboxEvent.objects.order_by(\"-sequence_number\")[:5]
print(\"Recent Events:\")
for event in recent_events:
    print(f\"  {event.sequence_number}: {event.event_type} - {event.status} ({event.created_at.strftime(\"%H:%M:%S\")})\")
"'
```

### Check AWS SNS Metrics
```bash
# Terminal 3: Check SNS topic metrics
aws cloudwatch get-metric-statistics \
  --namespace AWS/SNS \
  --metric-name NumberOfMessagesPublished \
  --dimensions Name=TopicName,Value=chatbook-user-notifications-sbx \
  --start-time $(date -u -d '1 hour ago' +%Y-%m-%dT%H:%M:%S) \
  --end-time $(date -u +%Y-%m-%dT%H:%M:%S) \
  --period 300 \
  --statistics Sum \
  --profile sbx01
```

### Check SQS Queue
```bash
# Terminal 3: Check SQS queue attributes (replace with your queue name)
aws sqs get-queue-attributes \
  --queue-url https://sqs.us-west-2.amazonaws.com/524752462460/your-queue-name \
  --attribute-names All \
  --profile sbx01
```

## 🔍 **Troubleshooting**

### If Events Are Not Being Processed
1. Check Terminal 2 for worker errors
2. Verify AWS credentials: `aws sts get-caller-identity --profile sbx01`
3. Check outbox events: Look for failed events in Django admin

### If SNS Publishing Fails
1. Verify SNS topic permissions
2. Check AWS CloudWatch logs for errors
3. Test SNS publishing manually:
```bash
aws sns publish \
  --topic-arn arn:aws:sns:us-west-2:524752462460:chatbook-user-notifications-sbx \
  --message "Test message" \
  --profile sbx01
```

### If Emails Are Not Received
1. Check if your email is subscribed to SNS topic
2. Check spam folder
3. Verify Lambda function logs in CloudWatch
4. Test SQS → Lambda integration

## 📧 **Expected Email Content**

You should receive emails with content like:
```
Subject: Chatbook Notification

Body:
{
  "device_token": "integration_test_device_token_2024",
  "alert": "🧪 Integration Test - Direct Notification at 14:30:15",
  "event_id": "uuid-here",
  "event_type": "notification.direct",
  "timestamp": "2024-07-07T14:30:15+00:00",
  "recipient": {
    "user_id": "uuid-here",
    "email": "<EMAIL>",
    "first_name": "Test",
    "last_name": "User"
  },
  "message": "🧪 Integration Test - Direct Notification at 14:30:15",
  "notification_type": "integration_test"
}
```

## ✅ **Success Criteria**

1. **Terminal 2 shows**: Events being processed and published
2. **Database shows**: Events changing from 'pending' to 'published'
3. **AWS SNS shows**: Messages being published
4. **Email inbox shows**: Notification emails received
5. **No errors**: In any of the terminals

This integration test will verify the complete end-to-end notification pipeline!
