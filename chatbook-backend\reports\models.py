from django.db import models
from django.core.validators import MinValueValidator
from decimal import Decimal
from django.utils import timezone

class Transaction(models.Model):
    """Transaction model for tracking employee service with business customers"""

    PAYMENT_METHOD_CHOICES = [
        ('cash', 'Cash'),
        ('credit_card', 'Credit Card'),
        ('debit_card', 'Debit Card'),
        ('check', 'Check'),
        ('paypal', 'Paypal'),
        ('stripe', 'Stripe'),
        ('venmo', 'Venmo'),
        ('zelle', 'Zelle'),
        ('other', 'Other'),
    ]

    """
    Transaction ID
    Customer Name
    Checkout Date
    Checkout By
    Item Sold
    Sold by
    Service Name
    Payment Method
    Price
    Tax
    Tip
    Discount
    Amount Paid
    """

    id = models.CharField(max_length=50, primary_key=True, verbose_name='Transaction ID', help_text='Transaction ID from external system')
    customer = models.ForeignKey('business.BusinessCustomer', on_delete=models.PROTECT, related_name='transactions', help_text='Customer who received the service')
    checkout_date = models.DateTimeField(default=timezone.now, help_text='Date and time when the transaction was processed')

    checkout_by = models.ForeignKey(
        'employees.Employee',
        on_delete=models.PROTECT,
        related_name='transactions_processed',
        help_text='Employee who processed the checkout'
    )

    item_sold = models.CharField(max_length=255, help_text='Description of item/service sold')

    quantity = models.PositiveIntegerField(
        default=1,
        validators=[MinValueValidator(1)],
        help_text='Quantity of service/item'
    )

    service = models.ForeignKey(
        'services.Service',
        on_delete=models.PROTECT,
        related_name='transactions',
        help_text='Service that was provided'
    )

    sold_by = models.ForeignKey(
        'employees.Employee',
        on_delete=models.PROTECT,
        related_name='transactions_sold',
        help_text='Employee who performed/sold the service'
    )

    # === APPOINTMENT CONNECTION ===
    appointment = models.ForeignKey(
        'appointments.Appointment',
        on_delete=models.PROTECT,
        related_name='transactions',
        null=True,
        blank=True,
        help_text='Appointment this transaction is paying for (when transaction comes from completed appointment)'
    )

    appointment_service = models.ForeignKey(
        'appointments.AppointmentService',
        on_delete=models.PROTECT,
        related_name='transactions',
        null=True,
        blank=True,
        help_text='Specific service within appointment being paid for (allows multiple transactions per appointment)'
    )

    # ✅ FIX: Add appointment_add_on field to distinguish add-on transactions
    appointment_add_on = models.ForeignKey(
        'appointments.AppointmentAddOn',
        on_delete=models.PROTECT,
        related_name='transactions',
        null=True,
        blank=True,
        help_text='Specific add-on within appointment being paid for (allows proper sales categorization)'
    )

    payment_method = models.CharField(
        max_length=20,
        choices=PAYMENT_METHOD_CHOICES,
        help_text='Method of payment used for the transaction'
    )

    price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text='Base price of the service/item'
    )
    
    tax = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text='Tax amount applied'
    )
    
    tip = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text='Tip amount given'
    )
    
    discount = models.DecimalField(
        max_digits=8,
        decimal_places=2,
        default=Decimal('0.00'),
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text='Discount amount applied'
    )

    amount_paid = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        validators=[MinValueValidator(Decimal('0.00'))],
        help_text='Total amount paid by customer'
    )

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['-checkout_date', '-id']
        indexes = [
            models.Index(fields=['checkout_date']),
            models.Index(fields=['sold_by', 'checkout_date']),
            models.Index(fields=['customer', 'checkout_date']),
            models.Index(fields=['service']),
            # Appointment-related indexes
            models.Index(fields=['appointment', 'checkout_date']),
            models.Index(fields=['appointment_service']),
            # ✅ FIX: Add index for appointment_add_on for better sales categorization performance
            models.Index(fields=['appointment_add_on']),
        ]
    
    def __str__(self):
        return f"Transaction #{self.id} - {self.customer.customer.user.get_full_name() or self.customer.customer.user.email} - {self.service.name} - ${self.amount_paid}"

    @property
    def customer_name(self):
        """Get the customer's full name or email"""
        user = self.customer.customer.user
        return user.get_full_name() or user.email
    
    @property
    def service_name(self):
        """Get the service name"""
        return self.service.name
    
    @property
    def subtotal(self):
        """Calculate subtotal (price * quantity - discount)"""
        return (self.price * self.quantity) - self.discount
    
    @property
    def total_before_tip(self):
        """Calculate total before tip (subtotal + tax)"""
        return self.subtotal + self.tax
    
    @property
    def is_appointment_based(self):
        """Check if this transaction is from a completed appointment"""
        return self.appointment is not None
    
    @property
    def is_service_transaction(self):
        """Check if this transaction is for a service (not an add-on)"""
        return self.appointment_service is not None and self.appointment_add_on is None
    
    @property
    def is_addon_transaction(self):
        """Check if this transaction is for an add-on"""
        return self.appointment_add_on is not None
    
    @property
    def transaction_type(self):
        """Get the type of transaction for reporting purposes"""
        if self.is_addon_transaction:
            return 'addon'
        elif self.is_service_transaction:
            return 'service'
        else:
            return 'direct_sale'  # Direct sales not linked to appointments
    
    @property
    def appointment_date(self):
        """Get the appointment date if this transaction is appointment-based"""
        return self.appointment.start_time if self.appointment else None
    
    @property
    def transaction_source(self):
        """Identify the source of this transaction"""
        if self.appointment:
            return f"Appointment #{self.appointment.id}"
        return "Direct Sale"
    
    def clean(self):
        """Validate transaction data including appointment relationships"""
        from django.core.exceptions import ValidationError
        
        # Validate amount calculation
        expected_total = self.total_before_tip + self.tip
        if self.amount_paid != expected_total:
            raise ValidationError(
                f'Amount paid (${self.amount_paid}) does not match calculated total (${expected_total})'
            )
        
        # ✅ FIX: Validate that transaction is either service-based OR add-on-based, not both
        if self.appointment_service and self.appointment_add_on:
            raise ValidationError(
                'Transaction cannot be linked to both appointment_service and appointment_add_on. Choose one.'
            )
        
        # Validate appointment relationships
        if self.appointment_service and not self.appointment:
            raise ValidationError(
                'If appointment_service is specified, appointment must also be specified'
            )
            
        if self.appointment_add_on and not self.appointment:
            raise ValidationError(
                'If appointment_add_on is specified, appointment must also be specified'
            )
        
        if self.appointment_service and self.appointment:
            if self.appointment_service.appointment != self.appointment:
                raise ValidationError(
                    'appointment_service must belong to the specified appointment'
                )
                
        if self.appointment_add_on and self.appointment:
            if self.appointment_add_on.appointment != self.appointment:
                raise ValidationError(
                    'appointment_add_on must belong to the specified appointment'
                )
        
        # Validate appointment is completed before creating transaction
        if self.appointment and self.appointment.status not in ['completed']:
            raise ValidationError(
                f'Cannot create transaction for appointment with status "{self.appointment.status}". '
                'Appointment must be completed first.'
            )
    
    def save(self, *args, **kwargs):
        # Auto-populate from appointment_service if provided and fields are missing
        if self.appointment_service:
            app_service = self.appointment_service
            if not self.service_id:  # Use _id to check if FK is set
                self.service = app_service.service
            if not self.customer_id:
                self.customer = app_service.appointment.customer
            if not self.sold_by_id:
                self.sold_by = app_service.appointment.employee
            if not self.quantity:
                self.quantity = app_service.quantity
            if not self.price:
                # Use override price if available, otherwise base price
                self.price = app_service.price_override or app_service.base_price
            # Auto-set appointment if not already set
            if not self.appointment_id:
                self.appointment = app_service.appointment
        
        # ✅ FIX: Auto-populate from appointment_add_on if provided and fields are missing
        elif self.appointment_add_on:
            app_addon = self.appointment_add_on
            if not self.service_id:  # Add-ons might link to services or have their own service representation
                # For add-ons, we might need to use the add_on's service or create a service representation
                # This depends on your business logic - for now, we'll handle this in the application layer
                pass
            if not self.customer_id:
                self.customer = app_addon.appointment.customer
            if not self.sold_by_id:
                self.sold_by = app_addon.appointment.employee
            if not self.quantity:
                self.quantity = 1  # Add-ons typically have quantity 1
            if not self.price:
                self.price = app_addon.add_on_price
            # Auto-set appointment if not already set
            if not self.appointment_id:
                self.appointment = app_addon.appointment
        
        # Auto-populate from appointment if provided and fields are missing
        elif self.appointment:
            if not self.customer_id:
                self.customer = self.appointment.customer
            if not self.sold_by_id:
                self.sold_by = self.appointment.employee
        
        # Auto-populate item description
        if not self.item_sold:
            if self.appointment_add_on:
                # For add-ons, use the add-on name
                self.item_sold = f"{self.appointment_add_on.add_on.name} (Add-on)"
                if self.quantity and self.quantity > 1:
                    self.item_sold += f" (x{self.quantity})"
            elif self.service:
                # For services, use the service name
                self.item_sold = f"{self.service.name}"
                if self.quantity and self.quantity > 1:
                    self.item_sold += f" (x{self.quantity})"
        
        # Auto-calculate amount_paid if not provided
        if not self.amount_paid:
            self.amount_paid = self.total_before_tip + self.tip
            
        self.full_clean()
        super().save(*args, **kwargs)