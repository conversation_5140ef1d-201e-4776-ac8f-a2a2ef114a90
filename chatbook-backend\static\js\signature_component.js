/**
 * Signature Component
 * 
 * This script handles the signature pad functionality in forms
 * with proper error handling for S3 uploads
 */

// Initialize when document is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeSignaturePad();
});

// Global variables
let signaturePad = null;
let canvas = null;

// Initialize signature pad
function initializeSignaturePad() {
    canvas = document.getElementById('signature-pad');
    
    // If there's no canvas, return early
    if (!canvas) {
        console.log('No signature pad canvas found on this page');
        return;
    }
    
    // Check if SignaturePad library is available
    if (typeof SignaturePad === 'undefined') {
        console.error('SignaturePad library not loaded');
        showError('Signature component could not be initialized. Please refresh the page.');
        return;
    }
    
    // Initialize the signature pad
    try {
        signaturePad = new SignaturePad(canvas, {
            backgroundColor: 'rgb(255, 255, 255)',
            penColor: 'rgb(0, 0, 0)'
        });
        
        // Add event listeners to buttons
        addButtonListeners();
        
        // Add the following code after initializing the signature pad
        signaturePad.addEventListener('endStroke', function() {
            // Store signature data in hidden field when user completes a stroke
            const signatureData = signaturePad.toDataURL('image/png');
            const signatureDataField = document.getElementById('signature-data');
            if (signatureDataField) {
                signatureDataField.value = signatureData;
            }
        });
        
        console.log('Signature pad initialized successfully');
    } catch (error) {
        console.error('Error initializing signature pad:', error);
        showError('Could not initialize signature component: ' + error.message);
    }
}

// Add event listeners to signature pad buttons
function addButtonListeners() {
    // Clear button
    const clearButton = document.getElementById('clear-signature');
    if (clearButton) {
        clearButton.addEventListener('click', function() {
            clearSignature();
        });
    }
    
    // Save as PDF button
    const saveButton = document.getElementById('save-signature-pdf');
    if (saveButton) {
        saveButton.addEventListener('click', function() {
            if (signaturePad.isEmpty()) {
                showError('Please provide a signature before saving');
                return;
            }
            
            savePDF();
        });
    }
    
    // Edit signature button
    const editButton = document.getElementById('edit-signature');
    if (editButton) {
        editButton.addEventListener('click', function() {
            // Toggle signature pad visibility or editing mode
            // Implementation depends on your UI
        });
    }
}

// Save signature as PDF
function savePDF() {
    if (!signaturePad || signaturePad.isEmpty()) {
        showError('Please provide a signature before saving');
        return;
    }
    
    // Show loading state
    showLoading('Saving signature...');
    
    // Get signature data
    const signatureData = signaturePad.toDataURL('image/png');
    
    // Get customer and business IDs from data attributes or hidden fields
    const customerId = document.getElementById('customer-id')?.value;
    const businessId = document.getElementById('business-id')?.value;
    
    if (!customerId) {
        showError('Customer information is missing');
        return;
    }
    
    // Prepare API request
    fetch('/api/v1/signatures/generate_pdf/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCsrfToken()
        },
        body: JSON.stringify({
            signature_data: signatureData,
            customer: customerId,
            business: businessId
        })
    })
    .then(response => {
        // Check if response is ok
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        
        // Get S3 URL from response headers
        const s3Url = response.headers.get('X-S3-URL');
        
        // Return both the blob and the S3 URL
        return response.blob().then(blob => {
            return {
                blob: blob,
                s3Url: s3Url
            };
        });
    })
    .then(data => {
        // Create a URL for the blob
        const url = window.URL.createObjectURL(data.blob);
        
        // Update status message
        if (data.s3Url) {
            showSuccess('Signature saved successfully!');
            
            // Update hidden field with S3 URL if it exists
            const s3UrlField = document.getElementById('signature-s3-url');
            if (s3UrlField) {
                s3UrlField.value = data.s3Url;
            }
        } else {
            showWarning('PDF saved locally but upload to cloud storage failed');
        }
        
        // Update signature status
        updateSignatureStatus(true);
        
        // Optional: Download or open PDF
        // window.open(url, '_blank');
    })
    .catch(error => {
        console.error('Error saving signature:', error);
        
        // Check if it's the specific makeFileRequest error
        if (error.message && error.message.includes('makeFileRequest')) {
            showWarning('PDF saved locally but upload to cloud storage failed: Cloud storage configuration issue');
        } else {
            showError('Error saving signature: ' + error.message);
        }
    })
    .finally(() => {
        hideLoading();
    });
}

// Helper function to get CSRF token
function getCsrfToken() {
    const csrfCookie = document.cookie.split(';')
        .find(cookie => cookie.trim().startsWith('csrftoken='));
    
    if (csrfCookie) {
        return csrfCookie.split('=')[1];
    }
    
    // Fallback to meta tag
    return document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
}

// Update signature status in the UI
function updateSignatureStatus(isCompleted) {
    const statusElement = document.getElementById('signature-status');
    if (statusElement) {
        statusElement.textContent = isCompleted ? 'Completed' : 'Pending';
        statusElement.className = isCompleted ? 'status-completed' : 'status-pending';
    }
    
    // Update hidden field if it exists
    const statusField = document.getElementById('signature-completed');
    if (statusField) {
        statusField.value = isCompleted ? 'true' : 'false';
    }
}

// Show error message
function showError(message) {
    const messageContainer = document.getElementById('signature-message');
    if (messageContainer) {
        messageContainer.innerHTML = `
            <div class="error-message">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                    <path d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z"/>
                </svg>
                <span>${message}</span>
            </div>
        `;
    }
}

// Show warning message
function showWarning(message) {
    const messageContainer = document.getElementById('signature-message');
    if (messageContainer) {
        messageContainer.innerHTML = `
            <div class="warning-message">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                    <path d="M7.002 11a1 1 0 1 1 2 0 1 1 0 0 1-2 0zM7.1 4.995a.905.905 0 1 1 1.8 0l-.35 3.507a.552.552 0 0 1-1.1 0L7.1 4.995z"/>
                </svg>
                <span>${message}</span>
            </div>
        `;
    }
}

// Show success message
function showSuccess(message) {
    const messageContainer = document.getElementById('signature-message');
    if (messageContainer) {
        messageContainer.innerHTML = `
            <div class="success-message">
                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="currentColor" viewBox="0 0 16 16">
                    <path d="M8 15A7 7 0 1 1 8 1a7 7 0 0 1 0 14zm0 1A8 8 0 1 0 8 0a8 8 0 0 0 0 16z"/>
                    <path d="M4.646 8.146a.5.5 0 0 1 .708 0L8 10.793l2.646-2.647a.5.5 0 0 1 .708.708l-3 3a.5.5 0 0 1-.708 0l-3-3a.5.5 0 0 1 0-.708z"/>
                </svg>
                <span>${message}</span>
            </div>
        `;
    }
}

// Show loading message
function showLoading(message) {
    const messageContainer = document.getElementById('signature-message');
    if (messageContainer) {
        messageContainer.innerHTML = `
            <div class="loading-message">
                <div class="spinner"></div>
                <span>${message}</span>
            </div>
        `;
    }
}

// Hide all messages
function hideMessages() {
    const messageContainer = document.getElementById('signature-message');
    if (messageContainer) {
        messageContainer.innerHTML = '';
    }
}

// Hide loading message
function hideLoading() {
    // You can implement specific loading indicator hiding if needed
    // For now, we'll just use hideMessages
    hideMessages();
}

// Add this to the clearSignature function
function clearSignature() {
    if (signaturePad) {
        signaturePad.clear();
        
        // Clear hidden fields
        const signatureDataField = document.getElementById('signature-data');
        if (signatureDataField) {
            signatureDataField.value = '';
        }
        
        // Reset status
        updateSignatureStatus(false);
        
        hideMessages();
    }
} 