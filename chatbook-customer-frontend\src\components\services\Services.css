/* Services Section Styles */
.services-section {
    padding: 2rem 0;
    max-width: 1200px;
    margin: 0 auto;
}

.services-section h2 {
    text-align: center;
    margin-bottom: 2rem;
    font-size: 2rem;
    color: #333;
}

/* Service Category Styles */
.service-category {
    margin-bottom: 2rem;
    border-radius: 8px;
    background-color: #f8f9fa;
    overflow: hidden;
}

.service-category h3.category-name {
    padding: 1rem 1.5rem;
    margin: 0;
    background-color: #f0f2f5;
    color: #333;
    font-size: 1.5rem;
    position: relative;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.service-category h3.category-name::after {
    content: '▼';
    font-size: 0.8rem;
    transition: transform 0.3s;
}

.service-category.collapsed h3.category-name::after {
    transform: rotate(90deg);
}

.category-content {
    max-height: 2000px; /* Set a large max-height for non-collapsed state */
    opacity: 1;
    overflow: hidden;
    transition: max-height 0.5s ease, opacity 0.5s ease;
}

.service-category.collapsed .category-content {
    max-height: 0;
    opacity: 0;
}

/* Individual Service Items */
.service-item {
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    transition: background-color 0.3s;
}

.service-item:last-child {
    border-bottom: none;
}

.service-item:hover {
    background-color: #f0f2f5;
}

/* Service Item Header */
.service-item-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.service-name {
    font-size: 1.25rem;
    margin: 0;
    color: #333;
}

.service-price {
    font-size: 1.25rem;
    font-weight: bold;
    color: #333;
}

/* Service Item Content */
.service-description {
    margin-bottom: 1rem;
    color: #666;
    line-height: 1.6;
}

.service-meta {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
}

.duration {
    color: #666;
    font-size: 0.9rem;
}

.book-service-btn {
    padding: 0.6rem 1.2rem;
    background-color: #8b5c3d;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-weight: 600;
    transition: background-color 0.3s;
}

.book-service-btn:hover {
    background-color: #704a31;
}

/* Loading and Error States */
.services-loading, .services-error {
    text-align: center;
    padding: 2rem;
    color: #666;
}

.services-error {
    color: #d32f2f;
}

/* Service details layout */
.service-details {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* Service cards layout */
.service-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1rem;
}

.service-card {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    overflow: hidden;
    transition: transform 0.3s, box-shadow 0.3s;
}

.service-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
}

.service-card-image {
    width: 100%;
    height: 200px;
    overflow: hidden;
}

.service-card-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
}

.service-card:hover .service-card-image img {
    transform: scale(1.05);
}

.service-card-content {
    padding: 1.5rem;
}

.service-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.service-card-name {
    font-size: 1.25rem;
    margin: 0;
    color: #333;
}

.service-card-price {
    font-size: 1.25rem;
    font-weight: bold;
    color: #333;
}

.service-card-description {
    color: #666;
    line-height: 1.6;
    margin-bottom: 1.5rem;
}

.service-card-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.service-card-meta {
    color: #666;
    font-size: 0.9rem;
}

/* Add-on overlay styles */
.add-on-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    padding: 20px;
}

.add-on-content {
    background-color: #fff;
    border-radius: 8px;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    overflow-y: auto;
    padding: 30px;
    position: relative;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
}

.close-btn {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 24px;
    cursor: pointer;
    color: #666;
}

.add-on-header {
    display: flex;
    gap: 20px;
    margin-bottom: 30px;
    border-bottom: 1px solid #eee;
    padding-bottom: 20px;
}

.service-image {
    flex: 0 0 150px;
}

.service-image img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
}

.service-info {
    flex: 1;
}

.service-info h2 {
    margin-top: 0;
    margin-bottom: 10px;
}

.service-price {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 10px;
}

.add-on-services {
    position: relative;
    margin-top: 20px;
    margin-bottom: 30px;
}

.optional-label {
    position: absolute;
    top: -10px;
    right: 0;
    background-color: #f0f2f5;
    padding: 5px 10px;
    border-radius: 4px;
    font-size: 0.8rem;
}

.add-on-item {
    border: 1px solid #eee;
    border-radius: 8px;
    margin-bottom: 15px;
}

.add-on-label {
    display: flex;
    padding: 15px;
    gap: 15px;
    cursor: pointer;
}

.add-on-image {
    flex: 0 0 80px;
}

.add-on-image img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 4px;
}

.add-on-details {
    flex: 1;
}

.add-on-details h4 {
    margin-top: 0;
    margin-bottom: 5px;
}

.add-on-details p {
    font-size: 0.9rem;
    color: #666;
}

.add-on-price {
    flex: 0 0 100px;
    text-align: right;
    font-weight: bold;
}

.add-on-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.total-price {
    display: flex;
    flex-direction: column;
    font-size: 1.2rem;
}

.total-price span:last-child {
    font-weight: bold;
    font-size: 1.5rem;
}

.continue-btn {
    padding: 12px 24px;
    background-color: #4CAF50;
    color: white;
    border: none;
    border-radius: 4px;
    font-size: 1rem;
    font-weight: bold;
    cursor: pointer;
}

.continue-btn:hover {
    background-color: #45a049;
}

@media (max-width: 768px) {
    .service-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 5px;
    }
}
