"""
Authentication URLs for API v1
"""
from django.urls import path
from rest_framework_simplejwt.views import TokenRefreshView
from django.views.decorators.csrf import csrf_exempt
from .views import LoginView, VerifyMFAView

app_name = 'auth'

urlpatterns = [
    path('login/', csrf_exempt(LoginView.as_view()), name='login'),
    path('verify-mfa/', csrf_exempt(VerifyMFAView.as_view()), name='verify_mfa'),
    path('refresh/', csrf_exempt(TokenRefreshView.as_view()), name='refresh'),
]
