import { useState } from 'react'
import { Link, useNavigate, useLocation } from 'react-router-dom'
import But<PERSON> from '../../components/Button'
import OTPModal from '../../components/OTPModal'
import { useApi } from '../../hooks/useApi'

function Login() {
  const navigate = useNavigate()
  const location = useLocation()
  const from = location.state?.from?.pathname || '/'

  const [username, setUsername] = useState('')
  const [password, setPassword] = useState('')
  const [rememberMe, setRememberMe] = useState(false)
  const [error, setError] = useState(null)
  const [otpOpen, setOtpOpen] = useState(false)

  const loginApi = useApi('/auth/login/')

  const validateUsername = (value) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    const phoneRegex = /^\+?[0-9]{7,15}$/
    return emailRegex.test(value) || phoneRegex.test(value)
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError(null)

    if (!validateUsername(username)) {
      setError('Please enter a valid email address or phone number')
      return
    }

    if (!password) {
      setError('Password is required')
      return
    }

    const payload = {
      email: username, // Backend accepts email or phone via this field as per spec
      password,
      remember: rememberMe,
    }

    const res = await loginApi.createData(payload)
    if (res && res.access) {
      localStorage.setItem('auth_token', res.access)
      if (res.refresh) {
        localStorage.setItem('refresh_token', res.refresh)
      }
      
      // Store user data for employee matching in calendar
      if (res.user) {
        localStorage.setItem('user_data', JSON.stringify(res.user))
        console.log('✅ Stored user data for calendar employee matching:', res.user)
      }
      
      navigate(from, { replace: true })
    } else {
      setError(res?.detail || 'Invalid credentials')
    }
  }

  return (
    <div className="container flex justify-center items-center py-16">
      <div className="w-full max-w-md bg-white rounded-lg shadow-md p-8">
        <h2 className="text-2xl font-bold text-center text-gray-900 mb-6">Sign in to your account</h2>

        {/* Error message placeholder for logout */}
        {error && (
          <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded mb-4 text-sm">
            {error}
          </div>
        )}

        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label htmlFor="username" className="block text-sm font-medium text-gray-700">Phone number or email <span className="text-red-500">*</span></label>
            <input
              id="username"
              type="text"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="Phone number or email"
              value={username}
              onChange={(e) => setUsername(e.target.value.trim())}
              required
            />
          </div>

          <div>
            <label htmlFor="password" className="block text-sm font-medium text-gray-700">Password <span className="text-red-500">*</span></label>
            <input
              id="password"
              type="password"
              className="mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>

          <div className="flex items-center justify-between">
            <label className="inline-flex items-center text-sm text-gray-700">
              <input
                type="checkbox"
                className="mr-2 rounded border-gray-300"
                checked={rememberMe}
                onChange={(e) => setRememberMe(e.target.checked)}
              />
              Remember me
            </label>
            {/* Login with OTP link */}
            <button
              type="button"
              className="text-sm text-primary-600 hover:underline"
              onClick={() => setOtpOpen(true)}
            >
              Login with OTP
            </button>
          </div>

          <Button type="submit" className="w-full" disabled={loginApi.loading}>
            {loginApi.loading ? 'Signing in...' : 'Sign in'}
          </Button>
        </form>

        {/* Business registration link */}
        <div className="text-center text-sm text-gray-700 mt-6">
          Are you a business owner?{' '}
          <Link to="/business/register" className="text-primary-600 hover:underline">Register your Business</Link>
        </div>

        {/* Divider */}
        <div className="flex items-center my-6">
          <hr className="flex-1 border-gray-300" />
          <span className="px-2 text-sm text-gray-500">Or continue with</span>
          <hr className="flex-1 border-gray-300" />
        </div>

        {/* Google social login */}
        <a
          href="/accounts/google/login/"
          className="flex items-center justify-center gap-2 border border-gray-300 rounded-md px-4 py-2 hover:bg-gray-50 w-full text-sm font-medium text-gray-700"
        >
          <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 48 48" className="w-5 h-5">
            <path fill="#FFC107" d="M43.611 20.083H42V20H24v8h11.292C33.323 33.73 29.064 37 24 37c-7.18 0-13-5.82-13-13s5.82-13 13-13c3.162 0 6.05 1.123 8.299 2.966l5.657-5.657C34.304 5.092 29.393 3 24 3 12.402 3 3 12.402 3 24s9.402 21 21 21 21-9.402 21-21c0-1.407-.144-2.781-.389-4.117z"/>
            <path fill="#FF3D00" d="M6.306 14.691l6.571 4.817C14.37 16.597 18.91 13 24 13c3.162 0 6.05 1.123 8.299 2.966l5.657-5.657C34.304 5.092 29.393 3 24 3c-7.18 0-13 5.82-13 13 0 2.445.675 4.733 1.849 6.694z"/>
            <path fill="#4CAF50" d="M24 45c5.093 0 9.879-1.965 13.463-5.182l-6.207-5.238C29.822 35.43 27.019 37 24 37c-5.04 0-9.284-3.261-10.794-7.787l-6.581 5.08C11.046 40.257 17.074 45 24 45z"/>
            <path fill="#1976D2" d="M43.611 20.083H42V20H24v8h11.292A11.956 11.956 0 0 1 24 37c-5.04 0-9.284-3.261-10.794-7.787l-6.581 5.08C11.046 40.257 17.074 45 24 45c11.598 0 21-9.402 21-21 0-1.407-.144-2.781-.389-4.117z"/>
          </svg>
          Sign in with Google
        </a>

        {/* Footer links */}
        <p className="text-center text-sm text-gray-600 mt-6">
          Don't have an account?{' '}
          <Link to="/auth/signup" className="text-primary-600 hover:underline">Sign up</Link>
        </p>
      </div>

      {/* OTP Modal */}
      <OTPModal isOpen={otpOpen} onClose={() => setOtpOpen(false)} />
    </div>
  )
}

export default Login 