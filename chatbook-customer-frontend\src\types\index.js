/**
 * Type definitions for the application
 * Using JSDoc comments for type safety in JavaScript
 */

/**
 * @typedef {Object} User
 * @property {number} id - User ID
 * @property {string} email - User email
 * @property {string} first_name - User first name
 * @property {string} last_name - User last name
 * @property {string} [phone] - User phone number
 * @property {boolean} is_active - Whether user is active
 * @property {string} date_joined - Date user joined
 */

/**
 * @typedef {Object} ConsentStatus
 * @property {boolean} all_forms_completed - Whether all forms are completed
 * @property {string} checked_at - When status was last checked
 * @property {number} [completed_forms_count] - Number of completed forms
 * @property {number} [required_forms_count] - Number of required forms
 * @property {Array<{form_name: string, form_id: number}>} [missing_forms] - Missing forms
 */

/**
 * @typedef {Object} AuthState
 * @property {User|null} user - Current user
 * @property {boolean} isAuthenticated - Whether user is authenticated
 * @property {boolean} loading - Whether auth is loading
 * @property {string|null} error - Auth error message
 * @property {ConsentStatus|null} consentStatus - User consent status
 */

/**
 * @typedef {Object} Service
 * @property {number} id - Service ID
 * @property {string} name - Service name
 * @property {string} description - Service description
 * @property {number} duration - Service duration in minutes
 * @property {string} price - Service price
 * @property {string} [category] - Service category
 */

/**
 * @typedef {Object} Employee
 * @property {number} id - Employee ID
 * @property {string} full_name - Employee full name
 * @property {string} title - Employee title
 * @property {string} [profile_image] - Employee profile image URL
 * @property {string} [bio] - Employee bio
 * @property {string[]} [specialties] - Employee specialties
 */

/**
 * @typedef {Object} AddOn
 * @property {number} id - Add-on ID
 * @property {string} name - Add-on name
 * @property {string} description - Add-on description
 * @property {string} price - Add-on price
 * @property {number} [duration] - Add-on duration in minutes
 */

/**
 * @typedef {Object} CustomerInfo
 * @property {string} name - Customer name
 * @property {string} email - Customer email
 * @property {string} phone - Customer phone
 */

/**
 * @typedef {Object} ConsentData
 * @property {boolean} consentAgreed - Whether consent was agreed to
 * @property {string} signature - Customer signature
 * @property {string} [agreedAt] - When consent was agreed to
 */

/**
 * @typedef {Object} PaymentInfo
 * @property {string} cardNumber - Credit card number
 * @property {string} expiryDate - Card expiry date
 * @property {string} cvv - Card CVV
 * @property {string} cardholderName - Cardholder name
 * @property {Object} [billingAddress] - Billing address
 * @property {string} billingAddress.street - Street address
 * @property {string} billingAddress.city - City
 * @property {string} billingAddress.state - State
 * @property {string} billingAddress.zipCode - ZIP code
 */

/**
 * @typedef {'service'|'employee'|'datetime'|'consent'|'review'|'payment'|'confirmation'} BookingStep
 */

/**
 * @typedef {Object} BookingData
 * @property {Service|null} selectedService - Selected service
 * @property {Employee|null} selectedEmployee - Selected employee
 * @property {AddOn[]} selectedAddOns - Selected add-ons
 * @property {string|null} selectedDate - Selected date
 * @property {string|null} selectedTime - Selected time
 * @property {CustomerInfo} customerInfo - Customer information
 * @property {ConsentData} consentData - Consent data
 * @property {PaymentInfo} paymentInfo - Payment information
 * @property {BookingStep} step - Current booking step
 * @property {number} subtotal - Booking subtotal
 * @property {number} tax - Tax amount
 * @property {number} total - Total amount
 */

/**
 * @typedef {Object} BookingState
 * @property {BookingData} bookingData - Booking data
 * @property {boolean} loading - Whether booking is loading
 * @property {string|null} error - Booking error message
 * @property {'not_signed'|'signed'|'checking'} consentStatus - Consent status
 */

/**
 * @typedef {Object} LoginCredentials
 * @property {string} email - User email
 * @property {string} password - User password
 */

/**
 * @typedef {Object} RegisterData
 * @property {string} email - User email
 * @property {string} password - User password
 * @property {string} first_name - User first name
 * @property {string} last_name - User last name
 * @property {string} [phone] - User phone number
 */

/**
 * @typedef {Object} AuthResponse
 * @property {string} access - Access token
 * @property {User} user - User data
 */

// Export empty object to make this a module
export {};
