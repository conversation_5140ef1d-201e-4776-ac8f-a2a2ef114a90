from django.core.management.base import BaseCommand
from bookings.models import Service
from employees.models import Employee, EmployeeService

class Command(BaseCommand):
    help = 'Creates employee services for existing employees'

    def handle(self, *args, **options):
        # Get the existing employee
        employee = Employee.objects.first()
        if not employee:
            self.stdout.write(self.style.ERROR('No employees found'))
            return

        # Get all services
        services = Service.objects.all()
        if not services:
            self.stdout.write(self.style.ERROR('No services found'))
            return

        # Create employee services
        created_count = 0
        for service in services:
            # Check if employee service already exists
            if not EmployeeService.objects.filter(employee=employee, service=service).exists():
                EmployeeService.objects.create(
                    employee=employee,
                    service=service,
                    custom_price=service.price,  # Use default service price
                    custom_duration=service.duration,  # Use default service duration
                    is_active=True
                )
                created_count += 1
                self.stdout.write(f'Created service {service.name} for {employee.full_name}')

        self.stdout.write(self.style.SUCCESS(f'Successfully created {created_count} employee services')) 