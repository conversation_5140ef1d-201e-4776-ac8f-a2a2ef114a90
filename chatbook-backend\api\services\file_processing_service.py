"""
File processing service for handling customer import from uploaded files.
Integrates file reading, field mapping, and customer import services.
"""
import uuid
from typing import Dict, List, Any, Optional
from django.core.cache import cache
from django.db import transaction

from .file_reader_service import FileReaderService
from .import_service import CustomerImportMappingService
from customers.models import UploadedFile
from business.models import Business
from employees.models import Employee


class FileProcessingService:
    """Service to process uploaded files and import customer data"""
    
    def __init__(self):
        self.file_reader = FileReaderService()
        self.mapping_service = CustomerImportMappingService()
        # Import here to avoid circular imports
        from api.views.customer_import import CustomerImportService
        self.import_service = CustomerImportService()
    
    def process_file(self, file_id: str, process_options: Dict[str, Any], user) -> Dict[str, Any]:
        """
        Process an uploaded file and import customers
        
        Args:
            file_id: ID of the uploaded file
            process_options: Processing options (skipDuplicates, updateExisting, validateEmails)
            user: Authenticated user
            
        Returns:
            Dictionary with job information and processing results
        """
        try:
            # Get uploaded file record
            uploaded_file = UploadedFile.objects.get(file_id=file_id)
            
            # Get business from user
            if hasattr(user, 'employee_profile'):
                business = user.employee_profile.business
            else:
                # For testing - use first business
                business = Business.objects.first()
                if not business:
                    raise ValueError("No business found")
            
            # Generate job ID
            job_id = f"job_{uuid.uuid4().hex[:10]}"
            
            # Read file and extract data first to get total rows
            file_path = uploaded_file.file.name
            headers, data_rows = self.file_reader.read_file(file_path)
            
            # Update file status to processing with timing and row count
            from django.utils import timezone
            uploaded_file.status = 'processing'
            uploaded_file.total_rows = len(data_rows)
            uploaded_file.processed_rows = 0
            uploaded_file.started_at = timezone.now()
            uploaded_file.save()
            
            # Auto-map fields based on headers
            field_mapping = self.mapping_service.auto_map_fields(headers)
            
            # Validate mapping
            validation = self.mapping_service.validate_mapping(field_mapping, headers)
            
            if not validation['is_valid']:
                # Update file status to failed
                uploaded_file.status = 'failed'
                uploaded_file.completed_at = timezone.now()
                uploaded_file.save()
                
                return {
                    'job_id': job_id,
                    'status': 'failed',
                    'message': f"Invalid field mapping: {', '.join(validation['missing_required'])}",
                    'validation_errors': validation
                }
            
            # Process customers using existing import service
            import_results = self.import_service.process_customers(
                customers=data_rows,
                field_mappings=field_mapping,
                business=business,
                job_id=job_id
            )
            
            # Update file status based on results
            uploaded_file.processed_rows = import_results['imported_count'] + import_results['error_count']
            uploaded_file.completed_at = timezone.now()
            
            # File processing is successful if:
            # 1. No errors occurred, OR
            # 2. Some customers were imported (even with some errors), OR  
            # 3. All customers were skipped (duplicates) - this is still successful processing
            total_processed = import_results['imported_count'] + import_results['error_count'] + len(import_results.get('warnings', []))
            
            if import_results['error_count'] == 0 or import_results['imported_count'] > 0 or total_processed > 0:
                uploaded_file.status = 'completed'
            else:
                uploaded_file.status = 'failed'
            
            uploaded_file.save()
            
            # Store results in cache for later retrieval
            cache.set(f'import_job_{job_id}', import_results, timeout=3600)
            
            # Also store a mapping from file_id to job_id for easier lookup
            cache.set(f'file_job_mapping_{file_id}', job_id, timeout=3600)
            
            return {
                'job_id': job_id,
                'status': 'completed' if import_results['status'] == 'completed' else 'failed',
                'message': 'File processing completed',
                'file_info': {
                    'total_rows': len(data_rows),
                    'headers': headers,
                    'auto_mapping': field_mapping
                },
                'validation': validation
            }
            
        except UploadedFile.DoesNotExist:
            return {
                'job_id': None,
                'status': 'failed',
                'message': f'File with ID {file_id} not found'
            }
        except Exception as e:
            # Update file status to failed if possible
            try:
                uploaded_file = UploadedFile.objects.get(file_id=file_id)
                uploaded_file.status = 'failed'
                uploaded_file.save()
            except:
                pass
            
            return {
                'job_id': None,
                'status': 'failed',
                'message': f'Error processing file: {str(e)}'
            }
    
    def get_file_preview(self, file_id: str) -> Dict[str, Any]:
        """
        Get file preview with headers and sample data for mapping
        
        Args:
            file_id: ID of the uploaded file
            
        Returns:
            Dictionary with file preview information
        """
        try:
            uploaded_file = UploadedFile.objects.get(file_id=file_id)
            file_path = uploaded_file.file.name
            
            # Get file information
            file_info = self.file_reader.get_file_info(file_path)
            
            if not file_info['is_valid']:
                return {
                    'success': False,
                    'error': file_info['error']
                }
            
            # Get auto mapping suggestions
            auto_mapping = self.mapping_service.auto_map_fields(file_info['headers'])
            validation = self.mapping_service.validate_mapping(auto_mapping, file_info['headers'])
            suggestions = self.mapping_service.get_field_suggestions(file_info['headers'])
            
            return {
                'success': True,
                'file_info': file_info,
                'auto_mapping': auto_mapping,
                'validation': validation,
                'suggestions': suggestions,
                'required_fields': self.mapping_service.get_required_fields(),
                'recommended_fields': self.mapping_service.get_recommended_fields()
            }
            
        except UploadedFile.DoesNotExist:
            return {
                'success': False,
                'error': f'File with ID {file_id} not found'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'Error reading file: {str(e)}'
            }
    
    def get_processing_status(self, job_id: str) -> Dict[str, Any]:
        """
        Get processing status for a job
        
        Args:
            job_id: Job ID
            
        Returns:
            Dictionary with status information
        """
        # Check cache for results
        results = cache.get(f'import_job_{job_id}')
        
        if results:
            return {
                'job_id': job_id,
                'status': results['status'],
                'progress': 100 if results['status'] == 'completed' else 0,
                'total_rows': results['total_count'],
                'processed_rows': results['imported_count'] + results['error_count'],
                'successful_rows': results['imported_count'],
                'failed_rows': results['error_count']
            }
        else:
            return {
                'job_id': job_id,
                'status': 'not_found',
                'message': 'Job not found or expired'
            }
    
    def get_processing_results(self, job_id: str) -> Dict[str, Any]:
        """
        Get full processing results for a job
        
        Args:
            job_id: Job ID
            
        Returns:
            Dictionary with complete results
        """
        results = cache.get(f'import_job_{job_id}')
        
        if results:
            return results
        else:
            return {
                'job_id': job_id,
                'status': 'not_found',
                'message': 'Results not found or expired'
            }
    
    def get_file_results(self, file_id: str) -> Dict[str, Any]:
        """
        Get detailed processing results for a file
        
        Args:
            file_id: ID of the uploaded file
            
        Returns:
            Dictionary with detailed results or error information
        """
        try:
            uploaded_file = UploadedFile.objects.get(file_id=file_id)
            
            # Build basic summary from file data
            summary = {
                'total_rows': uploaded_file.total_rows or 0,
                'successful': 0,
                'failed': 0,
                'duplicates_skipped': 0
            }
            
            imported = []
            errors = []
            
            # Try to get cached job results for detailed information
            cached_results = None
            
            from django.core.cache import cache
            
            # Look up the job_id for this file
            job_id = cache.get(f'file_job_mapping_{file_id}')
            if job_id:
                cached_results = cache.get(f'import_job_{job_id}')
            
            if cached_results:
                # Use detailed cached results
                summary.update({
                    'successful': cached_results.get('imported_count', 0),
                    'failed': cached_results.get('error_count', 0),
                    'duplicates_skipped': len(cached_results.get('warnings', []))
                })
                
                # Format successful imports
                for success in cached_results.get('successful', []):
                    imported.append({
                        'id': success.get('id'),
                        'firstName': success.get('name', '').split(' ')[0] if success.get('name') else '',
                        'lastName': ' '.join(success.get('name', '').split(' ')[1:]) if success.get('name') and ' ' in success.get('name', '') else '',
                        'email': success.get('email', '')
                    })
                
                # Format errors
                for error in cached_results.get('errors', []):
                    errors.append({
                        'row': error.get('row', 0),
                        'data': error.get('data', {}),
                        'errors': [error.get('message', 'Unknown error')]
                    })
                
                # Add warnings as "duplicates skipped" info
                for warning in cached_results.get('warnings', []):
                    if 'already exists' in warning.get('message', ''):
                        # These are duplicates, already counted in summary
                        pass
            else:
                # No cached results, use basic file information
                if uploaded_file.status == 'completed':
                    summary['successful'] = uploaded_file.processed_rows
                elif uploaded_file.status == 'failed':
                    summary['failed'] = uploaded_file.processed_rows
                    errors.append({
                        'row': 1,
                        'data': {},
                        'errors': ['Processing failed - detailed error information not available']
                    })
            
            return {
                'success': True,
                'data': {
                    'file_id': file_id,
                    'summary': summary,
                    'imported': imported,
                    'errors': errors
                }
            }
            
        except UploadedFile.DoesNotExist:
            return {
                'success': False,
                'error': f'File with ID {file_id} not found'
            }
        except Exception as e:
            return {
                'success': False,
                'error': f'Error retrieving results: {str(e)}'
            } 