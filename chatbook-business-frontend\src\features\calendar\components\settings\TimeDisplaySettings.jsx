import React from 'react'
import { TIME_RESOLUTIONS, DISPLAY_HOURS_PRESETS } from '../../hooks/useCalendarConfig'

/**
 * TimeDisplaySettings - Time resolution and display hours configuration
 */
const TimeDisplaySettings = ({ config, updateConfig }) => {
  return (
    <div className="space-y-6">
      {/* Time Resolution */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Time Resolution</label>
        <div className="grid grid-cols-3 gap-2">
          {TIME_RESOLUTIONS.map(resolution => (
            <button
              key={resolution.value}
              onClick={() => updateConfig({ 
                timeResolution: resolution.value,
                gridHeight: resolution.gridHeight 
              })}
              className={`p-3 rounded-lg text-center ${
                config.timeResolution === resolution.value
                  ? 'bg-blue-100 text-blue-700 border border-blue-200'
                  : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-150'
              }`}
            >
              <div className="font-medium">{resolution.value}min</div>
              <div className="text-xs">{resolution.label}</div>
            </button>
          ))}
        </div>
      </div>

      {/* Display Hours */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Display Hours</label>
        <div className="grid grid-cols-2 gap-2 mb-3">
          {DISPLAY_HOURS_PRESETS.map(preset => (
            <button
              key={preset.name}
              onClick={() => updateConfig({ 
                displayHourStart: preset.start,
                displayHourEnd: preset.end 
              })}
              className={`p-3 rounded-lg text-center ${
                config.displayHourStart === preset.start && config.displayHourEnd === preset.end
                  ? 'bg-blue-100 text-blue-700 border border-blue-200'
                  : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-150'
              }`}
            >
              <div className="font-medium">{preset.name}</div>
              <div className="text-xs">{preset.label}</div>
            </button>
          ))}
        </div>
        
        {/* Custom hours */}
        <div className="flex items-center gap-3">
          <div className="flex-1">
            <label className="block text-xs text-gray-500 mb-1">Start Hour</label>
            <input
              type="number"
              min="0"
              max="23"
              value={config.displayHourStart}
              onChange={(e) => updateConfig({ displayHourStart: parseInt(e.target.value) })}
              className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
            />
          </div>
          <div className="flex-1">
            <label className="block text-xs text-gray-500 mb-1">End Hour</label>
            <input
              type="number"
              min="1"
              max="24"
              value={config.displayHourEnd}
              onChange={(e) => updateConfig({ displayHourEnd: parseInt(e.target.value) })}
              className="w-full border border-gray-300 rounded px-3 py-2 text-sm"
            />
          </div>
        </div>
      </div>
    </div>
  )
}

export default TimeDisplaySettings 