from django.db import models, transaction
from django.contrib.auth.models import AbstractUser, UserManager
from django.utils.translation import gettext_lazy as _
import uuid
from phonenumber_field.modelfields import PhoneNumberField
from django.utils.functional import cached_property
from django.core.exceptions import ValidationError
from functools import lru_cache

# Extract choice lists into module-level constants
MFA_METHOD_CHOICES = [
    ('email', 'Email'),
    ('sms', 'SMS')
]

# Define callable default for JSONField to avoid mutable default issue
def get_default_social_accounts():
    return {}

class CustomUserManager(UserManager):
    """
    Custom user manager that handles users without a username field,
    using email or phone number for authentication instead.
    """
    def _create_user(self, identifier, password, signup_type='customer', **extra_fields):
        """
        Create and save a user with the given identifier (email or phone) and password.
        """
        if not identifier:
            raise ValueError('The identifier field must be set')
            
        # Check if identifier is an email
        if '@' in identifier:
            email = self.normalize_email(identifier)
            extra_fields['email'] = email
        else:
            # Identifier is a phone number - normalize it
            if 'phone_number' not in extra_fields:
                try:
                    phone_number = PhoneNumberField().to_python(identifier)
                    if not phone_number:
                        raise ValueError('Invalid phone number format')
                    extra_fields['phone_number'] = phone_number
                except ValidationError:
                    raise ValueError(f'Invalid phone number format: {identifier}')
            
            # Always require email regardless of identification method
            if 'email' not in extra_fields:
                raise ValueError('Email is required')
            
            email = extra_fields['email']
            extra_fields['email'] = self.normalize_email(email)
        
        # Process within a transaction to ensure atomicity
        with transaction.atomic():        
            # Don't set username since it's None in our model
            user = self.model(**extra_fields)
            user.set_password(password)
            # Set signup type for signal processing
            user.signup_type = signup_type
            user.save(using=self._db)
            return user

    def create_user(self, identifier, password=None, signup_type='customer', **extra_fields):
        extra_fields.setdefault('is_staff', False)
        extra_fields.setdefault('is_superuser', False)
        return self._create_user(identifier, password, signup_type=signup_type, **extra_fields)

    def create_business_user(self, identifier, password=None, **extra_fields):
        """
        Create a business user with admin role.
        """
        return self.create_user(identifier, password, signup_type='business', **extra_fields)

    def create_customer_user(self, identifier, password=None, **extra_fields):
        """
        Create a regular customer user.
        """
        return self.create_user(identifier, password, signup_type='customer', **extra_fields)

    def create_superuser(self, identifier, password=None, **extra_fields):
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)
        
        if extra_fields.get('is_staff') is not True:
            raise ValueError('Superuser must have is_staff=True.')
        if extra_fields.get('is_superuser') is not True:
            raise ValueError('Superuser must have is_superuser=True.')
            
        return self._create_user(identifier, password, **extra_fields)

    def with_roles(self):
        """Return a queryset with roles prefetched for efficiency"""
        return self.get_queryset().prefetch_related('roles')


class User(AbstractUser):
    """
    Custom user model that uses email for authentication and has no username field.
    Both email and phone_number fields are required and must be unique.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    username = None
    email = models.EmailField(_('email address'), unique=True)
    phone_number = PhoneNumberField(unique=True, db_index=True)
    first_name = models.CharField(max_length=30, blank=True)
    last_name = models.CharField(max_length=30, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    
    # User roles with explicit db_table to avoid migration issues
    roles = models.ManyToManyField(
        'accounts.Role', 
        related_name='users',
        db_table='accounts_user_roles'
    )
    
    # Signup type - not stored in DB, used for signal processing
    _signup_type = None
    
    @property
    def signup_type(self):
        return self._signup_type
        
    @signup_type.setter
    def signup_type(self, value):
        self._signup_type = value
    
    is_active = models.BooleanField(default=True)
    is_verified = models.BooleanField(default=False)
    
    # MFA Settings
    mfa_enabled = models.BooleanField(default=False)
    preferred_mfa_method = models.CharField(
        max_length=10,
        choices=MFA_METHOD_CHOICES,
        default='email'
    )
    
    # Social Auth - Use callable default function to avoid mutable default issues
    social_accounts = models.JSONField(default=get_default_social_accounts, blank=True)
    last_login_ip = models.GenericIPAddressField(null=True, blank=True)
    last_login_device = models.CharField(max_length=255, blank=True)
    
    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = ['phone_number', 'first_name', 'last_name']

    objects = CustomUserManager()

    class Meta:
        verbose_name = _('user')
        verbose_name_plural = _('users')
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['created_at'], name='user_created_idx'),
            models.Index(fields=['last_name', 'first_name'], name='user_name_idx'),
            models.Index(fields=['is_active', 'is_staff'], name='user_status_idx'),
        ]
        
    def __str__(self):
        return f"{self.first_name} {self.last_name} ({self.email})"

    def save(self, *args, **kwargs):
        """Ensure phone number is always validated before saving"""
        if self.phone_number:
            # PhoneNumberField already validates on assignment, no need for extra validation
            if not self.phone_number:
                raise ValidationError({'phone_number': 'Invalid phone number format'})
                
        super().save(*args, **kwargs)
        
        # Clear cached properties on save to avoid stale data
        if hasattr(self, '_user_type'):
            del self._user_type

    @property
    def user_type(self):
        """
        Returns the user's primary type based on their roles.
        Priority order: admin > employee > customer
        Returns None if the user has no roles.
        """
        role_names = list(self.get_role_names())
        if 'admin' in role_names:
            return 'admin'
        elif 'employee' in role_names:
            return 'employee'
        elif 'customer' in role_names:
            return 'customer'
        return None
    
    @lru_cache(maxsize=1)
    def get_role_names(self):
        """
        Caches and returns role names.
        Using lru_cache to ensure the query is only run once per instance lifecycle.
        """
        return list(self.roles.values_list('name', flat=True))
        
    @property
    def is_business_admin(self):
        return 'admin' in self.get_role_names()
        
    @property
    def is_employee(self):
        return 'employee' in self.get_role_names()
        
    @property
    def is_customer(self):
        role_names = self.get_role_names()
        return 'customer' in role_names or len(role_names) == 0

    def has_role(self, role_name):
        """Check if user has a specific role."""
        return role_name in self.get_role_names() 