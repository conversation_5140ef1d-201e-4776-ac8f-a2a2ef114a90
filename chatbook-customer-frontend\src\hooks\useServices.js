import { useQuery } from '@tanstack/react-query';
import { getServices } from '../api/bookingApi';
import { queryKeys } from '../lib/queryClient';

export const useServices = () => {
  return useQuery({
    queryKey: queryKeys.booking.services,
    queryFn: getServices,
    staleTime: 10 * 60 * 1000, // 10 minutes
    gcTime: 30 * 60 * 1000, // 30 minutes
    retry: 2,
    refetchOnWindowFocus: false,
  });
};

export const useServicesWithFallback = () => {
  const query = useServices();
  
  // Provide fallback data if API fails
  const fallbackServices = [
    {
      id: 1,
      name: 'Classic Lash Extensions',
      description: 'Individual lash extensions for a natural look',
      duration: 120,
      price: '150.00',
      category: 'Lash Extensions'
    },
    {
      id: 2,
      name: 'Volume Lash Extensions',
      description: 'Multiple lightweight lashes for dramatic volume',
      duration: 150,
      price: '180.00',
      category: 'Lash Extensions'
    },
    {
      id: 3,
      name: '<PERSON><PERSON> Fill',
      description: 'Maintenance for existing lash extensions',
      duration: 90,
      price: '80.00',
      category: 'Maintenance'
    }
  ];

  return {
    ...query,
    data: query.data || (query.isError ? fallbackServices : undefined),
    isLoading: query.isLoading && !query.isError,
  };
};
