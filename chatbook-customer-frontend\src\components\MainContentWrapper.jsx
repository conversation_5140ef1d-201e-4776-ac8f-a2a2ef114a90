import { useState } from 'react';
import { useBookingStore } from '../stores/bookingStore';
import { useServicesWithFallback } from '../hooks/useServices';
import ServicesList from './services/ServicesList.jsx';
import BookingPage from './booking/BookingPage.jsx';

const MainContentWrapper = ({
    activeTab,
    setActiveTab
}) => {
    const {
        bookingData,
        setSelectedService: setBookingService,
        setSelectedAddOns: setBookingAddOns
    } = useBookingStore();

    // Use React Query for services data
    const { data: services = [], isLoading: loading, error } = useServicesWithFallback();

    const handleServiceSelect = (service, addOns = []) => {
        console.log('Service selected with add-ons:', service, addOns);

        // Update booking store (for persistence)
        setBookingService(service);
        setBookingAddOns(addOns);

        // Automatically switch to booking tab when a service is selected
        setActiveTab('booking');
    };

    if (loading) {
        return <div className="loading-screen">Loading services...</div>;
    }

    if (error && !services.length) {
        return <div className="error-screen">Failed to load services. Please try again.</div>;
    }

    return (
        <>
            {activeTab === 'services' ? (
                <ServicesList
                    services={services}
                    onServiceSelect={handleServiceSelect}
                />
            ) : (
                <BookingPage
                    selectedService={bookingData.selectedService}
                    addOns={bookingData.selectedAddOns}
                />
            )}
        </>
    );
};

export default MainContentWrapper;
