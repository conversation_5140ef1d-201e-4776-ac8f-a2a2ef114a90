/**
 * Debug utilities for booking flow
 * Provides consistent logging and debugging helpers
 */

/**
 * Log booking flow navigation decisions
 * @param {string} step - Current step in the flow
 * @param {Object} data - Relevant data for the step
 * @param {string} decision - Navigation decision made
 */
export const logBookingFlow = (step, data, decision) => {
  const timestamp = new Date().toLocaleTimeString();
  console.group(`🔄 [${timestamp}] Booking Flow: ${step}`);
  console.log('📊 Data:', data);
  console.log('🎯 Decision:', decision);
  console.groupEnd();
};

/**
 * Log authentication state changes
 * @param {Object} user - User object
 * @param {boolean} isAuthenticated - Authentication status
 * @param {string} action - Action being performed
 */
export const logAuthState = (user, isAuthenticated, action) => {
  const timestamp = new Date().toLocaleTimeString();
  console.group(`🔐 [${timestamp}] Auth: ${action}`);
  console.log('👤 User:', user?.email || 'No user');
  console.log('✅ Authenticated:', isAuthenticated);
  console.groupEnd();
};

/**
 * Log consent status checks
 * @param {string} userId - User ID
 * @param {Object} status - Consent status object
 * @param {string} source - Source of the status (cache/api)
 */
export const logConsentStatus = (userId, status, source) => {
  const timestamp = new Date().toLocaleTimeString();
  console.group(`📝 [${timestamp}] Consent Status: ${source}`);
  console.log('👤 User ID:', userId);
  console.log('✅ All Forms Completed:', status?.all_forms_completed);
  console.log('📅 Checked At:', status?.checked_at);
  console.groupEnd();
};

/**
 * Log booking data validation
 * @param {string} step - Step being validated
 * @param {Object} bookingData - Current booking data
 * @param {boolean} isValid - Validation result
 */
export const logBookingValidation = (step, bookingData, isValid) => {
  const timestamp = new Date().toLocaleTimeString();
  console.group(`✅ [${timestamp}] Validation: ${step}`);
  console.log('📋 Booking Data:', {
    service: bookingData.selectedService?.name || 'None',
    employee: bookingData.selectedEmployee?.display_name || 'None',
    date: bookingData.selectedDate || 'None',
    time: bookingData.selectedTime || 'None',
    consent: bookingData.consentData?.consentAgreed || false
  });
  console.log('✅ Valid:', isValid);
  console.groupEnd();
};

/**
 * Log API errors with context
 * @param {string} operation - Operation that failed
 * @param {Error} error - Error object
 * @param {Object} context - Additional context
 */
export const logApiError = (operation, error, context = {}) => {
  const timestamp = new Date().toLocaleTimeString();
  console.group(`❌ [${timestamp}] API Error: ${operation}`);
  console.error('Error:', error.message);
  console.log('Status:', error.response?.status);
  console.log('Context:', context);
  console.groupEnd();
};

/**
 * Debug helper to check current booking state
 * @param {Object} bookingData - Current booking data from store
 * @param {Object} user - Current user
 */
export const debugBookingState = (bookingData, user) => {
  console.group('🔍 Debug: Current Booking State');
  console.log('👤 User:', user?.email || 'Not authenticated');
  console.log('🛍️ Service:', bookingData.selectedService?.name || 'None selected');
  console.log('👨‍💼 Employee:', bookingData.selectedEmployee?.display_name || 'None selected');
  console.log('📅 Date:', bookingData.selectedDate || 'None selected');
  console.log('⏰ Time:', bookingData.selectedTime || 'None selected');
  console.log('📝 Consent:', bookingData.consentData?.consentAgreed ? 'Agreed' : 'Not agreed');
  console.log('💰 Total:', `$${bookingData.total || 0}`);
  console.groupEnd();
};

/**
 * Check if debug mode is enabled
 * @returns {boolean} - Whether debug mode is enabled
 */
export const isDebugMode = () => {
  return process.env.NODE_ENV === 'development' || 
         localStorage.getItem('booking_debug') === 'true';
};

/**
 * Enable/disable debug mode
 * @param {boolean} enabled - Whether to enable debug mode
 */
export const setDebugMode = (enabled) => {
  if (enabled) {
    localStorage.setItem('booking_debug', 'true');
    console.log('🔧 Booking debug mode enabled');
  } else {
    localStorage.removeItem('booking_debug');
    console.log('🔧 Booking debug mode disabled');
  }
};

// Export all debug functions
export default {
  logBookingFlow,
  logAuthState,
  logConsentStatus,
  logBookingValidation,
  logApiError,
  debugBookingState,
  isDebugMode,
  setDebugMode
};
