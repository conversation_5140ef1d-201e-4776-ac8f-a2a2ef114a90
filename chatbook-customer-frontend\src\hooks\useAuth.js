import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { authService } from '../features/auth/services/authApi';
import { checkUserConsentStatus } from '../api/consentApi';
import { queryKeys } from '../lib/queryClient';
import { useAuthStore } from '../stores/authStore';

// Query for current user data
export const useCurrentUser = () => {
  const { isAuthenticated } = useAuthStore();
  
  return useQuery({
    queryKey: queryKeys.auth.user,
    queryFn: authService.getCurrentUser,
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error) => {
      // Don't retry on auth errors
      if (error?.response?.status === 401 || error?.response?.status === 403) {
        return false;
      }
      return failureCount < 2;
    },
  });
};

// Query for user consent status
export const useConsentStatus = (userId) => {
  return useQuery({
    queryKey: queryKeys.auth.consentStatus(userId),
    queryFn: checkUserConsentStatus,
    enabled: !!userId,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
    retry: 1,
  });
};

// Mutation for login
export const useLogin = () => {
  const queryClient = useQueryClient();
  const { login: authLogin } = useAuthStore();
  
  return useMutation({
    mutationFn: authLogin,
    onSuccess: (data) => {
      // Invalidate and refetch user data
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.user });
      if (data.user?.id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.auth.consentStatus(data.user.id) 
        });
      }
    },
    onError: (error) => {
      console.error('Login mutation error:', error);
    },
  });
};

// Mutation for logout
export const useLogout = () => {
  const queryClient = useQueryClient();
  const { logout: authLogout } = useAuthStore();
  
  return useMutation({
    mutationFn: authLogout,
    onSuccess: () => {
      // Clear all cached data on logout
      queryClient.clear();
    },
    onError: (error) => {
      console.error('Logout mutation error:', error);
      // Still clear cache even if logout API fails
      queryClient.clear();
    },
  });
};

// Mutation for registration
export const useRegister = () => {
  const queryClient = useQueryClient();
  const { register: authRegister } = useAuthStore();
  
  return useMutation({
    mutationFn: authRegister,
    onSuccess: (data) => {
      // Invalidate and refetch user data
      queryClient.invalidateQueries({ queryKey: queryKeys.auth.user });
      if (data.user?.id) {
        queryClient.invalidateQueries({ 
          queryKey: queryKeys.auth.consentStatus(data.user.id) 
        });
      }
    },
    onError: (error) => {
      console.error('Register mutation error:', error);
    },
  });
};
