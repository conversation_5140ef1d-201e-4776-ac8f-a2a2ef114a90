import React from 'react'

/**
 * Navigation Feedback Toast Component
 * Displays feedback when users navigate between calendar views or dates
 */
const NavigationFeedback = ({ navigationFeedback, onDismiss }) => {
  if (!navigationFeedback) return null

  return (
    <div className="fixed top-4 right-4 z-50 animate-fade-in">
      <div className={`
        px-4 py-3 rounded-lg shadow-lg max-w-sm
        ${navigationFeedback.type === 'view-change' 
          ? 'bg-blue-600 text-white' 
          : 'bg-green-600 text-white'
        }
      `}>
        <div className="flex items-center space-x-2">
          <div className="flex-shrink-0">
            {navigationFeedback.type === 'view-change' ? (
              <ViewChangeIcon />
            ) : (
              <NavigationIcon />
            )}
          </div>
          <div className="flex-1">
            <p className="text-sm font-medium">
              {navigationFeedback.message}
            </p>
          </div>
          <button
            onClick={onDismiss}
            className="flex-shrink-0 text-white hover:text-gray-200 transition-colors"
            aria-label="Dismiss notification"
          >
            <CloseIcon />
          </button>
        </div>
      </div>
    </div>
  )
}

// Icon Components
const ViewChangeIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
  </svg>
)

const NavigationIcon = () => (
  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" 
          d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
)

const CloseIcon = () => (
  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
  </svg>
)

export default NavigationFeedback 