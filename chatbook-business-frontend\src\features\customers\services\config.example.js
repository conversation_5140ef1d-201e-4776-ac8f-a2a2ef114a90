/**
 * Example configuration for Customer Service Layer
 * 
 * Add these to your .env file:
 */

// Environment variable examples:
// REACT_APP_CUSTOMER_SERVICE_TYPE=react-query
// REACT_APP_CUSTOMER_SERVICE_TYPE=indexed-db

/**
 * Runtime configuration example
 */
export const exampleConfiguration = {
  // Development setup
  development: {
    serviceType: 'react-query',
    options: {
      staleTime: 5 * 60 * 1000,  // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      retry: 2
    }
  },
  
  // Production setup
  production: {
    serviceType: 'react-query', // Change to 'indexed-db' when ready
    options: {
      staleTime: 10 * 60 * 1000,  // 10 minutes
      cacheTime: 30 * 60 * 1000,  // 30 minutes
      retry: 3
    }
  },
  
  // Testing setup
  testing: {
    serviceType: 'react-query',
    options: {
      staleTime: 0,              // No stale time for testing
      cacheTime: 0,              // No cache time for testing
      retry: 0                   // No retries for testing
    }
  }
}

/**
 * How to use environment variables:
 * 
 * 1. Create/update .env file in your project root:
 *    REACT_APP_CUSTOMER_SERVICE_TYPE=react-query
 * 
 * 2. The service factory will automatically read this variable
 * 
 * 3. You can also set it programmatically:
 *    CustomerServiceFactory.switchToService('indexed-db')
 * 
 * 4. User preferences are stored in localStorage automatically
 */ 