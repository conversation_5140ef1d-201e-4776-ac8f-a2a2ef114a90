from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON><PERSON>
from .views import (
    TransactionViewSet, 
    SalesSummaryViewSet, 
    ServicesReportViewSet, 
    BookingPercentageViewSet,
    ReportFilterOptionsView
)

app_name = 'reports'

# Create router for all report endpoints
router = DefaultRouter()
router.register(r'transactions', TransactionViewSet, basename='transaction')
router.register(r'sales-summary', SalesSummaryViewSet, basename='sales-summary')
router.register(r'services', ServicesReportViewSet, basename='services-report')
router.register(r'booking-percentage', BookingPercentageViewSet, basename='booking-percentage')

urlpatterns = [
    # Shared filter options endpoint (used by all reports)
    path('filter-options/', ReportFilterOptionsView.as_view(), name='filter-options'),
    
    # All report endpoints with router
    path('', include(router.urls)),
] 