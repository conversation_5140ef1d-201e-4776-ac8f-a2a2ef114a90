from rest_framework import serializers
from customers.models import CustomerBookingSession
from business.models import Business
from decimal import Decimal
import json


class CustomerBookingSessionSerializer(serializers.ModelSerializer):
    """
    Serializer for CustomerBookingSession model
    Handles saving and retrieving booking session data from the backend
    """

    class Meta:
        model = CustomerBookingSession
        fields = [
            'id',
            'user',
            'business',
            'booking_data',
            'current_step',
            'consent_status',
            'created_at',
            'updated_at',
            'expires_at',
            'is_expired'
        ]
        read_only_fields = ['id', 'user', 'created_at', 'updated_at', 'expires_at', 'is_expired']

    def create(self, validated_data):
        # Set the user from the request context
        validated_data['user'] = self.context['request'].user

        # Set IP address and user agent from request
        request = self.context['request']
        validated_data['ip_address'] = self.get_client_ip(request)
        validated_data['user_agent'] = request.META.get('HTTP_USER_AGENT', '')

        # Convert Decimal values in booking_data to strings for JSON serialization
        if 'booking_data' in validated_data:
            validated_data['booking_data'] = self.convert_decimals_to_strings(validated_data['booking_data'])

        return super().create(validated_data)

    def update(self, instance, validated_data):
        # Update IP address and user agent on each update
        request = self.context['request']
        validated_data['ip_address'] = self.get_client_ip(request)
        validated_data['user_agent'] = request.META.get('HTTP_USER_AGENT', '')

        # Convert Decimal values in booking_data to strings for JSON serialization
        if 'booking_data' in validated_data:
            validated_data['booking_data'] = self.convert_decimals_to_strings(validated_data['booking_data'])

        # Extend expiration time on update
        instance.extend_expiration()

        return super().update(instance, validated_data)

    def get_client_ip(self, request):
        """Get the client's IP address from the request"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def convert_decimals_to_strings(self, data):
        """Recursively convert Decimal objects to strings for JSON serialization"""
        if isinstance(data, dict):
            return {key: self.convert_decimals_to_strings(value) for key, value in data.items()}
        elif isinstance(data, list):
            return [self.convert_decimals_to_strings(item) for item in data]
        elif isinstance(data, Decimal):
            return str(data)
        else:
            return data


class BookingSessionDataSerializer(serializers.Serializer):
    """
    Serializer for just the booking data portion
    Used for partial updates to the booking session
    """
    selectedService = serializers.JSONField(required=False, allow_null=True)
    selectedEmployee = serializers.JSONField(required=False, allow_null=True)
    selectedDate = serializers.CharField(required=False, allow_null=True)
    selectedTime = serializers.CharField(required=False, allow_null=True)
    selectedAddOns = serializers.ListField(required=False, default=list)
    customerInfo = serializers.JSONField(required=False, default=dict)
    consentData = serializers.JSONField(required=False, default=dict)
    step = serializers.CharField(required=False, default='service-selection')
    totalCost = serializers.DecimalField(max_digits=10, decimal_places=2, required=False, default=0)

    def validate_step(self, value):
        """Validate that the step is one of the allowed choices"""
        valid_steps = ['service-selection', 'time-selection', 'consent', 'review', 'confirmation']
        if value not in valid_steps:
            raise serializers.ValidationError(f"Invalid step. Must be one of: {', '.join(valid_steps)}")
        return value

    def validate_totalCost(self, value):
        """Convert totalCost to string for JSON serialization"""
        if isinstance(value, Decimal):
            return str(value)
        return value