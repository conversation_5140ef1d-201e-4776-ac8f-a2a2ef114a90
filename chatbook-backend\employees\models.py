from django.db import models
from django.conf import settings
from django.core.validators import RegexValidator, MinValueValidator, EmailValidator
from decimal import Decimal
from django.core.exceptions import ValidationError
from django.db.models import Q, F
from utils.timezone_validators import validate_working_hours_timezone
import uuid
from datetime import time
from business.models import AccessLevel as BusinessAccessLevel

class Employee(models.Model):
    """
    Model for salon employees/stylists.
    """
    # User relationship - link to Django auth user
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='employee_profile',
        null=False,  # Enforce mandatory relationship with User
        blank=False
    )
    
    # Business relationship
    business = models.ForeignKey(
        'business.Business',
        on_delete=models.CASCADE,
        related_name='employees',
        null=False  # Enforce mandatory relationship with Business
    )
    
    # Professional Information - use business-specific stylist levels
    stylist_level = models.ForeignKey(
        'business.StylistLevel',
        on_delete=models.SET_NULL,
        related_name='employees',
        null=True,
        blank=True,
        verbose_name='Stylist Level'
    )
    
    # Access level permission
    access_level = models.ForeignKey(
        'business.AccessLevel',
        on_delete=models.PROTECT,
        limit_choices_to=Q(business=F('business')),
        related_name="employees",
        null=True,
        blank=True
    )
    
    class EmployeeType(models.TextChoices):
        SERVICE_PROVIDER = 'service_provider', 'Service Provider'
        ADMIN = 'admin', 'Account Admin'
    
    profile_image = models.ImageField(
        upload_to='employees/profile/', 
        blank=True, 
        null=True
    )

    # Calendar Integration
    calendar_sync_enabled = models.BooleanField(
        default=False,
        verbose_name='Enable Calendar Sync'
    )
    calendar_provider = models.CharField(
        max_length=50,
        choices=[
            ('google', 'Google Calendar'),
            ('outlook', 'Outlook Calendar'),
            ('apple', 'Apple Calendar'),
        ],
        blank=True,
        null=True
    )
    calendar_id = models.CharField(
        max_length=255,
        blank=True,
        null=True,
        help_text='Calendar ID from the third-party calendar service'
    )
    
    # ICS Feed Token for Calendar Subscription
    ics_feed_token = models.UUIDField(
        default=uuid.uuid4,
        unique=True,
        help_text='Unique token for ICS calendar feed subscription'
    )
    
    # Services and Booking Settings
    accept_online_bookings = models.BooleanField(
        default=True,
        verbose_name='Accept Online Bookings'
    )

    # Access Control
    employee_type = models.CharField(
        max_length=20,
        choices=EmployeeType.choices,
        default=EmployeeType.SERVICE_PROVIDER,
        verbose_name='Employee Type'
    )

    # System fields
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = "Employee Profile"
        verbose_name_plural = "Employee Profiles"
        ordering = ['user__last_name', 'user__first_name', 'pk']  # Deterministic ordering
        indexes = [
            models.Index(fields=['is_active'], name='employee_active_idx'),
            models.Index(fields=['stylist_level'], name='employee_level_idx'),
        ]
        constraints = [
            models.UniqueConstraint(
                fields=['business', 'user'],
                name='unique_employee_per_business'
            )
        ]

    def __str__(self):
        level_name = "No Level"
        if self.stylist_level:
            level_name = self.stylist_level.name
            
        if self.user:
            return f"{self.user.first_name} {self.user.last_name} - {level_name}"
        return f"Employee {self.id} - {level_name}"
    
    def clean(self):
        # Validate user relationship is set
        if not self.user:
            raise ValidationError({'user': 'Employee must be associated with a user account.'})
        
        # Validate business relationship is set
        if not self.business:
            raise ValidationError({'business': 'Employee must be associated with a business.'})
        
        # Validate stylist_level belongs to the same business
        if self.stylist_level and self.stylist_level.business != self.business:
            raise ValidationError({'stylist_level': 'Stylist level must belong to the same business.'})
            
        # Validate access_level belongs to the same business
        if self.access_level and self.access_level.business != self.business:
            raise ValidationError({'access_level': 'Access level must belong to the same business.'})

    # Properties to access user information
    @property
    def first_name(self):
        return self.user.first_name if self.user else ""
        
    @property
    def last_name(self):
        return self.user.last_name if self.user else ""
        
    @property
    def email(self):
        return self.user.email if self.user else ""
        
    @property
    def mobile_phone(self):
        return self.user.phone_number if self.user else ""
        
    @property
    def full_name(self):
        return f"{self.first_name} {self.last_name}" if self.user else ""

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)
        
    # Helper method to get available services for this employee
    def get_available_services(self):
        """
        Returns queryset of services this employee can provide based on their stylist level
        """
        from services.models import Service
        
        if not self.stylist_level:
            # Return empty queryset if no stylist level is set
            return Service.objects.none()
            
        # Get services available for this stylist level
        return Service.objects.filter(
            stylist_level_services__stylist_level=self.stylist_level,
            stylist_level_services__is_active=True
        ).distinct()
    
    def get_ics_feed_url(self, request=None):
        """
        Returns the ICS calendar feed URL for this employee.
        
        Args:
            request: Django request object to build absolute URL (optional)
        
        Returns:
            str: ICS feed URL for this employee's work schedule
        """
        from django.urls import reverse
        
        # Get the relative URL
        relative_url = reverse('employee_ics_feed', kwargs={'ics_token': self.ics_feed_token})
        
        if request:
            # Build absolute URL if request is provided
            return request.build_absolute_uri(relative_url)
        else:
            # Return relative URL
            return relative_url
    
    def regenerate_ics_token(self):
        """
        Regenerate the ICS feed token for security purposes.
        This will invalidate the old feed URL and create a new one.
        """
        import uuid
        self.ics_feed_token = uuid.uuid4()
        self.save(update_fields=['ics_feed_token'])


class EmployeeWorkingHours(models.Model):
    """
    Model for managing employee working hours with support for different hours on different days
    """
    class Day(models.TextChoices):
        MONDAY = 'monday', 'Monday'
        TUESDAY = 'tuesday', 'Tuesday'
        WEDNESDAY = 'wednesday', 'Wednesday'
        THURSDAY = 'thursday', 'Thursday'
        FRIDAY = 'friday', 'Friday'
        SATURDAY = 'saturday', 'Saturday'
        SUNDAY = 'sunday', 'Sunday'
    
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, related_name='working_hours')
    day = models.CharField(
        max_length=10,
        choices=Day.choices,
        help_text="Day of the week",
        default=Day.MONDAY
    )
    start_time = models.TimeField(
        help_text="Start time of the working block",
        default=time(9, 0)  # Type-safe time default
    )
    end_time = models.TimeField(
        help_text="End time of the working block",
        default=time(17, 0)  # Type-safe time default
    )
    timezone = models.CharField(
        max_length=50,
        blank=True,
        null=True,
        help_text="Timezone for these working hours. If blank, uses business timezone.",
        validators=[validate_working_hours_timezone]
    )
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Employee working hours'
        verbose_name_plural = 'Employee working hours'
        ordering = ['employee', 'day']
        constraints = [
            models.UniqueConstraint(
                fields=['employee', 'day'],
                name='unique_employee_day'
            ),
            models.CheckConstraint(
                check=Q(start_time__lt=F('end_time')),
                name='working_hours_start_before_end'
            )
        ]

    def get_effective_timezone(self):
        """
        Get the effective timezone for these working hours.
        Returns the explicit timezone if set, otherwise the business timezone.
        """
        if self.timezone:
            return self.timezone

        # Fall back to business timezone
        try:
            if hasattr(self.employee, 'business') and self.employee.business:
                if hasattr(self.employee.business, 'booking_rules') and self.employee.business.booking_rules:
                    return self.employee.business.booking_rules.timezone
        except Exception:
            pass

        # Final fallback to UTC
        return 'UTC'

    def __str__(self):
        return f"{self.employee} - {self.get_day_display()} ({self.start_time}-{self.end_time})"

    def clean(self):
        if self.start_time and self.end_time and self.start_time >= self.end_time:
            raise ValidationError("End time must be after start time")

    def save(self, *args, **kwargs):
        self.clean()
        super().save(*args, **kwargs)

class AccessLevel(BusinessAccessLevel):
    """
    Proxy model for AccessLevel to display it in the Employees section of admin.
    """
    class Meta:
        proxy = True
        verbose_name = 'Access Level'
        verbose_name_plural = 'Access Levels'
        # This ensures it appears in the employees app section in admin
        app_label = 'employees'

class EmployeeCalendarConfig(models.Model):
    """
    Model for employee calendar configuration preferences.
    """
    class WeekStartDay(models.TextChoices):
        MONDAY = 'monday', 'Monday'
        TUESDAY = 'tuesday', 'Tuesday'
        WEDNESDAY = 'wednesday', 'Wednesday'
        THURSDAY = 'thursday', 'Thursday'
        FRIDAY = 'friday', 'Friday'
        SATURDAY = 'saturday', 'Saturday'
        SUNDAY = 'sunday', 'Sunday'
    
    class DefaultView(models.TextChoices):
        DAY = 'day', 'Day'
        WEEK = 'week', 'Week'
    
    employee = models.OneToOneField(
        Employee,
        on_delete=models.CASCADE,
        related_name='calendar_config'
    )
    
    week_start_day = models.CharField(
        max_length=10,
        choices=WeekStartDay.choices,
        default=WeekStartDay.MONDAY,
        help_text="Week View Starting Day"
    )
    
    default_view = models.CharField(
        max_length=10,
        choices=DefaultView.choices,
        default=DefaultView.WEEK,
        help_text="Default View at Login"
    )
    
    calendar_resolution = models.PositiveSmallIntegerField(
        default=15,
        help_text="Calendar time resolution in minutes"
    )
    
    display_hour_start = models.PositiveSmallIntegerField(
        default=7,
        help_text="Display start hour for calendar view (24h format, default 7 AM)"
    )
    
    display_hour_end = models.PositiveSmallIntegerField(
        default=23,
        help_text="Display end hour for calendar view (24h format, default 11 PM)"
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    def __str__(self):
        return f"Calendar Config for {self.employee}"
