"""
Email template configuration for notifications.

This module defines the mapping between user types, event types, and email templates.
Templates are organized by user type (business/customer) and then by specific events.
"""

# User type enum - determines which template directory to use
USER_TYPES = {
    'BUSINESS': 'business',
    'CUSTOMER': 'customer'
}

# Role types - specific user roles within each user type
ROLES = {
    'CUSTOMER': 'customer',
    'EMPLOYEE': 'employee', 
    'BUSINESS_OWNER': 'business_owner',
    'ADMIN': 'admin'
}

# Template directory mapping
TEMPLATE_DIRECTORIES = {
    'business': 'notification_templates/business_templates/',
    'customer': 'notification_templates/customer_templates/'
}

# Event type to template file mapping
# Format: {event_type: {user_type: template_filename}}
EMAIL_TEMPLATE_MAPPING = {
    'appointment.booked': {
        'business': 'appointment_booked.html',
        'customer': 'appointment_booked.html'
    },
    'appointment.changed': {
        'business': 'appointment_changed.html',
        'customer': 'appointment_changed.html'
    },
    'appointment.cancelled': {
        'business': 'appointment_cancelled.html',
        'customer': 'appointment_cancelled.html'
    },
    'appointment.confirmation_requested': {
        'customer': 'appointment_confirmation_requested.html'
    },
    'appointment.reminder': {
        'customer': 'appointment_reminder.html'
    },
    'waitlist.added': {
        'business': 'waitlist_added.html',
        'customer': 'waitlist_added.html'
    },
    'notification.direct': {
        'business': 'direct_notification.html',
        'customer': 'direct_notification.html'
    }
}

# Recipient configuration - defines who should receive notifications for each event type
NOTIFICATION_RECIPIENTS = {
    'appointment.booked': ['business', 'customer'],
    'appointment.changed': ['business', 'customer'],
    'appointment.cancelled': ['business', 'customer'],
    'appointment.confirmation_requested': ['customer'],  # Customer only
    'appointment.reminder': ['customer'],  # Customer only
    'waitlist.added': ['business', 'customer'],
    'notification.direct': ['business', 'customer']
}

def get_email_template_key(event_type: str, user_type: str) -> str:
    """
    Get the email template key for a given event type and user type.
    
    Args:
        event_type: The type of event (e.g., 'appointment.created')
        user_type: The user type ('business' or 'customer')
        
    Returns:
        String template key in format: 'business_templates/new_booking.html'
        
    Raises:
        KeyError: If no template mapping exists for the given combination
    """
    if event_type not in EMAIL_TEMPLATE_MAPPING:
        raise KeyError(f"No template mapping found for event type: {event_type}")
    
    if user_type not in EMAIL_TEMPLATE_MAPPING[event_type]:
        raise KeyError(f"No template mapping found for user type '{user_type}' and event type '{event_type}'")
    
    template_dir = TEMPLATE_DIRECTORIES[user_type]
    template_file = EMAIL_TEMPLATE_MAPPING[event_type][user_type]
    
    return f"{template_dir}{template_file}"

def get_available_templates() -> dict:
    """
    Get all available template mappings.
    
    Returns:
        Dict with all event types and their template mappings
    """
    return EMAIL_TEMPLATE_MAPPING.copy()

def get_allowed_recipients(event_type: str) -> list:
    """
    Get the list of allowed recipient user types for a given event type.

    Args:
        event_type: The type of event (e.g., 'appointment.booked')

    Returns:
        List of allowed user types ['business', 'customer'] or ['customer']

    Raises:
        KeyError: If no recipient configuration exists for the given event type
    """
    if event_type not in NOTIFICATION_RECIPIENTS:
        raise KeyError(f"No recipient configuration found for event type: {event_type}")

    return NOTIFICATION_RECIPIENTS[event_type].copy()

def should_notify_user_type(event_type: str, user_type: str) -> bool:
    """
    Check if a user type should be notified for a given event type.

    Args:
        event_type: The type of event
        user_type: The user type ('business' or 'customer')

    Returns:
        True if the user type should be notified, False otherwise
    """
    try:
        allowed_recipients = get_allowed_recipients(event_type)
        return user_type in allowed_recipients
    except KeyError:
        # Default to allowing all user types if no configuration exists
        return True

def add_template_mapping(event_type: str, user_type: str, template_file: str) -> None:
    """
    Add a new template mapping.

    Args:
        event_type: The event type
        user_type: The user type ('business' or 'customer')
        template_file: The template filename
    """
    if event_type not in EMAIL_TEMPLATE_MAPPING:
        EMAIL_TEMPLATE_MAPPING[event_type] = {}

    EMAIL_TEMPLATE_MAPPING[event_type][user_type] = template_file

def add_recipient_configuration(event_type: str, allowed_user_types: list) -> None:
    """
    Add a new recipient configuration.

    Args:
        event_type: The event type
        allowed_user_types: List of user types that should receive notifications
    """
    NOTIFICATION_RECIPIENTS[event_type] = allowed_user_types.copy()
