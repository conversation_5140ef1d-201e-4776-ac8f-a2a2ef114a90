{% extends "admin/base_site.html" %}
{% load i18n static %}

{% block content %}
<div id="content-main">
    {% if app_list %}
        <div class="app-grid">
            {# User Management #}
            <div class="app-section module">
                <h2>{% trans 'User Management' %}</h2>
                <table>
                    <tbody>
                        {% for app in app_list %}
                            {% if app.app_label == 'accounts' %}
                                {% for model in app.models %}
                                    <tr>
                                        <th scope="row">
                                            {% if model.admin_url %}
                                                <a href="{{ model.admin_url }}">{{ model.name }}</a>
                                            {% else %}
                                                {{ model.name }}
                                            {% endif %}
                                        </th>
                                        <td>
                                            {% if model.add_url %}
                                                <a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% endif %}
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {# Business Management #}
            <div class="app-section module">
                <h2>{% trans 'Business Management' %}</h2>
                <table>
                    <tbody>
                        {% for app in app_list %}
                            {% if app.app_label == 'business' %}
                                {% for model in app.models %}
                                    <tr>
                                        <th scope="row">
                                            {% if model.admin_url %}
                                                <a href="{{ model.admin_url }}">{{ model.name }}</a>
                                            {% else %}
                                                {{ model.name }}
                                            {% endif %}
                                        </th>
                                        <td>
                                            {% if model.add_url %}
                                                <a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% endif %}
                        {% endfor %}
                    </tbody>
                </table>
            </div>

            {# Services & Bookings #}
            <div class="app-section module">
                <h2>{% trans 'Services & Bookings' %}</h2>
                <table>
                    <tbody>
                        {% for app in app_list %}
                            {% if app.app_label == 'services' or app.app_label == 'bookings' %}
                                {% for model in app.models %}
                                    <tr>
                                        <th scope="row">
                                            {% if model.admin_url %}
                                                <a href="{{ model.admin_url }}">{{ model.name }}</a>
                                            {% else %}
                                                {{ model.name }}
                                            {% endif %}
                                        </th>
                                        <td>
                                            {% if model.add_url %}
                                                <a href="{{ model.add_url }}" class="addlink">{% trans 'Add' %}</a>
                                            {% endif %}
                                        </td>
                                    </tr>
                                {% endfor %}
                            {% endif %}
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    {% else %}
        <p>{% trans "You don't have permission to view or edit anything." %}</p>
    {% endif %}
</div>

<style>
.app-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    padding: 1.5rem;
}

.app-section table {
    width: 100%;
}

.app-section th {
    text-align: left;
    padding: 0.75rem 0;
}

.app-section td {
    text-align: right;
}

.addlink {
    background: var(--primary);
    border-radius: 4px;
    color: white;
    padding: 0.375rem 0.75rem;
    text-decoration: none;
    font-size: 0.875rem;
}

.addlink:hover {
    background: #1557b0;
    text-decoration: none;
}

.app-section tr {
    border-bottom: 1px solid #eee;
}

.app-section tr:last-child {
    border-bottom: none;
}

.app-section a {
    color: var(--primary);
    text-decoration: none;
}

.app-section a:hover {
    text-decoration: underline;
}
</style>
{% endblock %} 