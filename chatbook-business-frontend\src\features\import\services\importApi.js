// importApi.js - API service for customer import functionality

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1'

// API service class for import operations
class ImportApiService {
  constructor() {
    this.baseURL = API_BASE_URL
  }

  // Helper method to get auth token (implement this based on your auth system)
  getAuthToken() {
    // Replace this with your actual token retrieval logic
    return localStorage.getItem('auth_token') || sessionStorage.getItem('auth_token')
  }

  // Helper method to make API requests
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`
    
    const authToken = this.getAuthToken()
    const defaultOptions = {
      headers: {
        'Content-Type': 'application/json',
        Authorization: `Bearer ${authToken}`
      },
      ...options
    }

    try {
      const response = await fetch(url, defaultOptions)
      
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}))
        throw new Error(errorData.message || errorData.error || `HTTP error! status: ${response.status}`)
      }
      
      return await response.json()
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error)
      throw error
    }
  }

  // Get field mapping suggestions based on available columns
  async getFieldMappingSuggestions(columns) {
    return await this.makeRequest('/customers/import/field-mapping-suggestions/', {
      method: 'POST',
      body: JSON.stringify({ columns })
    })
  }

  // Start customer import process
  async startCustomerImport(importData) {
    const payload = {
      fileName: importData.fileName,
      fileSize: importData.fileSize,
      totalRows: importData.totalRows,
      fieldMappings: importData.fieldMappings,
      customers: importData.customers,
      skipDuplicates: importData.skipDuplicates || true,
      updateExisting: importData.updateExisting || false,
      validateEmails: importData.validateEmails || true
    }

    return await this.makeRequest('/customers/import/', {
      method: 'POST',
      body: JSON.stringify(payload)
    })
  }

  // Get import job status and progress
  async getImportStatus(jobId) {
    return await this.makeRequest(`/customers/import/status/${jobId}/`)
  }

  // Get final import results
  async getImportResults(jobId) {
    return await this.makeRequest(`/customers/import/results/${jobId}/`)
  }

  // Validate customer data before import (if you want to add this later)
  async validateCustomerData(customers, fieldMappings) {
    const payload = {
      customers,
      fieldMappings,
      validateOnly: true
    }

    return await this.makeRequest('/customers/validate/', {
      method: 'POST',
      body: JSON.stringify(payload)
    })
  }
}

// Export singleton instance
export const importApi = new ImportApiService()

// Export individual methods for easier importing
export const {
  getFieldMappingSuggestions,
  startCustomerImport,
  getImportStatus,
  getImportResults,
  validateCustomerData
} = importApi