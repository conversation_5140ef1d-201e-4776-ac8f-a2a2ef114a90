/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
        },
        // Calendar-specific design tokens
        calendar: {
          surface: '#ffffff',
          'surface-variant': '#f9fafb',
          outline: '#e5e7eb',
          'outline-variant': '#f3f4f6',
          primary: '#3b82f6',
          'primary-container': '#dbeafe',
        },
        // Appointment service category colors
        appointment: {
          haircut: '#8b5cf6',
          'haircut-border': '#7c3aed',
          coloring: '#ec4899',
          'coloring-border': '#db2777',
          styling: '#06b6d4',
          'styling-border': '#0891b2',
          treatment: '#84cc16',
          'treatment-border': '#65a30d',
          consultation: '#f97316',
          'consultation-border': '#ea580c',
        },
        // Status colors
        status: {
          completed: '#22c55e',
          'no-show': '#ef4444',
          'in-progress': '#eab308',
          pending: '#6b7280',
        }
      },
      // Calendar-specific spacing
      spacing: {
        'calendar-hour': '80px',
        'calendar-slot': '20px',
        'calendar-slot-5': '6.67px',
        'calendar-slot-15': '20px',
        'calendar-slot-30': '40px',
      },
      // Z-index hierarchy for proper component layering
      zIndex: {
        'calendar-base': '1',
        'calendar-content': '10',
        'calendar-appointment': '20',
        'calendar-appointment-hover': '25',
        'calendar-headers': '100',
        'calendar-interactions': '200',
        'calendar-overlays': '300',
        'calendar-ui': '400',
        'calendar-ghost': '3000',
      },
      // Custom background patterns
      backgroundImage: {
        'grid-pattern': 'repeating-linear-gradient(transparent 0, transparent 79px, #e5e7eb 79px, #e5e7eb 80px)',
        'grid-slot-pattern': 'repeating-linear-gradient(transparent 0, transparent 19px, #f3f4f6 19px, #f3f4f6 20px)',
      },
      // Custom animations
      animation: {
        'pulse-status': 'pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite',
        'fade-in': 'fadeIn 0.15s ease-out',
        'scale-in': 'scaleIn 0.15s ease-out',
      },
      // Custom keyframes
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        scaleIn: {
          '0%': { opacity: '0', transform: 'scale(0.95)' },
          '100%': { opacity: '1', transform: 'scale(1)' },
        },
      },
    },
  },
  plugins: [],
}
