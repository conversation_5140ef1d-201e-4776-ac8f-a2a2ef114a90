import React, { useState, useEffect } from 'react'
import AvatarUpload from './AvatarUpload'
import ToggleSwitch from './ToggleSwitch'

const EmployeeDetailPanel = ({ employee, onClose, onEdit, onToggleStatus, onToggleBooking, loadingStates = {} }) => {
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)
  const [editForm, setEditForm] = useState({
    full_name: '',
    email: '',
    phone_number: '',
    employee_type: '',
    access_level: ''
  })

  // Update form when employee changes
  useEffect(() => {
    if (employee) {
      setEditForm({
        full_name: employee.full_name || '',
        email: employee.user_details?.email || '',
        phone_number: employee.user_details?.phone_number || '',
        employee_type: employee.employee_type || '',
        access_level: employee.access_level || ''
      })
      setIsEditing(false)
    }
  }, [employee?.id])

  const handleEdit = () => {
    setIsEditing(true)
  }

  const handleSave = async () => {
    if (!employee?.id) return

    setIsSaving(true)
    try {
      // Here you would make the actual API call
      // For now, simulating the API call
      console.log('Updating employee with:', editForm)
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000))
      
      setIsEditing(false)
      
      // In a real implementation, you would:
      // const response = await employeeApiService.updateEmployee(employee.id, editForm)
      // And then update the parent component with the response
      
    } catch (error) {
      console.error('Failed to update employee:', error)
      alert('Failed to update employee. Please try again.')
    } finally {
      setIsSaving(false)
    }
  }

  const handleCancel = () => {
    setEditForm({
      full_name: employee?.full_name || '',
      email: employee?.user_details?.email || '',
      phone_number: employee?.user_details?.phone_number || '',
      employee_type: employee?.employee_type || '',
      access_level: employee?.access_level || ''
    })
    setIsEditing(false)
  }

  if (!employee) {
    return (
      <div className="flex-1 flex items-center justify-center bg-gray-50 h-full">
        <div className="text-center">
          <svg className="w-12 h-12 text-gray-400 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Select an employee</h3>
          <p className="text-gray-600">Choose an employee from the list to view their details</p>
        </div>
      </div>
    )
  }

  // Employee type and level options (these would come from your API)
  const employeeTypeOptions = [
    { value: 'service_provider', label: 'Service Provider' },
    { value: 'manager', label: 'Manager' },
    { value: 'admin', label: 'Admin' }
  ]

  const accessLevelOptions = [
    { value: 'standard', label: 'Standard' },
    { value: 'elevated', label: 'Elevated' },
    { value: 'admin', label: 'Admin' }
  ]

  return (
    <div className="flex-1 flex flex-col bg-white h-full min-h-0">
      {/* Header with Update Button */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between p-4 lg:p-6 border-b border-gray-200 gap-4 flex-shrink-0">
        <div className="flex items-center gap-4 flex-1">
          <AvatarUpload 
            employee={employee} 
            onSave={(avatarData) => { 
              // In edit mode, update the form, otherwise update directly
              if (isEditing) {
                setEditForm(prev => ({ ...prev, profile_image: avatarData.profile_image }))
              } else {
                // Handle direct avatar update outside of edit mode
                console.log('Avatar updated:', avatarData)
              }
            }}
            size="lg" 
          />
          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              {isEditing ? (
                <input
                  type="text"
                  value={editForm.full_name}
                  onChange={(e) => setEditForm(prev => ({ ...prev, full_name: e.target.value }))}
                  className="text-xl lg:text-2xl font-semibold text-gray-900 bg-white border border-gray-300 rounded px-2 py-1 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Employee Name"
                />
              ) : (
                <h1 className="text-xl lg:text-2xl font-semibold text-gray-900">
                  {employee?.full_name || 'Unknown Employee'}
                </h1>
              )}
            </div>
            <p className="text-gray-600 text-sm lg:text-base">{employee?.stylist_level_display}</p>
            <div className="flex flex-wrap items-center gap-2 mt-1">
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                employee?.is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {employee?.is_active ? 'Active' : 'Inactive'}
              </span>
              <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {employee?.employee_type_display || 'Service Provider'}
              </span>
            </div>
          </div>
        </div>
        <div className="flex items-center gap-2 flex-shrink-0">
          {isEditing ? (
            <>
              <button
                onClick={handleSave}
                disabled={isSaving}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSaving ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Save
                  </>
                )}
              </button>
              <button
                onClick={handleCancel}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </>
          ) : (
            <>
              <button
                onClick={handleEdit}
                className="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                </svg>
                Edit
              </button>
              <button
                onClick={onClose}
                className="p-2 text-gray-400 hover:text-gray-600"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </>
          )}
        </div>
      </div>

      {/* Employee Details - Scrollable Content */}
      <div className="flex-1 overflow-y-auto min-h-0">
        <div className="p-4 lg:p-6">
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            {/* Contact Information */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Email</label>
                    {isEditing ? (
                      <input
                        type="email"
                        value={editForm.email}
                        onChange={(e) => setEditForm(prev => ({ ...prev, email: e.target.value }))}
                        className="mt-1 w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter email address"
                      />
                    ) : (
                      <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                        <p className="text-gray-900">{employee?.user_details?.email || 'Not provided'}</p>
                      </div>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Phone</label>
                    {isEditing ? (
                      <input
                        type="tel"
                        value={editForm.phone_number}
                        onChange={(e) => setEditForm(prev => ({ ...prev, phone_number: e.target.value }))}
                        className="mt-1 w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        placeholder="Enter phone number"
                      />
                    ) : (
                      <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                        <p className="text-gray-900">{employee?.user_details?.phone_number || 'Not provided'}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>

              {/* Professional Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Professional Information</h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Stylist Level</label>
                    <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                      <p className="text-gray-900">{employee?.stylist_level_display}</p>
                    </div>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Employee Type</label>
                    {isEditing ? (
                      <select
                        value={editForm.employee_type}
                        onChange={(e) => setEditForm(prev => ({ ...prev, employee_type: e.target.value }))}
                        className="mt-1 w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">Select employee type</option>
                        {employeeTypeOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    ) : (
                      <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                        <p className="text-gray-900">{employee?.employee_type_display || 'Service Provider'}</p>
                      </div>
                    )}
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Access Level</label>
                    {isEditing ? (
                      <select
                        value={editForm.access_level}
                        onChange={(e) => setEditForm(prev => ({ ...prev, access_level: e.target.value }))}
                        className="mt-1 w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      >
                        <option value="">Select access level</option>
                        {accessLevelOptions.map((option) => (
                          <option key={option.value} value={option.value}>
                            {option.label}
                          </option>
                        ))}
                      </select>
                    ) : (
                      <div className="mt-1 p-3 bg-gray-50 rounded-lg">
                        <p className="text-gray-900">{employee?.access_level_display || 'Standard'}</p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Settings & Preferences */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Settings & Preferences</h3>
                <div className="space-y-4">
                  <ToggleSwitch
                    label="Employee Status"
                    enabled={employee?.is_active}
                    onToggle={() => onToggleStatus(employee)}
                    loading={loadingStates.status}
                  />
                  
                  <ToggleSwitch
                    label="Accept Online Bookings"
                    enabled={employee?.accept_online_bookings}
                    onToggle={() => onToggleBooking(employee)}
                    loading={loadingStates.booking}
                  />
                  
                  <div className="flex items-center justify-between">
                    <label className="block text-sm font-medium text-gray-700">Calendar Sync</label>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                      employee?.calendar_sync_enabled ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                    }`}>
                      {employee?.calendar_sync_enabled ? employee?.calendar_provider || 'Enabled' : 'Disabled'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Account Information */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Account Information</h3>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Member Since</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {employee?.created_at ? new Date(employee.created_at).toLocaleDateString() : 'Not available'}
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700">Last Updated</label>
                    <p className="mt-1 text-sm text-gray-900">
                      {employee?.updated_at ? new Date(employee.updated_at).toLocaleDateString() : 'Not available'}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default EmployeeDetailPanel 