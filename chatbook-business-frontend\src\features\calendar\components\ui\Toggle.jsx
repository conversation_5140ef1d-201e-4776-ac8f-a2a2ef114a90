import React from 'react'

// Simple utility to combine class names
const cn = (...classes) => classes.filter(Boolean).join(' ')

/**
 * Toggle - Modern toggle switch component
 * Enhanced with smooth animations, focus states, and accessibility
 */
const Toggle = ({
  checked = false,
  onChange,
  disabled = false,
  size = 'md', // 'sm', 'md', 'lg'
  color = 'blue', // 'blue', 'green', 'purple', 'indigo'
  label,
  description,
  className,
  ...props
}) => {
  const sizes = {
    sm: {
      switch: 'w-9 h-5',
      thumb: 'w-4 h-4',
      translate: checked ? 'translate-x-4' : 'translate-x-0.5'
    },
    md: {
      switch: 'w-12 h-6',
      thumb: 'w-5 h-5',
      translate: checked ? 'translate-x-6' : 'translate-x-0.5'
    },
    lg: {
      switch: 'w-14 h-7',
      thumb: 'w-6 h-6',
      translate: checked ? 'translate-x-7' : 'translate-x-0.5'
    }
  }

  const colors = {
    blue: {
      on: 'bg-blue-600 border-blue-600',
      off: 'bg-gray-200 border-gray-200'
    },
    green: {
      on: 'bg-green-600 border-green-600',
      off: 'bg-gray-200 border-gray-200'
    },
    purple: {
      on: 'bg-purple-600 border-purple-600',
      off: 'bg-gray-200 border-gray-200'
    },
    indigo: {
      on: 'bg-indigo-600 border-indigo-600',
      off: 'bg-gray-200 border-gray-200'
    }
  }

  const currentSize = sizes[size]
  const currentColor = colors[color]

  const handleToggle = () => {
    if (!disabled && onChange) {
      onChange(!checked)
    }
  }

  const handleKeyDown = (e) => {
    if (e.key === ' ' || e.key === 'Enter') {
      e.preventDefault()
      handleToggle()
    }
  }

  return (
    <div className={cn('flex items-center', className)}>
      <div
        role="switch"
        aria-checked={checked}
        aria-disabled={disabled}
        tabIndex={disabled ? -1 : 0}
        onClick={handleToggle}
        onKeyDown={handleKeyDown}
        className={cn(
          'relative inline-flex items-center border-2 rounded-full cursor-pointer transition-all duration-200 ease-in-out transform hover:scale-105 focus:outline-none focus:ring-4 focus:ring-offset-2',
          currentSize.switch,
          checked ? currentColor.on : currentColor.off,
          checked 
            ? `focus:ring-${color}-200 shadow-lg` 
            : 'focus:ring-gray-200 shadow-md',
          disabled 
            ? 'opacity-50 cursor-not-allowed transform-none hover:scale-100' 
            : 'hover:shadow-lg active:scale-95'
        )}
        {...props}
      >
        {/* Background gradient overlay */}
        <div className={cn(
          'absolute inset-0 rounded-full transition-opacity duration-200',
          checked 
            ? 'bg-gradient-to-r from-white/20 to-transparent opacity-100' 
            : 'opacity-0'
        )} />
        
        {/* Toggle thumb */}
        <div
          className={cn(
            'relative bg-white rounded-full shadow-lg ring-0 transition-all duration-200 ease-in-out',
            currentSize.thumb,
            currentSize.translate,
            checked ? 'shadow-lg ring-2 ring-white/20' : 'shadow-md',
            disabled ? 'bg-gray-100' : 'bg-white'
          )}
        >
          {/* Thumb inner highlight */}
          <div className={cn(
            'absolute inset-0 rounded-full bg-gradient-to-tr from-white/40 to-transparent transition-opacity duration-200',
            checked ? 'opacity-100' : 'opacity-60'
          )} />
        </div>
      </div>
      
      {/* Label and description */}
      {(label || description) && (
        <div className="ml-3 flex-1">
          {label && (
            <div className={cn(
              'text-sm font-medium transition-colors duration-200',
              disabled ? 'text-gray-400' : 'text-gray-900'
            )}>
              {label}
            </div>
          )}
          {description && (
            <div className={cn(
              'text-xs transition-colors duration-200 mt-0.5',
              disabled ? 'text-gray-300' : 'text-gray-600'
            )}>
              {description}
            </div>
          )}
        </div>
      )}
    </div>
  )
}

export default Toggle 