import os
import base64
import io
from datetime import datetime
from reportlab.lib.pagesizes import letter
from reportlab.lib import colors
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image, Table, TableStyle
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.enums import TA_CENTER, TA_RIGHT
from django.conf import settings
from django.template.loader import get_template
from xhtml2pdf import pisa
from io import BytesIO
from PIL import Image as PILImage


def signature_to_image(signature_data, output_path=None):
    """
    Convert base64 signature data to an image file or object
    
    Args:
        signature_data: Base64 encoded signature data
        output_path: Optional path to save the image
        
    Returns:
        BytesIO object containing the image or path to saved image
    """
    # Remove data URL prefix if present
    if signature_data.startswith('data:image'):
        signature_data = signature_data.split(',')[1]
    
    # Decode base64 data
    image_data = base64.b64decode(signature_data)
    image_stream = BytesIO(image_data)
    
    # If output path is provided, save to file
    if output_path:
        with open(output_path, 'wb') as f:
            f.write(image_data)
        return output_path
    
    return image_stream


def create_signature_pdf(customer, form_data, signature_data, business=None, employee=None):
    """
    Create a PDF with customer signature
    
    Args:
        customer: CustomerProfile object
        form_data: Dict containing form fields and values
        signature_data: Base64 encoded signature data
        business: Optional Business object
        employee: Optional Employee object
        
    Returns:
        BytesIO object containing the PDF
    """
    buffer = BytesIO()
    doc = SimpleDocTemplate(buffer, pagesize=letter)
    styles = getSampleStyleSheet()
    elements = []
    
    # Add business header if available
    if business:
        business_style = ParagraphStyle(
            'BusinessHeader',
            parent=styles['Heading1'],
            alignment=TA_CENTER,
            spaceAfter=20
        )
        elements.append(Paragraph(business.name, business_style))
        if business.logo:
            try:
                logo_path = business.logo.path
                logo = Image(logo_path, width=2*inch, height=1*inch)
                logo.hAlign = 'CENTER'
                elements.append(logo)
                elements.append(Spacer(1, 0.5*inch))
            except:
                # If logo can't be loaded, skip it
                pass
    
    # Add form title
    title_style = styles["Heading1"]
    title = form_data.get('title', 'Signed Form')
    elements.append(Paragraph(title, title_style))
    elements.append(Spacer(1, 0.25*inch))
    
    # Add customer information
    elements.append(Paragraph(f"Customer: {customer.user.get_full_name()}", styles["Heading2"]))
    elements.append(Paragraph(f"Email: {customer.user.email}", styles["Normal"]))
    if hasattr(customer, 'phone_number') and customer.phone_number:
        elements.append(Paragraph(f"Phone: {customer.phone_number}", styles["Normal"]))
    elements.append(Spacer(1, 0.25*inch))
    
    # Add form content
    content = form_data.get('content', {})
    if isinstance(content, dict):
        for field_name, field_value in content.items():
            if isinstance(field_value, dict) and 'question' in field_value:
                question = field_value.get('question', '')
                answer = field_value.get('answer', '')
                elements.append(Paragraph(f"{question}", styles["Heading3"]))
                elements.append(Paragraph(f"{answer}", styles["Normal"]))
                elements.append(Spacer(1, 0.1*inch))
    
    elements.append(Spacer(1, 0.5*inch))
    
    # Add signature
    elements.append(Paragraph("Customer Signature:", styles["Heading3"]))
    
    try:
        # Convert signature data to image
        signature_stream = signature_to_image(signature_data)
        signature_img = PILImage.open(signature_stream)
        
        # Resize if needed (max width 4 inches)
        max_width = 4 * inch
        width, height = signature_img.size
        if width > max_width:
            ratio = max_width / width
            width = max_width
            height = height * ratio
        
        # Create reportlab image
        signature_stream.seek(0)
        signature = Image(signature_stream, width=width, height=height)
        elements.append(signature)
    except Exception as e:
        # If signature can't be processed, add a placeholder
        elements.append(Paragraph(f"[Signature Error: {str(e)}]", styles["Normal"]))
    
    # Add date and time
    date_style = ParagraphStyle(
        'DateStyle',
        parent=styles['Normal'],
        alignment=TA_RIGHT,
        spaceAfter=20
    )
    current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    elements.append(Spacer(1, 0.25*inch))
    elements.append(Paragraph(f"Date: {current_date}", date_style))
    
    # Add employee signature if available
    if employee:
        elements.append(Spacer(1, 0.5*inch))
        elements.append(Paragraph(f"Witnessed by: {employee.user.get_full_name()}", styles["Normal"]))
    
    # Build PDF
    doc.build(elements)
    buffer.seek(0)
    return buffer


def render_to_pdf(template_src, context_dict={}):
    """
    Create a PDF from an HTML template
    
    Args:
        template_src: Path to HTML template
        context_dict: Context data for template rendering
        
    Returns:
        BytesIO object containing the PDF
    """
    template = get_template(template_src)
    html = template.render(context_dict)
    result = BytesIO()
    
    # Create PDF
    pdf = pisa.pisaDocument(BytesIO(html.encode("UTF-8")), result)
    if not pdf.err:
        return result
    return None