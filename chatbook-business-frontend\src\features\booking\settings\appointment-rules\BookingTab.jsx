import React, { useState } from 'react'
import ToggleSwitch from './ToggleSwitch'

/**
 * Booking Configuration Tab Content
 */
const BookingTab = ({ bookingRules, updateRule, convertHoursToDisplayUnit, convertDisplayUnitToHours }) => {
  const [settings, setSettings] = useState({
    autoAssign: true,
    requireAcceptance: false,
    allowFamilyFriends: true,
    blockNewCustomers: false,
    captureCard: true
  })

  // Convert max_days_in_advance to display format
  const getAdvanceBookingDisplay = (days) => {
    if (days >= 7) {
      return { value: Math.floor(days / 7), unit: 'weeks' }
    } else {
      return { value: days, unit: 'days' }
    }
  }
  
  const advanceBookingDisplay = bookingRules ? getAdvanceBookingDisplay(bookingRules.maxDaysInAdvance) : { value: 0, unit: 'days' }
  
  // Handle advance booking change
  const handleAdvanceBookingChange = (value, unit) => {
    let days
    if (unit === 'weeks') {
      days = parseInt(value) * 7
    } else if (unit === 'months') {
      days = parseInt(value) * 30
    } else {
      days = parseInt(value)
    }
    updateRule('maxDaysInAdvance', days)
  }

  // Handle deposit settings
  const handleDepositToggle = (enabled) => {
    updateRule('requirePayment', enabled)
  }

  const handleDepositPercentageChange = (percentage) => {
    updateRule('depositPercentage', parseFloat(percentage))
  }

  const handleToggle = (key) => {
    setSettings(prev => ({
      ...prev,
      [key]: !prev[key]
    }))
  }

  return (
  <div className="space-y-6">
    <div className="bg-green-50 border border-green-200 rounded-lg p-4">
      <h3 className="text-lg font-medium text-green-900 mb-2">Booking Process Settings</h3>
      <p className="text-green-700">Configure the online booking flow and customer experience.</p>
    </div>
    
    <div className="space-y-4">
      {/* Auto-Assign Employee When Booking */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Auto-Assign Employee When Booking</h4>
        <p className="text-sm text-gray-600 mb-3">Automatically assign an employee to customer bookings</p>
        <ToggleSwitch
          label="Enable auto-assignment"
          checked={settings.autoAssign}
          onChange={(checked) => setSettings(prev => ({ ...prev, autoAssign: checked }))}
        />
      </div>

      {/* Online Employee Booking Order */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Online Employee Booking Order</h4>
        <p className="text-sm text-gray-600 mb-3">Set the order of employee assignment for online bookings</p>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Employee assignment order</label>
          <select className="w-full border border-gray-300 rounded-lg px-3 py-2">
            <option>First available</option>
            <option>Website Employee Line Up</option>
            <option>Best Reviewed</option>
            <option>Alphabetical Order</option>
          </select>
        </div>
      </div>
  
      {/* Require Acceptance for Online Booking */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Require Acceptance for Online Booking</h4>
        <p className="text-sm text-gray-600 mb-3">Manually approve all online bookings before confirmation</p>
        <ToggleSwitch
          label="Require manual approval"
          checked={settings.requireAcceptance}
          onChange={(checked) => setSettings(prev => ({ ...prev, requireAcceptance: checked }))}
        />
      </div>

      {/* Allow Family and Friend Booking */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Allow Family and Friend Booking</h4>
        <p className="text-sm text-gray-600 mb-3">Allow customers to book appointments for family members and friends</p>
        <ToggleSwitch
          label="Enable family and friend booking"
          checked={settings.allowFamilyFriends}
          onChange={(checked) => setSettings(prev => ({ ...prev, allowFamilyFriends: checked }))}
        />
      </div>

      {/* Block New Customers from Online Booking */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Block New Customers from Online Booking</h4>
        <p className="text-sm text-gray-600 mb-3">Restrict online booking to existing customers only</p>
        <ToggleSwitch
          label="Block new customers"
          checked={settings.blockNewCustomers}
          onChange={(checked) => setSettings(prev => ({ ...prev, blockNewCustomers: checked }))}
        />
      </div>

      {/* Limit How Far in Advance */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Limit How Far in Advance a Customer Can Book a Service Online</h4>
        <p className="text-sm text-gray-600 mb-3">Set maximum advance booking timeframe for online appointments</p>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">Maximum advance booking</label>
          <div className="flex items-center space-x-2">
            <input 
              type="number" 
              value={advanceBookingDisplay.value} 
              min="0" 
              onChange={(e) => handleAdvanceBookingChange(e.target.value, advanceBookingDisplay.unit)}
              className="w-20 border border-gray-300 rounded-lg px-3 py-2 text-center"
            />
            <select 
              value={advanceBookingDisplay.unit}
              onChange={(e) => handleAdvanceBookingChange(advanceBookingDisplay.value, e.target.value)}
              className="border border-gray-300 rounded-lg px-3 py-2"
            >
              <option value="days">days</option>
              <option value="weeks">weeks</option>
              <option value="months">months</option>
            </select>
          </div>
        </div>
      </div>

      {/* Require Deposits and Capture Credit Cards */}
      <div className="border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Require Deposits and Capture Credit Cards at the Time of Booking</h4>
        <p className="text-sm text-gray-600 mb-3">Configure payment requirements for online bookings</p>
        <div className="space-y-3">
          <ToggleSwitch
            label="Require deposit"
            checked={bookingRules?.requirePayment || false}
            onChange={handleDepositToggle}
          />
          <ToggleSwitch
            label="Capture credit card information"
            checked={settings.captureCard}
            onChange={(checked) => setSettings(prev => ({ ...prev, captureCard: checked }))}
          />
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">Deposit amount</label>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-600">$</span>
              <input 
                type="number" 
                defaultValue="0" 
                min="0" 
                step="0.01"
                disabled
                className="w-24 border border-gray-300 rounded-lg px-3 py-2 text-center bg-gray-100"
                placeholder="Fixed amount"
              />
              <span className="text-sm text-gray-600">or</span>
              <input 
                type="number" 
                value={bookingRules?.depositPercentage || 0}
                min="0" 
                max="100"
                onChange={(e) => handleDepositPercentageChange(e.target.value)}
                className="w-16 border border-gray-300 rounded-lg px-3 py-2 text-center"
              />
              <span className="text-sm text-gray-600">%</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  )
}

export default BookingTab 