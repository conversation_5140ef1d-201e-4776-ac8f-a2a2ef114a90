import axios from 'axios'

// API configuration - using VITE_API_URL to match other files and correct fallback
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1'

// Create axios instance
const apiClient = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    // Use 'auth_token' which is what login.jsx stores
    const token = localStorage.getItem('auth_token') || localStorage.getItem('access_token')
    
    // Debug logging for token issues
    if (!token) {
      console.log('⚠️ No auth token found in localStorage')
      console.log('Available localStorage keys:', Object.keys(localStorage))
    } else {
      console.log('✅ Found auth token for request to:', config.url)
    }
    
    if (token) {
      config.headers.Authorization = `Bear<PERSON> ${token}`
    }
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response) => response,
  async (error) => {
    const originalRequest = error.config

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true

      try {
        const refreshToken = localStorage.getItem('refresh_token')
        if (refreshToken) {
          console.log('🔄 Attempting token refresh...')
          const response = await axios.post(`${API_BASE_URL}/auth/refresh/`, {
            refresh: refreshToken,
          })
          
          const { access } = response.data
          // Store with the same key that login uses
          localStorage.setItem('auth_token', access)
          localStorage.setItem('access_token', access) // Keep both for compatibility
          
          console.log('✅ Token refreshed successfully')
          
          // Retry original request with new token
          originalRequest.headers.Authorization = `Bearer ${access}`
          return apiClient(originalRequest)
        } else {
          console.log('❌ No refresh token available')
          throw new Error('No refresh token')
        }
      } catch (refreshError) {
        console.log('❌ Token refresh failed:', refreshError.response?.data || refreshError.message)
        // Refresh failed, redirect to login
        localStorage.removeItem('auth_token')
        localStorage.removeItem('access_token')
        localStorage.removeItem('refresh_token')
        localStorage.removeItem('user_data')  // Clear user data on auth failure
        window.location.href = '/auth/login'
        return Promise.reject(refreshError)
      }
    }

    return Promise.reject(error)
  }
)

// Utility function to check authentication status
export const checkAuthStatus = () => {
  const authToken = localStorage.getItem('auth_token')
  const refreshToken = localStorage.getItem('refresh_token')
  const userData = localStorage.getItem('user_data')
  
  console.log('🔍 Authentication Status Check:', {
    hasAuthToken: !!authToken,
    hasRefreshToken: !!refreshToken,
    hasUserData: !!userData,
    authTokenLength: authToken?.length || 0,
    refreshTokenLength: refreshToken?.length || 0,
    userData: userData ? JSON.parse(userData) : null
  })
  
  return {
    isAuthenticated: !!(authToken && refreshToken),
    tokens: { authToken, refreshToken },
    userData: userData ? JSON.parse(userData) : null
  }
}

export default apiClient 