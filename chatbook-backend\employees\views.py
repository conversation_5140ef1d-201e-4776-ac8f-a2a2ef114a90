from django.shortcuts import render, redirect, get_object_or_404
from django.views.generic import ListView, DetailView, CreateView, UpdateView, DeleteView, TemplateView
from django.urls import reverse_lazy
from django.contrib.auth.mixins import LoginRequiredMixin
from django.utils import timezone
from django.db.models import Avg, Count, Q
from django.http import JsonResponse, HttpResponse, Http404
from icalendar import Calendar, Event
from datetime import datetime, timedelta
import pytz

from services.models import EmployeeService
from .models import Employee
from .forms import EmployeeForm

class EmployeeListView(LoginRequiredMixin, ListView):
    model = Employee
    template_name = 'employees/employee_list.html'
    context_object_name = 'employees'
    
    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Handle search query
        search_query = self.request.GET.get('search', None)
        if search_query:
            queryset = queryset.filter(
                Q(first_name__icontains=search_query) |
                Q(last_name__icontains=search_query) |
                Q(stylist_level__icontains=search_query) |
                Q(email__icontains=search_query)
            )
            
        # Handle filtering
        level_filter = self.request.GET.get('level', None)
        if level_filter:
            queryset = queryset.filter(stylist_level=level_filter)
            
        status_filter = self.request.GET.get('status', None)
        if status_filter:
            queryset = queryset.filter(is_active=(status_filter == 'active'))
            
        booking_filter = self.request.GET.get('booking', None)
        if booking_filter == 'yes':
            queryset = queryset.filter(accept_online_bookings=True)
        elif booking_filter == 'no':
            queryset = queryset.filter(accept_online_bookings=False)
            
        # Handle sorting
        sort_by = self.request.GET.get('sort', 'name')
        if sort_by == 'name':
            queryset = queryset.order_by('first_name', 'last_name')
        elif sort_by == 'level':
            queryset = queryset.order_by('stylist_level')
            
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # Basic statistics
        employees = self.get_queryset()
        context['active_employees'] = employees.filter(is_active=True).count()
        context['total_employees'] = employees.count()
        
        # Online booking statistics
        context['online_booking_count'] = employees.filter(accept_online_bookings=True).count()
        
        # Add search/filter parameters to context for form persistence
        context['search_query'] = self.request.GET.get('search', '')
        context['level_filter'] = self.request.GET.get('level', '')
        context['status_filter'] = self.request.GET.get('status', '')
        context['booking_filter'] = self.request.GET.get('booking', '')
        context['sort_by'] = self.request.GET.get('sort', 'name')
        
        return context

class EmployeeDetailView(LoginRequiredMixin, DetailView):
    model = Employee
    template_name = 'employees/employee_detail.html'
    context_object_name = 'employee'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['today'] = timezone.now().date()
        return context

class EmployeeCreateView(LoginRequiredMixin, CreateView):
    model = Employee
    form_class = EmployeeForm
    template_name = 'employees/employee_form.html'
    success_url = reverse_lazy('employees:employee_list')
    
    def form_valid(self, form):
        # Handle work days as a list from multiple checkbox inputs
        work_days = self.request.POST.getlist('work_days')
        if work_days:
            form.instance.work_days = ','.join(work_days)
        
        # Process any other special fields as needed
        return super().form_valid(form)

class EmployeeUpdateView(LoginRequiredMixin, UpdateView):
    model = Employee
    form_class = EmployeeForm
    template_name = 'employees/employee_form.html'
    
    def get_success_url(self):
        return reverse_lazy('employees:employee_detail', kwargs={'pk': self.object.pk})
    
    def form_valid(self, form):
        # Handle work days as a list from multiple checkbox inputs
        work_days = self.request.POST.getlist('work_days')
        if work_days:
            form.instance.work_days = ','.join(work_days)
            
        # Process any other special fields as needed
        return super().form_valid(form)
    
    def get_form(self, form_class=None):
        form = super().get_form(form_class)
        # Pre-populate the work days checkboxes
        if self.object.work_days:
            form.initial['work_days'] = self.object.work_days.split(',')
        return form

class EmployeeDeleteView(LoginRequiredMixin, DeleteView):
    model = Employee
    template_name = 'employees/employee_confirm_delete.html'
    success_url = reverse_lazy('employees:employee_list')
    context_object_name = 'employee'

class EmployeeScheduleView(LoginRequiredMixin, DetailView):
    model = Employee
    template_name = 'employees/employee_schedule.html'
    context_object_name = 'employee'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['today'] = timezone.now().date()
        
        # You can add more data here like appointments, time off records, etc.
        # Get employee appointments for the next 2 weeks
        start_date = timezone.now().date()
        end_date = start_date + timezone.timedelta(days=14)
        
        # If you have appointment data, you can load it here
        # context['appointments'] = Appointment.objects.filter(
        #     employee=self.object,
        #     start_time__date__gte=start_date,
        #     start_time__date__lte=end_date
        # ).order_by('start_time')
        
        # Format the dates for the calendar display
        context['start_date'] = start_date.strftime('%Y-%m-%d')
        context['end_date'] = end_date.strftime('%Y-%m-%d')
        
        return context
    
    def post(self, request, *args, **kwargs):
        """Handle AJAX requests for updating employee schedule"""
        employee = self.get_object()
        
        # Handle different types of updates
        action = request.POST.get('action')
        
        if action == 'update_settings':
            # Update employee schedule settings
            work_days = request.POST.getlist('work_days[]', [])
            if work_days:
                employee.work_days = ','.join(work_days)
            
            # Update work hours
            work_hours_start = request.POST.get('work_hours_start')
            work_hours_end = request.POST.get('work_hours_end')
            if work_hours_start:
                employee.work_hours_start = work_hours_start
            if work_hours_end:
                employee.work_hours_end = work_hours_end
            
            # Update other settings
            is_accepting_appointments = request.POST.get('is_accepting_appointments') == 'true'
            employee.accept_online_bookings = is_accepting_appointments
            
            employee.save()
            
            return JsonResponse({'status': 'success', 'message': 'Settings updated successfully'})
        
        elif action == 'add_time_off':
            # Add time off record
            # Implement time off record creation here
            return JsonResponse({'status': 'success', 'message': 'Time off added successfully'})
        
        # Handle other actions as needed
        
        return JsonResponse({'status': 'error', 'message': 'Invalid action'})


def employee_ics_feed(request, ics_token):
    """
    Generate ICS calendar feed for an employee's schedule.
    
    URL format: /employees/calendar/{ics_token}.ics
    
    This provides a one-way, non-real-time calendar feed that employees
    can subscribe to in their preferred calendar app (Google Calendar,
    Apple Calendar, Outlook, etc.).
    """
    try:
        # Get employee by ICS token
        employee = get_object_or_404(Employee, ics_feed_token=ics_token, is_active=True)
    except Employee.DoesNotExist:
        raise Http404("Calendar feed not found")
    
    # Create calendar object
    cal = Calendar()
    cal.add('prodid', f'-//Chatbook//Employee Calendar {employee.user.first_name} {employee.user.last_name}//EN')
    cal.add('version', '2.0')
    cal.add('calscale', 'GREGORIAN')
    cal.add('method', 'PUBLISH')
    cal.add('x-wr-calname', f"{employee.user.first_name} {employee.user.last_name} - Work Schedule")
    cal.add('x-wr-caldesc', f"Work schedule for {employee.user.first_name} {employee.user.last_name} at {employee.business.name}")
    
    # Get timezone for the business
    business_timezone = pytz.timezone(getattr(employee.business, 'timezone', 'UTC'))
    
    # Get appointments for past 6 months + next 6 months (1 year total)
    start_date = timezone.now().date() - timedelta(days=180)  # Past 6 months  
    end_date = timezone.now().date() + timedelta(days=180)    # Next 6 months
    
    # Import here to avoid circular imports
    from appointments.models import Appointment
    
    # Get employee's appointments
    appointments = Appointment.objects.filter(
        employee=employee,
        start_time__date__gte=start_date,
        start_time__date__lte=end_date,
        status__in=['confirmed', 'accepted', 'checked_in', 'service_started']  # Only confirmed appointments
    ).select_related('customer', 'employee').prefetch_related('appointment_services__service')
    
    # Add each appointment as an event
    for appointment in appointments:
        event = Event()
        
        # Generate unique ID for this event
        event.add('uid', f'chatbook-appointment-{appointment.id}@{request.get_host()}')
        
        # Convert to business timezone
        start_time = appointment.start_time.astimezone(business_timezone)
        end_time = appointment.end_time.astimezone(business_timezone)
        
        event.add('dtstart', start_time)
        event.add('dtend', end_time)
        
        # Create appointment summary
        try:
            customer_user = appointment.customer.customer.user
            customer_name = f"{customer_user.first_name} {customer_user.last_name}"
        except AttributeError:
            customer_name = "Unknown Customer"

        # Get services for this appointment
        services = [aps.service.name for aps in appointment.appointment_services.all()]
        services_text = ', '.join(services) if services else 'Service'

        event.add('summary', f"{customer_name} - {services_text}")

        # Add description with appointment details
        description_parts = [
            f"Customer: {customer_name}",
            f"Services: {services_text}",
            f"Status: {appointment.get_status_display()}",
        ]

        try:
            customer_user = appointment.customer.customer.user
            if hasattr(customer_user, 'phone_number') and customer_user.phone_number:
                description_parts.append(f"Phone: {customer_user.phone_number}")
            
            if hasattr(customer_user, 'email') and customer_user.email:
                description_parts.append(f"Email: {customer_user.email}")
        except AttributeError:
            pass  # Skip if customer data is missing
            
        if appointment.notes_from_customer:
            description_parts.append(f"Notes: {appointment.notes_from_customer}")
            
        event.add('description', '\n'.join(description_parts))
        
        # Add location (business address)
        if hasattr(employee.business, 'address') and employee.business.address:
            event.add('location', employee.business.address)
        else:
            event.add('location', employee.business.name)
        
        # Set status
        if appointment.status in ['confirmed', 'accepted']:
            event.add('status', 'CONFIRMED')
        elif appointment.status in ['cancelled']:
            event.add('status', 'CANCELLED')
        else:
            event.add('status', 'TENTATIVE')
        
        # Add timestamps
        event.add('created', appointment.created_at)
        event.add('last-modified', appointment.updated_at)
        
        # Set transparency (busy/free)
        event.add('transp', 'OPAQUE')  # Mark as busy time
        
        # Add categories
        event.add('categories', ['WORK', 'APPOINTMENT'])
        
        cal.add_component(event)
    
    # Generate ICS content
    ics_content = cal.to_ical().decode('utf-8')
    
    # Create HTTP response
    response = HttpResponse(ics_content, content_type='text/calendar; charset=utf-8')
    response['Content-Disposition'] = f'attachment; filename="{employee.user.first_name}-{employee.user.last_name}-schedule.ics"'
    response['Cache-Control'] = 'no-cache, must-revalidate'
    response['Expires'] = 'Sat, 26 Jul 1997 05:00:00 GMT'
    
    return response
