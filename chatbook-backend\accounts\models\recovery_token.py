from django.db import models
from django.utils import timezone
from django.db.models import CheckConstraint, Q, F
from django.db.models.functions import Now
from django.contrib.auth.tokens import PasswordResetTokenGenerator
from datetime import timedelta
from django.core.exceptions import ValidationError
import uuid

class RecoveryTokenGenerator(PasswordResetTokenGenerator):
    """
    Custom token generator for recovery tokens based on Django's PasswordResetTokenGenerator
    """
    def _make_hash_value(self, user, timestamp):
        return f"{user.pk}{timestamp}{user.password}"

recovery_token_generator = RecoveryTokenGenerator()

class RecoveryTokenQuerySet(models.QuerySet):
    def active(self):
        """Return only active (unused and not expired) tokens"""
        return self.filter(used=False, expires_at__gt=timezone.now())
    
    def expired(self):
        """Return only expired tokens"""
        return self.filter(expires_at__lte=timezone.now())
    
    def used(self):
        """Return only used tokens"""
        return self.filter(used=True)
    
    def cleanup_candidates(self):
        """Return tokens that should be cleaned up (expired or used)"""
        return self.filter(Q(expires_at__lt=timezone.now()) | Q(used=True))

class RecoveryTokenManager(models.Manager):
    def get_queryset(self):
        return RecoveryTokenQuerySet(self.model, using=self._db)
    
    def active(self):
        """Return only active tokens"""
        return self.get_queryset().active()
    
    def for_user_and_token(self, user, token_hash):
        """Find an active token for a specific user and token hash"""
        return self.active().filter(user=user, token_hash=token_hash).first()
        
    def cleanup_tokens(self, older_than_days=None):
        """
        Clean up expired and used tokens.
        If older_than_days is specified, also remove tokens older than that period regardless of status.
        
        Returns:
            int: Number of tokens deleted
        """
        conditions = Q(expires_at__lt=timezone.now()) | Q(used=True)
        
        if older_than_days is not None:
            cutoff = timezone.now() - timedelta(days=older_than_days)
            conditions |= Q(created_at__lt=cutoff)
            
        query = self.get_queryset().filter(conditions)
        count = query.count()
        query.delete()
        return count

class RecoveryToken(models.Model):
    """
    Model for storing password recovery tokens with enhanced security
    """
    id = models.UUIDField(
        primary_key=True, 
        default=uuid.uuid4, 
        editable=False,
        help_text="UUID used to prevent enumeration attacks and increase security."
    )
    user = models.ForeignKey('accounts.User', on_delete=models.CASCADE, related_name='recovery_tokens')
    token_hash = models.CharField(max_length=64, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    expires_at = models.DateTimeField()
    used = models.BooleanField(default=False)
    used_at = models.DateTimeField(null=True, blank=True)
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.CharField(max_length=255, blank=True)

    # Use the custom manager
    objects = RecoveryTokenManager()

    class Meta:
        verbose_name = 'Recovery Token'
        verbose_name_plural = 'Recovery Tokens'
        ordering = ['-created_at']
        
        constraints = [
            CheckConstraint(
                check=Q(expires_at__gt=F('created_at')),
                name='rt_expires_after_creation'
            )
        ]
        
        indexes = [
            # Index for looking up tokens where still valid
            models.Index(
                fields=['token_hash'],
                name='rt_valid_token_idx',
                condition=Q(used=False) & Q(expires_at__gt=Now())
            ),
            # Composite index for user's tokens with used status
            models.Index(fields=['user', 'used'], name='rt_user_unused_idx'),
            # Keep the expires_at index for cleanup operations
            models.Index(fields=['expires_at'], name='rt_expiry_idx'),
        ]

    def __str__(self):
        return f"Recovery token for {self.user.email}"

    def is_valid(self):
        return not self.used and timezone.now() < self.expires_at

    def clean(self):
        """Validate model fields."""
        super().clean()
        # Validate expires_at > created_at
        if self.expires_at and self.created_at and self.expires_at <= self.created_at:
            raise ValidationError({"expires_at": "Expiration time must be after creation time."})

    def invalidate(self, ip_address=None, user_agent=None):
        self.used = True
        self.used_at = timezone.now()
        self.ip_address = ip_address
        self.user_agent = user_agent
        self.save() 