function AppointmentFilters({ 
  dateRang<PERSON><PERSON><PERSON>er, 
  setDateRange<PERSON>ilter, 
  handleFilter<PERSON>hange, 
  clearFilters, 
  sortOrder, 
  toggleSort,
  onCollapseAll,
  hasExpandedTransactions
}) {
  return (
    <div className="mb-6 flex items-end justify-between">
      <div className="max-w-xs">
        <label className="block text-xs font-medium text-gray-700 mb-1">Date Range</label>
        <select
          value={dateRangeFilter}
          onChange={(e) => {
            setDateRangeFilter(e.target.value)
            handleFilterChange()
          }}
          className="block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-sm"
        >
          <option value="all">All Time</option>
          <option value="thisMonth">This Month</option>
          <option value="lastMonth">Last Month</option>
          <option value="last3Months">Last 3 Months</option>
          <option value="thisYear">This Year</option>
        </select>
      </div>
      
      <div className="flex items-center space-x-2">
        <button
          onClick={toggleSort}
          className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
        >
          <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d={sortOrder === 'desc' ? "M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12" : "M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4"} />
          </svg>
          {sortOrder === 'desc' ? 'Newest First' : 'Oldest First'}
        </button>
        {hasExpandedTransactions && (
          <button
            onClick={onCollapseAll}
            className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 15l7-7 7 7" />
            </svg>
            Collapse All
          </button>
        )}
        {dateRangeFilter !== 'all' && (
          <button
            onClick={clearFilters}
            className="inline-flex items-center px-3 py-1 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
          >
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
            Clear Filters
          </button>
        )}
      </div>
    </div>
  )
}

export default AppointmentFilters 