from django.contrib import admin
from django.utils.html import format_html
from django.utils.translation import gettext_lazy as _
from .models import FormTemplate, FormSubmission, Signature, BusinessRequiredForm


@admin.register(FormTemplate)
class FormTemplateAdmin(admin.ModelAdmin):
    """Admin interface for FormTemplate model"""
    
    list_display = ['name', 'document_type', 'status', 'business', 'created_at', 'updated_at']
    list_filter = ['status', 'document_type', 'business', 'created_at']
    search_fields = ['name', 'document_type', 'business__name']
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        (None, {
            'fields': ('name', 'document_type', 'status', 'business')
        }),
        ('Content', {
            'fields': ('content',),
            'classes': ('wide',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('business')


@admin.register(FormSubmission)
class FormSubmissionAdmin(admin.ModelAdmin):
    """Admin interface for FormSubmission model"""
    
    list_display = [
        'form_template_name', 'business_name', 'customer_name', 
        'submitted_by_name', 'status', 'created_at'
    ]
    list_filter = ['status', 'form_template', 'created_at']
    search_fields = [
        'form_template__name', 'business_customer__business__name', 
        'business_customer__customer__user__email', 'submitted_by__email'
    ]
    readonly_fields = ['created_at', 'updated_at']
    
    fieldsets = (
        (None, {
            'fields': ('form_template', 'business_customer', 'submitted_by', 'status')
        }),
        ('Submission Content', {
            'fields': ('content',),
            'classes': ('wide',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'form_template', 'business_customer__business', 'business_customer__customer__user', 'submitted_by'
        )
    
    def form_template_name(self, obj):
        return obj.form_template.name if obj.form_template else '-'
    form_template_name.short_description = 'Form Template'
    form_template_name.admin_order_field = 'form_template__name'
    
    def business_name(self, obj):
        return obj.business_customer.business.name if obj.business_customer else '-'
    business_name.short_description = 'Business'
    business_name.admin_order_field = 'business_customer__business__name'
    
    def customer_name(self, obj):
        if obj.business_customer and obj.business_customer.customer and obj.business_customer.customer.user:
            user = obj.business_customer.customer.user
            return f"{user.first_name} {user.last_name}".strip() or user.email
        return '-'
    customer_name.short_description = 'Customer'
    customer_name.admin_order_field = 'business_customer__customer__user__email'
    
    def submitted_by_name(self, obj):
        if obj.submitted_by:
            return f"{obj.submitted_by.first_name} {obj.submitted_by.last_name}".strip() or obj.submitted_by.email
        return '-'
    submitted_by_name.short_description = 'Submitted By'
    submitted_by_name.admin_order_field = 'submitted_by__email'


@admin.register(Signature)
class SignatureAdmin(admin.ModelAdmin):
    """Admin interface for Signature model"""
    
    list_display = [
        'signer_display', 'signer_type', 'business', 
        'form_submission_name', 'created_at'
    ]
    list_filter = ['signer_type', 'business', 'created_at']
    search_fields = [
        'business__name', 'user__email', 'customer__user__email', 
        'employee__user__email', 'form_submission__form_template__name'
    ]
    readonly_fields = ['created_at', 'updated_at', 'signature_preview']
    
    fieldsets = (
        (None, {
            'fields': ('business', 'user', 'signer_type')
        }),
        ('Signer Details', {
            'fields': ('customer', 'employee'),
            'description': 'Select the appropriate signer based on signer type'
        }),
        ('Signature Data', {
            'fields': ('signature_data', 'signature_preview', 'form_submission'),
            'classes': ('wide',)
        }),
        ('Metadata', {
            'fields': ('ip_address', 'user_agent'),
            'classes': ('collapse',)
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related(
            'business', 'user', 'customer__user', 'employee__user', 
            'form_submission__form_template'
        )
    
    def signer_display(self, obj):
        if obj.signer_type == 'customer' and obj.customer:
            return f"{obj.customer.user.first_name} {obj.customer.user.last_name}".strip() or obj.customer.user.email
        elif obj.signer_type == 'employee' and obj.employee:
            return f"{obj.employee.user.first_name} {obj.employee.user.last_name}".strip() or obj.employee.user.email
        return obj.user.email
    signer_display.short_description = 'Signer'
    
    def form_submission_name(self, obj):
        if obj.form_submission and obj.form_submission.form_template:
            return obj.form_submission.form_template.name
        return '-'
    form_submission_name.short_description = 'Form Submission'
    form_submission_name.admin_order_field = 'form_submission__form_template__name'
    
    def signature_preview(self, obj):
        if obj.signature_data:
            # Truncate signature data for preview
            preview = obj.signature_data[:100] + '...' if len(obj.signature_data) > 100 else obj.signature_data
            return format_html('<code>{}</code>', preview)
        return '-'
    signature_preview.short_description = 'Signature Data Preview'


@admin.register(BusinessRequiredForm)
class BusinessRequiredFormAdmin(admin.ModelAdmin):
    """Admin interface for BusinessRequiredForm model"""
    
    list_display = [
        'business', 'form_template', 'is_required', 'required_for_new_customers',
        'required_for_existing_customers', 'order', 'created_at'
    ]
    list_filter = [
        'is_required', 'required_for_new_customers', 'required_for_existing_customers',
        'business', 'created_at'
    ]
    search_fields = [
        'business__name', 'form_template__name', 'form_template__document_type'
    ]
    readonly_fields = ['created_at', 'updated_at']
    ordering = ['business', 'order', 'form_template__name']
    
    fieldsets = (
        (None, {
            'fields': ('business', 'form_template', 'order')
        }),
        ('Requirements', {
            'fields': (
                'is_required', 
                'required_for_new_customers', 
                'required_for_existing_customers'
            ),
            'description': _('Configure when this form is required for customers')
        }),
        ('Timestamps', {
            'fields': ('created_at', 'updated_at'),
            'classes': ('collapse',)
        }),
    )
    
    def get_queryset(self, request):
        return super().get_queryset(request).select_related('business', 'form_template')
