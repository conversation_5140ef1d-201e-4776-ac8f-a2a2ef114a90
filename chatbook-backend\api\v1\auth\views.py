from rest_framework import status
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from django.conf import settings
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.utils.translation import gettext_lazy as _
from accounts.models import UserDevice
# Business relations
from business.models import Business, BusinessUser, BusinessCustomer

@method_decorator(csrf_exempt, name='dispatch')
class LoginView(APIView):
    permission_classes = []  # Allow unauthenticated access

    def post(self, request, *args, **kwargs):
        email = request.data.get('email')
        password = request.data.get('password')
        device_info = request.data.get('device_info', {})

        if not email or not password:
            return Response({
                'error': _('Both email and password are required.')
            }, status=status.HTTP_400_BAD_REQUEST)

        # Authenticate user
        user = authenticate(request, username=email, password=password)

        if user is None:
            return Response({
                'error': _('Invalid credentials.')
            }, status=status.HTTP_401_UNAUTHORIZED)

        if not user.is_active:
            return Response({
                'error': _('This account is inactive.')
            }, status=status.HTTP_401_UNAUTHORIZED)

        # Handle MFA if enabled
        if user.mfa_enabled:
            # Generate and send MFA code based on preferred method
            mfa_token = self._generate_mfa_token(user)
            return Response({
                'requires_mfa': True,
                'mfa_method': user.preferred_mfa_method,
                'mfa_token': mfa_token
            }, status=status.HTTP_200_OK)

        # Track device and IP
        ip_address = request.META.get('REMOTE_ADDR')
        user.last_login_ip = ip_address

        if device_info:
            device = UserDevice.objects.create(
                user=user,
                device_type=device_info.get('type'),
                device_name=device_info.get('name'),
                device_id=device_info.get('id'),
                ip_address=ip_address
            )
            user.last_login_device = device.device_name

        user.save()

        # ---------------------------------------------------------------
        # Determine businesses the user belongs to and log to console
        # ---------------------------------------------------------------
        business_qs = Business.objects.none()

        role_names = user.get_role_names()

        # Customer role (or no explicit role defaults to customer)
        if 'customer' in role_names or len(role_names) == 0:
            if hasattr(user, 'customer_profile'):
                business_ids = BusinessCustomer.objects.filter(
                    customer=user.customer_profile
                ).values_list('business_id', flat=True)
                business_qs = Business.objects.filter(id__in=business_ids)
        else:
            # Admin / employee path via BusinessUser table
            business_ids = BusinessUser.objects.filter(
                user=user
            ).values_list('business_id', flat=True)
            business_qs = Business.objects.filter(id__in=business_ids)

        # Convert to simple list for logging & response
        businesses_payload = [
            {
                'id': str(b.id),
                'name': b.name,
                'slug': b.slug,
            }
            for b in business_qs
        ]

        # Print to terminal so developers can see the mapping immediately
        print(f"[LOGIN] User {user.email} belongs to businesses: {[b['id'] for b in businesses_payload]}")

        # Generate tokens
        refresh = RefreshToken.for_user(user)

        # Add user info and roles to response
        return Response({
            'access': str(refresh.access_token),
            'refresh': str(refresh),
            'user': {
                'id': str(user.id),
                'email': user.email,
                'phone_number': str(user.phone_number),
                'first_name': user.first_name,
                'last_name': user.last_name,
                'is_verified': user.is_verified,
                'user_type': user.user_type,
                'roles': user.get_role_names(),
                'mfa_enabled': user.mfa_enabled,
                'preferred_mfa_method': user.preferred_mfa_method
            },
            'businesses': businesses_payload
        })

    def _generate_mfa_token(self, user):
        """Generate MFA token and send via preferred method"""
        from accounts.recovery import generate_recovery_token

        token = generate_recovery_token(user)

        if user.preferred_mfa_method == 'email':
            # Send email with token
            user.email_user(
                _('Your Login Code'),
                f'Your verification code is: {token}'
            )
        elif user.preferred_mfa_method == 'sms':
            # Send SMS with token
            if user.phone_number:
                # Implement SMS sending here
                pass

        return token

@method_decorator(csrf_exempt, name='dispatch')
class VerifyMFAView(APIView):
    permission_classes = []

    def post(self, request, *args, **kwargs):
        email = request.data.get('email')
        mfa_token = request.data.get('mfa_token')
        verification_code = request.data.get('verification_code')
        device_info = request.data.get('device_info', {})

        if not all([email, mfa_token, verification_code]):
            return Response({
                'error': _('Email, MFA token and verification code are required.')
            }, status=status.HTTP_400_BAD_REQUEST)

        # Verify the token matches
        from accounts.recovery import verify_recovery_token
        from accounts.models import User
        user = User.objects.filter(email=email).first()

        if not user:
            return Response({
                'error': _('Invalid user.')
            }, status=status.HTTP_400_BAD_REQUEST)

        if not verify_recovery_token(user, mfa_token, verification_code):
            return Response({
                'error': _('Invalid verification code.')
            }, status=status.HTTP_400_BAD_REQUEST)

        # Track device and IP
        ip_address = request.META.get('REMOTE_ADDR')
        user.last_login_ip = ip_address

        if device_info:
            device = UserDevice.objects.create(
                user=user,
                device_type=device_info.get('type'),
                device_name=device_info.get('name'),
                device_id=device_info.get('id'),
                ip_address=ip_address
            )
            user.last_login_device = device.device_name

        user.save()

        # Generate tokens after successful MFA
        refresh = RefreshToken.for_user(user)

        return Response({
            'access': str(refresh.access_token),
            'refresh': str(refresh),
            'user': {
                'id': str(user.id),
                'email': user.email,
                'phone_number': str(user.phone_number),
                'first_name': user.first_name,
                'last_name': user.last_name,
                'is_verified': user.is_verified,
                'user_type': user.user_type,
                'roles': user.get_role_names(),
                'mfa_enabled': user.mfa_enabled,
                'preferred_mfa_method': user.preferred_mfa_method
            }
        })
