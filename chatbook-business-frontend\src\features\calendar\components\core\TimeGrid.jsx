import React, { useEffect } from 'react'
import CurrentTimeIndicator from './CurrentTimeIndicator'

/**
 * TimeGrid - Reusable time column component for calendar views
 */
const TimeGrid = ({
  timeSlots,
  config,
  children,
  selectedDate,
  weekDays = [],
  displayMode = 'day',
  selectedEmployees = []
}) => {
  // Update container height when config changes
  useEffect(() => {
    // Force a small delay to ensure CSS variables are updated
    const timer = setTimeout(() => {
      // Trigger a minimal reflow to ensure proper rendering
      if (document.querySelector('.calendar-scroll-container')) {
        const element = document.querySelector('.calendar-scroll-container')
        element.style.height = element.style.height
      }
    }, 10)
    
    return () => clearTimeout(timer)
  }, [config.timeResolution, config.gridHeight])

  return (
    <div 
      className="flex-1" 
      style={{ 
        minHeight: `${config.gridHeight * (config.displayHourEnd - config.displayHourStart)}px`,
        maxHeight: '100vh'
      }}
    >
      <div className="flex min-h-full">
        {/* Time column */}
        <div className="w-16 border-r border-gray-200 bg-gray-50 flex-shrink-0 relative">
          {timeSlots.map((slot, index) => (
            <div 
              key={index} 
              className="relative border-b border-gray-100"
              style={{ height: `${config.gridHeight / (60 / config.timeResolution)}px` }}
            >
              {slot.label && (
                <span className="absolute -top-2 right-1 text-[10px] leading-none text-gray-500 select-none">
                  {slot.label}
                </span>
              )}
            </div>
          ))}
        </div>

        {/* Content columns (passed as children) */}
        <div className="flex-1 relative">
          {children}
          
          {/* Current Time Indicator */}
          <CurrentTimeIndicator
            config={config}
            selectedDate={selectedDate}
            weekDays={weekDays}
            displayMode={displayMode}
            selectedEmployees={selectedEmployees}
          />
        </div>
      </div>
    </div>
  )
}

export default TimeGrid 