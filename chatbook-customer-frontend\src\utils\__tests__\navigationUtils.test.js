/**
 * Tests for navigation utilities
 * Basic validation tests for the centralized navigation logic
 */

import { canProceedToBookingStep, getBookingRedirectRoute } from '../navigationUtils';

// Mock data for testing
const mockUser = {
  id: 1,
  email: '<EMAIL>'
};

const mockBookingDataComplete = {
  selectedService: { id: 1, name: 'Test Service' },
  selectedEmployee: { id: 1, display_name: 'Test Employee' },
  selectedDate: '2024-01-15',
  selectedTime: '10:00',
  consentData: { consentAgreed: true },
  customerInfo: {
    name: 'Test User',
    email: '<EMAIL>',
    phone: '************'
  }
};

const mockBookingDataPartial = {
  selectedService: { id: 1, name: 'Test Service' },
  selectedEmployee: null,
  selectedDate: null,
  selectedTime: null,
  consentData: { consentAgreed: false },
  customerInfo: { name: '', email: '', phone: '' }
};

// Test canProceedToBookingStep function
describe('canProceedToBookingStep', () => {
  test('should return false if no user', () => {
    const result = canProceedToBookingStep('consent', mockBookingDataComplete, null);
    expect(result).toBe(false);
  });

  test('should return false for consent step with incomplete booking data', () => {
    const result = canProceedToBookingStep('consent', mockBookingDataPartial, mockUser);
    expect(result).toBe(false);
  });

  test('should return true for consent step with complete booking data', () => {
    const result = canProceedToBookingStep('consent', mockBookingDataComplete, mockUser);
    expect(result).toBe(true);
  });

  test('should return false for review step without consent', () => {
    const dataWithoutConsent = {
      ...mockBookingDataComplete,
      consentData: { consentAgreed: false }
    };
    const result = canProceedToBookingStep('review', dataWithoutConsent, mockUser);
    expect(result).toBe(false);
  });

  test('should return true for review step with consent', () => {
    const result = canProceedToBookingStep('review', mockBookingDataComplete, mockUser);
    expect(result).toBe(true);
  });
});

// Test getBookingRedirectRoute function
describe('getBookingRedirectRoute', () => {
  test('should return login route if no user', () => {
    const result = getBookingRedirectRoute(mockBookingDataComplete, null);
    expect(result).toBe('/login');
  });

  test('should return home route if cannot proceed to consent', () => {
    const result = getBookingRedirectRoute(mockBookingDataPartial, mockUser);
    expect(result).toBe('/');
  });

  test('should return consent form route if can proceed to consent but not review', () => {
    const dataWithoutConsent = {
      ...mockBookingDataComplete,
      consentData: { consentAgreed: false }
    };
    const result = getBookingRedirectRoute(dataWithoutConsent, mockUser);
    expect(result).toBe('/consent-form');
  });

  test('should return review route if can proceed to review', () => {
    const result = getBookingRedirectRoute(mockBookingDataComplete, mockUser);
    expect(result).toBe('/review-booking');
  });
});

// Manual test runner (since we don't have Jest setup)
export const runTests = () => {
  console.group('🧪 Navigation Utils Tests');
  
  try {
    // Test 1: No user
    const test1 = canProceedToBookingStep('consent', mockBookingDataComplete, null);
    console.log('✅ Test 1 (No user):', test1 === false ? 'PASS' : 'FAIL');

    // Test 2: Incomplete booking data
    const test2 = canProceedToBookingStep('consent', mockBookingDataPartial, mockUser);
    console.log('✅ Test 2 (Incomplete data):', test2 === false ? 'PASS' : 'FAIL');

    // Test 3: Complete booking data
    const test3 = canProceedToBookingStep('consent', mockBookingDataComplete, mockUser);
    console.log('✅ Test 3 (Complete data):', test3 === true ? 'PASS' : 'FAIL');

    // Test 4: Redirect routes
    const test4a = getBookingRedirectRoute(mockBookingDataComplete, null);
    console.log('✅ Test 4a (No user redirect):', test4a === '/login' ? 'PASS' : 'FAIL');

    const test4b = getBookingRedirectRoute(mockBookingDataPartial, mockUser);
    console.log('✅ Test 4b (Incomplete data redirect):', test4b === '/' ? 'PASS' : 'FAIL');

    const dataWithoutConsent = {
      ...mockBookingDataComplete,
      consentData: { consentAgreed: false }
    };
    const test4c = getBookingRedirectRoute(dataWithoutConsent, mockUser);
    console.log('✅ Test 4c (No consent redirect):', test4c === '/consent-form' ? 'PASS' : 'FAIL');

    const test4d = getBookingRedirectRoute(mockBookingDataComplete, mockUser);
    console.log('✅ Test 4d (Complete data redirect):', test4d === '/review-booking' ? 'PASS' : 'FAIL');

    console.log('🎉 All tests completed!');
  } catch (error) {
    console.error('❌ Test error:', error);
  }
  
  console.groupEnd();
};

// Export for use in browser console
if (typeof window !== 'undefined') {
  window.runNavigationTests = runTests;
}
