"""
Management command to synchronize BusinessSettings and OnlineBookingRules
"""
from django.core.management.base import BaseCommand
from django.utils.translation import gettext_lazy as _
from business.models import BusinessSettings, OnlineBookingRules, Business

class Command(BaseCommand):
    help = _("Synchronize BusinessSettings and OnlineBookingRules to prevent data discrepancies")

    def add_arguments(self, parser):
        parser.add_argument(
            '--business-id',
            type=int,
            help=_('ID of a specific business to synchronize (optional)')
        )

    def handle(self, *args, **options):
        self.stdout.write(self.style.SUCCESS("Starting synchronization between BusinessSettings and OnlineBookingRules..."))
        
        # Get all businesses or a specific one
        if options['business_id']:
            try:
                businesses = Business.objects.filter(id=options['business_id'])
                if not businesses.exists():
                    self.stdout.write(self.style.ERROR(f"No business found with ID {options['business_id']}"))
                    return
            except Exception as e:
                self.stdout.write(self.style.ERROR(f"Error finding business: {e}"))
                return
        else:
            businesses = Business.objects.all()
        
        count_updated = 0
        count_created = 0
        
        for business in businesses:
            self.stdout.write(f"Processing business: {business.name} (ID: {business.id})")
            
            # Check for BusinessSettings
            try:
                business_settings = BusinessSettings.objects.get(business=business)
                self.stdout.write(f"  Found BusinessSettings (ID: {business_settings.id})")
                
                # Check if OnlineBookingRules exist
                try:
                    booking_rules = OnlineBookingRules.objects.get(business=business)
                    self.stdout.write(f"  Found OnlineBookingRules (ID: {booking_rules.id})")
                    
                    # Check for discrepancies
                    bs_window = business_settings.booking_window
                    bs_lead = business_settings.booking_lead_time
                    obr_window = booking_rules.max_days_in_advance
                    obr_lead = booking_rules.min_hours_before * 60  # Convert to minutes
                    
                    if bs_window != obr_window or bs_lead != obr_lead:
                        self.stdout.write(self.style.WARNING(f"  Discrepancy found!"))
                        self.stdout.write(f"    BusinessSettings: window={bs_window}, lead={bs_lead}")
                        self.stdout.write(f"    OnlineBookingRules: window={obr_window}, lead={obr_lead/60} hours")
                        
                        # Update OnlineBookingRules from BusinessSettings
                        booking_rules.max_days_in_advance = bs_window
                        booking_rules.min_hours_before = bs_lead // 60
                        booking_rules.save()
                        self.stdout.write(self.style.SUCCESS(f"  Updated OnlineBookingRules to match BusinessSettings"))
                        count_updated += 1
                    else:
                        self.stdout.write(self.style.SUCCESS(f"  No discrepancy found"))
                        
                except OnlineBookingRules.DoesNotExist:
                    self.stdout.write(f"  No OnlineBookingRules found, creating...")
                    # Create OnlineBookingRules from BusinessSettings
                    OnlineBookingRules.objects.create(
                        business=business,
                        max_days_in_advance=business_settings.booking_window,
                        min_hours_before=business_settings.booking_lead_time // 60
                    )
                    self.stdout.write(self.style.SUCCESS(f"  Created OnlineBookingRules from BusinessSettings"))
                    count_created += 1
                    
            except BusinessSettings.DoesNotExist:
                self.stdout.write(f"  No BusinessSettings found, checking for OnlineBookingRules...")
                
                # Check if OnlineBookingRules exist
                try:
                    booking_rules = OnlineBookingRules.objects.get(business=business)
                    self.stdout.write(f"  Found OnlineBookingRules (ID: {booking_rules.id})")
                    
                    # Create BusinessSettings from OnlineBookingRules
                    BusinessSettings.objects.create(
                        business=business,
                        booking_window=booking_rules.max_days_in_advance,
                        booking_lead_time=booking_rules.min_hours_before * 60
                    )
                    self.stdout.write(self.style.SUCCESS(f"  Created BusinessSettings from OnlineBookingRules"))
                    count_created += 1
                    
                except OnlineBookingRules.DoesNotExist:
                    self.stdout.write(f"  No OnlineBookingRules found, creating default settings for both...")
                    
                    # Create default settings for both
                    bs = BusinessSettings.objects.create(business=business)
                    OnlineBookingRules.objects.create(
                        business=business,
                        max_days_in_advance=bs.booking_window,
                        min_hours_before=bs.booking_lead_time // 60
                    )
                    self.stdout.write(self.style.SUCCESS(f"  Created default settings for both models"))
                    count_created += 2
        
        self.stdout.write(self.style.SUCCESS(f"Synchronization completed! Updated {count_updated} records, created {count_created} records.")) 