import { useQuery } from '@tanstack/react-query';
import { getEmployees } from '../api/bookingApi';
import { queryKeys } from '../lib/queryClient';

export const useEmployees = () => {
  return useQuery({
    queryKey: queryKeys.booking.employees,
    queryFn: getEmployees,
    staleTime: 15 * 60 * 1000, // 15 minutes
    gcTime: 60 * 60 * 1000, // 1 hour
    retry: 2,
    refetchOnWindowFocus: false,
  });
};

export const useEmployeesWithFallback = () => {
  const query = useEmployees();
  
  // Provide fallback data if API fails
  const fallbackEmployees = [
    {
      id: 1,
      full_name: 'Clément Lash',
      display_name: 'C<PERSON><PERSON>',
      title: 'Senior Lash Artist',
      profile_image: '/images/clement-profile.jpg',
      bio: 'Expert lash artist with 5+ years of experience',
      specialties: ['Classic Extensions', 'Volume Extensions']
    },
    {
      id: 2,
      full_name: '<PERSON>',
      display_name: '<PERSON>',
      title: 'Lash Technician',
      profile_image: '/images/sarah-profile.jpg',
      bio: 'Certified lash technician specializing in natural looks',
      specialties: ['Classic Extensions', 'Lash Lifts']
    }
  ];

  return {
    ...query,
    data: query.data || (query.isError ? fallbackEmployees : undefined),
    isLoading: query.isLoading && !query.isError,
  };
};
