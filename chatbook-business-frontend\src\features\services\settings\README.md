# ServiceMenu Component Structure

This directory contains the modularized ServiceMenu component, broken down into logical modules for better maintainability and reusability.

## Directory Structure

```
ServiceMenu/
├── index.jsx                  # Main ServiceMenu component
├── constants.js               # Color palette and API endpoints
├── README.md                  # This documentation file
├── components/
│   ├── CategoryModal.jsx      # Category creation/editing modal
│   ├── ServiceModal.jsx       # Service creation/editing modal
│   ├── ServiceTree.jsx        # Tree view of categories and services
│   └── ServiceDetails.jsx     # Detail view panel (placeholder)
├── hooks/
│   └── useServiceData.js      # Custom hook for data management
└── utils/
    └── formatters.js          # Utility functions for formatting

```

## Component Breakdown

### Main Components

- **index.jsx**: Main component that orchestrates all sub-components
- **ServiceTree.jsx**: Handles the tree view of categories and services with expand/collapse functionality
- **CategoryModal.jsx**: Modal for creating and editing service categories
- **ServiceModal.jsx**: Modal for creating and editing services
- **ServiceDetails.jsx**: Placeholder for detailed view (to be implemented)

### Hooks

- **useServiceData.js**: Custom hook that manages:
  - Data fetching for categories and services
  - Loading and error states
  - CRUD operations (create, read, update, delete)
  - Service filtering by category

### Utilities

- **formatters.js**: Utility functions for:
  - Price formatting (currency)
  - Duration formatting (minutes to hours/minutes)

### Constants

- **constants.js**: Contains:
  - Color palette for categories
  - API endpoint definitions

## Benefits of This Structure

1. **Separation of Concerns**: Each component has a single responsibility
2. **Reusability**: Components can be reused in other parts of the application
3. **Maintainability**: Easier to find and modify specific functionality
4. **Testability**: Smaller components are easier to test
5. **Performance**: Components can be lazy-loaded if needed

## Usage

The main component is exported from `index.jsx` and can be imported as:

```javascript
import ServiceMenu from './ServiceMenu'
```

## Future Enhancements

1. **ServiceDetails.jsx**: Implement detailed view for selected categories/services
2. **Add-on Management**: Create components for managing service add-ons
3. **Bulk Operations**: Add functionality for bulk editing/deleting
4. **Search/Filter**: Add search and filtering capabilities
5. **Drag & Drop**: Implement drag-and-drop for reordering

## Dependencies

- React 18+
- @heroicons/react
- API client for backend communication
- Tailwind CSS for styling 