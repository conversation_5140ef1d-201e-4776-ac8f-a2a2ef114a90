function CreditCardSection({ customer }) {
  // Parse credit card info from backend data
  const parseCreditCardInfo = (creditCardString) => {
    if (!creditCardString || creditCardString.trim() === '') {
      return null
    }
    
    // Handle different formats like "ending in 8616", "Visa ****1234", etc.
    const cleanString = creditCardString.toLowerCase()
    
    let lastFour = ''
    let type = 'Unknown'
    
    // Extract last 4 digits
    const numberMatch = creditCardString.match(/\d{4}/)
    if (numberMatch) {
      lastFour = numberMatch[0]
    }
    
    // Determine card type
    if (cleanString.includes('visa')) {
      type = 'Visa'
    } else if (cleanString.includes('master') || cleanString.includes('mc')) {
      type = 'MasterCard'
    } else if (cleanString.includes('amex') || cleanString.includes('american express')) {
      type = 'American Express'
    } else if (cleanString.includes('discover')) {
      type = 'Discover'
    }
    
    return {
      id: 1,
      type: type,
      lastFour: lastFour,
      expiryDate: 'Hidden', // Not available in imported data
      isDefault: true,
      cardHolder: customer.name,
      originalInfo: creditCardString
    }
  }
  
  // Get credit cards from backend data
  const creditCards = []
  if (customer.creditCard) {
    const parsedCard = parseCreditCardInfo(customer.creditCard)
    if (parsedCard) {
      creditCards.push(parsedCard)
    }
  }

  const getCardIcon = (type) => {
    switch (type) {
      case 'Visa':
        return (
          <div className="w-8 h-8 bg-blue-600 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">V</span>
          </div>
        )
      case 'MasterCard':
        return (
          <div className="w-8 h-8 bg-red-600 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">MC</span>
          </div>
        )
      case 'American Express':
        return (
          <div className="w-8 h-8 bg-green-600 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">AE</span>
          </div>
        )
      case 'Discover':
        return (
          <div className="w-8 h-8 bg-orange-600 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">D</span>
          </div>
        )
      case 'Unknown':
        return (
          <div className="w-8 h-8 bg-purple-600 rounded flex items-center justify-center">
            <span className="text-white text-xs font-bold">?</span>
          </div>
        )
      default:
        return (
          <div className="w-8 h-8 bg-gray-400 rounded flex items-center justify-center">
            <svg className="w-4 h-4 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
          </div>
        )
    }
  }

  return (
    <div className="bg-white rounded-xl shadow-sm p-8">
      <div className="flex items-center justify-between mb-6">
        <h2 className="text-lg font-semibold text-gray-900">Payment Methods</h2>
        <span className="text-sm text-gray-500">View Only</span>
      </div>

      {creditCards.length === 0 ? (
        <div className="text-center py-12">
          <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
            </svg>
          </div>
          <p className="text-gray-500 text-sm">No payment methods on file</p>
        </div>
      ) : (
        <div className="space-y-4">
          {creditCards.map((card) => (
            <div
              key={card.id}
              className={`relative p-4 border rounded-xl ${
                card.isDefault 
                  ? 'border-blue-200 bg-blue-50' 
                  : 'border-gray-200 bg-gray-50'
              }`}
            >
              {card.isDefault && (
                <div className="absolute -top-2 -right-2">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-600 text-white">
                    Default
                  </span>
                </div>
              )}

              <div className="flex items-center space-x-4">
                {getCardIcon(card.type)}
                
                <div className="flex-1">
                  <div className="flex items-center space-x-2">
                    <span className="text-sm font-medium text-gray-900">
                      {card.type}
                    </span>
                    <span className="text-sm text-gray-600">
                      {card.lastFour ? `•••• •••• •••• ${card.lastFour}` : card.originalInfo}
                    </span>
                  </div>
                  <div className="flex items-center space-x-4 mt-1">
                    <span className="text-xs text-gray-500">
                      {card.originalInfo}
                    </span>
                    <span className="text-xs text-gray-500">
                      {card.cardHolder}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      <div className="mt-6 p-4 bg-gray-50 rounded-lg">
        <div className="flex items-start space-x-3">
          <svg className="w-5 h-5 text-gray-400 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
          <div>
            <p className="text-sm font-medium text-gray-900">Customer Payment Information</p>
            <p className="text-xs text-gray-600 mt-1">
              Payment information is encrypted and securely stored. Only masked card details are visible for privacy protection.
            </p>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CreditCardSection 