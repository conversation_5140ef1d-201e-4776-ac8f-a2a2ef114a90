#!/usr/bin/env python
import os
import sys
import django
from django.db import transaction

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from services.models import StyleGroup, Service, LashServiceSuggestionRule
from business.models import Business

def get_business():
    """Get the Clement Lash business"""
    try:
        business = Business.objects.get(name="<PERSON> Lash")
        print(f'Found business: {business.name}')
        return business
    except Business.DoesNotExist:
        raise Exception("Business 'Clement Lash' not found. Please create it first.")

def get_services(business):
    """Get all active services for the business, organized by style group and category"""
    services = Service.objects.filter(business=business, is_active=True)

    service_map = {}

    # Get all style groups that have services
    style_groups = StyleGroup.objects.filter(services__business=business, services__is_active=True).distinct()

    print(f"Found style groups for {business.name}: {[sg.name for sg in style_groups]}")

    for style_group in style_groups:
        service_map[style_group.name] = {}

        # Get services for this style group
        group_services = services.filter(style_group=style_group)

        # Organize by category
        service_map[style_group.name]['fullset'] = group_services.filter(category__name="Lash Fullset").first()
        service_map[style_group.name]['2week'] = group_services.filter(category__name="2-Week Lash Refill").first()
        service_map[style_group.name]['3week'] = group_services.filter(category__name="3-Week Lash Refill").first()
        service_map[style_group.name]['4week'] = group_services.filter(category__name="4-Week Lash Refill").first()

        print(f"  {style_group.name}:")
        for category, service in service_map[style_group.name].items():
            if service:
                print(f"    {category}: {service.short_name} (ID: {service.id})")
            else:
                print(f"    {category}: NOT FOUND")

    return service_map

def clean_existing_rules(business):
    """Remove existing lash service suggestion rules for this business"""
    existing_rules = LashServiceSuggestionRule.objects.filter(business=business)
    count = existing_rules.count()
    
    if count > 0:
        print(f"Removing {count} existing lash service suggestion rules...")
        existing_rules.delete()
        print("✅ Existing rules cleaned up.")
    else:
        print("No existing lash service suggestion rules found.")

def create_service_progression_rules(business, service_map):
    """Create service progression rules based on style groups and timing"""
    rules_created = []

    print("\nCreating lash service suggestion rules...")

    # Get all style groups
    style_groups = StyleGroup.objects.filter(services__business=business, services__is_active=True).distinct()

    # Create rules per style group (not per individual service)
    for style_group in style_groups:
        style_group_name = style_group.name
        target_services = service_map.get(style_group_name)

        if not target_services:
            print(f"⚠️  Skipping {style_group_name} - no service mapping found")
            continue

        print(f"\nCreating rules for {style_group_name} style group...")

        # Skip if any target service is missing
        if not all([target_services['fullset'], target_services['2week'], target_services['3week'], target_services['4week']]):
            print(f"⚠️  Skipping {style_group_name} - missing target services")
            continue

        # Rule 1: After any Classic service → Classic 2-week refill (0-16 days)
        rule1 = LashServiceSuggestionRule.objects.create(
            business=business,
            last_service_style_group=style_group,
            next_service=target_services['2week'],
            min_days_since=0,
            max_days_since=17  # exclusive, so 0-16 days inclusive
        )
        rules_created.append(rule1)
        print(f"✅ Created rule: After any {style_group_name} service → {target_services['2week'].short_name} (0-16 days)")

        # Rule 2: After any Classic service → Classic 3-week refill (17-23 days)
        rule2 = LashServiceSuggestionRule.objects.create(
            business=business,
            last_service_style_group=style_group,
            next_service=target_services['3week'],
            min_days_since=17,
            max_days_since=24  # exclusive, so 17-23 days inclusive
        )
        rules_created.append(rule2)
        print(f"✅ Created rule: After any {style_group_name} service → {target_services['3week'].short_name} (17-23 days)")

        # Rule 3: After any Classic service → Classic 4-week refill (24-30 days)
        rule3 = LashServiceSuggestionRule.objects.create(
            business=business,
            last_service_style_group=style_group,
            next_service=target_services['4week'],
            min_days_since=24,
            max_days_since=31  # exclusive, so 24-30 days inclusive
        )
        rules_created.append(rule3)
        print(f"✅ Created rule: After any {style_group_name} service → {target_services['4week'].short_name} (24-30 days)")

        # Rule 4: After any Classic service → Classic fullset (>30 days - reset cycle)
        rule4 = LashServiceSuggestionRule.objects.create(
            business=business,
            last_service_style_group=style_group,
            next_service=target_services['fullset'],
            min_days_since=31,
            max_days_since=90  # up to 3 months
        )
        rules_created.append(rule4)
        print(f"✅ Created rule: After any {style_group_name} service → {target_services['fullset'].short_name} (>30 days)")

    return rules_created

def create_lash_service_suggestion_rules():
    """Main function to create lash service suggestion rules"""
    print("Creating Lash Service Suggestion Rules for Clement Lash")
    print("=" * 70)
    
    try:
        with transaction.atomic():
            # Get the business
            business = get_business()
            
            # Get services organized by type
            service_map = get_services(business)
            
            # Clean existing rules
            clean_existing_rules(business)
            
            # Create new rules
            rules = create_service_progression_rules(business, service_map)
            
            print(f"\n✅ Successfully created {len(rules)} lash service suggestion rules!")
            print(f"\nTotal rules created: {len(rules)}")
            
            return rules
            
    except Exception as e:
        print(f"❌ Error creating lash service suggestion rules: {str(e)}")
        raise

def main():
    """Entry point for the script"""
    try:
        rules = create_lash_service_suggestion_rules()
        print(f"\n🎉 Lash service suggestion rules setup completed successfully!")
        print(f"Created {len(rules)} rules for Clement Lash business.")
        
    except Exception as e:
        print(f"\n💥 Script failed: {str(e)}")
        sys.exit(1)

if __name__ == '__main__':
    main()
