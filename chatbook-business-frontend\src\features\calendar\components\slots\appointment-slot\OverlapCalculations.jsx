/**
 * OverlapCalculations - Handles overlap detection and positioning for appointments
 */
export const useOverlapCalculations = (appointment, allAppointments, employeeId, currentDate, displayMode) => {
  
  // Check if two appointments overlap in time
  const doAppointmentsOverlap = (apt1, apt2) => {
    const start1 = new Date(apt1.start)
    const end1 = new Date(apt1.end || apt1.endTime)
    const start2 = new Date(apt2.start)
    const end2 = new Date(apt2.end || apt2.endTime)
    
    // Check if appointments overlap (start1 < end2 && start2 < end1)
    return start1 < end2 && start2 < end1
  }
  
  // Get appointments for the same day and employee
  const getSameDayEmployeeAppointments = () => {
    const appointmentDate = new Date(appointment.start)
    const appointmentDay = appointmentDate.toDateString()
    const appointmentEmployeeId = appointment.employeeId
    
    return allAppointments.filter(apt => {
      if (!apt || apt.id === appointment.id) return false
      
      const aptDate = new Date(apt.start)
      const aptDay = aptDate.toDateString()
      const aptEmployeeId = apt.employeeId
      
      // Same day and same employee
      return aptDay === appointmentDay && aptEmployeeId === appointmentEmployeeId
    })
  }
  
  // Calculate overlap info for this appointment
  const calculateOverlapInfo = () => {
    const sameDayAppointments = getSameDayEmployeeAppointments()
    
    // Find all appointments that overlap with this one
    const overlappingAppointments = sameDayAppointments.filter(apt => 
      doAppointmentsOverlap(appointment, apt)
    )
    
    if (overlappingAppointments.length === 0) {
      return {
        hasOverlap: false,
        overlapCount: 0,
        overlapIndex: 0,
        overlappingAppointments: []
      }
    }
    
    // Include the current appointment in the overlap group
    const allOverlappingAppointments = [appointment, ...overlappingAppointments]
    
    // Sort by start time to determine order
    allOverlappingAppointments.sort((a, b) => {
      const startA = new Date(a.start)
      const startB = new Date(b.start)
      return startA - startB
    })
    
    // Find the index of the current appointment in the sorted list
    const currentIndex = allOverlappingAppointments.findIndex(apt => apt.id === appointment.id)
    
    return {
      hasOverlap: true,
      overlapCount: allOverlappingAppointments.length,
      overlapIndex: currentIndex,
      overlappingAppointments: allOverlappingAppointments
    }
  }
  
  // Calculate width and left position for overlapping appointments
  const calculateOverlapPositioning = (overlapInfo) => {
    const baseWidth = 90 // 90% of the time grid width
    
    if (!overlapInfo.hasOverlap) {
      return {
        width: `${baseWidth}%`,
        left: '0%',
        zIndex: 1,
        marginRight: '0px'
      }
    }
    
    const { overlapCount, overlapIndex } = overlapInfo
    const widthPercentage = baseWidth / overlapCount
    const leftPercentage = (baseWidth / overlapCount) * overlapIndex
    
    return {
      width: `${widthPercentage}%`,
      left: `${leftPercentage}%`,
      zIndex: overlapIndex + 1, // Higher z-index for later appointments
      marginRight: overlapIndex < overlapCount - 1 ? '2px' : '0px' // Small gap between overlapping appointments
    }
  }
  
  // Main calculation function
  const calculateOverlap = () => {
    const overlapInfo = calculateOverlapInfo()
    const positioning = calculateOverlapPositioning(overlapInfo)
    
    return {
      ...overlapInfo,
      positioning
    }
  }
  
  return {
    calculateOverlap,
    doAppointmentsOverlap,
    getSameDayEmployeeAppointments,
    calculateOverlapInfo,
    calculateOverlapPositioning
  }
} 