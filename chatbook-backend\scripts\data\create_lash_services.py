#!/usr/bin/env python
import os
import sys
import django
from decimal import Decimal
from datetime import timed<PERSON><PERSON>
from django.db import transaction
from django.db import connection
from django.db.models import Q

# Add project root to Python path
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.append(project_root)

# Set up Django environment
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'settings')
django.setup()

from services.models import StyleGroup, ServiceCategory, Service, AddOn, ServiceAddOn
from business.models import Business
from accounts.models import User, Role


def get_style_groups():
    """Get or create style groups"""
    style_groups = {}

    style_group_data = [
        {'name': 'Classic', 'description': 'Classic lash extensions with natural look', 'display_order': 10},
        {'name': 'Styling', 'description': 'Styling lash extensions with enhanced curl', 'display_order': 20},
        {'name': 'Volume', 'description': 'Volume lash extensions for fuller look', 'display_order': 30},
        {'name': 'Real Mink', 'description': 'Premium real mink lash extensions', 'display_order': 40},
    ]

    for group_data in style_group_data:
        style_group, created = StyleGroup.objects.get_or_create(
            name=group_data['name'],
            defaults={
                'description': group_data['description'],
                'display_order': group_data['display_order']
            }
        )
        style_groups[group_data['name']] = style_group

    return style_groups

def check_and_drop_stale_tables():
    """
    Check for stale tables with '_old' suffix and ask user if they should be dropped
    """
    with connection.cursor() as cursor:
        # Get list of tables
        if connection.vendor == 'sqlite':
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name LIKE '%_old';")
            tables = [row[0] for row in cursor.fetchall()]
        elif connection.vendor == 'postgresql':
            cursor.execute("SELECT tablename FROM pg_catalog.pg_tables WHERE tablename LIKE '%_old' AND schemaname = 'public';")
            tables = [row[0] for row in cursor.fetchall()]
        else:
            print("Unsupported database vendor")
            return
        
        if not tables:
            print("No stale tables found with '_old' suffix")
            return
        
        print(f"Found {len(tables)} potentially stale tables:")
        for i, table in enumerate(tables, 1):
            # Try to count rows in the table
            try:
                cursor.execute(f"SELECT COUNT(*) FROM {table};")
                row_count = cursor.fetchone()[0]
                print(f"{i}. {table} - Contains {row_count} rows")
            except:
                print(f"{i}. {table} - Unable to count rows")
        
        # Ask user if they want to drop specific tables
        print("\nDo you want to drop these tables? They appear to be old/stale tables")
        print("This action will permanently delete these tables and their data!")
        response = input("Type 'Y' to drop all, or comma-separated numbers (e.g., '1,3') to drop specific tables: ")
        
        if response.strip().upper() == 'Y':
            tables_to_drop = tables
        else:
            try:
                indices = [int(i.strip()) - 1 for i in response.split(',') if i.strip()]
                tables_to_drop = [tables[i] for i in indices if 0 <= i < len(tables)]
            except:
                print("Invalid input. No tables will be dropped.")
                return
        
        # Drop selected tables
        if tables_to_drop:
            print(f"\nDropping {len(tables_to_drop)} tables:")
            for table in tables_to_drop:
                try:
                    cursor.execute(f"DROP TABLE {table};")
                    print(f"✓ Successfully dropped {table}")
                except Exception as e:
                    print(f"✗ Failed to drop {table}: {str(e)}")
            
            # Commit changes
            connection.commit()
            print("\nTable drop operation completed.")
        else:
            print("No tables selected for dropping.")

def drop_table(table_name):
    """Drop a specific table from the database"""
    try:
        with connection.cursor() as cursor:
            cursor.execute(f"DROP TABLE IF EXISTS {table_name};")
            connection.commit()
        print(f"Successfully dropped table {table_name}")
        return True
    except Exception as e:
        print(f"Error dropping table {table_name}: {str(e)}")
        return False

def find_service_references(service_id):
    """
    Find all references to a service across the database
    """
    print(f"\n=== FINDING REFERENCES TO SERVICE ID {service_id} ===")
    
    try:
        # Get the service details
        service = Service.objects.get(id=service_id)
        print(f"Service: {service.name} (ID: {service.id}, Business: {service.business.name})")
    except Service.DoesNotExist:
        print(f"Service with ID {service_id} does not exist")
        return
    
    # Check for ServiceAddOn references
    service_addons = ServiceAddOn.objects.filter(service_id=service_id)
    if service_addons.exists():
        print(f"\nFound {service_addons.count()} ServiceAddOn references:")
        for sa in service_addons:
            print(f"  - ServiceAddOn ID: {sa.id}, AddOn: {sa.addon.name}")
    else:
        print("\nNo ServiceAddOn references found")
    
    # Check for AppointmentService references
    try:
        from appointments.models import AppointmentService
        appt_services = AppointmentService.objects.filter(service_id=service_id)
        if appt_services.exists():
            print(f"\nFound {appt_services.count()} AppointmentService references:")
            for aps in appt_services:
                print(f"  - AppointmentService ID: {aps.id}, Appointment: {aps.appointment.id}, "
                      f"Customer: {aps.appointment.customer}, Date: {aps.appointment.start_time}")
        else:
            print("\nNo AppointmentService references found")
    except (ImportError, django.core.exceptions.AppRegistryNotReady):
        print("\nCouldn't check AppointmentService references (model not available)")
    
    # Check for Appointment references (via many-to-many)
    try:
        from appointments.models import Appointment
        appointments = Appointment.objects.filter(services=service_id)
        if appointments.exists():
            print(f"\nFound {appointments.count()} Appointment references (via M2M):")
            for appt in appointments:
                print(f"  - Appointment ID: {appt.id}, Customer: {appt.customer}, Date: {appt.start_time}")
        else:
            print("\nNo Appointment references found (via M2M)")
    except (ImportError, django.core.exceptions.AppRegistryNotReady):
        print("\nCouldn't check Appointment references (model not available)")
    
    # Check for EmployeeService references
    try:
        from services.models import EmployeeService
        emp_services = EmployeeService.objects.filter(service_id=service_id)
        if emp_services.exists():
            print(f"\nFound {emp_services.count()} EmployeeService references:")
            for es in emp_services:
                print(f"  - EmployeeService ID: {es.id}, Employee: {es.employee}")
        else:
            print("\nNo EmployeeService references found")
    except (ImportError, django.core.exceptions.AppRegistryNotReady):
        print("\nCouldn't check EmployeeService references (model not available)")
    
    # Check for StylistLevelService references
    try:
        from services.models import StylistLevelService
        stylist_services = StylistLevelService.objects.filter(service_id=service_id)
        if stylist_services.exists():
            print(f"\nFound {stylist_services.count()} StylistLevelService references:")
            for sls in stylist_services:
                print(f"  - StylistLevelService ID: {sls.id}, StylistLevel: {sls.stylist_level}")
        else:
            print("\nNo StylistLevelService references found")
    except (ImportError, django.core.exceptions.AppRegistryNotReady):
        print("\nCouldn't check StylistLevelService references (model not available)")
    
    # Check for raw database references using SQL query
    with connection.cursor() as cursor:
        print("\nChecking for additional references via SQL:")
        tables = []
        
        # Get a list of all tables in the database
        if connection.vendor == 'sqlite':
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
            tables = [row[0] for row in cursor.fetchall()]
        elif connection.vendor == 'postgresql':
            cursor.execute("SELECT tablename FROM pg_catalog.pg_tables WHERE schemaname = 'public';")
            tables = [row[0] for row in cursor.fetchall()]
        
        # Exclude Django internal tables and tables we've already checked
        exclude_tables = [
            'django_migrations', 'django_session', 'django_admin_log', 
            'auth_permission', 'django_content_type', 'services_service',
            'services_serviceaddon', 'services_employeeservice', 
            'services_stylistlevelservice', 'appointments_appointmentservice'
        ]
        tables = [t for t in tables if t not in exclude_tables]
        
        # Check each table for columns that might reference service_id
        references_found = False
        for table in tables:
            # Get column names for this table
            try:
                if connection.vendor == 'sqlite':
                    cursor.execute(f"PRAGMA table_info({table});")
                    columns = [row[1] for row in cursor.fetchall()]
                elif connection.vendor == 'postgresql':
                    cursor.execute(f"SELECT column_name FROM information_schema.columns WHERE table_name = '{table}';")
                    columns = [row[0] for row in cursor.fetchall()]
                
                # Check potential service_id columns
                potential_columns = [
                    col for col in columns if 'service' in col.lower() or 
                    'service_id' == col.lower() or 'serviceid' == col.lower()
                ]
                
                for col in potential_columns:
                    try:
                        cursor.execute(f"SELECT COUNT(*) FROM {table} WHERE {col} = %s", [service_id])
                        count = cursor.fetchone()[0]
                        if count > 0:
                            references_found = True
                            print(f"  - Table: {table}, Column: {col}, References: {count}")
                    except Exception as e:
                        pass  # Ignore errors from invalid queries
            except Exception as e:
                print(f"  - Error checking table {table}: {str(e)}")
        
        if not references_found:
            print("  - No additional references found via SQL")
    
    print("\n=== REFERENCE SEARCH COMPLETE ===")

def delete_appointment_service_references(service_id):
    """Delete all AppointmentService references to a service"""
    try:
        from appointments.models import AppointmentService
        appt_services = AppointmentService.objects.filter(service_id=service_id)
        count = appt_services.count()
        if count > 0:
            print(f"Deleting {count} AppointmentService references to service ID {service_id}...")
            appt_services.delete()
            print(f"Successfully deleted {count} AppointmentService references")
            return True
        else:
            print(f"No AppointmentService references found for service ID {service_id}")
            return False
    except ImportError:
        print("Could not import AppointmentService model")
        return False

def reset_sequence(table_name):
    """Reset the SQLite sequence counter for a table"""
    with connection.cursor() as cursor:
        cursor.execute(f"DELETE FROM sqlite_sequence WHERE name='{table_name}'")
        print(f"Reset ID sequence for {table_name}")

def get_or_create_owner():
    """Get or create a business owner user"""
    try:
        owner = User.objects.get(email='<EMAIL>')
        print(f'Using existing admin user: {owner.email}')
        
        # Ensure they have the owner role
        owner_role, _ = Role.objects.get_or_create(name='owner')
        if not owner.roles.filter(name='owner').exists():
            owner.roles.add(owner_role)
            owner.save()
            print('Added owner role to admin user')
        
        return owner
    except User.DoesNotExist:
        raise Exception("Admin user not found. Please create an admin user first.")

def get_or_create_business():
    """Get the existing business"""
    try:
        business = Business.objects.get(name="Clement Lash")
        print(f'Found business: {business.name}')
        return business
    except Business.DoesNotExist:
        raise Exception("Business 'Clement Lash' not found. Please create it first.")

def clean_existing_services(business):
    """Remove existing services and related data"""
    print("Cleaning up existing service data...")
    
    # Import the appointment models here to avoid circular imports
    from django.db import connection
    try:
        from appointments.models import AppointmentService, AppointmentAddOn
        
        # First check if there are any appointments using these services
        appointment_services_count = AppointmentService.objects.filter(
            service__business=business
        ).count()
        
        if appointment_services_count > 0:
            print(f"WARNING: Found {appointment_services_count} appointment services referencing this business's services.")
            print("You will need to delete or update those appointments first.")
            return False
        
        appointment_addons_count = AppointmentAddOn.objects.filter(
            add_on__business=business
        ).count()
        
        if appointment_addons_count > 0:
            print(f"WARNING: Found {appointment_addons_count} appointment add-ons referencing this business's add-ons.")
            print("You will need to delete or update those appointments first.")
            return False
    except ImportError:
        print("Appointment models not available, skipping appointment checks.")
    
    # Check for other potential foreign key relationships
    try:
        # Delete ServiceAddOn records one by one
        service_addons = ServiceAddOn.objects.filter(service__business=business)
        print(f"Deleting {service_addons.count()} service add-on relationships...")
        for service_addon in service_addons:
            try:
                service_addon.delete()
            except Exception as e:
                print(f"Error deleting ServiceAddOn {service_addon.id}: {str(e)}")
                print("Continuing with other deletions...")
        
        # Check for EmployeeService records
        try:
            from services.models import EmployeeService
            employee_services = EmployeeService.objects.filter(service__business=business)
            print(f"Deleting {employee_services.count()} employee service records...")
            for emp_service in employee_services:
                try:
                    emp_service.delete()
                except Exception as e:
                    print(f"Error deleting EmployeeService {emp_service.id}: {str(e)}")
        except (ImportError, RuntimeError):
            print("EmployeeService model not available, skipping.")
            
        # Check for StylistLevelService records
        try:
            from services.models import StylistLevelService
            stylist_services = StylistLevelService.objects.filter(service__business=business)
            print(f"Deleting {stylist_services.count()} stylist level service records...")
            for stylist_service in stylist_services:
                try:
                    stylist_service.delete()
                except Exception as e:
                    print(f"Error deleting StylistLevelService {stylist_service.id}: {str(e)}")
        except (ImportError, RuntimeError):
            print("StylistLevelService model not available, skipping.")
        
        # Delete services one by one
        services = Service.objects.filter(business=business)
        print(f"Deleting {services.count()} services...")
        for service in services:
            try:
                service.delete()
            except Exception as e:
                print(f"Error deleting Service {service.id} ({service.name}): {str(e)}")
                print("You may need to manually check for references to this service.")
                # When an error occurs, run the diagnostic function
                find_service_references(service.id)
                return False
        
        # Delete add-ons one by one
        addons = AddOn.objects.filter(business=business)
        print(f"Deleting {addons.count()} add-ons...")
        for addon in addons:
            try:
                addon.delete()
            except Exception as e:
                print(f"Error deleting AddOn {addon.id} ({addon.name}): {str(e)}")
                print("You may need to manually check for references to this add-on.")
                return False
        
        # Delete categories one by one
        categories = ServiceCategory.objects.filter(business=business)
        print(f"Deleting {categories.count()} service categories...")
        for category in categories:
            try:
                category.delete()
            except Exception as e:
                print(f"Error deleting ServiceCategory {category.id} ({category.name}): {str(e)}")
        
        # Reset the ID sequences in SQLite
        try:
            reset_sequence('services_servicecategory')
            reset_sequence('services_service')
            reset_sequence('services_addon')
            reset_sequence('services_serviceaddon')
            
            try:
                reset_sequence('services_employeeservice')
                reset_sequence('services_stylistlevelservice')
            except:
                pass
        except Exception as e:
            print(f"Error resetting sequences: {str(e)}")
        
        print("Existing service data cleaned up successfully.")
        return True
        
    except Exception as e:
        print(f"Unexpected error during cleanup: {str(e)}")
        return False

def create_service_categories(business):
    """Create main service categories with specific colors"""
    categories = [
        {
            'name': 'Lash Fullset',
            'description': 'Full set of eyelash extensions',
            'order': 1,
            'color': '#B47E62',  # Brown
        },
        {
            'name': '2-Week Lash Refill',
            'description': 'Lash refill services within 2 weeks',
            'order': 2,
            'color': '#C3BFDF',  # Light Purple
        },
        {
            'name': '3-Week Lash Refill',
            'description': 'Lash refill services within 3 weeks',
            'order': 3,
            'color': '#7AA6BA',  # Light Blue
        },
        {
            'name': '4-Week Lash Refill',
            'description': 'Lash refill services within 4 weeks',
            'order': 4,
            'color': '#FDD628',  # Yellow
        },
        {
            'name': 'Other Services',
            'description': 'Additional lash services',
            'order': 5,
            'color': '#B668AA',  # Dark Purple
        },
        {
            'name': 'Add-on Services',
            'description': 'Optional add-on services for lash treatments',
            'order': 6,
            'color': '#FFFFFF',  # White
        },
    ]
    
    created_categories = {}
    for cat_data in categories:
        try:
            # First try to get the category if it exists
            category = ServiceCategory.objects.get(
                business=business,
                name=cat_data['name']
            )
            # Update existing category with new color if different
            if category.color != cat_data['color']:
                category.color = cat_data['color']
                category.save()
                print(f"Updated category color: {category.name} -> {cat_data['color']}")
            else:
                print(f"Using existing category: {category.name}")
        except ServiceCategory.DoesNotExist:
            # If it doesn't exist, create it with all required fields including color
            category = ServiceCategory.objects.create(
                business=business,
                name=cat_data['name'],
                description=cat_data['description'],
                order=cat_data['order'],
                color=cat_data['color'],
                is_active=True
            )
            print(f"Created category: {category.name} with color {cat_data['color']}")

        created_categories[cat_data['name']] = category
    
    return created_categories

def create_fullset_services(business, category, style_groups):
    """Create fullset services"""
    services = [
        {
            'name': 'Classic Lash Fullset(C01-C02)',
            'short_name': 'Classic Fullset',
            'style_group': style_groups['Classic'],
            'description': 'Classic eyelash extension service using C01-C02 lashes',
            'base_duration': timedelta(minutes=70),
            'base_price': Decimal('199.00'),
            'display_order': 10,
        },
        {
            'name': 'Styling Lash Fullset(S01-S08)',
            'short_name': 'Styling Fullset',
            'style_group': style_groups['Styling'],
            'description': 'Styled eyelash extension service using S01-S08 lashes',
            'base_duration': timedelta(minutes=105),
            'base_price': Decimal('229.00'),
            'display_order': 20,
        },
        {
            'name': 'Volume Fullset(Non-Mega)',
            'short_name': 'Volume Fullset',
            'style_group': style_groups['Volume'],
            'description': 'Volume eyelash extension service (non-mega volume)',
            'base_duration': timedelta(minutes=125),
            'base_price': Decimal('259.00'),
            'display_order': 30,
        },
        {
            'name': 'Premium Clément Real Mink Fullset',
            'short_name': 'Real Mink Fullset',
            'style_group': style_groups['Real Mink'],
            'description': 'Premium eyelash extension service using real mink lashes',
            'base_duration': timedelta(minutes=95),
            'base_price': Decimal('279.00'),
            'display_order': 40,
        }
    ]
    return create_services(business, services, category)

def create_2week_refill_services(business, category, style_groups):
    """Create 2-week refill services"""
    services = [
        {
            'name': 'Classic Within 2-week refill',
            'short_name': 'Classic 2-week refill',
            'style_group': style_groups['Classic'],
            'description': 'Classic eyelash extension refill service',
            'base_duration': timedelta(minutes=55),
            'base_price': Decimal('79.00'),
            'display_order': 10,
        },
        {
            'name': 'Styling Within 2-week refill',
            'short_name': 'Styling 2-week refill',
            'style_group': style_groups['Styling'],
            'description': 'Styled eyelash extension refill service',
            'base_duration': timedelta(minutes=70),
            'base_price': Decimal('89.00'),
            'display_order': 20,
        },
        {
            'name': 'Volume Within 2-week refill',
            'short_name': 'Volume 2-week refill',
            'style_group': style_groups['Volume'],
            'description': 'Volume eyelash extension refill service',
            'base_duration': timedelta(minutes=75),
            'base_price': Decimal('109.00'),
            'display_order': 30,
        },
        {
            'name': 'Premium Within 2-week refill',
            'short_name': 'Premium 2-week refill',
            'style_group': style_groups['Real Mink'],
            'description': 'Premium eyelash extension refill service',
            'base_duration': timedelta(minutes=55),
            'base_price': Decimal('119.00'),
            'display_order': 40,
        }
    ]
    return create_services(business, services, category)

def create_3week_refill_services(business, category, style_groups):
    """Create 3-week refill services"""
    services = [
        {
            'name': 'Classic Within 3-week refill',
            'short_name': 'Classic 3-week refill',
            'style_group': style_groups['Classic'],
            'description': 'Classic eyelash extension refill service',
            'base_duration': timedelta(minutes=60),
            'base_price': Decimal('99.00'),
            'display_order': 10,
        },
        {
            'name': 'Styling Within 3-week refill',
            'short_name': 'Styling 3-week refill',
            'style_group': style_groups['Styling'],
            'description': 'Styled eyelash extension refill service',
            'base_duration': timedelta(minutes=80),
            'base_price': Decimal('119.00'),
            'display_order': 20,
        },
        {
            'name': 'Volume Within 3-week refill',
            'short_name': 'Volume 3-week refill',
            'style_group': style_groups['Volume'],
            'description': 'Volume eyelash extension refill service',
            'base_duration': timedelta(minutes=85),
            'base_price': Decimal('139.00'),
            'display_order': 30,
        },
        {
            'name': 'Premium Within 3-week refill',
            'short_name': 'Premium 3-week refill',
            'style_group': style_groups['Real Mink'],
            'description': 'Premium eyelash extension refill service',
            'base_duration': timedelta(minutes=65),
            'base_price': Decimal('149.00'),
            'display_order': 40,
        }
    ]
    return create_services(business, services, category)

def create_4week_refill_services(business, category, style_groups):
    """Create 4-week refill services"""
    services = [
        {
            'name': 'Classic Within 4-week refill',
            'short_name': 'Classic 4-week refill',
            'style_group': style_groups['Classic'],
            'description': 'Classic eyelash extension refill service',
            'base_duration': timedelta(minutes=65),
            'base_price': Decimal('139.00'),
            'display_order': 10,
        },
        {
            'name': 'Styling Within 4-week refill',
            'short_name': 'Styling 4-week refill',
            'style_group': style_groups['Styling'],
            'description': 'Styled eyelash extension refill service',
            'base_duration': timedelta(minutes=90),
            'base_price': Decimal('159.00'),
            'display_order': 20,
        },
        {
            'name': 'Volume Within 4-week refill',
            'short_name': 'Volume 4-week refill',
            'style_group': style_groups['Volume'],
            'description': 'Volume eyelash extension refill service',
            'base_duration': timedelta(minutes=95),
            'base_price': Decimal('179.00'),
            'display_order': 30,
        },
        {
            'name': 'Premium Within 4-week refill',
            'short_name': 'Premium 4-week refill',
            'style_group': style_groups['Real Mink'],
            'description': 'Premium eyelash extension refill service',
            'base_duration': timedelta(minutes=75),
            'base_price': Decimal('189.00'),
            'display_order': 40,
        }
    ]
    return create_services(business, services, category)

def create_other_services(business, category):
    """Create other services"""
    services = [
        {
            'name': 'Glue test and Consultation',
            'short_name': 'Glue test',
            'description': 'Allergy test for eyelash extension adhesive and consultation',
            'base_duration': timedelta(minutes=15),
            'base_price': Decimal('0.00'),
            'display_order': 10,
        },
        {
            'name': 'Lash Removal',
            'short_name': 'Removal',
            'description': 'Professional removal of eyelash extensions',
            'base_duration': timedelta(minutes=25),
            'base_price': Decimal('0.00'),
            'display_order': 20,
        }
    ]
    return create_services(business, services, category)

def create_addon_services(business, category):
    """Create add-on services"""
    # Create AddOn items
    addons = [
        {
            'name': 'Bottom Lash(as an add-on service)',
            'short_name': 'Bottom',
            'description': 'Bottom lash extension service as an add-on',
            'base_duration': timedelta(minutes=25),
            'base_price': Decimal('29.00'),
            'display_order': 10,
        },
        {
            'name': 'Lash Removal',
            'short_name': 'Removal',
            'description': 'Professional removal of eyelash extensions',
            'base_duration': timedelta(minutes=25),
            'base_price': Decimal('00.00'),
            'display_order': 20,
        },
        {
            'name': 'Consulting Session',
            'short_name': 'Consult',
            'description': 'Professional consultation for lash services',
            'base_duration': timedelta(minutes=15),
            'base_price': Decimal('0.00'),
            'display_order': 30,
        }
    ]
    
    created_addons = []
    for addon_data in addons:
        try:
            # First try to get the add-on if it exists
            addon = AddOn.objects.get(
                business=business,
                name=addon_data['name']
            )
            # Update category and short_name if needed
            updated = False
            if not addon.category:
                addon.category = category
                updated = True
            
            if not addon.short_name and 'short_name' in addon_data:
                addon.short_name = addon_data['short_name']
                updated = True
                
            if updated:
                addon.save()
                print(f'Updated {addon.name} with category {category.name} and short_name {addon.short_name}')
            else:
                print(f'Found add-on: {addon.name}')
        except AddOn.DoesNotExist:
            # If it doesn't exist, create it with all required fields
            addon = AddOn.objects.create(
                business=business,
                name=addon_data['name'],
                short_name=addon_data.get('short_name', ''),
                description=addon_data.get('description', ''),
                base_price=addon_data['base_price'],
                base_duration=addon_data['base_duration'],
                category=category,
                is_active=True,
                display_order=addon_data['display_order']
            )
            print(f'Created add-on: {addon.name}')
            
        created_addons.append(addon)
    return created_addons

def create_services(business, services, category):
    """Create services in a category, updating fields if they already exist and differ"""
    created_services = []
    for service_data in services:
        # Determine buffer_time based on service name and rules
        name = service_data['name']
        if 'Classic' in name and 'Fullset' in name:
            buffer_time = timedelta(minutes=30)
        elif name == 'Glue test and Consultation':
            buffer_time = timedelta(minutes=0)
        elif 'Lash Removal' in name:
            buffer_time = timedelta(minutes=5)
        else:
            buffer_time = timedelta(minutes=15)

        try:
            # First try to get the service if it exists
            service = Service.objects.get(
                business=business,
                category=category,
                name=name
            )
            # Update fields if necessary
            updated = False
            if (service.description != service_data['description'] or
                service.base_price != service_data['base_price'] or
                service.base_duration != service_data['base_duration'] or
                service.buffer_time != buffer_time or
                service.display_order != service_data['display_order'] or
                (not service.short_name and 'short_name' in service_data) or
                (service_data.get('style_group') and service.style_group != service_data['style_group'])):
                
                service.description = service_data['description']
                service.base_price = service_data['base_price']
                service.base_duration = service_data['base_duration']
                service.buffer_time = buffer_time
                service.display_order = service_data['display_order']

                # Update short_name if it's empty and provided in data
                if not service.short_name and 'short_name' in service_data:
                    service.short_name = service_data['short_name']

                # Update style_group if provided
                if 'style_group' in service_data:
                    service.style_group = service_data['style_group']
                
                service.save()
                updated = True
                print(f"Updated service: {service.name}")
            else:
                print(f"Found service: {service.name}")
        except Service.DoesNotExist:
            # If it doesn't exist, create it with all required fields
            service = Service.objects.create(
                business=business,
                category=category,
                name=name,
                short_name=service_data.get('short_name', ''),
                style_group=service_data.get('style_group', None),
                description=service_data['description'],
                base_price=service_data['base_price'],
                base_duration=service_data['base_duration'],
                buffer_time=buffer_time,
                is_active=True,
                show_online=True,
                display_order=service_data['display_order']
            )
            print(f"Created service: {service.name}")
            
        created_services.append(service)
    return created_services

def create_lash_services(business_id=1):
    """Create all lash services"""
    business = Business.objects.get(id=business_id)

    # Get or create style groups
    style_groups = get_style_groups()

    # Clean up existing services first
    if not clean_existing_services(business):
        print("Aborting service creation due to existing appointment references.")
        return None

    # Create new service categories
    categories = create_service_categories(business)

    # Create services in each category
    fullset_services = create_fullset_services(business, categories['Lash Fullset'], style_groups)
    two_week_services = create_2week_refill_services(business, categories['2-Week Lash Refill'], style_groups)
    three_week_services = create_3week_refill_services(business, categories['3-Week Lash Refill'], style_groups)
    four_week_services = create_4week_refill_services(business, categories['4-Week Lash Refill'], style_groups)
    other_services = create_other_services(business, categories['Other Services'])
    
    # Create add-ons (as AddOn objects) in the add-on services category
    addon_items = create_addon_services(business, categories['Add-on Services'])
    
    # Collect all services that should have add-ons
    services_for_addons = []
    services_for_addons.extend(fullset_services)
    services_for_addons.extend(two_week_services)
    services_for_addons.extend(three_week_services)
    services_for_addons.extend(four_week_services)
    
    # Create service add-ons connections for all services except "Other Services"
    create_service_addons(services_for_addons, addon_items)
    
    print("\nCreated services and add-ons successfully!")
    return {
        'business': business,
        'categories': categories,
        'services': {
            'fullset': fullset_services,
            'two_week': two_week_services,
            'three_week': three_week_services,
            'four_week': four_week_services,
            'other': other_services
        },
        'addons': addon_items
    }

def create_service_addons(services, addons):
    """Create connections between services and their available add-ons"""
    for service in services:
        for addon in addons:
            try:
                # First check if connection already exists
                service_addon = ServiceAddOn.objects.get(
                    service=service,
                    addon=addon
                )
                # Update existing connection if needed
                if not service_addon.is_active:
                    service_addon.is_active = True
                    service_addon.save()
                    print(f"Reactivated {addon.name} for {service.name}")
                else:
                    print(f"Found add-on {addon.name} for {service.name}")
            except ServiceAddOn.DoesNotExist:
                # Create if it doesn't exist
                service_addon = ServiceAddOn.objects.create(
                    service=service,
                    addon=addon,
                    is_required=False,
                    display_order=addon.display_order,
                    is_active=True
                )
                print(f"Added {addon.name} to {service.name}")

if __name__ == '__main__':
    # Get business ID from command line args, or use default (1)
    if len(sys.argv) > 1 and sys.argv[1] == 'diagnose':
        if len(sys.argv) > 2:
            service_id = int(sys.argv[2])
            find_service_references(service_id)
        else:
            print("Please provide a service ID to diagnose, e.g., python create_lash_services.py diagnose 2")
    elif len(sys.argv) > 1 and sys.argv[1] == 'clean_appointments':
        if len(sys.argv) > 2:
            service_id = int(sys.argv[2])
            delete_appointment_service_references(service_id)
        else:
            print("Please provide a service ID to clean, e.g., python create_lash_services.py clean_appointments 2")
    elif len(sys.argv) > 1 and sys.argv[1] == 'drop_table':
        if len(sys.argv) > 2:
            table_name = sys.argv[2]
            drop_table(table_name)
        else:
            print("Please provide a table name to drop, e.g., python create_lash_services.py drop_table services_serviceaddon_old")
    elif len(sys.argv) > 1 and sys.argv[1] == 'check_stale_tables':
        check_and_drop_stale_tables()
    else:
        business_id = int(sys.argv[1]) if len(sys.argv) > 1 else 1
        print(f"Creating lash services for business ID: {business_id}")
        create_lash_services(business_id) 