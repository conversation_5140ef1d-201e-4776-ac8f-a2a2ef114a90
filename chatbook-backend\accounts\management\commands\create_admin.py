from django.core.management.base import BaseCommand
from django.contrib.auth import get_user_model
from django.contrib.auth.hashers import make_password

class Command(BaseCommand):
    help = 'Creates a superuser with the specified credentials'

    def handle(self, *args, **options):
        User = get_user_model()
        phone_number = '+1234567890'
        
        # Delete any existing users
        User.objects.all().delete()
        
        try:
            user = User.objects.create(
                phone_number=phone_number,
                first_name='Admin',
                last_name='User',
                is_staff=True,
                is_superuser=True,
                password=make_password('admin123!')
            )
            self.stdout.write(self.style.SUCCESS('Successfully created superuser'))
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'Failed to create superuser: {str(e)}')) 