import { useQuery, useQueryClient } from '@tanstack/react-query'
import { customerApi } from './customerApi'
import { CustomerDataService } from './CustomerDataService'

/**
 * React Query implementation of CustomerDataService
 * Uses @tanstack/react-query for intelligent caching and state management
 */
export class ReactQueryCustomerService extends CustomerDataService {
  constructor(options = {}) {
    super(options)
    this.queryClient = null // Will be set automatically when hooks are used
    this.defaultOptions = {
      staleTime: 10 * 60 * 1000, // 10 minutes
      cacheTime: 30 * 60 * 1000, // 30 minutes
      detailStaleTime: 5 * 60 * 1000, // 5 minutes for detailed data
      detailCacheTime: 15 * 60 * 1000, // 15 minutes for detailed data
      retry: 2,
      ...options
    }
  }



  /**
   * Get React hook for customers minimal data
   * @returns {Object} Hook object with { data, isLoading, error, refetch }
   */
  useCustomersMinimal() {
    // Get query client from React Query context for cache operations
    const queryClient = useQueryClient()
    
    // Set the query client for other methods if not already set
    if (!this.queryClient) {
      this.queryClient = queryClient
    }
    
    return useQuery({
      queryKey: ['customers-minimal'],
      queryFn: () => customerApi.getCustomersMinimal(),
      staleTime: this.defaultOptions.staleTime,
      cacheTime: this.defaultOptions.cacheTime,
      retry: this.defaultOptions.retry,
    })
  }

  /**
   * Get React hook for customer details
   * @param {string|number} customerId - Customer ID
   * @returns {Object} Hook object with { data, isLoading, error, refetch }
   */
  useCustomerDetails(customerId) {
    // Get query client from React Query context for cache operations
    const queryClient = useQueryClient()
    
    // Set the query client for other methods if not already set
    if (!this.queryClient) {
      this.queryClient = queryClient
    }
    
    return useQuery({
      queryKey: ['customer-details', customerId],
      queryFn: () => customerApi.getCustomer(customerId),
      enabled: !!customerId,
      staleTime: this.defaultOptions.detailStaleTime,
      cacheTime: this.defaultOptions.detailCacheTime,
    })
  }

  /**
   * Get React Query configuration for customers minimal data
   * @returns {Object} Query configuration
   */
  getCustomersMinimalQuery() {
    return {
      queryKey: ['customers-minimal'],
      queryFn: () => customerApi.getCustomersMinimal(),
      staleTime: this.defaultOptions.staleTime,
      cacheTime: this.defaultOptions.cacheTime,
      retry: this.defaultOptions.retry,
    }
  }

  /**
   * Get React Query configuration for customer details
   * @param {string|number} customerId - Customer ID
   * @returns {Object} Query configuration
   */
  getCustomerDetailsQuery(customerId) {
    return {
      queryKey: ['customer-details', customerId],
      queryFn: () => customerApi.getCustomer(customerId),
      enabled: !!customerId,
      staleTime: this.defaultOptions.detailStaleTime,
      cacheTime: this.defaultOptions.detailCacheTime,
    }
  }

  /**
   * Get all customers with minimal data (for direct API usage)
   * @returns {Promise<Array>} Array of customer objects
   */
  async getCustomersMinimal() {
    if (!this.queryClient) {
      // Direct API call if no query client available
      return await customerApi.getCustomersMinimal()
    }
    
    return await this.queryClient.fetchQuery(this.getCustomersMinimalQuery())
  }

  /**
   * Get detailed customer data by ID (for direct API usage)
   * @param {string|number} customerId - Customer ID
   * @returns {Promise<Object>} Detailed customer object
   */
  async getCustomerDetails(customerId) {
    if (!this.queryClient) {
      // Direct API call if no query client available
      return await customerApi.getCustomer(customerId)
    }
    
    return await this.queryClient.fetchQuery(this.getCustomerDetailsQuery(customerId))
  }

  /**
   * Add a new customer to the React Query cache
   * @param {Object} customer - Customer object to add
   * @returns {Promise<void>}
   */
  async addCustomerToCache(customer) {
    if (!this.queryClient) {
      console.warn('ReactQueryCustomerService: No query client available for cache update')
      return
    }

    this.queryClient.setQueryData(['customers-minimal'], (oldData) => {
      if (!oldData) return oldData
      
      const newCustomerFormatted = {
        id: customer.id,
        first_name: customer.first_name || customer.name?.split(' ')[0] || '',
        last_name: customer.last_name || customer.name?.split(' ').slice(1).join(' ') || '',
        email: customer.email || '',
        phone: customer.phone || '',
        has_upcoming_appointment: false,
      }

      if (oldData.results) {
        return {
          ...oldData,
          results: [newCustomerFormatted, ...oldData.results]
        }
      } else if (Array.isArray(oldData)) {
        return [newCustomerFormatted, ...oldData]
      }
      
      return oldData
    })
  }

  /**
   * Refresh/invalidate the customer cache
   * @returns {Promise<void>}
   */
  async refreshCustomers() {
    if (!this.queryClient) {
      console.warn('ReactQueryCustomerService: No query client available for refresh')
      return
    }

    await this.queryClient.invalidateQueries(['customers-minimal'])
  }

  /**
   * Clear all cached customer data
   * @returns {Promise<void>}
   */
  async clearCache() {
    if (!this.queryClient) {
      console.warn('ReactQueryCustomerService: No query client available for cache clear')
      return
    }

    await this.queryClient.removeQueries(['customers-minimal'])
    await this.queryClient.removeQueries(['customer-details'])
  }

  /**
   * Get service metadata
   * @returns {Promise<Object>} Service metadata
   */
  async getServiceInfo() {
    const info = {
      type: 'ReactQuery',
      version: '1.0.0',
      cacheType: 'in-memory',
      persistent: false,
      options: this.defaultOptions
    }

    if (this.queryClient) {
      const cache = this.queryClient.getQueryCache()
      const queries = cache.getAll()
      
      const customerQueries = queries.filter(q => 
        q.queryKey[0] === 'customers-minimal' || 
        q.queryKey[0] === 'customer-details'
      )

      info.cacheStats = {
        totalQueries: customerQueries.length,
        customersMinimalCached: queries.some(q => q.queryKey[0] === 'customers-minimal'),
        customerDetailsCached: queries.filter(q => q.queryKey[0] === 'customer-details').length
      }
    }

    return info
  }

  /**
   * Check if service is available/ready
   * @returns {Promise<boolean>} True if service is ready
   */
  async isReady() {
    // React Query service is ready when query client is available
    return !!this.queryClient
  }
} 