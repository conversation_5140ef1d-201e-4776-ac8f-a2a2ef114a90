/* Custom Admin Styles */

/* Modern font for better readability */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON>l, sans-serif;
}

/* Enhance table appearance */
.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
}

.table tbody tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* Style form elements */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

/* Custom button styles */
.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

/* Card styling */
.card {
    border-radius: 0.5rem;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
}

/* Navigation improvements */
.nav-link {
    color: #495057;
}

.nav-link:hover {
    color: #007bff;
}

/* Custom alert styling */
.alert {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.alert-success {
    background-color: #d4edda;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    color: #721c24;
}

/* Pagination styling */
.pagination .page-link {
    color: #007bff;
    border: 1px solid #dee2e6;
}

.pagination .page-link:hover {
    background-color: #e9ecef;
    border-color: #dee2e6;
}

.pagination .page-item.active .page-link {
    background-color: #007bff;
    border-color: #007bff;
}

/* Custom checkbox and radio styling */
.custom-control-input:checked ~ .custom-control-label::before {
    background-color: #007bff;
    border-color: #007bff;
}

/* Modal improvements */
.modal-content {
    border-radius: 0.5rem;
    border: none;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

.modal-header {
    border-bottom: 1px solid #dee2e6;
    background-color: #f8f9fa;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .table-responsive {
        border: none;
    }
    
    .card {
        margin-bottom: 1rem;
    }
}

/* Navbar customization */
.navbar {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Sidebar customization */
.nav-sidebar .nav-item > .nav-link {
    padding: 0.75rem 1rem;
    transition: all 0.3s ease;
}

.nav-sidebar .nav-item > .nav-link:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Table customization */
.table {
    margin-bottom: 1rem;
}

.table td, .table th {
    padding: 0.75rem;
    vertical-align: middle;
}

/* Pagination customization */
.pagination {
    margin-top: 1rem;
}

/* Dashboard widgets */
.small-box {
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    margin-bottom: 1.5rem;
}

.small-box > .inner {
    padding: 1rem;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .card {
        background-color: #2b2b2b;
        border-color: #404040;
    }
    
    .card-header {
        background-color: #333;
        border-bottom-color: #404040;
    }
    
    .table {
        color: #e1e1e1;
    }
    
    .table thead th {
        border-bottom-color: #404040;
    }
    
    .form-control {
        background-color: #333;
        border-color: #404040;
        color: #e1e1e1;
    }
    
    .form-control:focus {
        background-color: #404040;
        border-color: #666;
    }
} 