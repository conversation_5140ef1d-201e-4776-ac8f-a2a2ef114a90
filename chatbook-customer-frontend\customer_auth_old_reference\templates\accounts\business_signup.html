{% extends "base.html" %}
{% load static %}

{% block title %}Business Signup - ChatBook{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 bg-white p-8 rounded-lg shadow">
        <div>
            <h2 class="text-center text-3xl font-extrabold text-gray-900">
                Create Business Account
            </h2>
            <p class="mt-2 text-center text-sm text-gray-600">
                Set up your business profile and start managing appointments
            </p>
        </div>

        {% if messages %}
        <div class="rounded-md bg-red-50 p-4">
            <div class="flex">
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">
                        {% for message in messages %}
                            {{ message }}
                        {% endfor %}
                    </h3>
                </div>
            </div>
        </div>
        {% endif %}

        <form class="mt-8 space-y-6" method="post">
            {% csrf_token %}
            
            <div class="space-y-4">
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label for="{{ form.first_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            First Name <span class="text-red-500">*</span>
                        </label>
                        {{ form.first_name }}
                        {% if form.first_name.help_text %}
                            <p class="text-sm text-gray-500 mt-1">{{ form.first_name.help_text }}</p>
                        {% endif %}
                        {% for error in form.first_name.errors %}
                            <p class="text-sm text-red-600">{{ error }}</p>
                        {% endfor %}
                    </div>
                    <div>
                        <label for="{{ form.last_name.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                            Last Name <span class="text-red-500">*</span>
                        </label>
                        {{ form.last_name }}
                        {% if form.last_name.help_text %}
                            <p class="text-sm text-gray-500 mt-1">{{ form.last_name.help_text }}</p>
                        {% endif %}
                        {% for error in form.last_name.errors %}
                            <p class="text-sm text-red-600">{{ error }}</p>
                        {% endfor %}
                    </div>
                </div>

                <div>
                    <label for="{{ form.email.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Email address <span class="text-red-500">*</span>
                    </label>
                    {{ form.email }}
                    {% if form.email.help_text %}
                        <p class="text-sm text-gray-500 mt-1">{{ form.email.help_text }}</p>
                    {% endif %}
                    {% for error in form.email.errors %}
                        <p class="text-sm text-red-600">{{ error }}</p>
                    {% endfor %}
                </div>

                <div>
                    <label for="{{ form.phone_number.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Phone Number <span class="text-red-500">*</span>
                    </label>
                    {{ form.phone_number }}
                    {% if form.phone_number.help_text %}
                        <p class="text-sm text-gray-500 mt-1">{{ form.phone_number.help_text }}</p>
                    {% endif %}
                    {% for error in form.phone_number.errors %}
                        <p class="text-sm text-red-600">{{ error }}</p>
                    {% endfor %}
                </div>

                <div>
                    <label for="{{ form.password1.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Password <span class="text-red-500">*</span>
                    </label>
                    {{ form.password1 }}
                    {% if form.password1.help_text %}
                        <p class="text-sm text-gray-500 mt-1">{{ form.password1.help_text }}</p>
                    {% endif %}
                    {% for error in form.password1.errors %}
                        <p class="text-sm text-red-600">{{ error }}</p>
                    {% endfor %}
                </div>

                <div>
                    <label for="{{ form.password2.id_for_label }}" class="block text-sm font-medium text-gray-700 mb-1">
                        Confirm Password <span class="text-red-500">*</span>
                    </label>
                    {{ form.password2 }}
                    {% if form.password2.help_text %}
                        <p class="text-sm text-gray-500 mt-1">{{ form.password2.help_text }}</p>
                    {% endif %}
                    {% for error in form.password2.errors %}
                        <p class="text-sm text-red-600">{{ error }}</p>
                    {% endfor %}
                </div>
            </div>

            <div>
                <button type="submit"
                    class="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-indigo-600 hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500">
                    Create Business Account
                </button>
            </div>
        </form>

        <div class="mt-6">
            <div class="relative">
                <div class="absolute inset-0 flex items-center">
                    <div class="w-full border-t border-gray-300"></div>
                </div>
                <div class="relative flex justify-center text-sm">
                    <span class="px-2 bg-white text-gray-500">
                        Or sign up with
                    </span>
                </div>
            </div>

            <div class="mt-6 flex justify-center">
                <a href="/accounts/google/login/"
                    class="inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50">
                    <span class="flex items-center">
                        <img src="{% static 'images/google.svg' %}" alt="Google" class="h-5 w-5 mr-2">
                        <span>Sign up with Google</span>
                    </span>
                </a>
            </div>
        </div>

        <div class="text-sm text-center text-gray-600">
            Already have an account? 
            <a href="{% url 'login' %}" class="font-medium text-indigo-600 hover:text-indigo-500">
                Sign in
            </a>
        </div>
        <div class="text-sm text-center text-gray-600">
            Looking to create a customer account? 
            <a href="{% url 'customer_signup' %}" class="font-medium text-indigo-600 hover:text-indigo-500">
                Sign up as customer
            </a>
        </div>
    </div>
</div>
{% endblock %} 