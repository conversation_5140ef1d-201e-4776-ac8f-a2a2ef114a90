"""
File Management Models

Models for handling file uploads, processing, and metadata.
"""
from django.db import models
from django.contrib.auth import get_user_model
from django.utils import timezone

User = get_user_model()


class UploadedFile(models.Model):
    """Model for tracking uploaded files."""
    
    FILE_TYPE_CHOICES = [
        ('customer_import', 'Customer Import'),
        ('appointment_import', 'Appointment Import'),
        ('service_import', 'Service Import'),
        ('employee_import', 'Employee Import'),
        ('document', 'Document'),
        ('image', 'Image'),
        ('other', 'Other'),
    ]
    
    STATUS_CHOICES = [
        ('uploaded', 'Uploaded'),
        ('processing', 'Processing'),
        ('processed', 'Processed'),
        ('failed', 'Failed'),
        ('archived', 'Archived'),
    ]
    
    # File identification
    file_id = models.CharField(max_length=50, unique=True, db_index=True)
    
    # File metadata
    file_name = models.CharField(max_length=255)
    file_size = models.BigIntegerField()
    file_type = models.CharField(max_length=50, choices=FILE_TYPE_CHOICES, default='other')
    content_type = models.CharField(max_length=100, blank=True)
    file_hash = models.CharField(max_length=32, blank=True)  # MD5 hash
    
    # Storage information
    s3_key = models.CharField(max_length=500)  # S3 object key
    s3_bucket = models.CharField(max_length=100, blank=True)
    
    # Processing information
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='uploaded')
    description = models.TextField(blank=True)
    
    # Processing options
    skip_duplicates = models.BooleanField(default=True)
    update_existing = models.BooleanField(default=False)
    
    # Relationships
    uploaded_by = models.ForeignKey(User, on_delete=models.CASCADE, related_name='uploaded_files')
    
    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    processed_at = models.DateTimeField(null=True, blank=True)
    
    class Meta:
        db_table = 'files_uploaded_file'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['file_id']),
            models.Index(fields=['status']),
            models.Index(fields=['uploaded_by', 'created_at']),
            models.Index(fields=['file_type', 'status']),
        ]
    
    def __str__(self):
        return f"{self.file_name} ({self.file_id})"
    
    @property
    def is_processed(self):
        return self.status == 'processed'
    
    @property
    def is_processing(self):
        return self.status == 'processing'
    
    @property
    def has_failed(self):
        return self.status == 'failed'


class FileProcessingLog(models.Model):
    """Model for tracking file processing steps and results."""
    
    LOG_LEVEL_CHOICES = [
        ('info', 'Info'),
        ('warning', 'Warning'),
        ('error', 'Error'),
        ('debug', 'Debug'),
    ]
    
    # Relationships
    uploaded_file = models.ForeignKey(UploadedFile, on_delete=models.CASCADE, related_name='processing_logs')
    
    # Log information
    level = models.CharField(max_length=10, choices=LOG_LEVEL_CHOICES, default='info')
    message = models.TextField()
    step = models.CharField(max_length=100, blank=True)  # e.g., 'validation', 'parsing', 'import'
    
    # Additional context
    details = models.JSONField(default=dict, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    
    class Meta:
        db_table = 'files_processing_log'
        ordering = ['-created_at']
        indexes = [
            models.Index(fields=['uploaded_file', 'created_at']),
            models.Index(fields=['level']),
        ]
    
    def __str__(self):
        return f"{self.uploaded_file.file_id} - {self.level}: {self.message[:50]}"


class ImportResult(models.Model):
    """Model for tracking import results and statistics."""
    
    # Relationships
    uploaded_file = models.OneToOneField(UploadedFile, on_delete=models.CASCADE, related_name='import_result')
    
    # Import statistics
    total_rows = models.IntegerField(default=0)
    successful_imports = models.IntegerField(default=0)
    failed_imports = models.IntegerField(default=0)
    skipped_rows = models.IntegerField(default=0)
    
    # Import details
    import_type = models.CharField(max_length=50)  # e.g., 'customers', 'appointments'
    errors = models.JSONField(default=list, blank=True)  # List of error messages
    warnings = models.JSONField(default=list, blank=True)  # List of warnings
    
    # Processing time
    processing_started_at = models.DateTimeField(null=True, blank=True)
    processing_completed_at = models.DateTimeField(null=True, blank=True)
    
    # Timestamps
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        db_table = 'files_import_result'
        indexes = [
            models.Index(fields=['import_type']),
            models.Index(fields=['created_at']),
        ]
    
    def __str__(self):
        return f"Import result for {self.uploaded_file.file_name}"
    
    @property
    def success_rate(self):
        if self.total_rows == 0:
            return 0
        return (self.successful_imports / self.total_rows) * 100
    
    @property
    def processing_duration(self):
        if self.processing_started_at and self.processing_completed_at:
            return self.processing_completed_at - self.processing_started_at
        return None
