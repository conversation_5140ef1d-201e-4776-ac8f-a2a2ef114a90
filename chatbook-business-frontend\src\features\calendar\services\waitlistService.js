import apiClient from '../../employees/services/apiClient'

/**
 * Waitlist Service
 * Handles all waitlist-related API operations following calendar service patterns
 */
class WaitlistService {
  constructor() {
    this.customersCache = new Map()
    this.servicesCache = new Map()
    this.employeesCache = new Map()
    this.customersLoadingPromise = null
  }

  /**
   * Create a new waitlist entry
   * @param {Object} waitlistData - Waitlist entry data
   * @returns {Promise<Object>} Created waitlist entry
   */
  async createWaitlistEntry(waitlistData) {
    try {
      console.log('📋 WaitlistService: Creating waitlist entry:', waitlistData)
      
      const response = await apiClient.post('/waitlist/', waitlistData)
      console.log('✅ WaitlistService: Waitlist entry created:', response.data)
      
      return response.data
    } catch (error) {
      console.error('❌ WaitlistService: Failed to create waitlist entry:', error)
      console.error('❌ Error response:', error.response?.data)
      console.error('❌ Error status:', error.response?.status)
      
      throw new Error(error.response?.data?.message || 'Failed to create waitlist entry')
    }
  }

  /**
   * Get all waitlist entries
   * @param {Object} params - Query parameters
   * @returns {Promise<Array>} Waitlist entries
   */
  async getWaitlistEntries(params = {}) {
    try {
      console.log('📋 WaitlistService: Fetching waitlist entries with params:', params)
      
      const response = await apiClient.get('/waitlist/', { params })
      const entries = response.data.results || response.data
      
      console.log(`✅ WaitlistService: Retrieved ${entries.length} waitlist entries`)
      return entries
    } catch (error) {
      console.error('❌ WaitlistService: Failed to fetch waitlist entries:', error)
      throw new Error('Failed to fetch waitlist entries')
    }
  }

  /**
   * Search customers for waitlist with caching
   * @param {string} searchTerm - Search term
   * @returns {Promise<Array>} Matching customers
   */
  async searchCustomers(searchTerm) {
    if (!searchTerm || searchTerm.length < 2) {
      return []
    }

    try {
      console.log('🔍 WaitlistService: Searching customers for:', searchTerm)
      
      const response = await apiClient.get('/business-customers/me/', {
        params: { q: searchTerm }
      })
      
      const customers = response.data.results || response.data
      console.log(`✅ WaitlistService: Found ${customers.length} matching customers`)
      
      return customers
    } catch (error) {
      console.error('❌ WaitlistService: Customer search failed:', error)
      return []
    }
  }

  /**
   * Get customer name by ID with caching
   * @param {number|string} customerId - Customer ID
   * @returns {Promise<string>} Customer name
   */
  async getCustomerName(customerId) {
    if (!customerId) return 'Customer'
    
    const normalizedId = customerId.toString()
    
    // Check cache first
    if (this.customersCache.has(normalizedId)) {
      return this.customersCache.get(normalizedId)
    }
    
    try {
      await this.loadAllCustomers()
      return this.customersCache.get(normalizedId) || `Customer ${customerId}`
    } catch (error) {
      console.warn('⚠️ WaitlistService: Failed to load customer name, using fallback:', error)
      return `Customer ${customerId}`
    }
  }

  /**
   * Load all customers into cache
   */
  async loadAllCustomers() {
    if (this.customersLoadingPromise) {
      return this.customersLoadingPromise
    }
    
    this.customersLoadingPromise = this._doLoadCustomers()
    
    try {
      await this.customersLoadingPromise
    } finally {
      this.customersLoadingPromise = null
    }
  }

  /**
   * Get all customers (loads if not cached)
   * @returns {Promise<Array>} All customers
   */
  async getAllCustomers() {
    try {
      const response = await apiClient.get('/business-customers/me/')
      const customers = response.data.results || response.data
      
      if (!Array.isArray(customers)) {
        console.error('❌ WaitlistService: API did not return an array:', typeof customers)
        return []
      }
      
      // Cache the customers
      customers.forEach((customer) => {
        // Try different possible name structures
        const firstName = customer.customer?.user?.first_name || customer.first_name || ''
        const lastName = customer.customer?.user?.last_name || customer.last_name || ''
        const email = customer.customer?.user?.email || customer.email || ''
        const fullName = `${firstName} ${lastName}`.trim()
        
        const name = fullName || email || `Customer ${customer.id}`
        
        this.customersCache.set(customer.id.toString(), name)
        this.customersCache.set(customer.id, name)
      })
      
      return customers
    } catch (error) {
      console.error('❌ WaitlistService: Failed to get all customers:', error)
      throw error
    }
  }

  /**
   * Internal method to load customers
   */
  async _doLoadCustomers() {
    try {
      const response = await apiClient.get('/business-customers/me/')
      const customers = response.data.results || response.data
      
      customers.forEach(customer => {
        // Try different possible name structures
        const firstName = customer.customer?.user?.first_name || customer.first_name || ''
        const lastName = customer.customer?.user?.last_name || customer.last_name || ''
        const email = customer.customer?.user?.email || customer.email || ''
        const fullName = `${firstName} ${lastName}`.trim()
        
        const name = fullName || email || `Customer ${customer.id}`
        
        this.customersCache.set(customer.id.toString(), name)
        this.customersCache.set(customer.id, name)
      })
    } catch (error) {
      console.error('❌ WaitlistService: Failed to load customers:', error)
      throw error
    }
  }

  /**
   * Update waitlist entry status
   * @param {number} entryId - Waitlist entry ID
   * @param {Object} updates - Updates to apply
   * @returns {Promise<Object>} Updated entry
   */
  async updateWaitlistEntry(entryId, updates) {
    try {
      console.log('📝 WaitlistService: Updating waitlist entry:', entryId, updates)
      
      const response = await apiClient.patch(`/waitlist/${entryId}/`, updates)
      console.log('✅ WaitlistService: Waitlist entry updated:', response.data)
      
      return response.data
    } catch (error) {
      console.error('❌ WaitlistService: Failed to update waitlist entry:', error)
      throw new Error('Failed to update waitlist entry')
    }
  }

  /**
   * Expire a waitlist entry
   * @param {number} entryId - Waitlist entry ID
   * @returns {Promise<Object>} Response data
   */
  async expireWaitlistEntry(entryId) {
    try {
      console.log('⏰ WaitlistService: Expiring waitlist entry:', entryId)
      
      const response = await apiClient.post(`/waitlist/${entryId}/expire/`)
      console.log('✅ WaitlistService: Waitlist entry expired:', response.data)
      
      return response.data
    } catch (error) {
      console.error('❌ WaitlistService: Failed to expire waitlist entry:', error)
      throw new Error('Failed to expire waitlist entry')
    }
  }

  /**
   * Reactivate an expired waitlist entry
   * @param {number} entryId - Waitlist entry ID
   * @returns {Promise<Object>} Response data
   */
  async reactivateWaitlistEntry(entryId) {
    try {
      console.log('🔄 WaitlistService: Reactivating waitlist entry:', entryId)
      
      const response = await apiClient.post(`/waitlist/${entryId}/reactivate/`)
      console.log('✅ WaitlistService: Waitlist entry reactivated:', response.data)
      
      return response.data
    } catch (error) {
      console.error('❌ WaitlistService: Failed to reactivate waitlist entry:', error)
      throw new Error('Failed to reactivate waitlist entry')
    }
  }



  /**
   * Clear all caches
   */
  clearCache() {
    this.customersCache.clear()
    this.servicesCache.clear()
    this.employeesCache.clear()
    this.customersLoadingPromise = null
    console.log('🧹 WaitlistService: Cache cleared')
  }
}

// Export singleton instance
export const waitlistService = new WaitlistService()

export default waitlistService 