# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Django
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/

# Django Migrations
apps/files/migrations/[0-9]*.py
apps/files/migrations/__pycache__/
*/migrations/[0-9]*.py
*/migrations/__pycache__/

# Virtual Environment
venv/
ENV/
env/
.env
.venv
env.bak/
venv.bak/

# IDE
.idea/
.vscode/
*.swp
*.swo
.DS_Store
.cursorrules

# Backup files
backups/
*.bak
*.backup
*.bkp

# Database
*.sqlite3
*.db

# Environment variables
.env
.env.local
.env.*.local

# Poetry
poetry.lock

# Test files
test_*.py
*_test.py
*.test.py

# Logs
*.log
logs/
log/

# Local development scripts
create_test_*.py
check_*.py
update_*.py
setup_*.py

# Temporary files
*.tmp
*.temp
.temp/
tmp/
temp/

# Coverage reports
htmlcov/
.tox/
.coverage
.coverage.*
.cache
coverage.xml
*.cover 

# Sample import data files
scripts/sample-import-data/*.csv
scripts/sample-import-data/*.xlsx
scripts/sample-import-data/*.xls
scripts/sample-import-data/*.json