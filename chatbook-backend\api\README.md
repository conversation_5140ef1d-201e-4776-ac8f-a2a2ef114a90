# API Documentation

## Overview
This document describes the structure and organization of the ChatBook Backend API. The API follows industry best practices for RESTful design, proper versioning, and modular organization.

## Current Structure
The API is organized following Django REST Framework best practices with proper versioning and modular structure:

```
api/
├── exceptions.py          # Shared API exceptions
├── permissions.py         # Shared API permissions
├── middleware/           # API middleware
├── services/            # Shared business logic services
├── tests/              # API-wide tests
├── urls.py             # Legacy redirect (for backward compatibility)
└── v1/                 # Version 1 API - Main Implementation
    ├── urls.py         # Main v1 URL configuration
    ├── views/          # Shared v1 views (users, health, bff)
    ├── appointments/   # Appointment endpoints
    │   └── views.py
    ├── auth/          # Authentication endpoints
    │   ├── views.py
    │   └── urls.py
    ├── business/      # Business endpoints
    │   ├── views.py
    │   └── serializers.py
    ├── customers/     # Customer endpoints
    │   └── views.py
    ├── employees/     # Employee endpoints
    │   ├── views.py
    │   └── serializers.py
    ├── files/         # File endpoints
    │   ├── views.py
    │   └── serializers.py
    ├── forms/         # Form endpoints
    │   ├── views.py
    │   └── serializers.py
    ├── services/      # Service endpoints
    │   ├── views.py
    │   └── serializers.py
    └── waitlist/      # Waitlist endpoints
        ├── views.py
        └── serializers.py
```

## API Versioning
All API endpoints are versioned and accessible under `/api/v1/`. This ensures:
- **Backward Compatibility**: Old clients continue to work
- **Future-Proof**: Easy to add v2, v3, etc.
- **Clear Structure**: All endpoints follow consistent patterns

## Base URL
All API endpoints are prefixed with `/api/v1/`

Example: `https://your-domain.com/api/v1/services/`

## Main API Endpoints

### Authentication
- `POST /api/v1/auth/login/` - User login
- `POST /api/v1/auth/verify-mfa/` - Multi-factor authentication
- `POST /api/v1/auth/refresh/` - Refresh JWT token

### Services
- `GET /api/v1/service-categories/` - List service categories
- `GET /api/v1/services/` - List services
- `GET /api/v1/stylist-level-services/` - List services by stylist level
- `GET /api/v1/employee-services/` - List employee services

### Business Management
- `GET /api/v1/businesses/` - List businesses
- `GET /api/v1/business-customers/` - List business customers
- `GET /api/v1/locations/` - List business locations
- `GET /api/v1/stylist-levels/` - List stylist levels

### Appointments
- `GET /api/v1/appointments/` - List appointments
- `POST /api/v1/appointments/` - Create appointment
- `GET /api/v1/appointments/{id}/` - Get appointment details
- `PUT /api/v1/appointments/{id}/` - Update appointment

### Employees
- `GET /api/v1/employees/` - List employees
- `GET /api/v1/employees/me/` - Current employee info
- `GET /api/v1/employees/me/working-hours/` - Employee working hours
- `GET /api/v1/employees/me/permissions/` - Employee permissions

### Forms
- `GET /api/v1/forms/templates/` - List form templates
- `GET /api/v1/forms/submissions/` - List form submissions
- `GET /api/v1/forms/signatures/` - List signatures

### Files
- `POST /api/v1/files/upload/` - Upload files
- `GET /api/v1/files/{id}/status/` - Check file status
- `GET /api/v1/files/{id}/results/` - Get file results

### Waitlist
- `GET /api/v1/waitlist/` - List waitlist entries
- `POST /api/v1/waitlist/` - Create waitlist entry
- `POST /api/v1/waitlist/{id}/expire/` - Mark entry as expired

### Health Checks
- `GET /api/v1/health/` - General health check
- `GET /api/v1/health/aws/` - AWS services health check

---

## REST API Design Principles

### 1. Resource-Based URLs
- Use nouns, not verbs (e.g., `/services`, not `/getServices`)
- Pluralize collection names (`/services`, `/appointments`)
- Use lowercase with hyphens for multi-word segments (`/service-categories`)

### 2. HTTP Methods
- `GET` - Retrieve resources
- `POST` - Create new resources
- `PUT` - Update entire resources
- `PATCH` - Partial updates
- `DELETE` - Remove resources

### 3. Status Codes
- `200` - Success
- `201` - Created
- `400` - Bad Request
- `401` - Unauthorized
- `404` - Not Found
- `500` - Internal Server Error

### 4. Content Types
- Request/Response: `application/json`
- File uploads: `multipart/form-data`
- Use headers, not file extensions in URLs

## Query Parameters & Filtering

### Common Query Parameters
- `business_id` - Filter by business (required for most endpoints)
- `q` - Search query for text-based filtering
- `page` - Pagination page number
- `page_size` - Number of items per page
- `ordering` - Sort results (e.g., `?ordering=-created_at`)

### Examples
```
GET /api/v1/service-categories/?business_id=1
GET /api/v1/business-customers/?business_id=1&q=john
GET /api/v1/appointments/?business_id=1&date=2025-07-15
```

## Authentication

### JWT Token Authentication
All protected endpoints require a valid JWT token in the Authorization header:
```
Authorization: Bearer <your-jwt-token>
```

### Getting a Token
```bash
POST /api/v1/auth/login/
{
  "email": "<EMAIL>",
  "password": "password"
}
```

Response:
```json
{
  "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "user": {
    "id": "123",
    "email": "<EMAIL>",
    "user_type": "business_owner"
  }
}
```

## Error Handling

### Error Response Format
```json
{
  "error": "Error message description",
  "details": {
    "field_name": ["Field-specific error message"]
  }
}
```

### Common Error Scenarios
- **401 Unauthorized**: Invalid or expired token
- **403 Forbidden**: Insufficient permissions
- **404 Not Found**: Resource doesn't exist
- **400 Bad Request**: Invalid request data
- **500 Internal Server Error**: Server-side error

---

## Development Guidelines

### Adding New Endpoints

1. **Choose the Right Module**: Place new endpoints in the appropriate v1 module:
   - Business-related: `api/v1/business/`
   - Service-related: `api/v1/services/`
   - User management: `api/v1/employees/` or `api/v1/customers/`
   - File operations: `api/v1/files/`

2. **Follow the Structure**:
   ```
   api/v1/module_name/
   ├── views.py        # ViewSets and API views
   ├── serializers.py  # DRF serializers
   └── __init__.py     # Module marker
   ```

3. **Register in URLs**: Add your ViewSet to `api/v1/urls.py`:
   ```python
   router.register(r'your-endpoint', YourViewSet)
   ```

### Code Organization Principles

- **Separation of Concerns**: Keep views, serializers, and business logic separate
- **Consistent Naming**: Use kebab-case for URLs, snake_case for Python
- **Proper Imports**: Use relative imports within modules (`.serializers`)
- **Error Handling**: Use consistent error response formats
- **Documentation**: Document all endpoints and parameters

### Testing

- Place tests in `api/tests/` for API-wide functionality
- Use Django's test framework and DRF's test utilities
- Test both success and error scenarios
- Include authentication and permission tests

### Future Versioning

When creating API v2:
1. Create `api/v2/` directory with same structure
2. Update main `urls.py` to include v2 routes
3. Maintain v1 for backward compatibility
4. Document migration path for clients

---

## Contributing

1. Follow the established module structure
2. Write tests for new endpoints
3. Update this README when adding new functionality
4. Use consistent error handling and response formats
5. Ensure proper authentication and permissions

---

## Support

For questions about the API structure or adding new endpoints, refer to:
- Django REST Framework documentation
- This README for structural guidelines
- Existing code examples in the v1 modules