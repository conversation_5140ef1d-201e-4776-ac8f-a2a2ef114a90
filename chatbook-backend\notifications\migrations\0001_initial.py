# Generated by Django 4.2.22 on 2025-08-07 05:31

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='OutboxEvent',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('sequence_number', models.BigIntegerField(db_index=True, editable=False, unique=True)),
                ('event_type', models.CharField(choices=[('appointment.booked', 'Appointment Booked'), ('appointment.changed', 'Appointment Changed'), ('appointment.cancelled', 'Appointment Cancelled'), ('appointment.confirmation_requested', 'Appointment Confirmation Requested'), ('appointment.reminder', 'Appointment Reminder'), ('waitlist.entry_created', 'Waitlist Entry Created'), ('notification.direct', 'Direct Notification')], db_index=True, max_length=50)),
                ('aggregate_type', models.CharField(db_index=True, max_length=50)),
                ('aggregate_id', models.CharField(db_index=True, max_length=100)),
                ('payload', models.JSONField(help_text='Event data as JSON')),
                ('metadata', models.JSONField(default=dict, help_text='Additional metadata (user_id, correlation_id, etc.)')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('published', 'Published'), ('failed', 'Failed'), ('dead_letter', 'Dead Letter')], db_index=True, default='pending', max_length=20)),
                ('retry_count', models.PositiveIntegerField(default=0)),
                ('max_retries', models.PositiveIntegerField(default=3)),
                ('created_at', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('scheduled_at', models.DateTimeField(db_index=True, default=django.utils.timezone.now)),
                ('processed_at', models.DateTimeField(blank=True, null=True)),
                ('last_error', models.TextField(blank=True, help_text='Last error message if processing failed')),
                ('error_details', models.JSONField(default=dict, help_text='Detailed error information')),
                ('published_to', models.CharField(blank=True, help_text='SNS topic ARN or destination', max_length=200)),
                ('message_id', models.CharField(blank=True, help_text='External message ID (e.g., SNS MessageId)', max_length=100)),
            ],
            options={
                'verbose_name': 'Outbox Event',
                'verbose_name_plural': 'Outbox Events',
                'ordering': ['sequence_number'],
                'indexes': [models.Index(fields=['status', 'scheduled_at'], name='outbox_status_scheduled_idx'), models.Index(fields=['event_type', 'created_at'], name='outbox_type_created_idx'), models.Index(fields=['aggregate_type', 'aggregate_id'], name='outbox_aggregate_idx'), models.Index(fields=['created_at'], name='outbox_created_idx')],
            },
        ),
        migrations.CreateModel(
            name='DeviceToken',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(max_length=200, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('last_active', models.DateTimeField(auto_now=True)),
                ('is_active', models.BooleanField(default=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='device_tokens', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Device Token',
                'verbose_name_plural': 'Device Tokens',
                'ordering': ['-last_active'],
            },
        ),
    ]
