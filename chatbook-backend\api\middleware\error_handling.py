"""
Custom error handling middleware for comprehensive error logging and user-friendly responses.
"""
import json
import logging
import traceback
import uuid
from datetime import datetime

from django.conf import settings
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from django.core.exceptions import ValidationError, PermissionDenied
from django.db import DatabaseError
from rest_framework import status

logger = logging.getLogger('api.middleware')


class ErrorHandlingMiddleware(MiddlewareMixin):
    """
    Middleware to catch and log all unhandled exceptions with full context.
    Provides structured error responses for API endpoints.
    """
    
    def process_exception(self, request, exception):
        """
        Process unhandled exceptions and return structured error responses.
        """
        # Generate unique error ID for tracking
        error_id = str(uuid.uuid4())[:8]
        
        # Get user information
        user_info = "Anonymous"
        if hasattr(request, 'user') and request.user.is_authenticated:
            user_info = f"{request.user.email} (ID: {request.user.id})"
        
        # Collect request context
        request_context = {
            'error_id': error_id,
            'method': request.method,
            'path': request.path,
            'user': user_info,
            'user_agent': request.META.get('HTTP_USER_AGENT', 'Unknown'),
            'remote_addr': self._get_client_ip(request),
            'timestamp': datetime.now().isoformat(),
        }
        
        # Add request data (be careful with sensitive data)
        if request.method in ['POST', 'PUT', 'PATCH']:
            try:
                if hasattr(request, 'data'):
                    # DRF request
                    request_data = dict(request.data)
                else:
                    # Regular Django request
                    request_data = dict(request.POST)
                
                # Filter sensitive fields
                filtered_data = self._filter_sensitive_data(request_data)
                request_context['request_data'] = filtered_data
            except Exception:
                request_context['request_data'] = "Could not parse request data"
        
        # Log the full exception with context
        logger.error(
            f"Unhandled exception [{error_id}]: {str(exception)}",
            extra={
                'error_id': error_id,
                'exception_type': type(exception).__name__,
                'request_context': request_context,
                'stack_trace': traceback.format_exc(),
            },
            exc_info=True
        )
        
        # Determine response based on exception type and environment
        if self._is_api_request(request):
            return self._create_api_error_response(exception, error_id, request_context)
        
        # For non-API requests, let Django handle it normally
        return None
    
    def _get_client_ip(self, request):
        """Get the client's IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def _filter_sensitive_data(self, data):
        """Filter out sensitive data from request data."""
        sensitive_fields = [
            'password', 'token', 'secret', 'key', 'authorization',
            'csrf', 'session', 'cookie', 'auth'
        ]
        
        filtered = {}
        for key, value in data.items():
            if any(sensitive in key.lower() for sensitive in sensitive_fields):
                filtered[key] = "[FILTERED]"
            else:
                filtered[key] = value
        
        return filtered
    
    def _is_api_request(self, request):
        """Check if this is an API request."""
        return (
            request.path.startswith('/api/') or
            request.content_type == 'application/json' or
            'application/json' in request.META.get('HTTP_ACCEPT', '')
        )
    
    def _create_api_error_response(self, exception, error_id, request_context):
        """Create a structured API error response."""
        
        # Default error response
        error_response = {
            'error': True,
            'error_id': error_id,
            'timestamp': request_context['timestamp'],
            'message': 'An internal server error occurred',
            'details': None
        }
        
        status_code = status.HTTP_500_INTERNAL_SERVER_ERROR
        
        # Customize response based on exception type
        if isinstance(exception, ValidationError):
            status_code = status.HTTP_400_BAD_REQUEST
            error_response.update({
                'message': 'Validation error',
                'details': str(exception)
            })
        
        elif isinstance(exception, PermissionDenied):
            status_code = status.HTTP_403_FORBIDDEN
            error_response.update({
                'message': 'Permission denied',
                'details': str(exception)
            })
        
        elif isinstance(exception, DatabaseError):
            error_response.update({
                'message': 'Database error occurred',
                'details': 'Please try again later'
            })
        
        elif hasattr(exception, 'status_code'):
            # DRF exceptions
            status_code = exception.status_code
            error_response.update({
                'message': str(exception),
                'details': getattr(exception, 'detail', None)
            })
        
        # In debug mode, include more details
        if settings.DEBUG:
            error_response.update({
                'debug_info': {
                    'exception_type': type(exception).__name__,
                    'exception_message': str(exception),
                    'request_path': request_context['path'],
                    'request_method': request_context['method'],
                }
            })
        
        return JsonResponse(error_response, status=status_code)


class RequestResponseLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log all incoming requests and outgoing responses.
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
        self.logger = logging.getLogger('requests_logger')
    
    def __call__(self, request):
        # Log incoming request
        self._log_request(request)
        
        # Process request
        response = self.get_response(request)
        
        # Log outgoing response
        self._log_response(request, response)
        
        return response
    
    def _log_request(self, request):
        """Log incoming request details."""
        if self._should_log_request(request):
            # Get full path with query parameters
            full_path = request.get_full_path()

            # Simple console log for API endpoints
            if request.path.startswith('/api/'):
                print(f"API: {request.method} {full_path}")
            elif request.path.startswith('/admin/') and not request.path.startswith('/admin/jsi18n/'):
                print(f"Admin: {request.method} {full_path}")

            # Detailed log to file
            log_data = {
                'type': 'request',
                'method': request.method,
                'path': request.path,
                'full_path': full_path,
                'query_params': dict(request.GET),
                'user': str(request.user) if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous',
                'user_agent': request.META.get('HTTP_USER_AGENT', 'Unknown'),
                'remote_addr': self._get_client_ip(request),
                'timestamp': datetime.now().isoformat(),
            }

            self.logger.info(json.dumps(log_data))
    
    def _log_response(self, request, response):
        """Log outgoing response details."""
        if self._should_log_request(request):
            # Get full path with query parameters
            full_path = request.get_full_path()

            # Simple console log for errors or important endpoints
            if response.status_code >= 400:
                print(f"ERROR: {request.method} {full_path} -> {response.status_code}")
            elif request.path.startswith('/api/') and response.status_code < 300:
                print(f"✅ {request.method} {full_path} -> {response.status_code}")

            # Detailed log to file
            log_data = {
                'type': 'response',
                'method': request.method,
                'path': request.path,
                'full_path': full_path,
                'query_params': dict(request.GET),
                'status_code': response.status_code,
                'user': str(request.user) if hasattr(request, 'user') and request.user.is_authenticated else 'Anonymous',
                'timestamp': datetime.now().isoformat(),
            }

            # Log errors with more detail
            if response.status_code >= 400:
                log_data['error'] = True
                if hasattr(response, 'content'):
                    try:
                        content = response.content.decode('utf-8')
                        if len(content) < 1000:  # Only log short error messages
                            log_data['response_content'] = content
                    except Exception:
                        pass

            self.logger.info(json.dumps(log_data))
    
    def _should_log_request(self, request):
        """Determine if request should be logged."""
        # Skip static files, media files, and other noise
        skip_paths = [
            '/static/', '/media/', '/health/', '/favicon.ico',
            '/admin/jsi18n/',  # Skip Django admin i18n
            '.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.woff', '.woff2'
        ]
        return not any(request.path.startswith(path) or request.path.endswith(path) for path in skip_paths)
    
    def _get_client_ip(self, request):
        """Get the client's IP address."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
