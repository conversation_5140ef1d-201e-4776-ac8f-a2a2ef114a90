import { useState, useEffect } from 'react'
import { bookingRulesApiService } from '../services/bookingRulesApiService'

/**
 * Hook to provide waitlist settings to calendar components
 * This is a lightweight way to access just the waitlist settings without 
 * the full booking rules management overhead
 */
export const useWaitlistSettings = () => {
  const [waitlistSettings, setWaitlistSettings] = useState({
    allowCustomerWaitlistAddition: true, // Default to enabled
    waitlistEmailNotifications: false,
    waitlistSMSNotifications: false,
    waitlistPushNotifications: false,
    isLoading: true
  })

  useEffect(() => {
    loadWaitlistSettings()
  }, [])

  const loadWaitlistSettings = async () => {
    try {
      // First try to get from API
      const backendRules = await bookingRulesApiService.getBookingRules()
      const frontendRules = bookingRulesApiService.transformToFrontendFormat(backendRules)
      
      setWaitlistSettings({
        allowCustomerWaitlistAddition: frontendRules.allowCustomerWaitlistAddition,
        waitlistEmailNotifications: frontendRules.waitlistEmailNotifications,
        waitlistSMSNotifications: frontendRules.waitlistSMSNotifications,
        waitlistPushNotifications: frontendRules.waitlistPushNotifications,
        isLoading: false
      })
    } catch (error) {
      console.warn('Failed to load booking rules, using frontend-only settings:', error)
      
      // Fallback to frontend-only settings
      const frontendOnlySettings = bookingRulesApiService.getFrontendOnlySettings()
      setWaitlistSettings({
        allowCustomerWaitlistAddition: frontendOnlySettings.allowCustomerWaitlistAddition,
        waitlistEmailNotifications: frontendOnlySettings.waitlistEmailNotifications,
        waitlistSMSNotifications: frontendOnlySettings.waitlistSMSNotifications,
        waitlistPushNotifications: frontendOnlySettings.waitlistPushNotifications,
        isLoading: false
      })
    }
  }

  // Listen for changes in localStorage (when settings are updated in another tab/component)
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'booking_rules_frontend_settings') {
        const frontendOnlySettings = bookingRulesApiService.getFrontendOnlySettings()
        setWaitlistSettings(prev => ({
          ...prev,
          allowCustomerWaitlistAddition: frontendOnlySettings.allowCustomerWaitlistAddition,
          waitlistEmailNotifications: frontendOnlySettings.waitlistEmailNotifications,
          waitlistSMSNotifications: frontendOnlySettings.waitlistSMSNotifications,
          waitlistPushNotifications: frontendOnlySettings.waitlistPushNotifications
        }))
      }
    }

    window.addEventListener('storage', handleStorageChange)
    return () => {
      window.removeEventListener('storage', handleStorageChange)
    }
  }, [])

  return {
    allowCustomerWaitlistAddition: waitlistSettings.allowCustomerWaitlistAddition,
    waitlistEmailNotifications: waitlistSettings.waitlistEmailNotifications,
    waitlistSMSNotifications: waitlistSettings.waitlistSMSNotifications,
    waitlistPushNotifications: waitlistSettings.waitlistPushNotifications,
    isLoading: waitlistSettings.isLoading,
    refreshSettings: loadWaitlistSettings
  }
}

export default useWaitlistSettings 