/**
 * Booking Session API Service
 * Handles backend storage of booking session data
 * Replaces frontend sessionStorage to prevent cross-contamination
 */

import api from '../../../utils/publicApi';

const ENDPOINTS = {
    bookingSessions: '/booking-sessions/',
    currentSession: '/booking-sessions/current/',
    updateData: '/booking-sessions/update-data/',
    clearSession: '/booking-sessions/clear/'
};

export const bookingSessionApi = {
    /**
     * Get current booking session for a business
     * @param {number} businessId - The business ID
     * @returns {Promise} - The booking session data
     */
    async getCurrentSession(businessId) {
        try {
            const response = await api.get(ENDPOINTS.currentSession, {
                params: { business_id: businessId }
            });
            return response.data;
        } catch (error) {
            console.error('Error fetching booking session:', error);
            throw error;
        }
    },

    /**
     * Update booking session data
     * @param {number} businessId - The business ID
     * @param {object} bookingData - The booking data to save
     * @param {string} currentStep - The current booking step
     * @returns {Promise} - The updated booking session
     */
    async updateBookingData(businessId, bookingData, currentStep) {
        try {
            const response = await api.post(ENDPOINTS.updateData, {
                business_id: businessId,
                booking_data: bookingData,
                current_step: currentStep
            });
            return response.data;
        } catch (error) {
            console.error('Error updating booking session:', error);
            throw error;
        }
    },

    /**
     * Clear booking session for a business
     * @param {number} businessId - The business ID
     * @returns {Promise} - Success message
     */
    async clearSession(businessId) {
        try {
            const response = await api.delete(ENDPOINTS.clearSession, {
                params: { business_id: businessId }
            });
            return response.data;
        } catch (error) {
            console.error('Error clearing booking session:', error);
            throw error;
        }
    },

    /**
     * Save specific booking step data
     * @param {number} businessId - The business ID
     * @param {object} stepData - The step-specific data to save
     * @param {string} step - The booking step
     * @returns {Promise} - The updated booking session
     */
    async saveStepData(businessId, stepData, step) {
        try {
            // Get current session first
            const currentSession = await this.getCurrentSession(businessId);
            
            // Merge the new step data with existing booking data
            const updatedBookingData = {
                ...currentSession.booking_data,
                ...stepData,
                step: step
            };

            return await this.updateBookingData(businessId, updatedBookingData, step);
        } catch (error) {
            console.error('Error saving step data:', error);
            throw error;
        }
    },

    /**
     * Save service and add-ons selection
     * @param {number} businessId - The business ID
     * @param {object} service - Selected service
     * @param {array} addOns - Selected add-ons
     * @returns {Promise} - The updated booking session
     */
    async saveServiceSelection(businessId, service, addOns = []) {
        const stepData = {
            selectedService: service,
            selectedAddOns: addOns,
            totalCost: this.calculateTotalCost(service, addOns)
        };
        return await this.saveStepData(businessId, stepData, 'time-selection');
    },

    /**
     * Save employee and time selection
     * @param {number} businessId - The business ID
     * @param {object} employee - Selected employee
     * @param {string} date - Selected date
     * @param {string} time - Selected time
     * @returns {Promise} - The updated booking session
     */
    async saveTimeSelection(businessId, employee, date, time) {
        const stepData = {
            selectedEmployee: employee,
            selectedDate: date,
            selectedTime: time
        };
        return await this.saveStepData(businessId, stepData, 'consent');
    },

    /**
     * Save consent data
     * @param {number} businessId - The business ID
     * @param {object} consentData - Consent form data
     * @returns {Promise} - The updated booking session
     */
    async saveConsentData(businessId, consentData) {
        console.log('💾 Saving consent data:', {
            consentAgreed: consentData.consentAgreed,
            hasSignature: !!consentData.signature,
            timestamp: consentData.consentTimestamp
        });

        const stepData = {
            consentData: {
                ...consentData,
                consentTimestamp: new Date().toISOString()
            }
        };
        return await this.saveStepData(businessId, stepData, 'review');
    },

    /**
     * Check if consent is already signed for a business
     * @param {number} businessId - The business ID
     * @returns {Promise<boolean>} - Whether consent is signed
     */
    async isConsentSigned(businessId) {
        try {
            const session = await this.getCurrentSession(businessId);
            const consentData = session.booking_data?.consentData;
            const backendStatus = session.consent_status;
            const isSigned = !!(consentData?.consentAgreed && consentData?.signature) || backendStatus === 'signed';

            console.log('🔍 Checking consent status:', {
                consentAgreed: consentData?.consentAgreed,
                hasSignature: !!consentData?.signature,
                backendStatus: backendStatus,
                isSigned: isSigned
            });

            return isSigned;
        } catch (error) {
            console.error('Error checking consent status:', error);
            return false;
        }
    },

    /**
     * Get consent status from backend
     * @param {number} businessId - The business ID
     * @returns {Promise<string>} - Consent status ('not_signed', 'signed', 'expired')
     */
    async getConsentStatus(businessId) {
        try {
            const session = await this.getCurrentSession(businessId);
            return session.consent_status || 'not_signed';
        } catch (error) {
            console.error('Error getting consent status:', error);
            return 'not_signed';
        }
    },

    /**
     * Save customer information
     * @param {number} businessId - The business ID
     * @param {object} customerInfo - Customer information
     * @returns {Promise} - The updated booking session
     */
    async saveCustomerInfo(businessId, customerInfo) {
        const stepData = {
            customerInfo: customerInfo
        };
        return await this.saveStepData(businessId, stepData, 'review');
    },

    /**
     * Calculate total cost for service and add-ons
     * @param {object} service - Selected service
     * @param {array} addOns - Selected add-ons
     * @returns {number} - Total cost
     */
    calculateTotalCost(service, addOns = []) {
        const serviceCost = parseFloat(service?.price || 0);
        const addOnsCost = addOns.reduce((sum, addon) => 
            sum + parseFloat(addon.price || 0), 0
        );
        return serviceCost + addOnsCost;
    },

    /**
     * Get formatted booking data for API submission
     * @param {object} bookingData - The booking session data
     * @returns {object} - Formatted data for appointment creation
     */
    formatForAppointmentAPI(bookingData) {
        return {
            service: bookingData.selectedService?.id,
            employee: bookingData.selectedEmployee?.id,
            date: bookingData.selectedDate,
            time: bookingData.selectedTime,
            addOns: bookingData.selectedAddOns?.map(addon => addon.id) || [],
            customer: {
                name: bookingData.customerInfo?.name,
                email: bookingData.customerInfo?.email,
                phone: bookingData.customerInfo?.phone,
                address: bookingData.customerInfo?.address1,
                address2: bookingData.customerInfo?.address2
            },
            consent: bookingData.consentData,
            totalCost: bookingData.totalCost
        };
    }
};

export default bookingSessionApi;
