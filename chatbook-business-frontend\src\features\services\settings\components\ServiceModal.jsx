import React, { useState } from 'react'
import apiClient from '../../../employees/services/apiClient'
import { API_ENDPOINTS } from '../constants'

const ServiceModal = ({ service, categories, onClose, onSave, preselectedCategory = null }) => {
  const [formData, setFormData] = useState({
    name: service?.name || '',
    description: service?.description || '',
    price: service?.price || '',
    duration: service?.duration || 60,
    buffer_time: service?.buffer_time || 0,
    category: service?.category || preselectedCategory || '', // Allow empty category
    is_active: service?.is_active !== false,
    show_online: service?.show_online !== false
  })
  const [isSubmitting, setIsSubmitting] = useState(false)

  const handleSubmit = async (e) => {
    e.preventDefault()
    setIsSubmitting(true)
    
    try {
      // Get business ID from user data or use default (following app pattern)
      let businessId = service?.business
      if (!businessId) {
        const userDataString = localStorage.getItem('user_data')
        console.log('🔍 Raw user_data from localStorage:', userDataString)
        if (userDataString) {
          try {
            const userData = JSON.parse(userDataString)
            console.log('🔍 Parsed user data:', userData)
            businessId = userData.business_id || userData.business || 1
            console.log('🔍 Extracted business ID:', businessId)
          } catch (error) {
            console.warn('Failed to parse user data for business ID:', error)
            businessId = 1 // fallback
          }
        } else {
          console.warn('No user_data found in localStorage, using fallback business ID: 1')
          businessId = 1 // default fallback
        }
      }
      
      // Prepare data for backend - use exact format from working Postman example
      const submitData = {
        business: businessId,
        category: formData.category || null, // Send null if no category selected  
        name: formData.name,
        description: formData.description,
        price: formData.price, // String format like "99.99"
        duration: formData.duration, // Integer minutes like 60
        is_active: formData.is_active
      }
      
      // Note: buffer_time omitted for now - SerializerMethodField without input handling
      // if (formData.buffer_time && formData.buffer_time > 0) {
      //   submitData.buffer_time = formData.buffer_time
      // }
      
      console.log('🚀 Sending service data to API:', submitData)
      console.log('🔍 Business ID:', businessId)
      console.log('🔍 Form data:', formData)
      
      if (service?.id) {
        await apiClient.put(`${API_ENDPOINTS.SERVICES}${service.id}/`, submitData)
      } else {
        await apiClient.post(API_ENDPOINTS.SERVICES, submitData)
      }
      onSave()
      onClose()
    } catch (error) {
      console.error('❌ Failed to save service:', error)
      console.error('❌ Error response:', error.response?.data)
      console.error('❌ Error status:', error.response?.status)
      console.error('❌ Request data was:', submitData)
      
      // More detailed error message
      let errorMessage = 'Failed to save service'
      if (error.response?.status === 500) {
        errorMessage = 'Server error occurred. Please check the console for details and try again.'
      } else if (error.response?.data) {
        const errorData = error.response.data
        if (typeof errorData === 'object') {
          const fieldErrors = Object.entries(errorData).map(([field, errors]) => {
            return `${field}: ${Array.isArray(errors) ? errors.join(', ') : errors}`
          }).join('; ')
          errorMessage = `Validation errors: ${fieldErrors}`
        }
      }
      alert(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 w-full max-w-md max-h-[90vh] overflow-y-auto">
        <h3 className="text-lg font-semibold mb-4">
          {service?.id ? 'Edit Service' : 'Add Service'}
        </h3>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Service Name *
            </label>
            <input
              type="text"
              value={formData.name}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              placeholder="Enter service name"
              required
            />
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Category (Optional)
            </label>
            <select
              value={formData.category}
              onChange={(e) => setFormData({ ...formData, category: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
            >
              <option value="">No Category</option>
              {categories.map((category) => (
                <option key={category.id} value={category.id}>
                  {category.name}
                </option>
              ))}
            </select>
            <p className="text-xs text-gray-500 mt-1">
              Leave empty to create a service without a category
            </p>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            <textarea
              value={formData.description}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              rows="3"
              placeholder="Enter service description"
            />
          </div>
          
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Price ($) *
              </label>
              <input
                type="number"
                step="0.01"
                min="0"
                value={formData.price}
                onChange={(e) => setFormData({ ...formData, price: e.target.value })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="0.00"
                required
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Duration (minutes) *
              </label>
              <input
                type="number"
                min="1"
                value={formData.duration}
                onChange={(e) => setFormData({ ...formData, duration: parseInt(e.target.value) || 60 })}
                className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                placeholder="60"
                required
              />
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Buffer Time (minutes)
            </label>
            <input
              type="number"
              min="0"
              value={formData.buffer_time}
              onChange={(e) => setFormData({ ...formData, buffer_time: parseInt(e.target.value) || 0 })}
              className="w-full p-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500"
              placeholder="15"
            />
            <p className="text-xs text-gray-500 mt-1">
              Time buffer before and after the service
            </p>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="is_active_service"
                checked={formData.is_active}
                onChange={(e) => setFormData({ ...formData, is_active: e.target.checked })}
                className="mr-2"
              />
              <label htmlFor="is_active_service" className="text-sm text-gray-700">
                Active
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="show_online_service"
                checked={formData.show_online}
                onChange={(e) => setFormData({ ...formData, show_online: e.target.checked })}
                className="mr-2"
              />
              <label htmlFor="show_online_service" className="text-sm text-gray-700">
                Show in Online Booking
              </label>
            </div>
          </div>
          
          <div className="flex space-x-3 pt-4">
            <button
              type="button"
              onClick={onClose}
              className="flex-1 px-4 py-2 text-gray-700 bg-gray-100 rounded-lg hover:bg-gray-200"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={isSubmitting}
              className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {isSubmitting ? 'Saving...' : (service?.id ? 'Update' : 'Create')}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default ServiceModal 