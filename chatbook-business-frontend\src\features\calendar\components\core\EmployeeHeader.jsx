import React from 'react'

/**
 * EmployeeHeader - Displays employee information in calendar headers
 * Supports both compact and full layouts
 */
const EmployeeHeader = ({
  employee,
  date,
  isToday = false,
  compact = false,
  showAvatar = true,
  showDate = true,
  onClick
}) => {
  const handleClick = () => {
    if (onClick) {
      onClick(employee)
    }
  }

  if (compact) {
    return (
      <div 
        className={`p-2 text-center cursor-pointer hover:bg-gray-100 transition-colors ${onClick ? 'cursor-pointer' : ''}`}
        onClick={handleClick}
      >
        {showAvatar && (
          <div 
            className="w-6 h-6 rounded-full text-xs font-semibold text-white flex items-center justify-center mx-auto mb-1"
            style={{ backgroundColor: employee.color }}
          >
            {employee.avatar || employee.name.charAt(0)}
          </div>
        )}
        <div className="text-xs font-medium text-gray-900 truncate">
          {employee.name.split(' ')[0]}
        </div>
        {showDate && (
          <div className={`text-xs ${isToday ? 'text-blue-600 font-semibold' : 'text-gray-500'}`}>
            {date.getDate()}
          </div>
        )}
      </div>
    )
  }

  return (
    <div 
      className={`p-3 text-center cursor-pointer hover:bg-gray-100 transition-colors ${onClick ? 'cursor-pointer' : ''}`}
      onClick={handleClick}
    >
      <div className="flex items-center justify-center space-x-2 mb-2">
        {showAvatar && (
          <div 
            className="w-8 h-8 rounded-full text-sm font-semibold text-white flex items-center justify-center"
            style={{ backgroundColor: employee.color }}
          >
            {employee.avatar || employee.name.charAt(0)}
          </div>
        )}
        <div className="text-sm font-medium text-gray-900">
          {employee.name}
        </div>
      </div>
      
      {showDate && (
        <>
          <div className="text-sm font-medium text-gray-600">
            {date.toLocaleDateString('en-US', { weekday: 'short' })}
          </div>
          <div className={`text-lg font-semibold ${isToday ? 'text-blue-600' : 'text-gray-900'}`}>
            {date.getDate()}
          </div>
        </>
      )}
    </div>
  )
}

export default EmployeeHeader 