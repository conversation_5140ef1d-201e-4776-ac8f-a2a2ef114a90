from django.db import models
from django.utils.translation import gettext_lazy as _
from accounts.models import SocialAccount, User
import uuid


class CalendarConnection(models.Model):
    """
    Model for storing calendar-specific connection metadata.
    Extends the SocialAccount functionality with calendar-specific fields.
    """
    
    class ProviderChoices(models.TextChoices):
        GOOGLE = 'google', 'Google Calendar'
        OUTLOOK = 'outlook', 'Microsoft Outlook'
        APPLE = 'apple', 'Apple iCloud Calendar'
    
    class StatusChoices(models.TextChoices):
        ACTIVE = 'active', 'Active'
        INACTIVE = 'inactive', 'Inactive'
        ERROR = 'error', 'Error'
        EXPIRED = 'expired', 'Token Expired'

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey(
        User, 
        on_delete=models.CASCADE, 
        related_name='calendar_connections'
    )
    
    # Link to social account for OAuth data
    social_account = models.OneToOneField(
        SocialAccount,
        on_delete=models.CASCADE,
        related_name='calendar_connection',
        null=True,
        blank=True
    )
    
    # Calendar-specific fields
    provider = models.CharField(
        max_length=20, 
        choices=ProviderChoices.choices,
        db_index=True
    )
    
    provider_calendar_id = models.CharField(
        max_length=255,
        default='primary',
        help_text=_("Calendar ID from the provider (e.g., 'primary' for Google)")
    )
    
    calendar_name = models.CharField(
        max_length=255,
        blank=True,
        help_text=_("Display name of the calendar")
    )
    
    connection_status = models.CharField(
        max_length=20,
        choices=StatusChoices.choices,
        default=StatusChoices.ACTIVE,
        db_index=True
    )
    
    # Sync tracking
    last_sync_at = models.DateTimeField(null=True, blank=True, db_index=True)
    last_sync_status = models.CharField(max_length=50, blank=True)
    sync_enabled = models.BooleanField(default=True)
    
    # Error tracking
    last_error = models.TextField(blank=True)
    error_count = models.PositiveIntegerField(default=0)
    
    # Metadata
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Settings
    sync_settings = models.JSONField(
        default=dict,
        blank=True,
        help_text=_("Provider-specific sync settings")
    )

    class Meta:
        verbose_name = _('Calendar Connection')
        verbose_name_plural = _('Calendar Connections')
        ordering = ['-created_at']
        constraints = [
            models.UniqueConstraint(
                fields=['user', 'provider'],
                name='unique_user_provider_calendar'
            )
        ]
        indexes = [
            models.Index(fields=['user', 'connection_status'], name='cal_user_status_idx'),
            models.Index(fields=['provider', 'connection_status'], name='cal_provider_status_idx'),
            models.Index(fields=['last_sync_at'], name='cal_last_sync_idx'),
        ]

    def __str__(self):
        return f"{self.user.email} - {self.get_provider_display()}"

    @property
    def is_active(self):
        """Check if connection is active"""
        return self.connection_status == self.StatusChoices.ACTIVE

    @property
    def needs_reauth(self):
        """Check if connection needs re-authentication"""
        return self.connection_status in [
            self.StatusChoices.EXPIRED,
            self.StatusChoices.ERROR
        ]

    def mark_error(self, error_message):
        """Mark connection as having an error"""
        self.connection_status = self.StatusChoices.ERROR
        self.last_error = error_message
        self.error_count += 1
        self.save(update_fields=['connection_status', 'last_error', 'error_count', 'updated_at'])

    def mark_success(self):
        """Mark connection as successful"""
        self.connection_status = self.StatusChoices.ACTIVE
        self.last_error = ''
        self.error_count = 0
        self.last_sync_at = models.functions.Now()
        self.save(update_fields=['connection_status', 'last_error', 'error_count', 'last_sync_at', 'updated_at'])

    def get_oauth_tokens(self):
        """Get OAuth tokens from associated social account"""
        if not self.social_account:
            return None
        
        return {
            'access_token': self.social_account.access_token,
            'refresh_token': self.social_account.refresh_token,
            'token_expiry': self.social_account.token_expiry,
        }


class CalendarEvent(models.Model):
    """
    Model for tracking calendar events synced to external calendars.
    Links internal appointments to external calendar events.
    """
    
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    
    # Link to internal appointment
    appointment_id = models.IntegerField(db_index=True)
    
    # Calendar connection
    calendar_connection = models.ForeignKey(
        CalendarConnection,
        on_delete=models.CASCADE,
        related_name='synced_events'
    )
    
    # External calendar event details
    external_event_id = models.CharField(
        max_length=255,
        help_text=_("Event ID from the external calendar provider")
    )
    
    external_calendar_id = models.CharField(
        max_length=255,
        default='primary',
        help_text=_("Calendar ID where the event was created")
    )
    
    # Sync metadata
    last_synced_at = models.DateTimeField(auto_now=True)
    sync_status = models.CharField(
        max_length=20,
        choices=[
            ('synced', 'Synced'),
            ('pending', 'Pending'),
            ('failed', 'Failed'),
            ('deleted', 'Deleted'),
        ],
        default='synced'
    )
    
    # Event data snapshot (for change detection)
    event_data_hash = models.CharField(
        max_length=64,
        help_text=_("Hash of event data to detect changes")
    )
    
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = _('Calendar Event')
        verbose_name_plural = _('Calendar Events')
        ordering = ['-created_at']
        constraints = [
            models.UniqueConstraint(
                fields=['appointment_id', 'calendar_connection'],
                name='unique_appointment_calendar'
            ),
            models.UniqueConstraint(
                fields=['external_event_id', 'calendar_connection'],
                name='unique_external_event_calendar'
            )
        ]
        indexes = [
            models.Index(fields=['appointment_id'], name='cal_appointment_idx'),
            models.Index(fields=['external_event_id'], name='cal_external_event_idx'),
            models.Index(fields=['sync_status'], name='cal_sync_status_idx'),
        ]

    def __str__(self):
        return f"Event {self.appointment_id} -> {self.external_event_id}"