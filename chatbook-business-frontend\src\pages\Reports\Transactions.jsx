import { useState, useEffect } from "react";
import { ChevronDownIcon, CalendarIcon } from "@heroicons/react/24/outline";
import Button from "../../components/Button";
import CustomerSelector from "../../components/CustomerSelector";
import DateRangePicker from "../../components/DateRangePicker";
import { transactionApi } from "../../features/reports/services/transactionApi";

function Transactions() {
  // ========== State Management ==========

  // Filter conditions
  const [filters, setFilters] = useState({
    startDate: new Date(),
    endDate: new Date(),
    serviceProvider: "all",
    customer: [], // Change to array to support multi-select
  });

  // Advanced filter conditions
  const [advancedFilters, setAdvancedFilters] = useState({
    appointmentDate: null,
    depositInMerchant: "all",
    transactionType: "all",
    checkedOutBy: "all",
    source: "all",
    ccTransactionMode: "all",
    discount: "all",
    depositStatus: "all",
    search: "",
    showRefundedOnly: false,
    includePastEmployees: true,
  });

  // Show/hide advanced filters
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Transaction data
  const [transactions, setTransactions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasRunReport, setHasRunReport] = useState(false);

  // Summary statistics
  const [summary, setSummary] = useState({
    moneyEarned: 0,
    drawerBalance: 0,
  });

  // Dropdown options data
  const [serviceProviders, setServiceProviders] = useState([
    { id: "all", name: "Service Providers" },
  ]);

  const [customers, setCustomers] = useState([]);

  // ========== Initial Data Loading ==========

  useEffect(() => {
    loadInitialData();
  }, []);

  // Load initial data (service providers and customers)
  const loadInitialData = async () => {
    try {
      // Load service providers
      const staffResponse = await transactionApi.getServiceProviders();
      const staffList = staffResponse.results || staffResponse;
      setServiceProviders([
        { id: "all", name: "Service Providers" },
        ...staffList.map((staff) => ({
          id: staff.id,
          name: staff.name,
        })),
      ]);

      // Load customer list
      const customersResponse = await transactionApi.getCustomers({
        limit: 100,
      });
      const customerList = customersResponse.results || customersResponse;

      // Use the same customer data as checkout page
      const checkoutCustomers = [
        {
          id: "customer1",
          name: "Yuechen Guo",
          email: "<EMAIL>",
          phone: null,
          avatar: "YG",
          customerSince: "Jun 30, 2025",
          lastVisit: "---",
          birthday: "Not available",
          membership: "---",
          points: "0 pt",
        },
        {
          id: "customer2",
          name: "Jane Smith",
          email: "<EMAIL>",
          phone: null,
          avatar: "JS",
          customerSince: "Jan 15, 2024",
          lastVisit: "Dec 20, 2024",
          birthday: "Mar 15, 1990",
          membership: "Gold",
          points: "150 pt",
        },
        {
          id: "customer3",
          name: "Bob Johnson",
          email: "<EMAIL>",
          phone: null,
          avatar: "BJ",
          customerSince: "Sep 08, 2023",
          lastVisit: "Nov 30, 2024",
          birthday: "Jul 22, 1985",
          membership: "Silver",
          points: "85 pt",
        },
      ];

      setCustomers(checkoutCustomers);
    } catch (error) {
      console.error("Failed to load initial data:", error);
      // Set checkout page customer data
      setCustomers([
        {
          id: "customer1",
          name: "Yuechen Guo",
          email: "<EMAIL>",
          phone: null,
          avatar: "YG",
          customerSince: "Jun 30, 2025",
          lastVisit: "---",
          birthday: "Not available",
          membership: "---",
          points: "0 pt",
        },
        {
          id: "customer2",
          name: "Jane Smith",
          email: "<EMAIL>",
          phone: null,
          avatar: "JS",
          customerSince: "Jan 15, 2024",
          lastVisit: "Dec 20, 2024",
          birthday: "Mar 15, 1990",
          membership: "Gold",
          points: "150 pt",
        },
        {
          id: "customer3",
          name: "Bob Johnson",
          email: "<EMAIL>",
          phone: null,
          avatar: "BJ",
          customerSince: "Sep 08, 2023",
          lastVisit: "Nov 30, 2024",
          birthday: "Jul 22, 1985",
          membership: "Silver",
          points: "85 pt",
        },
      ]);
    }
  };

  // ========== Event Handlers ==========

  // Handle filter condition changes
  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Handle advanced filter condition changes
  const handleAdvancedFilterChange = (key, value) => {
    setAdvancedFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Run report
  const handleRunReport = async () => {
    setLoading(true);
    setError(null);
    setHasRunReport(true);

    try {
      // Build API query parameters
      const queryParams = {
        startDate: filters.startDate.toISOString().split("T")[0],
        endDate: filters.endDate.toISOString().split("T")[0],
        serviceProvider:
          filters.serviceProvider !== "all"
            ? filters.serviceProvider
            : undefined,
      };

      // Handle multi-select customers
      if (filters.customer && filters.customer.length > 0) {
        queryParams.customers = filters.customer;
      }

      // Add advanced filter parameters
      if (advancedFilters.appointmentDate) {
        queryParams.appointmentDate = advancedFilters.appointmentDate;
      }
      if (advancedFilters.depositInMerchant !== "all") {
        queryParams.depositInMerchant = advancedFilters.depositInMerchant;
      }
      if (advancedFilters.transactionType !== "all") {
        queryParams.transactionType = advancedFilters.transactionType;
      }
      if (advancedFilters.checkedOutBy !== "all") {
        queryParams.checkedOutBy = advancedFilters.checkedOutBy;
      }
      if (advancedFilters.source !== "all") {
        queryParams.source = advancedFilters.source;
      }
      if (advancedFilters.ccTransactionMode !== "all") {
        queryParams.ccTransactionMode = advancedFilters.ccTransactionMode;
      }
      if (advancedFilters.discount !== "all") {
        queryParams.discount = advancedFilters.discount;
      }
      if (advancedFilters.depositStatus !== "all") {
        queryParams.depositStatus = advancedFilters.depositStatus;
      }
      if (advancedFilters.search.trim()) {
        queryParams.search = advancedFilters.search.trim();
      }
      if (advancedFilters.showRefundedOnly) {
        queryParams.showRefundedOnly = true;
      }
      if (!advancedFilters.includePastEmployees) {
        queryParams.excludePastEmployees = true;
      }

      // Call the real API
      const transactionsResponse = await transactionApi.getTransactions(
        queryParams
      );

      const transactionsList =
        transactionsResponse.results || transactionsResponse;
      setTransactions(transactionsList);

      // Get summary statistics
      const summaryResponse = await transactionApi.getTransactionSummary(
        queryParams
      );

      const drawerResponse = await transactionApi.getDrawerBalance(
        filters.startDate.toISOString().split("T")[0]
      );

      setSummary({
        moneyEarned: summaryResponse.total_earned || 0,
        drawerBalance: drawerResponse.balance || 0,
      });
    } catch (err) {
      setError(err.message);
      console.error("Failed to load transactions:", err);
    } finally {
      setLoading(false);
    }
  };

  // Export data
  const handleExport = async () => {
    try {
      setLoading(true);

      // Build export parameters
      const exportParams = {
        startDate: filters.startDate.toISOString().split("T")[0],
        endDate: filters.endDate.toISOString().split("T")[0],
        serviceProvider:
          filters.serviceProvider !== "all"
            ? filters.serviceProvider
            : undefined,
      };

      // Handle multi-select customers
      if (filters.customer && filters.customer.length > 0) {
        exportParams.customers = filters.customer;
      }

      // Add advanced filter parameters to export
      if (advancedFilters.appointmentDate) {
        exportParams.appointmentDate = advancedFilters.appointmentDate;
      }
      if (advancedFilters.depositInMerchant !== "all") {
        exportParams.depositInMerchant = advancedFilters.depositInMerchant;
      }
      if (advancedFilters.transactionType !== "all") {
        exportParams.transactionType = advancedFilters.transactionType;
      }
      if (advancedFilters.checkedOutBy !== "all") {
        exportParams.checkedOutBy = advancedFilters.checkedOutBy;
      }
      if (advancedFilters.source !== "all") {
        exportParams.source = advancedFilters.source;
      }
      if (advancedFilters.ccTransactionMode !== "all") {
        exportParams.ccTransactionMode = advancedFilters.ccTransactionMode;
      }
      if (advancedFilters.discount !== "all") {
        exportParams.discount = advancedFilters.discount;
      }
      if (advancedFilters.depositStatus !== "all") {
        exportParams.depositStatus = advancedFilters.depositStatus;
      }
      if (advancedFilters.search.trim()) {
        exportParams.search = advancedFilters.search.trim();
      }
      if (advancedFilters.showRefundedOnly) {
        exportParams.showRefundedOnly = true;
      }
      if (!advancedFilters.includePastEmployees) {
        exportParams.excludePastEmployees = true;
      }

      const blob = await transactionApi.exportTransactions(exportParams, "csv");

      // Create download link
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = url;
      a.download = `transactions_${
        filters.startDate.toISOString().split("T")[0]
      }_${filters.endDate.toISOString().split("T")[0]}.csv`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } catch (error) {
      console.error("Export failed:", error);
      alert("Export failed: " + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Print report
  const handlePrint = () => {
    // Implement print functionality
    window.print();
  };

  // ========== Calculations ==========

  // Calculate totals
  const calculateTotals = () => {
    return transactions.reduce(
      (totals, tx) => ({
        qty: totals.qty + (tx.qty || 0),
        price: totals.price + (tx.price || 0),
        tax: totals.tax + (tx.tax || 0),
        tip: totals.tip + (tx.tip || 0),
        discount: totals.discount + (tx.discount || 0),
        amountPaid: totals.amountPaid + (tx.amountPaid || 0),
        cash: totals.cash + (tx.cash || 0),
        check: totals.check + (tx.check || 0),
        giftCard: totals.giftCard + (tx.giftCard || 0),
        package: totals.package + (tx.package || 0),
        membership: totals.membership + (tx.membership || 0),
        creditCard: totals.creditCard + (tx.creditCard || 0),
        bankAccount: totals.bankAccount + (tx.bankAccount || 0),
        vagaro: totals.vagaro + (tx.vagaro || 0),
        payLater: totals.payLater + (tx.payLater || 0),
        other: totals.other + (tx.other || 0),
        iouInvoice: totals.iouInvoice + (tx.iouInvoice || 0),
        points: totals.points + (parseInt(tx.points) || 0),
        merchantAccount: totals.merchantAccount + (tx.merchantAccount || 0),
        changeDue: totals.changeDue + (tx.changeDue || 0),
      }),
      {
        qty: 0,
        price: 0,
        tax: 0,
        tip: 0,
        discount: 0,
        amountPaid: 0,
        cash: 0,
        check: 0,
        giftCard: 0,
        package: 0,
        membership: 0,
        creditCard: 0,
        bankAccount: 0,
        vagaro: 0,
        payLater: 0,
        other: 0,
        iouInvoice: 0,
        points: 0,
        merchantAccount: 0,
        changeDue: 0,
      }
    );
  };

  const totals = calculateTotals();

  // ========== Component Rendering ==========

  return (
    <div className="container mx-auto px-4 py-6">
      {/* 筛选条件区域 */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
          {/* 交易日期 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Transaction Date:
            </label>
            <DateRangePicker
              startDate={filters.startDate}
              endDate={filters.endDate}
              onChange={({ startDate, endDate }) => {
                setFilters((prev) => ({
                  ...prev,
                  startDate,
                  endDate,
                }));
              }}
              placeholder="Select date range"
            />
          </div>

          {/* 服务提供者 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Service Providers:
            </label>
            <div className="relative">
              <select
                value={filters.serviceProvider}
                onChange={(e) =>
                  handleFilterChange("serviceProvider", e.target.value)
                }
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none"
              >
                {serviceProviders.map((provider) => (
                  <option key={provider.id} value={provider.id}>
                    {provider.name}
                  </option>
                ))}
              </select>
              <ChevronDownIcon className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none" />
            </div>
          </div>

          {/* 客户 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Customer:
            </label>
            <CustomerSelector
              value={filters.customer}
              onChange={(selectedCustomers) =>
                handleFilterChange("customer", selectedCustomers)
              }
              customers={customers}
              placeholder="All Customers"
            />
          </div>

          {/* 运行报告按钮 */}
          <div>
            <Button
              onClick={handleRunReport}
              disabled={loading}
              className="w-full bg-blue-600 text-white hover:bg-blue-700 focus:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 py-2 px-4 rounded-md disabled:opacity-50 disabled:cursor-not-allowed font-medium shadow-sm transition-colors duration-200"
            >
              {loading ? "Loading..." : "Run Report"}
            </Button>
          </div>
        </div>
      </div>

      {/* 交易列表标题和高级筛选 */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-gray-800">
          Transaction List
        </h2>
        <button
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
        >
          Advanced Filters {showAdvancedFilters ? "▲" : "▼"}
        </button>
      </div>

      {/* 高级筛选面板 */}
      {showAdvancedFilters && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {/* 预约日期 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Appointment Date:
              </label>
              <div className="relative">
                <input
                  type="date"
                  value={advancedFilters.appointmentDate || ""}
                  onChange={(e) =>
                    handleAdvancedFilterChange(
                      "appointmentDate",
                      e.target.value
                    )
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
              </div>
            </div>

            {/* 商户存款 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Deposit in Merchant:
              </label>
              <div className="relative">
                <select
                  value={advancedFilters.depositInMerchant}
                  onChange={(e) =>
                    handleAdvancedFilterChange(
                      "depositInMerchant",
                      e.target.value
                    )
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                >
                  <option value="all">Select Merchant Account</option>
                  <option value="primary">Primary Account</option>
                  <option value="secondary">Secondary Account</option>
                </select>
                <ChevronDownIcon className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none" />
              </div>
            </div>

            {/* 交易类型 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Transaction Type:
              </label>
              <div className="relative">
                <select
                  value={advancedFilters.transactionType}
                  onChange={(e) =>
                    handleAdvancedFilterChange(
                      "transactionType",
                      e.target.value
                    )
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                >
                  <option value="all">All</option>
                  <option value="sale">Sale</option>
                  <option value="refund">Refund</option>
                  <option value="void">Void</option>
                </select>
                <ChevronDownIcon className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none" />
              </div>
            </div>

            {/* 结账人 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Checked Out By:
              </label>
              <div className="relative">
                <select
                  value={advancedFilters.checkedOutBy}
                  onChange={(e) =>
                    handleAdvancedFilterChange("checkedOutBy", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                >
                  <option value="all">Select Service Providers</option>
                  {serviceProviders.slice(1).map((provider) => (
                    <option key={provider.id} value={provider.id}>
                      {provider.name}
                    </option>
                  ))}
                </select>
                <ChevronDownIcon className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none" />
              </div>
            </div>

            {/* 来源 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Source:
              </label>
              <div className="relative">
                <select
                  value={advancedFilters.source}
                  onChange={(e) =>
                    handleAdvancedFilterChange("source", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                >
                  <option value="all">Source</option>
                  <option value="walk-in">Walk-in</option>
                  <option value="online">Online</option>
                  <option value="phone">Phone</option>
                  <option value="app">Mobile App</option>
                </select>
                <ChevronDownIcon className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none" />
              </div>
            </div>

            {/* 信用卡交易模式 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                CC Transaction Mode:
              </label>
              <div className="relative">
                <select
                  value={advancedFilters.ccTransactionMode}
                  onChange={(e) =>
                    handleAdvancedFilterChange(
                      "ccTransactionMode",
                      e.target.value
                    )
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                >
                  <option value="all">All</option>
                  <option value="chip">Chip</option>
                  <option value="swipe">Swipe</option>
                  <option value="tap">Tap</option>
                  <option value="manual">Manual Entry</option>
                </select>
                <ChevronDownIcon className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none" />
              </div>
            </div>

            {/* 折扣 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Discount:
              </label>
              <div className="relative">
                <select
                  value={advancedFilters.discount}
                  onChange={(e) =>
                    handleAdvancedFilterChange("discount", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                >
                  <option value="all">Select Discount</option>
                  <option value="percentage">Percentage Discount</option>
                  <option value="fixed">Fixed Amount</option>
                  <option value="loyalty">Loyalty Discount</option>
                </select>
                <ChevronDownIcon className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none" />
              </div>
            </div>

            {/* 存款状态 */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Deposit Status:
              </label>
              <div className="relative">
                <select
                  value={advancedFilters.depositStatus}
                  onChange={(e) =>
                    handleAdvancedFilterChange("depositStatus", e.target.value)
                  }
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 appearance-none"
                >
                  <option value="all">Select</option>
                  <option value="pending">Pending</option>
                  <option value="completed">Completed</option>
                  <option value="failed">Failed</option>
                </select>
                <ChevronDownIcon className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none" />
              </div>
            </div>
          </div>

          {/* 搜索框 */}
          <div className="mt-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Search:
            </label>
            <div className="relative">
              <input
                type="text"
                value={advancedFilters.search}
                onChange={(e) =>
                  handleAdvancedFilterChange("search", e.target.value)
                }
                placeholder="Barcode, Brand, Product, Product Type, Transaction ID"
                className="w-full px-10 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <div className="absolute left-3 top-2.5">
                <svg
                  className="h-5 w-5 text-gray-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                  />
                </svg>
              </div>
            </div>
          </div>

          {/* 复选框选项 */}
          <div className="mt-4 flex gap-8">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={advancedFilters.showRefundedOnly}
                onChange={(e) =>
                  handleAdvancedFilterChange(
                    "showRefundedOnly",
                    e.target.checked
                  )
                }
                className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">
                Show Refunded Items Only
              </span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={advancedFilters.includePastEmployees}
                onChange={(e) =>
                  handleAdvancedFilterChange(
                    "includePastEmployees",
                    e.target.checked
                  )
                }
                className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">
                Include Past Employees
              </span>
            </label>
          </div>
        </div>
      )}

      {!hasRunReport ? (
        // Placeholder content
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 min-h-96">
          <div className="flex flex-col items-center justify-center py-20">
            <div className="text-6xl text-gray-400 mb-4">↑</div>
            <p className="text-lg text-gray-600 font-medium">
              Select filters above, then press Run Report.
            </p>
          </div>
        </div>
      ) : (
        <div>
          {/* Statistics info bar */}
          <div className="bg-red-600 text-white rounded-lg p-4 mb-6 flex justify-between items-center">
            <div className="flex items-center">
              <span className="text-lg font-medium">
                Money Earned: ${summary.moneyEarned.toFixed(2)}
              </span>
            </div>
            <div className="flex items-center">
              <button className="bg-white bg-opacity-20 hover:bg-opacity-30 rounded-full p-2 mr-4">
                <ChevronDownIcon className="h-5 w-5" />
              </button>
              <span className="text-lg font-medium">
                Drawer Balance: ${summary.drawerBalance.toFixed(2)}
              </span>
            </div>
          </div>
          {/* Transaction table */}
          <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Checkout Date
                      <br />
                      Checkout By
                      <br />
                      Transaction ID
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      App.Date
                      <br />
                      Customer
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Item Sold
                      <br />
                      Sold By
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Source
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Qty
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Price
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tax
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Tip
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Disc
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Amt
                      <br />
                      Paid
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Cash
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Check
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      GC
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Pkg
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Mbsp
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      CC
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Bank
                      <br />
                      Account
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Vagaro
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Pay
                      <br />
                      Later
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Other
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      IOU/
                      <br />
                      Invoice
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Points
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Merchant
                      <br />
                      Account
                    </th>
                    <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Change
                      <br />
                      Due
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {transactions.length === 0 ? (
                    <tr>
                      <td
                        colSpan="24"
                        className="px-6 py-8 text-center text-gray-500"
                      >
                        No Records Found.
                      </td>
                    </tr>
                  ) : (
                    transactions.map((transaction, index) => (
                      <tr key={index} className="hover:bg-gray-50">
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div>{transaction.checkoutDate}</div>
                          <div className="text-gray-500">
                            {transaction.checkoutBy}
                          </div>
                          <div className="text-gray-500">
                            {transaction.transactionId}
                          </div>
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div>{transaction.appDate}</div>
                          <div className="text-gray-500">
                            {transaction.customer}
                          </div>
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          <div>{transaction.itemSold}</div>
                          <div className="text-gray-500">
                            {transaction.soldBy}
                          </div>
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          {transaction.source}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          {transaction.qty}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.price?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.tax?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.tip?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.discount?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.amountPaid?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.cash?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.check?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.giftCard?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.package?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.membership?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.creditCard?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.bankAccount?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.vagaro?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.payLater?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.other?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.iouInvoice?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          {transaction.points || "0(0)"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.merchantAccount?.toFixed(2) || "0.00"}
                        </td>
                        <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                          ${transaction.changeDue?.toFixed(2) || "0.00"}
                        </td>
                      </tr>
                    ))
                  )}

                  {/* Total row */}
                  <tr className="bg-gray-100 font-medium">
                    <td className="px-3 py-4 text-sm text-gray-900" colSpan="4">
                      Total
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      {totals.qty}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.price.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.tax.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.tip.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      (${totals.discount.toFixed(2)})
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.amountPaid.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.cash.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.check.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.giftCard.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.package.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.membership.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.creditCard.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.bankAccount.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.vagaro.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.payLater.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.other.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.iouInvoice.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      {totals.points}(0)
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.merchantAccount.toFixed(2)}
                    </td>
                    <td className="px-3 py-4 text-sm text-gray-900">
                      ${totals.changeDue.toFixed(2)}
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          {/* Bottom action buttons */}
          <div className="flex justify-end space-x-4 mt-6">
            <button
              onClick={handleExport}
              className="px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              Export ▼
            </button>
            <button
              onClick={handlePrint}
              className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500"
            >
              Print
            </button>
          </div>
        </div>
      )}

      {/* Error notification */}
      {error && (
        <div className="fixed top-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded">
          <strong className="font-bold">Error: </strong>
          <span className="block sm:inline">{error}</span>
        </div>
      )}
    </div>
  );
}

export default Transactions;
