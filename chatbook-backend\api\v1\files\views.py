from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from rest_framework.response import Response
from rest_framework.parsers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, JSONPars<PERSON>
from rest_framework.views import APIView
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.shortcuts import get_object_or_404
from django.conf import settings

# S3 imports for file upload
import boto3
from botocore.exceptions import ClientError
import uuid
from datetime import datetime
import re
import os
import logging
import urllib.parse

from .serializers import FileUploadSerializer, UploadedFileResponseSerializer, FileProcessSerializer
from api.services.file_processing_service import FileProcessingService
from aws_services.sqs import sqs_service
from api.exceptions import S3UploadError, FileProcessingError, ConfigurationError
from customers.models import UploadedFile
from business.models import BusinessCustomer  # Local import to avoid circular deps
from apps.files.services.upload import file_upload_service  # NEW

# Get logger for this module
logger = logging.getLogger('api.views.files')


@method_decorator(csrf_exempt, name='dispatch')
class FileUploadView(APIView):
    """
    Simple file upload endpoint
    POST /api/files/upload/
    """
    
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]
    
    def post(self, request, *args, **kwargs):
        """Upload file to S3 and return metadata"""
        # Log the start of file upload
        user_id = getattr(request.user, 'id', 'anonymous')
        user_email = getattr(request.user, 'email', 'anonymous')
        logger.info(
            f"File upload started by user {user_email} (ID: {user_id})",
            extra={
                'user_id': user_id,
                'user_email': user_email,
                'request_method': request.method,
                'request_path': request.path,
            }
        )

        # Validate request data
        serializer = FileUploadSerializer(data=request.data)

        if not serializer.is_valid():
            logger.warning(
                f"File upload validation failed for user {user_email}",
                extra={
                    'user_id': user_id,
                    'validation_errors': serializer.errors,
                }
            )
            return Response(
                {'error': 'Invalid request data', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )

        # Get validated data
        file_obj = serializer.validated_data['file']
        file_type = serializer.validated_data.get('file_type', 'customer_import')
        description = serializer.validated_data.get('description', '')
        skip_duplicates = serializer.validated_data.get('skip_duplicates', True)
        update_existing = serializer.validated_data.get('update_existing', False)
        customer_uuid = serializer.validated_data.get('customer_uuid')
        category = serializer.validated_data.get('category')
        is_signed = serializer.validated_data.get('is_signed', False)

        # --------------------------------------------------------------
        #  Delegate the upload to FileUploadService to support multi-tenant
        #  logic and uniform behaviour across API + backend scripts.
        # --------------------------------------------------------------

        try:
            upload_result = file_upload_service.upload_file(
                file_obj=file_obj,
                user=request.user,
                is_signed=is_signed,
                file_type=file_type,
                description=description,
                skip_duplicates=skip_duplicates,
                update_existing=update_existing,
                customer_uuid=customer_uuid,
                category=category,
            )

            # upload_result can be a single UploadedFile or a list
            if isinstance(upload_result, list):
                uploaded_instances = upload_result
            else:
                uploaded_instances = [upload_result]

            # Prepare response data – map each UploadedFile via serializer
            response_data = UploadedFileResponseSerializer(
                uploaded_instances if len(uploaded_instances) > 1 else uploaded_instances[0],
                many=len(uploaded_instances) > 1,
            ).data

            logger.info(
                f"File(s) uploaded via service for user {user_email}: {len(uploaded_instances)} instance(s)",
                extra={
                    'user_id': user_id,
                    'uploaded_file_ids': [uf.file_id for uf in uploaded_instances],
                }
            )

            return Response(response_data, status=status.HTTP_201_CREATED)

        except Exception as e:
            logger.error(
                f"File upload failed via service for user {user_email}: {str(e)}",
                exc_info=True,
            )
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

        # --- Early exit above ---

        # Legacy path (should not reach here)

        # Generate file_id
        file_id = str(uuid.uuid4())

        # --------------------------------------------------------------
        # Determine business context for folder naming.
        # Mirror logic in FileUploadService so both endpoints behave identically.
        # --------------------------------------------------------------

        business = None

        # a. BusinessUser membership
        membership = (
            request.user.business_memberships.filter(is_active=True, is_primary=True).first()
            or request.user.business_memberships.filter(is_active=True).first()
        )
        if membership and membership.business:
            business = membership.business

        # b. Employee profile
        if not business and hasattr(request.user, "employee_profile") and request.user.employee_profile:
            business = request.user.employee_profile.business

        # c. Business owner
        if not business:
            owner_business_qs = getattr(request.user, "owned_businesses", None)
            if owner_business_qs:
                business = owner_business_qs.first()

        # d. Customer relationship -> BusinessCustomer mapping
        if not business and hasattr(request.user, "customer_profile") and request.user.customer_profile:
            bc = (
                BusinessCustomer.objects
                .filter(customer=request.user.customer_profile)
                .select_related("business")
                .first()
            )
            if bc:
                business = bc.business

        tenant_id = business.id if business else "global"

        # Normalise category
        allowed_categories = {"forms", "photos", "notes", "shared"}
        category = (category or "forms").lower()
        if category not in allowed_categories:
            category = "forms"

        # ---------------------------------------------------------------
        #  Auto-scope uploads initiated by customer users
        # ---------------------------------------------------------------
        # If the caller did not supply a customer_uuid (which front-end
        # clients often omit), but the authenticated user *is* a customer
        # profile, default to their UUID so assets are stored under
        # tenant_<id>/customer_<customerUUID>/ rather than the shared bucket.
        if customer_uuid is None and hasattr(request.user, "customer_profile") and request.user.customer_profile:
            # Use the authenticated user's UUID so folder name is UUID rather
            # than the integer PK of CustomerProfile.
            customer_uuid = str(request.user.id)

        ext = os.path.splitext(file_obj.name)[1].lower()
        timestamp_utc = datetime.utcnow().strftime("%Y%m%dT%H%M%SZ")
        unique_filename = f"{request.user.id}_{timestamp_utc}{ext}"

        tenant_prefix = f"tenant_{tenant_id}"
        if customer_uuid is None:
            s3_key = f"{tenant_prefix}/shared/{unique_filename}"
        else:
            s3_key = f"{tenant_prefix}/customer_{customer_uuid}/{category}/{unique_filename}"

        # Log file details
        logger.info(
            f"Uploading file to S3: {s3_key}",
            extra={
                'file_id': file_id,
                's3_key': s3_key,
                'user_id': request.user.id,
            }
        )
        
        # ------------------------------------------------------------------
        #  Bucket policy requires env / tenant / form / status tags.
        # ------------------------------------------------------------------
        default_tags = {
            'env': os.getenv('ENV_NAME', 'dev'),
            'tenant': str(tenant_id),
            'form': 'shared' if customer_uuid is None else category,
            'status': 'uploaded',
        }

        # S3 UPLOAD LOGIC
        try:
            logger.debug(f"Initializing S3 client for file upload {file_id}")

            # Initialize S3 client using AWS credential chain
            # This will automatically use:
            # 1. Environment variables (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY)
            # 2. AWS credentials file (~/.aws/credentials)
            # 3. IAM roles (for EC2/ECS/Lambda)
            # 4. AWS CLI profiles (if AWS_PROFILE is set)
            aws_profile = getattr(settings, 'AWS_PROFILE', None)
            region_name = getattr(settings, 'AWS_S3_REGION_NAME', 'us-west-2')

            if aws_profile:
                # Use specific profile for local development
                logger.debug(f"Using AWS profile: {aws_profile}")
                session = boto3.Session(
                    profile_name=aws_profile,
                    region_name=region_name
                )
                s3_client = session.client('s3')
            else:
                # Use default credential chain (recommended for production)
                logger.debug("Using default AWS credential chain")
                s3_client = boto3.client('s3', region_name=region_name)

            # Validate AWS configuration
            bucket_name = getattr(settings, 'AWS_STORAGE_BUCKET_NAME', os.getenv('AWS_STORAGE_BUCKET_NAME'))
            access_key = os.getenv('AWS_ACCESS_KEY_ID')
            secret_key = os.getenv('AWS_SECRET_ACCESS_KEY')

            # Provide detailed error messages for configuration issues
            if not bucket_name:
                logger.error(
                    f"AWS S3 bucket name not configured for file upload {file_id}",
                    extra={
                        'file_id': file_id,
                        'user_id': getattr(request.user, 'id', 'anonymous'),
                        'aws_access_key_configured': bool(access_key),
                        'aws_secret_key_configured': bool(secret_key)
                    }
                )
                raise ConfigurationError('AWS S3 bucket name not configured. Please set AWS_STORAGE_BUCKET_NAME in your environment variables.')

            if not access_key or not secret_key:
                logger.error(
                    f"AWS credentials not configured for file upload {file_id}",
                    extra={
                        'file_id': file_id,
                        'user_id': getattr(request.user, 'id', 'anonymous'),
                        'bucket_name': bucket_name,
                        'aws_access_key_configured': bool(access_key),
                        'aws_secret_key_configured': bool(secret_key)
                    }
                )
                missing_creds = []
                if not access_key:
                    missing_creds.append('AWS_ACCESS_KEY_ID')
                if not secret_key:
                    missing_creds.append('AWS_SECRET_ACCESS_KEY')

                raise ConfigurationError(f'AWS credentials not configured. Missing: {", ".join(missing_creds)}. Please set these in your .env file.')

            # Upload file to S3
            file_obj.seek(0)  # Reset file pointer to beginning
            s3_client.upload_fileobj(
                file_obj,
                bucket_name,
                s3_key,
                ExtraArgs={
                    'ContentType': file_obj.content_type or 'application/octet-stream',
                    'Metadata': {
                        'file_id': file_id,
                        'original_name': file_obj.name,
                        'uploaded_by': str(request.user.id)
                    },
                    'Tagging': urllib.parse.urlencode(default_tags)
                }
            )

            logger.info(
                f"File successfully uploaded to S3: {file_id}",
                extra={
                    'file_id': file_id,
                    's3_key': s3_key,
                    'user_id': request.user.id,
                }
            )

            # Create uploaded file record with S3 information
            uploaded_file = UploadedFile(
                file_id=file_id,
                file_name=file_obj.name,
                file_size=file_obj.size,
                file_type=file_type,
                description=description,
                skip_duplicates=skip_duplicates,
                update_existing=update_existing,
                uploaded_by=request.user
            )

            # Store only the S3 key in the FileField so .name is the key and storage.url() can build the URL
            uploaded_file.file.name = s3_key
            uploaded_file.save()

            logger.info(
                f"File record created in database: {file_id}",
                extra={
                    'file_id': file_id,
                    'uploaded_file_id': uploaded_file.id,
                    'user_id': request.user.id,
                }
            )

        except ClientError as e:
            logger.error(
                f"S3 upload failed for file {file_id}: {str(e)}",
                extra={
                    'file_id': file_id,
                    'error_code': e.response.get('Error', {}).get('Code'),
                    'error_message': e.response.get('Error', {}).get('Message'),
                    'user_id': request.user.id,
                },
                exc_info=True
            )
            raise S3UploadError(f'S3 upload failed: {str(e)}')

        except Exception as e:
            logger.error(
                f"Unexpected error during file upload {file_id}: {str(e)}",
                extra={
                    'file_id': file_id,
                    'user_id': request.user.id,
                },
                exc_info=True
            )
            raise
        
        # Return response in specified format
        response_serializer = UploadedFileResponseSerializer(uploaded_file)

        logger.info(
            f"File upload completed successfully: {file_id}",
            extra={
                'file_id': file_id,
                'uploaded_file_id': uploaded_file.id,
                'user_id': request.user.id,
                'response_status': 201,
            }
        )

        return Response(response_serializer.data, status=status.HTTP_201_CREATED)


@method_decorator(csrf_exempt, name='dispatch')
class FileProcessView(APIView):
    """
    Process uploaded file endpoint
    POST /api/files/{file_id}/process/
    
    Queues file processing to SQS for background processing by ECS workers.
    Workers auto-scale based on queue depth (0 when idle, scales up when needed).
    """
    
    permission_classes = [IsAuthenticated]
    parser_classes = [JSONParser]
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.processing_service = FileProcessingService()
    
    def post(self, request, file_id, *args, **kwargs):
        """Process uploaded file - queue to SQS for background processing"""
        # Validate request data
        serializer = FileProcessSerializer(data=request.data)
        
        if not serializer.is_valid():
            return Response(
                {'error': 'Invalid request data', 'details': serializer.errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get validated data
        validated_data = serializer.validated_data
        
        try:
            # Check if SQS queue is available for async processing
            if sqs_service.is_available():
                # Queue to SQS for background processing
                return self._queue_to_sqs(request, file_id, validated_data)
            else:
                # Return error if SQS not available (no fallback for now)
                return Response({
                    "error": "service_unavailable",
                    "message": "File processing service is currently unavailable",
                    "timestamp": datetime.now().isoformat()
                }, status=status.HTTP_503_SERVICE_UNAVAILABLE)
                
        except Exception as e:
            # Return a proper error response
            return Response({
                "error": "processing_error",
                "message": f"Error during file processing: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _queue_to_sqs(self, request, file_id, validated_data):
        """
        Send file processing job to SQS queue for background processing.
        Based on message format from DJANGO_AWS_INTEGRATION.md
        """
        try:
            # Get the uploaded file to verify it exists
            uploaded_file = get_object_or_404(UploadedFile, file_id=file_id, uploaded_by=request.user)
            
            # Build SQS message payload as per DJANGO_AWS_INTEGRATION.md
            s3_key = uploaded_file.file.name
            user_email = request.user.email
            
            # Convert request options to processing_options format
            processing_options = {
                "batch_size": 100,
                "validation_mode": "strict" if validated_data.get('validate_emails', True) else "lenient",
                "duplicate_handling": "skip" if validated_data.get('skip_duplicates', True) else "update"
            }
            
            # Build metadata
            metadata = {
                "original_filename": uploaded_file.file_name,
                "file_size": uploaded_file.file_size,
                "upload_timestamp": uploaded_file.uploaded_at.isoformat() if uploaded_file.uploaded_at else None
            }
            
            # Update file status to queued
            uploaded_file.status = 'queued'
            uploaded_file.save()
            
            # Send message to SQS queue using boto3
            queue_result = sqs_service.send_file_processing_message(
                file_id=file_id,
                s3_key=s3_key,
                user_email=user_email,
                tenant_id=None,  # Add tenant support later if needed
                uploaded_by=str(request.user.id),
                processing_options=processing_options,
                metadata=metadata
            )
            
            # Return 202 Accepted - job is queued for background processing
            return Response({
                "status": "queued",
                "message": "File processing job queued successfully",
                "file_id": file_id,
                "queue_url": queue_result.get('queue_url', ''),
                "estimated_processing_time": "2-5 minutes",
                "status_check_url": f"/api/files/{file_id}/status/"
            }, status=status.HTTP_202_ACCEPTED)
            
        except Exception as e:
            # Update file status to failed
            try:
                uploaded_file = UploadedFile.objects.get(file_id=file_id)
                uploaded_file.status = 'failed'
                uploaded_file.error_message = f"Failed to queue processing: {str(e)}"
                uploaded_file.save()
            except:
                pass
            
            return Response({
                "error": "queue_error",
                "message": f"Failed to queue processing job: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def _process_synchronously(self, request, file_id, validated_data):
        """Process file synchronously using existing logic (backward compatibility)"""
        # Extract legacy format options
        process_options = {
            'skip_duplicates': validated_data.get('skip_duplicates', True),
            'update_existing': validated_data.get('update_existing', False),
            'validate_emails': validated_data.get('validate_emails', True),
            'custom_field_mapping': validated_data.get('custom_field_mapping', {})
        }
        
        # Process the file using existing service
        result = self.processing_service.process_file(
            file_id=file_id,
            process_options=process_options,
            user=request.user
        )
        
        if result['status'] == 'failed':
            return Response(result, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(result, status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name='dispatch')
class FilePreviewView(APIView):
    """
    Get file preview with field mapping suggestions
    GET /api/files/{file_id}/preview/
    """
    
    permission_classes = [IsAuthenticated]
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.processing_service = FileProcessingService()
    
    def get(self, request, file_id, *args, **kwargs):
        """Get file preview and mapping suggestions"""
        # Get the uploaded file (ensure user owns it)
        uploaded_file = get_object_or_404(UploadedFile, file_id=file_id, uploaded_by=request.user)
        
        # Get file preview
        preview_result = self.processing_service.get_file_preview(file_id)
        
        if not preview_result['success']:
            return Response(
                {'error': preview_result['error']},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return Response(preview_result, status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name='dispatch')
class FileStatusView(APIView):
    """
    Get file processing status
    GET /api/files/{file_id}/status/
    """
    
    permission_classes = [IsAuthenticated]
    
    def get(self, request, file_id, *args, **kwargs):
        """Get file processing status"""
        # Get the uploaded file (ensure user owns it)
        uploaded_file = get_object_or_404(UploadedFile, file_id=file_id, uploaded_by=request.user)
        
        # Build response based on status
        response_data = {
            'file_id': uploaded_file.file_id,
            'status': uploaded_file.status,
        }
        
        # Add progress information for queued/processing/completed files
        if uploaded_file.status in ['queued', 'processing', 'completed']:
            response_data.update({
                'progress': uploaded_file.progress,
                'processed_rows': uploaded_file.processed_rows,
                'total_rows': uploaded_file.total_rows,
            })
            
            # Add timing information
            if uploaded_file.started_at:
                response_data['started_at'] = uploaded_file.started_at.isoformat()
            
            if uploaded_file.status == 'processing':
                # Add estimated completion for processing files
                estimated_completion = uploaded_file.get_estimated_completion()
                if estimated_completion:
                    response_data['estimated_completion'] = estimated_completion.isoformat()
            elif uploaded_file.status == 'completed' and uploaded_file.completed_at:
                # Add completion time for completed files
                response_data['completed_at'] = uploaded_file.completed_at.isoformat()
        
        return Response(response_data, status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name='dispatch')
class FileResultsView(APIView):
    """
    Get file processing results
    GET /api/files/{file_id}/results/
    """
    
    permission_classes = [IsAuthenticated]
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.processing_service = FileProcessingService()
    
    def get(self, request, file_id, *args, **kwargs):
        """Get detailed file processing results"""
        # Get the uploaded file (ensure user owns it)
        uploaded_file = get_object_or_404(UploadedFile, file_id=file_id, uploaded_by=request.user)
        
        # Only return results for completed or failed files
        if uploaded_file.status not in ['completed', 'failed']:
            return Response(
                {'error': 'File processing not yet completed'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Get processing results
        results = self.processing_service.get_file_results(file_id)
        
        if not results['success']:
            return Response(
                {'error': results['error']},
                status=status.HTTP_404_NOT_FOUND
            )
        
        return Response(results['data'], status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name='dispatch')
class FileProcessStatusView(APIView):
    """
    Get processing status for a job
    GET /api/files/processing/{job_id}/status/
    """
    
    permission_classes = [IsAuthenticated]
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.processing_service = FileProcessingService()
    
    def get(self, request, job_id, *args, **kwargs):
        """Get processing status"""
        status_result = self.processing_service.get_processing_status(job_id)
        
        if status_result['status'] == 'not_found':
            return Response(status_result, status=status.HTTP_404_NOT_FOUND)
        
        return Response(status_result, status=status.HTTP_200_OK)


@method_decorator(csrf_exempt, name='dispatch')
class FileProcessResultsView(APIView):
    """
    Get processing results for a job
    GET /api/files/processing/{job_id}/results/
    """
    
    permission_classes = [IsAuthenticated]
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.processing_service = FileProcessingService()
    
    def get(self, request, job_id, *args, **kwargs):
        """Get processing results"""
        results = self.processing_service.get_processing_results(job_id)
        
        if results['status'] == 'not_found':
            return Response(results, status=status.HTTP_404_NOT_FOUND)
        
        return Response(results, status=status.HTTP_200_OK) 