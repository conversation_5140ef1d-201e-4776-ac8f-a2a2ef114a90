"""
Forms-related API views for templates, submissions, and signatures.

This module consolidates all forms-related viewsets in one place for better organization.
URL structure: /api/forms/
- /api/forms/templates/
- /api/forms/submissions/
- /api/forms/signatures/
"""
import logging
from rest_framework import viewsets, permissions, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import AllowAny
from django.shortcuts import get_object_or_404
from django.http import HttpResponse

from forms.models import FormTemplate, FormSubmission, Signature
from .serializers import (
    FormTemplateSerializer, FormSubmissionSerializer, SignatureSerializer
)
from api.permissions import IsAdminUserOrReadOnly
# TODO: Re-enable when reportlab is installed
# from core.utils.pdf_utils import create_signature_pdf, signature_to_image
# from aws_services.s3 import S3Service, s3_service
# from config.aws import aws_config

# Set up logging
logger = logging.getLogger(__name__)


class FormTemplateViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing form templates.

    Provides CRUD operations for form templates with proper permissions.
    The `list` and `retrieve` (detail) actions are intentionally exposed publicly so
    that customer-facing apps can fetch available form templates without first
    authenticating. All other write-type actions (create, update, delete, custom
    actions such as `publish` / `archive`) continue to require authentication.
    """

    queryset = FormTemplate.objects.all()
    serializer_class = FormTemplateSerializer

    # Default to authenticated access – overridden in get_permissions where needed
    permission_classes = [permissions.IsAuthenticated]

    def get_permissions(self):
        """Dynamically choose permissions based on action.

        - list & retrieve: Anyone (AllowAny)
        - others: Authenticated users only
        """
        if self.action in {"list", "retrieve"}:
            permission_classes = [AllowAny]
        else:
            permission_classes = [permissions.IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    def get_queryset(self):
        """
        Filter templates based on user permissions and query parameters.
        """
        queryset = FormTemplate.objects.select_related('business')
        
        # Filter by business if provided
        business_id = self.request.query_params.get('business_id')
        if business_id:
            queryset = queryset.filter(business_id=business_id)
            
        # Filter by status if provided
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
            
        return queryset.order_by('-created_at')
    
    @action(detail=True, methods=['post'])
    def publish(self, request, pk=None):
        """Publish a template (change status to Published)"""
        template = self.get_object()
        template.status = 'Published'
        template.save()
        
        serializer = self.get_serializer(template)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def archive(self, request, pk=None):
        """Archive a template (change status to Draft)"""
        template = self.get_object()
        template.status = 'Draft'
        template.save()
        
        serializer = self.get_serializer(template)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def submissions(self, request, pk=None):
        """Get all submissions for this template"""
        template = self.get_object()
        submissions = template.submissions.select_related(
            'business', 'customer__user', 'submitted_by'
        ).order_by('-created_at')
        
        serializer = FormSubmissionSerializer(submissions, many=True)
        return Response(serializer.data)


class FormSubmissionViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing form submissions.
    
    Provides CRUD operations for form submissions with filtering capabilities.
    """
    queryset = FormSubmission.objects.all()
    serializer_class = FormSubmissionSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """
        Filter submissions based on query parameters.
        """
        queryset = FormSubmission.objects.select_related(
            'form_template', 'business_customer__business', 'business_customer__customer__user', 'submitted_by'
        )
        
        # Filter by business
        business_id = self.request.query_params.get('business_id')
        if business_id:
            queryset = queryset.filter(business_customer__business_id=business_id)
        
        # Filter by template
        template_id = self.request.query_params.get('template_id')
        if template_id:
            queryset = queryset.filter(form_template_id=template_id)
            
        # Filter by status
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
            
        # Filter by customer
        customer_id = self.request.query_params.get('customer_id')
        if customer_id:
            queryset = queryset.filter(business_customer__customer_id=customer_id)
            
        # Filter by business_customer
        business_customer_id = self.request.query_params.get('business_customer_id')
        if business_customer_id:
            queryset = queryset.filter(business_customer_id=business_customer_id)
            
        return queryset.order_by('-created_at')
    
    @action(detail=True, methods=['post'])
    def approve(self, request, pk=None):
        """Approve a submission"""
        submission = self.get_object()
        submission.status = 'Approved'
        submission.save()
        
        serializer = self.get_serializer(submission)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def reject(self, request, pk=None):
        """Reject a submission"""
        submission = self.get_object()
        submission.status = 'Rejected'
        submission.save()
        
        serializer = self.get_serializer(submission)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def signatures(self, request, pk=None):
        """Get all signatures for this submission"""
        submission = self.get_object()
        signatures = submission.signatures.select_related(
            'business', 'user', 'customer__user', 'employee__user'
        ).order_by('-created_at')
        
        serializer = SignatureSerializer(signatures, many=True)
        return Response(serializer.data)


class SignatureViewSet(viewsets.ModelViewSet):
    """
    API endpoint for managing electronic signatures.

    Provides CRUD operations for signatures with PDF generation capabilities.
    """
    queryset = Signature.objects.all()
    serializer_class = SignatureSerializer
    permission_classes = [permissions.IsAuthenticated]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # TODO: Re-enable when AWS services are available
        # try:
        #     self.s3_service = s3_service if aws_config.is_configured() else None
        # except Exception as e:
        #     logger.warning(f"S3 service not available: {e}")
        #     self.s3_service = None

    def get_queryset(self):
        """
        Filter signatures based on query parameters.
        """
        queryset = Signature.objects.select_related(
            'business', 'user', 'customer__user', 'employee__user',
            'form_submission__form_template', 'form_submission__business_customer__business'
        )

        # Filter by business
        business_id = self.request.query_params.get('business_id')
        if business_id:
            queryset = queryset.filter(business_id=business_id)

        # Filter by signer type
        signer_type = self.request.query_params.get('signer_type')
        if signer_type:
            queryset = queryset.filter(signer_type=signer_type)

        # Filter by customer
        customer_id = self.request.query_params.get('customer_id')
        if customer_id:
            queryset = queryset.filter(customer_id=customer_id)

        # Filter by employee
        employee_id = self.request.query_params.get('employee_id')
        if employee_id:
            queryset = queryset.filter(employee_id=employee_id)

        # Filter by form submission
        submission_id = self.request.query_params.get('submission_id')
        if submission_id:
            queryset = queryset.filter(form_submission_id=submission_id)

        # Filter by business_customer
        business_customer_id = self.request.query_params.get('business_customer_id')
        if business_customer_id:
            queryset = queryset.filter(form_submission__business_customer_id=business_customer_id)

        return queryset.order_by('-created_at')

    def perform_create(self, serializer):
        """
        Set additional fields when creating a signature.
        """
        # Get the client IP address
        x_forwarded_for = self.request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip_address = x_forwarded_for.split(',')[0]
        else:
            ip_address = self.request.META.get('REMOTE_ADDR')

        # Get the user agent
        user_agent = self.request.META.get('HTTP_USER_AGENT', '')

        serializer.save(
            user=self.request.user,
            ip_address=ip_address,
            user_agent=user_agent
        )

    @action(detail=True, methods=['get'])
    def download_pdf(self, request, pk=None):
        """
        Generate and download a PDF with the signature.
        """
        signature = self.get_object()
        logger.info(f"Generating PDF for signature ID: {signature.id}")

        try:
            # Prepare form data
            form_data = {
                'title': 'Signed Form',
                'content': {}
            }

            # If there's a form submission, get its data
            if signature.form_submission:
                form_data['title'] = signature.form_submission.form_template.name
                form_data['content'] = signature.form_submission.content

            # TODO: Re-enable when PDF generation is available
            return Response(
                {"error": "PDF generation temporarily disabled"},
                status=status.HTTP_501_NOT_IMPLEMENTED
            )

            # # Generate PDF
            # pdf_buffer = create_signature_pdf(
            #     form_data=form_data,
            #     signature_data=signature.signature_data,
            #     customer=signature.customer,
            #     business=signature.business,
            #     employee=signature.employee
            # )
            #
            # if not pdf_buffer:
            #     return Response(
            #         {"error": "Failed to generate PDF"},
            #         status=status.HTTP_500_INTERNAL_SERVER_ERROR
            #     )
            #
            # # Create HTTP response with PDF
            # response = HttpResponse(pdf_buffer.getvalue(), content_type='application/pdf')
            # filename = f"signature_{signature.id}_{signature.created_at.strftime('%Y%m%d')}.pdf"
            # response['Content-Disposition'] = f'attachment; filename="{filename}"'
            #
            # return response

        except Exception as e:
            logger.error(f"Error generating PDF for signature {signature.id}: {e}")
            return Response(
                {"error": "Failed to generate PDF"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
