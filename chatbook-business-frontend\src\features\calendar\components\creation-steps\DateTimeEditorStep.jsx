import React, { useState, useEffect, useCallback } from 'react'
import { 
  CalendarDaysIcon,
  ClockIcon,
  CurrencyDollarIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  CheckCircleIcon,
  UserIcon
} from '@heroicons/react/24/outline'
import { workingHoursService } from '../../services/workingHoursService'

/**
 * DateTimeEditorStep - Simplified and logical date/time selection
 * Features: Prefilled employee, date picker, time selector with 5-min resolution, pricing display
 */
const DateTimeEditorStep = ({
  selectedDate,
  selectedEmployee,
  selectedService,
  totalDuration = 60,
  onDateChange,
  onEmployeeChange,
  validationErrors = {}
}) => {
  const [currentMonth, setCurrentMonth] = useState(new Date())
  const [selectedTime, setSelectedTime] = useState('')
  const [workingHoursError, setWorkingHoursError] = useState('')
  const [isValidating, setIsValidating] = useState(false)

  // Working hours validation function (defined first to avoid circular dependency)
  const validateWorkingHours = useCallback(async (date, time, employee) => {
    if (!employee || !date || !time) {
      return true
    }

    try {
      setIsValidating(true)
      console.log('🔍 Validating working hours in DateTimeEditorStep:', { date, time, employee: employee.name })

      const dateString = date.toISOString().split('T')[0]
      const endTime = new Date(date)
      endTime.setMinutes(endTime.getMinutes() + totalDuration)
      const endTimeString = endTime.toLocaleTimeString('en-US', { 
        hour12: false, 
        hour: '2-digit', 
        minute: '2-digit' 
      })

      const isAvailable = await workingHoursService.isEmployeeAvailable(
        String(employee.id),
        dateString,
        time,
        endTimeString
      )

      if (!isAvailable) {
        const workingHours = await workingHoursService.fetchWorkingHours(String(employee.id))
        const dayName = date.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase()
        const dayHours = workingHours[dayName]
        
        if (!dayHours?.isWorking) {
          setWorkingHoursError(`${employee.name} is not working on this day.`)
        } else {
          setWorkingHoursError(`${employee.name} is not available at this time. Working hours: ${dayHours.startTime} - ${dayHours.endTime}`)
        }
        return false
      }

      setWorkingHoursError('')
      return true
    } catch (error) {
      console.error('❌ Working hours validation error:', error)
      setWorkingHoursError('Unable to validate working hours')
      return false
    } finally {
      setIsValidating(false)
    }
  }, [totalDuration])

  // Initialize from selectedDate when component mounts
  useEffect(() => {
    if (selectedDate) {
      // Set the calendar month to match the selected date
      setCurrentMonth(new Date(selectedDate))
      
      // Extract and set the time
      const timeString = selectedDate.toLocaleTimeString('en-US', { 
        hour12: false, 
        hour: '2-digit', 
        minute: '2-digit' 
      })
      setSelectedTime(timeString)
    }
  }, [selectedDate])

  // Validate working hours when employee, date, or time changes
  useEffect(() => {
    if (selectedEmployee && selectedDate && selectedTime) {
      validateWorkingHours(selectedDate, selectedTime, selectedEmployee)
    }
  }, [selectedEmployee, selectedDate, selectedTime, validateWorkingHours])

  // Generate time options with 5-minute resolution
  const generateTimeOptions = () => {
    const options = []
    const startHour = 7 // 7 AM
    const endHour = 20 // 8 PM
    
    for (let hour = startHour; hour <= endHour; hour++) {
      for (let minute = 0; minute < 60; minute += 5) { // 5-minute resolution
        const timeString = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`
        const displayTime = new Date()
        displayTime.setHours(hour, minute)
        const displayString = displayTime.toLocaleTimeString('en-US', {
          hour: 'numeric',
          minute: '2-digit',
          hour12: true
        })
        
        options.push({
          value: timeString,
          label: displayString
        })
      }
    }
    return options
  }

  const timeOptions = generateTimeOptions()

  // Navigate calendar months
  const navigateMonth = (direction) => {
    const newMonth = new Date(currentMonth)
    newMonth.setMonth(newMonth.getMonth() + direction)
    setCurrentMonth(newMonth)
  }

  // Generate calendar days for display
  const generateCalendarDays = () => {
    const firstDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1)
    const lastDay = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0)
    const startDate = new Date(firstDay)
    startDate.setDate(startDate.getDate() - firstDay.getDay()) // Start from Sunday
    
    const days = []
    const endDate = new Date(startDate)
    endDate.setDate(endDate.getDate() + 41) // 6 weeks
    
    for (let date = new Date(startDate); date < endDate; date.setDate(date.getDate() + 1)) {
      days.push(new Date(date))
    }
    
    return days
  }

  const calendarDays = generateCalendarDays()

  // Handle date selection
  const handleDateSelect = async (date) => {
    // Combine selected date with current time
    const newDateTime = new Date(date)
    if (selectedTime) {
      const [hours, minutes] = selectedTime.split(':').map(Number)
      newDateTime.setHours(hours, minutes, 0, 0)
    }
    onDateChange(newDateTime)
    
    // Validate working hours if we have both date and time
    if (selectedEmployee && selectedTime) {
      await validateWorkingHours(newDateTime, selectedTime, selectedEmployee)
    }
  }

  // Handle time selection
  const handleTimeSelect = async (time) => {
    setSelectedTime(time)
    
    // Update the full datetime
    const newDateTime = selectedDate ? new Date(selectedDate) : new Date()
    const [hours, minutes] = time.split(':').map(Number)
    newDateTime.setHours(hours, minutes, 0, 0)
    onDateChange(newDateTime)
    
    // Validate working hours
    if (selectedEmployee && time) {
      await validateWorkingHours(newDateTime, time, selectedEmployee)
    }
  }

  // Format date for display
  const formatDate = (date) => {
    return date.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    })
  }

  // Format duration
  const formatDuration = (minutes) => {
    const hours = Math.floor(minutes / 60)
    const mins = minutes % 60
    if (hours > 0) {
      return `${hours}h ${mins > 0 ? `${mins}m` : ''}`
    }
    return `${mins}m`
  }

  // Calculate end time
  const getEndTime = () => {
    if (!selectedDate || !totalDuration) return null
    const endTime = new Date(selectedDate)
    endTime.setMinutes(endTime.getMinutes() + totalDuration)
    return endTime
  }

  // Check if date is selectable (today or future)
  const isDateSelectable = (date) => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    const checkDate = new Date(date)
    checkDate.setHours(0, 0, 0, 0)
    return checkDate >= today
  }

  // Check if date is selected
  const isDateSelected = (date) => {
    if (!selectedDate) return false
    return date.toDateString() === selectedDate.toDateString()
  }

  // Check if date is in current month
  const isCurrentMonth = (date) => {
    return date.getMonth() === currentMonth.getMonth()
  }

  // Calculate pricing information
  const getPricingInfo = () => {
    if (!selectedService) return null
    
    const basePrice = parseFloat(selectedService.price || 0)
    const tax = basePrice * 0.1 // 10% tax (adjust as needed)
    const total = basePrice + tax
    
    return {
      basePrice,
      tax,
      total
    }
  }

  const pricingInfo = getPricingInfo()
  const endTime = getEndTime()

  return (
    <div className="p-6 space-y-6">
      {/* Step Header */}
      <div className="text-center">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Set Date & Time
        </h3>
        <p className="text-gray-600">
          Choose when the appointment should be scheduled
        </p>
      </div>

      {/* Employee Info (Read-only, prefilled) */}
      {selectedEmployee && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
              <UserIcon className="h-5 w-5 text-white" />
            </div>
            <div>
              <h4 className="font-medium text-gray-900">Appointment with</h4>
              <p className="text-blue-600 font-semibold">{selectedEmployee.name}</p>
            </div>
          </div>
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Date Picker */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900 flex items-center space-x-2">
            <CalendarDaysIcon className="h-5 w-5" />
            <span>Select Date</span>
          </h4>

          {/* Calendar Header */}
          <div className="flex items-center justify-between mb-4">
            <button
              onClick={() => navigateMonth(-1)}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ChevronLeftIcon className="h-5 w-5" />
            </button>
            <h5 className="text-lg font-semibold">
              {currentMonth.toLocaleDateString('en-US', { month: 'long', year: 'numeric' })}
            </h5>
            <button
              onClick={() => navigateMonth(1)}
              className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            >
              <ChevronRightIcon className="h-5 w-5" />
            </button>
          </div>

          {/* Calendar Grid */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map(day => (
              <div key={day} className="text-center text-sm font-medium text-gray-500 py-2">
                {day}
              </div>
            ))}
          </div>

          <div className="grid grid-cols-7 gap-1">
            {calendarDays.map((date, index) => {
              const selectable = isDateSelectable(date)
              const selected = isDateSelected(date)
              const currentMonthDay = isCurrentMonth(date)

              return (
                <button
                  key={index}
                  onClick={() => selectable && handleDateSelect(date)}
                  disabled={!selectable}
                  className={`
                    h-10 w-10 rounded-lg text-sm font-medium transition-colors
                    ${selected 
                      ? 'bg-blue-600 text-white' 
                      : currentMonthDay && selectable
                        ? 'text-gray-900 hover:bg-blue-50'
                        : currentMonthDay
                          ? 'text-gray-400 cursor-not-allowed'
                          : 'text-gray-300 cursor-default'
                    }
                  `}
                >
                  {date.getDate()}
                </button>
              )
            })}
          </div>

          {validationErrors.date && (
            <p className="text-sm text-red-600">{validationErrors.date}</p>
          )}
        </div>

        {/* Time Selection and Details */}
        <div className="space-y-4">
          <h4 className="font-medium text-gray-900 flex items-center space-x-2">
            <ClockIcon className="h-5 w-5" />
            <span>Select Time</span>
          </h4>

          {/* Time Picker */}
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Start Time</label>
            <select
              value={selectedTime}
              onChange={(e) => handleTimeSelect(e.target.value)}
              className={`w-full p-3 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                workingHoursError ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={isValidating}
            >
              <option value="">Select time...</option>
              {timeOptions.map(option => (
                <option key={option.value} value={option.value}>
                  {option.label}
                </option>
              ))}
            </select>
            {isValidating && (
              <div className="text-sm text-gray-500 flex items-center gap-2">
                <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Validating working hours...
              </div>
            )}
          </div>

          {/* Working Hours Validation Error */}
          {workingHoursError && (
            <div className="text-red-600 text-sm flex items-center gap-2 bg-red-50 border border-red-200 rounded-lg p-3">
              <svg className="w-4 h-4 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
              </svg>
              <span>{workingHoursError}</span>
            </div>
          )}

          {/* Appointment Summary */}
          {selectedDate && selectedTime && (
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-4 space-y-3">
              <h5 className="font-medium text-gray-900">Appointment Summary</h5>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Date:</span>
                  <span className="font-medium">{formatDate(selectedDate)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Start Time:</span>
                  <span className="font-medium">
                    {selectedDate.toLocaleTimeString('en-US', {
                      hour: 'numeric',
                      minute: '2-digit',
                      hour12: true
                    })}
                  </span>
                </div>
                
                {endTime && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">End Time:</span>
                    <span className="font-medium">
                      {endTime.toLocaleTimeString('en-US', {
                        hour: 'numeric',
                        minute: '2-digit',
                        hour12: true
                      })}
                    </span>
                  </div>
                )}
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Duration:</span>
                  <span className="font-medium">{formatDuration(totalDuration)}</span>
                </div>
              </div>
            </div>
          )}

          {/* Pricing Information */}
          {pricingInfo && (
            <div className="bg-green-50 border border-green-200 rounded-lg p-4 space-y-3">
              <h5 className="font-medium text-gray-900 flex items-center space-x-2">
                <CurrencyDollarIcon className="h-5 w-5" />
                <span>Pricing</span>
              </h5>
              
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-gray-600">Service Price:</span>
                  <span className="font-medium">${pricingInfo.basePrice.toFixed(2)}</span>
                </div>
                
                <div className="flex justify-between">
                  <span className="text-gray-600">Tax:</span>
                  <span className="font-medium">${pricingInfo.tax.toFixed(2)}</span>
                </div>
                
                <div className="flex justify-between border-t border-green-300 pt-2">
                  <span className="font-semibold text-gray-900">Total:</span>
                  <span className="font-bold text-green-600">${pricingInfo.total.toFixed(2)}</span>
                </div>
              </div>
            </div>
          )}

          {validationErrors.employee && (
            <p className="text-sm text-red-600">{validationErrors.employee}</p>
          )}
        </div>
      </div>
    </div>
  )
}

export default DateTimeEditorStep 