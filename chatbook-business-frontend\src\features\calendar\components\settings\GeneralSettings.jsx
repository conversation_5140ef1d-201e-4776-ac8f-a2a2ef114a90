import React, { useState, useMemo } from 'react'
import { WEEK_START_OPTIONS } from '../../hooks/useCalendarConfig'
import { generateWeekDays, getWeekDayHeaders, calculateWeekShift, formatWeekRange } from '../../utils/weekUtils'

/**
 * GeneralSettings - Calendar general configuration tab
 */
const GeneralSettings = ({ config, updateConfig }) => {
  const [previewStartDay, setPreviewStartDay] = useState(config.weekStartDay)
  
  // Generate current week for preview
  const currentWeekDays = useMemo(() => {
    return generateWeekDays(new Date(), config.weekStartDay)
  }, [config.weekStartDay])
  
  // Generate preview week for the selected start day
  const previewWeekDays = useMemo(() => {
    return generateWeekDays(new Date(), previewStartDay)
  }, [previewStartDay])
  
  // Calculate what changes when switching start days
  const weekShiftInfo = useMemo(() => {
    return calculateWeekShift(currentWeekDays, previewStartDay)
  }, [currentWeekDays, previewStartDay])
  
  // Handle week start day change with preview
  const handleWeekStartDayChange = (newStartDay) => {
    setPreviewStartDay(newStartDay)
    updateConfig({ weekStartDay: newStartDay })
  }
  return (
    <div className="space-y-6">
      {/* Default View */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Default View</label>
        <div className="flex gap-2">
          {['day', 'week'].map(view => (
            <button
              key={view}
              onClick={() => updateConfig({ defaultView: view })}
              className={`px-4 py-2 rounded-lg font-medium ${
                config.defaultView === view
                  ? 'bg-blue-100 text-blue-700 border border-blue-200'
                  : 'bg-gray-100 text-gray-700 border border-gray-200 hover:bg-gray-150'
              }`}
            >
              {view.charAt(0).toUpperCase() + view.slice(1)} View
            </button>
          ))}
        </div>
      </div>

      {/* Week Start Day */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-2">Week Starts On</label>
        <select
          value={previewStartDay}
          onChange={(e) => handleWeekStartDayChange(parseInt(e.target.value))}
          className="w-full border border-gray-300 rounded-lg px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          {WEEK_START_OPTIONS.map(option => (
            <option key={option.value} value={option.value}>
              {option.label}
            </option>
          ))}
        </select>
        
        {/* Week Preview */}
        <div className="mt-4 p-3 bg-gray-50 rounded-lg">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Week Preview</h4>
          
          {/* Current Week */}
          <div className="mb-3">
            <div className="text-xs text-gray-500 mb-1">Current: {formatWeekRange(currentWeekDays)}</div>
            <div className="grid grid-cols-7 gap-1">
              {currentWeekDays.map((day, index) => (
                <div key={index} className="text-center p-1 bg-white rounded border">
                  <div className="text-xs text-gray-500">
                    {day.toLocaleDateString('en-US', { weekday: 'short' })}
                  </div>
                  <div className="text-sm font-medium">
                    {day.getDate()}
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* Preview Week (if different) */}
          {previewStartDay !== config.weekStartDay && (
            <div className="mb-3">
              <div className="text-xs text-gray-500 mb-1">Preview: {formatWeekRange(previewWeekDays)}</div>
              <div className="grid grid-cols-7 gap-1">
                {previewWeekDays.map((day, index) => {
                  const isPushedOut = weekShiftInfo.pushedOut.some(d => d.date.toDateString() === day.toDateString())
                  const isAdded = weekShiftInfo.added.some(d => d.date.toDateString() === day.toDateString())
                  
                  return (
                    <div 
                      key={index} 
                      className={`text-center p-1 rounded border ${
                        isPushedOut 
                          ? 'bg-red-100 border-red-200' 
                          : isAdded 
                            ? 'bg-green-100 border-green-200' 
                            : 'bg-blue-50 border-blue-200'
                      }`}
                    >
                      <div className="text-xs text-gray-500">
                        {day.toLocaleDateString('en-US', { weekday: 'short' })}
                      </div>
                      <div className="text-sm font-medium">
                        {day.getDate()}
                      </div>
                    </div>
                  )
                })}
              </div>
              
              {/* Change Description */}
              {weekShiftInfo.shift > 0 && (
                <div className="mt-2 text-xs text-gray-600">
                  <div className="flex items-center gap-2 mb-1">
                    <div className="w-3 h-3 bg-red-100 border border-red-200 rounded"></div>
                    <span>Days pushed out: {weekShiftInfo.pushedOut.map(d => `${d.dayName} ${d.dayNumber}`).join(', ')}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-3 h-3 bg-green-100 border border-green-200 rounded"></div>
                    <span>Days added: {weekShiftInfo.added.map(d => `${d.dayName} ${d.dayNumber}`).join(', ')}</span>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default GeneralSettings 