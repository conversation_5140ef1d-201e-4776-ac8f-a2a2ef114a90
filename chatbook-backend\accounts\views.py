from django.shortcuts import render, redirect
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from django.contrib.auth import login, logout, authenticate
from django.contrib.auth.decorators import login_required
from django.contrib.auth import get_user_model
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.conf import settings
from django.contrib import messages
from django.utils import timezone
from django.core.cache import cache
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.utils.crypto import get_random_string
from django.views import View
import pyotp
from ipware import get_client_ip
from django.contrib.auth.mixins import LoginRequiredMixin
from django.db.utils import IntegrityError
from django.urls import reverse_lazy
from django.views.generic import CreateView
from django.contrib.auth.forms import UserCreationForm
from .forms import BusinessSignupForm, CustomerSignupForm

from .models.user import User

User = get_user_model()

class LoginView(View):
    """
    View to handle user login with either OTP or password.
    Supports both API and template-based authentication.
    """
    template_name = 'accounts/login.html'

    def get(self, request):
        if request.user.is_authenticated:
            return redirect('home')
        return render(request, self.template_name)

    def post(self, request):
        username = request.POST.get('username')
        password = request.POST.get('password')

        # Check if username is email or phone number
        if '@' in username:
            # Try email authentication
            user = authenticate(request, username=username, password=password)
        else:
            # Try phone number authentication
            if not username.startswith('+1'):
                username = '+1' + username
            user = authenticate(request, username=username, password=password)

        if user is not None:
            login(request, user)
            return redirect('home')
        else:
            messages.error(request, "Invalid username/email/phone or password")
            return redirect('login')

    def handle_api_login(self, request):
        phone_number = request.data.get('phone_number')
        password = request.data.get('password')
        otp = request.data.get('otp')
        
        if not phone_number:
            return Response(
                {'error': 'Phone number is required'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
            
        try:
            user = User.objects.get(phone_number=phone_number)
        except User.DoesNotExist:
            return Response(
                {'error': 'User not found'}, 
                status=status.HTTP_404_NOT_FOUND
            )
            
        # Try OTP authentication if OTP is provided
        if otp is not None:
            if user.verify_otp(otp):
                login(request, user)
                return Response({
                    'message': 'Login successful with OTP',
                    'user': {
                        'id': user.id,
                        'phone_number': user.phone_number,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'email': user.email
                    }
                })
            return Response(
                {'error': 'Invalid OTP'}, 
                status=status.HTTP_401_UNAUTHORIZED
            )
            
        # Try password authentication if password is provided
        if password is not None:
            if user.check_password(password):
                login(request, user)
                return Response({
                    'message': 'Login successful with password',
                    'user': {
                        'id': user.id,
                        'phone_number': user.phone_number,
                        'first_name': user.first_name,
                        'last_name': user.last_name,
                        'email': user.email
                    }
                })
            return Response(
                {'error': 'Invalid password'}, 
                status=status.HTTP_401_UNAUTHORIZED
            )
            
        return Response(
            {'error': 'Either password or OTP is required'}, 
            status=status.HTTP_400_BAD_REQUEST
        )

def generate_mfa_code():
    """Generate a random 6-digit code."""
    return get_random_string(length=settings.MFA_CODE_LENGTH, allowed_chars='**********')

def send_mfa_code(user, code, method='email'):
    """Send MFA code via email or SMS."""
    if method == 'email':
        subject = f'{settings.MFA_ISSUER_NAME} - Your verification code'
        message = render_to_string('accounts/email/mfa_code.html', {
            'code': code,
            'validity': settings.MFA_VALIDITY_WINDOW // 60,  # Convert to minutes
        })
        send_mail(subject, message, None, [user.email])
    else:
        # Implement SMS sending logic here using Twilio
        pass

@require_http_methods(['GET', 'POST'])
def mfa_verify(request):
    user_id = request.session.get('mfa_user_id')
    if not user_id:
        return redirect('login')
    
    if request.method == 'POST':
        code = request.POST.get('code')
        cache_key = f'mfa_code_{user_id}'
        stored_code = cache.get(cache_key)
        
        if stored_code and code == stored_code:
            user = User.objects.get(id=user_id)
            login(request, user)
            
            # Mark device as trusted if requested
            if request.POST.get('trust_device'):
                client_ip, _ = get_client_ip(request)
                device_id = request.META.get('HTTP_USER_AGENT', '') + str(client_ip)
                trusted_key = f'trusted_device_{user.id}_{device_id}'
                cache.set(trusted_key, True, 30 * 24 * 60 * 60)  # 30 days
            
            # Mark MFA as verified for this session
            request.session['mfa_verified'] = True
            
            messages.success(request, 'Login successful!')
            return redirect(request.session.get('next_url', '/'))
        else:
            messages.error(request, 'Invalid verification code.')
    
    return render(request, 'accounts/mfa_verify.html')

@login_required
def mfa_setup(request):
    if request.method == 'POST':
        enable = request.POST.get('enable') == 'true'
        method = request.POST.get('method', 'email')
        
        if enable:
            request.user.mfa_enabled = True
            request.user.preferred_mfa_method = method
            
            # Test MFA delivery
            code = generate_mfa_code()
            send_mfa_code(request.user, code, method)
            
            messages.success(request, 'MFA has been enabled.')
        else:
            request.user.mfa_enabled = False
            messages.success(request, 'MFA has been disabled.')
        
        request.user.save()
        return redirect('profile')
    
    return render(request, 'accounts/mfa_setup.html')

class LogoutView(LoginRequiredMixin, View):
    """
    View to handle user logout.
    """
    def get(self, request):
        logout(request)
        messages.success(request, 'You have been logged out.')
        return redirect('login')

    def post(self, request):
        return self.get(request)

@login_required
def profile(request):
    if request.method == 'POST':
        # Update user profile
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        email = request.POST.get('email')
        phone_number = request.POST.get('phone_number')
        
        # Validate email
        if email != request.user.email:
            try:
                validate_email(email)
                if User.objects.filter(email=email).exclude(id=request.user.id).exists():
                    messages.error(request, 'This email is already in use.')
                    return redirect('profile')
            except ValidationError:
                messages.error(request, 'Please enter a valid email address.')
                return redirect('profile')
                
        # Update user object
        request.user.first_name = first_name
        request.user.last_name = last_name
        request.user.email = email
        request.user.phone_number = phone_number
        request.user.save()
        
        messages.success(request, 'Profile updated successfully.')
        return redirect('profile')
    
    # Get connected social accounts
    social_providers = []
    for provider in ['google', 'facebook', 'apple']:
        connected = False
        # Check if the user has this social account connected
        # Implement this based on your social account model structure
        social_providers.append({
            'id': provider,
            'name': provider.capitalize(),
            'connected': connected
        })
    
    return render(request, 'accounts/profile.html', {
        'social_providers': social_providers
    })

@login_required
def social_disconnect(request, provider):
    """
    Disconnect a social account from the user's profile.
    """
    if request.method == 'POST':
        try:
            # Get the social account
            social_account = request.user.socialaccount_set.get(provider=provider)
            social_account.delete()
            messages.success(request, f'Successfully disconnected {provider} account.')
        except Exception as e:
            messages.error(request, f'Failed to disconnect {provider} account: {str(e)}')
    
    return redirect('profile')

def signup(request):
    if request.method == 'POST':
        # Get form data
        first_name = request.POST.get('first_name')
        last_name = request.POST.get('last_name')
        email = request.POST.get('email')
        phone_number = request.POST.get('phone_number')
        password1 = request.POST.get('password1')
        password2 = request.POST.get('password2')

        # Validate phone number format
        if not phone_number.startswith('+1'):
            phone_number = '+1' + phone_number

        # Validate passwords match
        if password1 != password2:
            messages.error(request, "Passwords don't match")
            return redirect('signup')

        try:
            # Create user with both email and phone number as possible usernames
            user = User.objects.create_user(
                username=email,  # Use email as primary username
                email=email,
                phone_number=phone_number,
                first_name=first_name,
                last_name=last_name,
                password=password1
            )
            user.save()

            # Log the user in with the EmailOrPhoneBackend
            login(request, user, backend='accounts.backends.EmailOrPhoneBackend')
            return redirect('home')
        except IntegrityError:
            messages.error(request, "A user with that email or phone number already exists")
            return redirect('signup')
        except ValidationError as e:
            messages.error(request, str(e))
            return redirect('signup')

    return render(request, 'accounts/signup.html')

class HomeView(LoginRequiredMixin, View):
    """
    View for the home/dashboard page.
    """
    template_name = 'accounts/home.html'

    def get(self, request):
        context = {
            'user': request.user,
        }
        return render(request, self.template_name, context)

class BusinessSignupView(CreateView):
    model = User
    form_class = BusinessSignupForm
    template_name = 'accounts/business_signup.html'
    success_url = reverse_lazy('login')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Business account created successfully! Please log in.')
        return response

class CustomerSignupView(CreateView):
    model = User
    form_class = CustomerSignupForm
    template_name = 'accounts/customer_signup.html'
    success_url = reverse_lazy('login')

    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, 'Account created successfully! Please log in.')
        return response
