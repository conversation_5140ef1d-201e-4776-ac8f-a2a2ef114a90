Defaulting to user installation because normal site-packages is not writeable
Requirement already satisfied: pandas in /home/<USER>/.local/lib/python3.10/site-packages (2.2.3)
Collecting openpyxl
  Downloading openpyxl-3.1.5-py2.py3-none-any.whl (250 kB)
     ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 250.9/250.9 KB 2.8 MB/s eta 0:00:00
Requirement already satisfied: tzdata>=2022.7 in /home/<USER>/.local/lib/python3.10/site-packages (from pandas) (2025.2)
Requirement already satisfied: pytz>=2020.1 in /home/<USER>/.local/lib/python3.10/site-packages (from pandas) (2025.2)
Requirement already satisfied: python-dateutil>=2.8.2 in /home/<USER>/.local/lib/python3.10/site-packages (from pandas) (2.9.0.post0)
Requirement already satisfied: numpy>=1.22.4 in /home/<USER>/.local/lib/python3.10/site-packages (from pandas) (1.26.4)
Collecting et-xmlfile
  Downloading et_xmlfile-2.0.0-py3-none-any.whl (18 kB)
Requirement already satisfied: six>=1.5 in /usr/lib/python3/dist-packages (from python-dateutil>=2.8.2->pandas) (1.16.0)
Installing collected packages: et-xmlfile, openpyxl
Successfully installed et-xmlfile-2.0.0 openpyxl-3.1.5
