# Chatbook API Postman Collection

This directory contains Postman configuration files to help you test the Chatbook API endpoints.

## Files

- `chatbook_api_postman_collection.json` - The main Postman collection with all API endpoints
- `chatbook_postman_environment.json` - Environment variables for the collection

## Setup Instructions

1. **Install Postman** (if you haven't already): Download and install from [postman.com](https://www.postman.com/downloads/)

2. **Import the Collection**:
   - Open Postman
   - Click "Import" in the top left corner
   - Select the `chatbook_api_postman_collection.json` file
   - The collection will appear in your Postman workspace

3. **Import the Environment**:
   - Click "Import" again
   - Select the `chatbook_postman_environment.json` file
   - The environment will appear in your environments list

4. **Select the Environment**:
   - Click the environment dropdown in the top right corner
   - Select "Chatbook - Development" from the list

5. **Update Default Values (Optional)**:
   - Click the "eye" icon next to the environment dropdown
   - Update default values like user_email and user_password
   - Close the environment viewer

## Important Note on API URLs

The collection is configured to use the `/api/v1` path prefix. If your backend uses a different path structure, update the `api_path` variable in your environment.

For example:
- If your API is at `/api/v1`: Set `api_path` to `/api/v1`
- If your API is at `/api`: Set `api_path` to `/api`

## Using the Collection

### Authentication Flow

1. **Login**:
   - Open the "Authentication" folder
   - Select the "Login" request
   - Click "Send"
   - The environment variables will automatically update with your tokens

2. **MFA Verification (if required)**:
   - If MFA is required, the "Verify MFA" request will be pre-populated with your temporary token
   - Enter the MFA code and send the request
   - Your authentication tokens will be stored in the environment

3. **Token Refresh**:
   - When your token expires, use the "Refresh Token" request
   - It automatically uses your stored refresh token

### Business Customer Endpoints

All Business Customer endpoints require a `business_id` parameter. This is automatically supplied from your environment variables.

- **List/Search Customers**: Use the query parameter `q` to search
- **Create/Update Customer**: Send the appropriate JSON in the request body
- **Assign Tags**: Send an array of tag IDs in the request body

## URL Format for Requests

When testing your endpoints, make sure the URL structure matches your backend configuration:

- All endpoints should use the format `{{base_url}}{{api_path}}/endpoint/`
- Example: `{{base_url}}{{api_path}}/business-customers/?business_id={{business_id}}`

## Tips

- You can use the "eye" icon next to the environment dropdown to see your current values for tokens
- All requests automatically include the Bearer token in the Authorization header
- Test scripts automatically update your tokens after login/refresh operations
- Change the `base_url` in your environment to point to different servers (development, staging, production)

## Troubleshooting

- If you receive 401 Unauthorized errors, your token may have expired. Use the "Refresh Token" request
- If "Refresh Token" fails, you need to log in again
- Make sure your environment is selected before sending requests
- Check that your `base_url` and `api_path` are correct for your server environment
- If you get 404 errors, verify that the URL structure matches your backend configuration 