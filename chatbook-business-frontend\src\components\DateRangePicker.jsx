import { useState, useRef, useEffect } from "react";
import {
  CalendarIcon,
  ChevronDownIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
} from "@heroicons/react/24/outline";

function DateRangePicker({
  startDate,
  endDate,
  onChange,
  placeholder = "Select date range",
}) {
  const [isOpen, setIsOpen] = useState(false);
  const [fromDate, setFromDate] = useState(startDate || new Date());
  const [toDate, setToDate] = useState(endDate || new Date());
  const [fromMonth, setFromMonth] = useState(fromDate.getMonth());
  const [fromYear, setFromYear] = useState(fromDate.getFullYear());
  const [toMonth, setToMonth] = useState(toDate.getMonth());
  const [toYear, setToYear] = useState(toDate.getFullYear());
  const [selectingFrom, setSelectingFrom] = useState(true);

  const dropdownRef = useRef(null);

  // 月份名称
  const months = [
    "January",
    "February",
    "March",
    "April",
    "May",
    "June",
    "July",
    "August",
    "September",
    "October",
    "November",
    "December",
  ];

  // 星期名称
  const weekDays = ["Su", "Mo", "Tu", "We", "Th", "Fr", "Sa"];

  // 关闭下拉框
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => document.removeEventListener("mousedown", handleClickOutside);
  }, []);

  // 格式化日期显示
  const formatDate = (date) => {
    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
      year: "numeric",
    });
  };

  // 获取显示文本
  const getDisplayText = () => {
    if (!startDate || !endDate) return placeholder;
    return `${formatDate(startDate)} - ${formatDate(endDate)}`;
  };

  // 生成日历天数
  const generateCalendarDays = (month, year) => {
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    const currentDate = new Date(startDate);

    for (let i = 0; i < 42; i++) {
      days.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return days;
  };

  // 处理日期点击
  const handleDateClick = (date) => {
    if (selectingFrom) {
      setFromDate(date);
      setSelectingFrom(false);
      if (date > toDate) {
        setToDate(date);
      }
    } else {
      if (date >= fromDate) {
        setToDate(date);
        setSelectingFrom(true);
      } else {
        setFromDate(date);
        setToDate(fromDate);
      }
    }
  };

  // 快捷选择
  const handleQuickSelect = (type) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    let newFromDate, newToDate;

    switch (type) {
      case "today":
        newFromDate = newToDate = today;
        break;
      case "yesterday":
        newFromDate = newToDate = yesterday;
        break;
      case "last7":
        newFromDate = new Date(today);
        newFromDate.setDate(today.getDate() - 6);
        newToDate = today;
        break;
      case "last30":
        newFromDate = new Date(today);
        newFromDate.setDate(today.getDate() - 29);
        newToDate = today;
        break;
      case "thisMonth":
        newFromDate = new Date(today.getFullYear(), today.getMonth(), 1);
        newToDate = today;
        break;
      case "lastMonth":
        newFromDate = new Date(today.getFullYear(), today.getMonth() - 1, 1);
        newToDate = new Date(today.getFullYear(), today.getMonth(), 0);
        break;
      case "thisYear":
        newFromDate = new Date(today.getFullYear(), 0, 1);
        newToDate = today;
        break;
      case "lastYear":
        newFromDate = new Date(today.getFullYear() - 1, 0, 1);
        newToDate = new Date(today.getFullYear() - 1, 11, 31);
        break;
      default:
        return;
    }

    setFromDate(newFromDate);
    setToDate(newToDate);
    setFromMonth(newFromDate.getMonth());
    setFromYear(newFromDate.getFullYear());
    setToMonth(newToDate.getMonth());
    setToYear(newToDate.getFullYear());
  };

  // 提交选择
  const handleSubmit = () => {
    onChange({ startDate: fromDate, endDate: toDate });
    setIsOpen(false);
  };

  // 清除选择
  const handleClear = () => {
    const today = new Date();
    setFromDate(today);
    setToDate(today);
    setFromMonth(today.getMonth());
    setFromYear(today.getFullYear());
    setToMonth(today.getMonth());
    setToYear(today.getFullYear());
  };

  // 检查日期是否在范围内
  const isDateInRange = (date) => {
    return date >= fromDate && date <= toDate;
  };

  // 检查是否是今天
  const isToday = (date) => {
    const today = new Date();
    return date.toDateString() === today.toDateString();
  };

  return (
    <div className="relative" ref={dropdownRef}>
      {/* 输入框 */}
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white text-left flex items-center justify-between"
      >
        <span
          className={startDate && endDate ? "text-gray-900" : "text-gray-500"}
        >
          {getDisplayText()}
        </span>
        <CalendarIcon className="h-5 w-5 text-gray-400" />
      </button>

      {/* 日历下拉框 */}
      {isOpen && (
        <div className="absolute z-50 mt-1 bg-white border border-gray-300 rounded-lg shadow-lg w-[800px] left-0">
          <div className="flex">
            {/* 左侧日历 */}
            <div className="flex-1 p-4 border-r border-gray-200">
              {/* FROM 标题和月份选择 */}
              <div className="mb-4">
                <div className="text-sm font-medium text-gray-700 mb-2">
                  FROM
                </div>
                <div className="flex items-center justify-between mb-4">
                  <button
                    onClick={() => {
                      if (fromMonth === 0) {
                        setFromMonth(11);
                        setFromYear(fromYear - 1);
                      } else {
                        setFromMonth(fromMonth - 1);
                      }
                    }}
                    className="p-1 hover:bg-gray-100 rounded"
                  >
                    <ChevronLeftIcon className="h-4 w-4" />
                  </button>

                  <div className="flex gap-2">
                    <select
                      value={fromMonth}
                      onChange={(e) => setFromMonth(parseInt(e.target.value))}
                      className="border border-gray-300 rounded px-2 py-1 text-sm"
                    >
                      {months.map((month, index) => (
                        <option key={index} value={index}>
                          {month}
                        </option>
                      ))}
                    </select>
                    <select
                      value={fromYear}
                      onChange={(e) => setFromYear(parseInt(e.target.value))}
                      className="border border-gray-300 rounded px-2 py-1 text-sm"
                    >
                      {Array.from(
                        { length: 10 },
                        (_, i) => new Date().getFullYear() - 5 + i
                      ).map((year) => (
                        <option key={year} value={year}>
                          {year}
                        </option>
                      ))}
                    </select>
                  </div>

                  <button
                    onClick={() => {
                      if (fromMonth === 11) {
                        setFromMonth(0);
                        setFromYear(fromYear + 1);
                      } else {
                        setFromMonth(fromMonth + 1);
                      }
                    }}
                    className="p-1 hover:bg-gray-100 rounded"
                  >
                    <ChevronRightIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* 星期标题 */}
              <div className="grid grid-cols-7 gap-1 mb-2">
                {weekDays.map((day) => (
                  <div
                    key={day}
                    className="text-center text-xs font-medium text-gray-500 py-2"
                  >
                    {day}
                  </div>
                ))}
              </div>

              {/* 日期网格 */}
              <div className="grid grid-cols-7 gap-1">
                {generateCalendarDays(fromMonth, fromYear).map(
                  (date, index) => {
                    const isCurrentMonth = date.getMonth() === fromMonth;
                    const isSelected = isDateInRange(date);
                    const isTodayDate = isToday(date);

                    return (
                      <button
                        key={index}
                        onClick={() => handleDateClick(date)}
                        className={`
                        p-2 text-sm rounded hover:bg-gray-100
                        ${!isCurrentMonth ? "text-gray-400" : "text-gray-900"}
                        ${
                          isSelected
                            ? "bg-red-500 text-white hover:bg-red-600"
                            : ""
                        }
                        ${
                          isTodayDate && !isSelected
                            ? "bg-blue-100 text-blue-900"
                            : ""
                        }
                      `}
                      >
                        {date.getDate()}
                      </button>
                    );
                  }
                )}
              </div>

              {/* FROM 日期显示 */}
              <div className="mt-4 text-center">
                <div className="text-sm text-gray-600">FROM</div>
                <div className="font-medium">{formatDate(fromDate)}</div>
              </div>
            </div>

            {/* 右侧日历 */}
            <div className="flex-1 p-4 border-r border-gray-200">
              {/* TO 标题和月份选择 */}
              <div className="mb-4">
                <div className="text-sm font-medium text-gray-700 mb-2">TO</div>
                <div className="flex items-center justify-between mb-4">
                  <button
                    onClick={() => {
                      if (toMonth === 0) {
                        setToMonth(11);
                        setToYear(toYear - 1);
                      } else {
                        setToMonth(toMonth - 1);
                      }
                    }}
                    className="p-1 hover:bg-gray-100 rounded"
                  >
                    <ChevronLeftIcon className="h-4 w-4" />
                  </button>

                  <div className="flex gap-2">
                    <select
                      value={toMonth}
                      onChange={(e) => setToMonth(parseInt(e.target.value))}
                      className="border border-gray-300 rounded px-2 py-1 text-sm"
                    >
                      {months.map((month, index) => (
                        <option key={index} value={index}>
                          {month}
                        </option>
                      ))}
                    </select>
                    <select
                      value={toYear}
                      onChange={(e) => setToYear(parseInt(e.target.value))}
                      className="border border-gray-300 rounded px-2 py-1 text-sm"
                    >
                      {Array.from(
                        { length: 10 },
                        (_, i) => new Date().getFullYear() - 5 + i
                      ).map((year) => (
                        <option key={year} value={year}>
                          {year}
                        </option>
                      ))}
                    </select>
                  </div>

                  <button
                    onClick={() => {
                      if (toMonth === 11) {
                        setToMonth(0);
                        setToYear(toYear + 1);
                      } else {
                        setToMonth(toMonth + 1);
                      }
                    }}
                    className="p-1 hover:bg-gray-100 rounded"
                  >
                    <ChevronRightIcon className="h-4 w-4" />
                  </button>
                </div>
              </div>

              {/* 星期标题 */}
              <div className="grid grid-cols-7 gap-1 mb-2">
                {weekDays.map((day) => (
                  <div
                    key={day}
                    className="text-center text-xs font-medium text-gray-500 py-2"
                  >
                    {day}
                  </div>
                ))}
              </div>

              {/* 日期网格 */}
              <div className="grid grid-cols-7 gap-1">
                {generateCalendarDays(toMonth, toYear).map((date, index) => {
                  const isCurrentMonth = date.getMonth() === toMonth;
                  const isSelected = isDateInRange(date);
                  const isTodayDate = isToday(date);

                  return (
                    <button
                      key={index}
                      onClick={() => handleDateClick(date)}
                      className={`
                        p-2 text-sm rounded hover:bg-gray-100
                        ${!isCurrentMonth ? "text-gray-400" : "text-gray-900"}
                        ${
                          isSelected
                            ? "bg-red-500 text-white hover:bg-red-600"
                            : ""
                        }
                        ${
                          isTodayDate && !isSelected
                            ? "bg-blue-100 text-blue-900"
                            : ""
                        }
                      `}
                    >
                      {date.getDate()}
                    </button>
                  );
                })}
              </div>

              {/* TO 日期显示 */}
              <div className="mt-4 text-center">
                <div className="text-sm text-gray-600">TO</div>
                <div className="font-medium">{formatDate(toDate)}</div>
              </div>
            </div>

            {/* 快捷选择面板 */}
            <div className="w-48 p-4">
              <div className="space-y-1">
                <button
                  onClick={() => handleQuickSelect("today")}
                  className="w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100 bg-red-500 text-white"
                >
                  Today
                </button>
                <button
                  onClick={() => handleQuickSelect("yesterday")}
                  className="w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100"
                >
                  Yesterday
                </button>
                <button
                  onClick={() => handleQuickSelect("last7")}
                  className="w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100"
                >
                  Last 7 Days
                </button>
                <button
                  onClick={() => handleQuickSelect("last30")}
                  className="w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100"
                >
                  Last 30 Days
                </button>
                <button
                  onClick={() => handleQuickSelect("thisMonth")}
                  className="w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100"
                >
                  This Month
                </button>
                <button
                  onClick={() => handleQuickSelect("lastMonth")}
                  className="w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100"
                >
                  Last Month
                </button>
                <button
                  onClick={() => handleQuickSelect("thisYear")}
                  className="w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100"
                >
                  This Year
                </button>
                <button
                  onClick={() => handleQuickSelect("lastYear")}
                  className="w-full text-left px-3 py-2 text-sm rounded hover:bg-gray-100"
                >
                  Last Year
                </button>
              </div>

              {/* 底部按钮 */}
              <div className="flex gap-2 mt-6">
                <button
                  onClick={handleClear}
                  className="flex-1 px-3 py-2 text-sm border border-gray-300 rounded hover:bg-gray-50"
                >
                  Clear
                </button>
                <button
                  onClick={handleSubmit}
                  className="flex-1 px-3 py-2 text-sm bg-green-600 text-white rounded hover:bg-green-700"
                >
                  Submit
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default DateRangePicker;
