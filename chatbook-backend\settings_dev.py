"""
Development settings for chatbook-backend that help bypass migration checks.
Import this after the main settings to override specific settings.

Usage:
    python manage.py runserver --settings=settings_dev
"""

from settings import *  # Import all settings from the main settings file

# Ignore migrations to avoid circular dependencies
MIGRATION_MODULES = {
    'bookings': None,  # Tell Django to ignore bookings migrations
    # 'services': None,  # Ignore services migrations too
    # 'employees': None,  # Ignore employee migrations
    # 'appointments': None,  # Ignore appointment migrations
}

# Additional development-specific settings
DEBUG = True
ALLOWED_HOSTS = ['localhost', '127.0.0.1', '*.ngrok.io', '*.ngrok-free.app', 'b61dd41593b1.ngrok-free.app'] 