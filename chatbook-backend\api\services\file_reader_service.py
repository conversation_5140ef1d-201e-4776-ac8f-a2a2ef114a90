"""
File reading service for processing uploaded CSV and Excel files.
Handles file parsing, header extraction, and data conversion.
Supports both local storage and S3 storage.
"""
import pandas as pd
import os
import tempfile
from typing import Dict, List, Any, Tuple
from django.core.files.storage import default_storage
import boto3
from botocore.exceptions import ClientError
from django.conf import settings


class FileReaderService:
    """Service to read and parse CSV/Excel files from local storage or S3"""
    
    def __init__(self):
        self.supported_extensions = ['.csv', '.xlsx', '.xls']
        # S3 configuration from settings
        self.s3_bucket = getattr(settings, 'AWS_STORAGE_BUCKET_NAME', None)
        self.s3_region = getattr(settings, 'AWS_S3_REGION_NAME', 'us-west-2')
        self.aws_profile = getattr(settings, 'AWS_PROFILE', None)
    
    def _is_s3_path(self, file_path: str) -> bool:
        """Check if the file path is an S3 key"""
        return (
            file_path.startswith('uploads/') or
            file_path.startswith('user-uploads/') or
            file_path.startswith('s3://')
        )
    
    def _download_s3_file(self, s3_key: str) -> str:
        """Download S3 file to temporary local file and return local path"""
        try:
            # Create S3 client using AWS credential chain
            if self.aws_profile:
                session = boto3.Session(profile_name=self.aws_profile, region_name=self.s3_region)
                s3_client = session.client('s3')
            else:
                s3_client = boto3.client('s3', region_name=self.s3_region)
            
            # Get file extension for temporary file
            file_extension = os.path.splitext(s3_key)[1]
            
            # Create temporary file
            temp_file = tempfile.NamedTemporaryFile(delete=False, suffix=file_extension)
            temp_path = temp_file.name
            temp_file.close()
            
            # Download file from S3
            s3_client.download_file(self.s3_bucket, s3_key, temp_path)
            
            return temp_path
            
        except ClientError as e:
            raise FileNotFoundError(f"Failed to download S3 file {s3_key}: {str(e)}")
        except Exception as e:
            raise ValueError(f"Error downloading S3 file: {str(e)}")
    
    def read_file(self, file_path: str) -> Tuple[List[str], List[Dict[str, Any]]]:
        """
        Read file and return headers and data rows
        Supports both local files and S3 files
        
        Args:
            file_path: Path to the uploaded file (local path or S3 key)
            
        Returns:
            Tuple of (headers, data_rows)
            
        Raises:
            ValueError: If file format is not supported or file is corrupted
            FileNotFoundError: If file doesn't exist
        """
        temp_file_path = None
        
        try:
            # Check if this is an S3 file
            if self._is_s3_path(file_path):
                # Download from S3 to temporary file
                temp_file_path = self._download_s3_file(file_path)
                full_path = temp_file_path
                file_extension = os.path.splitext(file_path)[1].lower()
            else:
                # LOCAL FILE LOGIC
                # Check if file exists locally
                if not default_storage.exists(file_path):
                    raise FileNotFoundError(f"File not found: {file_path}")
                
                # Get full file path
                full_path = default_storage.path(file_path)
                file_extension = os.path.splitext(file_path)[1].lower()
            
            # Read file based on extension
            try:
                if file_extension == '.csv':
                    df = self._read_csv_file(full_path)
                elif file_extension in ['.xlsx', '.xls']:
                    df = self._read_excel_file(full_path)
                else:
                    raise ValueError(f"Unsupported file format: {file_extension}")
                
                # Extract headers and data
                headers = df.columns.tolist()
                
                # Convert DataFrame to list of dictionaries
                # Replace NaN values with empty strings
                df = df.fillna('')
                data_rows = df.to_dict('records')
                
                return headers, data_rows
                
            except Exception as e:
                raise ValueError(f"Error reading file: {str(e)}")
                
        finally:
            # Clean up temporary file if it was created
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except:
                    pass  # Ignore cleanup errors
    
    def _read_csv_file(self, file_path: str) -> pd.DataFrame:
        """Read CSV file with proper encoding detection and smart header detection"""
        # First, determine the best encoding
        encoding = self._detect_csv_encoding(file_path)
        
        # Read without header to inspect structure
        df_raw = pd.read_csv(file_path, encoding=encoding, header=None)
        
        # Look for the actual header row by finding customer-related terms
        customer_terms = ['customer', 'first', 'last', 'name', 'email', 'phone', 'mobile', 'address']
        header_row = None
        
        # Check first 10 rows for potential headers
        for row_idx in range(min(10, len(df_raw))):
            row_values = df_raw.iloc[row_idx].astype(str).str.lower()
            
            # Count how many customer-related terms we find in this row
            term_count = 0
            for term in customer_terms:
                if any(term in str(val) for val in row_values if pd.notna(val)):
                    term_count += 1
            
            # If we find 2 or more customer terms, this is likely the header row
            if term_count >= 2:
                header_row = row_idx
                break
        
        if header_row is not None:
            # Re-read with the correct header row
            df = pd.read_csv(file_path, encoding=encoding, header=header_row)
            # Skip any rows before the header (they're now part of the data)
            if header_row > 0:
                df = df.iloc[1:]  # Skip the header row itself since it's now the column names
        else:
            # Fallback to default behavior
            df = pd.read_csv(file_path, encoding=encoding)
        
        return df
    
    def _detect_csv_encoding(self, file_path: str) -> str:
        """Detect the best encoding for CSV file"""
        encodings = ['utf-8', 'cp1252', 'iso-8859-1']
        
        for encoding in encodings:
            try:
                # Try to read just the first few rows to test encoding
                pd.read_csv(file_path, encoding=encoding, nrows=5)
                return encoding
            except UnicodeDecodeError:
                continue
        
        # If all fail, default to utf-8 and let pandas handle errors
        return 'utf-8'
    
    def _read_excel_file(self, file_path: str) -> pd.DataFrame:
        """Read Excel file (first sheet) with smart header detection"""
        # First, try to read without header to inspect the structure
        df_raw = pd.read_excel(file_path, sheet_name=0, engine='openpyxl', header=None)
        
        # Look for the actual header row by finding customer-related terms
        customer_terms = ['customer', 'first', 'last', 'name', 'email', 'phone', 'mobile', 'address']
        header_row = None
        
        # Check first 10 rows for potential headers
        for row_idx in range(min(10, len(df_raw))):
            row_values = df_raw.iloc[row_idx].astype(str).str.lower()
            
            # Count how many customer-related terms we find in this row
            term_count = 0
            for term in customer_terms:
                if any(term in str(val) for val in row_values if pd.notna(val)):
                    term_count += 1
            
            # If we find 2 or more customer terms, this is likely the header row
            if term_count >= 2:
                header_row = row_idx
                break
        
        if header_row is not None:
            # Re-read with the correct header row
            df = pd.read_excel(file_path, sheet_name=0, engine='openpyxl', header=header_row)
            # Skip any rows before the header
            if header_row > 0:
                df = df.iloc[1:]  # Skip the header row itself since it's now the column names
        else:
            # Fallback to default behavior
            df = pd.read_excel(file_path, sheet_name=0, engine='openpyxl')
        
        return df
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        Get basic information about the file
        Supports both local files and S3 files
        
        Args:
            file_path: Path to the uploaded file (local path or S3 key)
            
        Returns:
            Dictionary with file information
        """
        try:
            headers, data_rows = self.read_file(file_path)
            
            # Get file size
            if self._is_s3_path(file_path):
                # Get S3 file size
                try:
                    if self.aws_profile:
                        session = boto3.Session(profile_name=self.aws_profile, region_name=self.s3_region)
                        s3_client = session.client('s3')
                    else:
                        s3_client = boto3.client('s3', region_name=self.s3_region)
                    response = s3_client.head_object(Bucket=self.s3_bucket, Key=file_path)
                    file_size = response['ContentLength']
                except:
                    file_size = 0
            else:
                # Local file size
                file_size = default_storage.size(file_path)
            
            return {
                'total_rows': len(data_rows),
                'total_columns': len(headers),
                'headers': headers,
                'sample_data': data_rows[:3] if data_rows else [],  # First 3 rows as sample
                'file_size': file_size,
                'is_valid': True,
                'error': None
            }
        except Exception as e:
            # Get file size for error case
            if self._is_s3_path(file_path):
                try:
                    if self.aws_profile:
                        session = boto3.Session(profile_name=self.aws_profile, region_name=self.s3_region)
                        s3_client = session.client('s3')
                    else:
                        s3_client = boto3.client('s3', region_name=self.s3_region)
                    response = s3_client.head_object(Bucket=self.s3_bucket, Key=file_path)
                    file_size = response['ContentLength']
                except:
                    file_size = 0
            else:
                file_size = default_storage.size(file_path) if default_storage.exists(file_path) else 0
            
            return {
                'total_rows': 0,
                'total_columns': 0,
                'headers': [],
                'sample_data': [],
                'file_size': file_size,
                'is_valid': False,
                'error': str(e)
            }
    
    def validate_file_format(self, file_path: str) -> bool:
        """Check if file format is supported"""
        file_extension = os.path.splitext(file_path)[1].lower()
        return file_extension in self.supported_extensions 