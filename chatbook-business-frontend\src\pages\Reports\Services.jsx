import { useState, useEffect } from "react";
import { ChevronDownIcon } from "@heroicons/react/24/outline";
import Button from "../../components/Button";
import CustomerSelector from "../../components/CustomerSelector";
import DateRangePicker from "../../components/DateRangePicker";
import { transactionApi } from "../../features/reports/services/transactionApi";

function Services() {
  // ========== State Management ==========

  // Filter conditions
  const [filters, setFilters] = useState({
    startDate: new Date(2025, 5, 1), // Jun 1, 2025
    endDate: new Date(2025, 5, 30), // Jun 30, 2025
    employee: "all",
    customer: [], // Change to array to support multi-select
  });

  // Advanced filter conditions
  const [advancedFilters, setAdvancedFilters] = useState({
    serviceType: "all",
    serviceCategory: "all",
    location: "all",
    priceRange: "all",
    duration: "all",
    minAmount: "",
    maxAmount: "",
    includeRefunds: false,
    includeTax: true,
    pricesIncludePointDeduction: true,
    includePastEmployees: false,
  });

  // Services data
  const [servicesData, setServicesData] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [hasRunReport, setHasRunReport] = useState(false);

  // Show/hide advanced filters
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Dropdown options data
  const [employees, setEmployees] = useState([
    { id: "all", name: "All Employees" },
  ]);

  const [customers, setCustomers] = useState([]);

  // ========== Initial Data Loading ==========

  useEffect(() => {
    loadInitialData();
  }, []);

  // Load initial data (employees and customers)
  const loadInitialData = async () => {
    try {
      // Temporarily use mock employee data to avoid API calls
      const mockEmployees = [
        { id: "all", name: "All Employees" },
        { id: "emp1", name: "John Smith" },
        { id: "emp2", name: "Sarah Johnson" },
        { id: "emp3", name: "Mike Wilson" },
      ];

      setEmployees(mockEmployees);

      // Use the same customer data as checkout page
      const checkoutCustomers = [
        {
          id: "customer1",
          name: "Yuechen Guo",
          email: "<EMAIL>",
          phone: null,
          avatar: "YG",
          customerSince: "Jun 30, 2025",
          lastVisit: "---",
          birthday: "Not available",
          membership: "---",
          points: "0 pt",
        },
        {
          id: "customer2",
          name: "Jane Smith",
          email: "<EMAIL>",
          phone: null,
          avatar: "JS",
          customerSince: "Jan 15, 2024",
          lastVisit: "Dec 20, 2024",
          birthday: "Mar 15, 1990",
          membership: "Gold",
          points: "150 pt",
        },
        {
          id: "customer3",
          name: "Bob Johnson",
          email: "<EMAIL>",
          phone: null,
          avatar: "BJ",
          customerSince: "Sep 08, 2023",
          lastVisit: "Nov 30, 2024",
          birthday: "Jul 22, 1985",
          membership: "Silver",
          points: "85 pt",
        },
      ];

      setCustomers(checkoutCustomers);
    } catch (error) {
      console.error("Failed to load initial data:", error);
      // Set default data
      setEmployees([
        { id: "all", name: "All Employees" },
        { id: "emp1", name: "John Smith" },
        { id: "emp2", name: "Sarah Johnson" },
        { id: "emp3", name: "Mike Wilson" },
      ]);

      setCustomers([
        {
          id: "customer1",
          name: "Yuechen Guo",
          email: "<EMAIL>",
          phone: null,
          avatar: "YG",
          customerSince: "Jun 30, 2025",
          lastVisit: "---",
          birthday: "Not available",
          membership: "---",
          points: "0 pt",
        },
        {
          id: "customer2",
          name: "Jane Smith",
          email: "<EMAIL>",
          phone: null,
          avatar: "JS",
          customerSince: "Jan 15, 2024",
          lastVisit: "Dec 20, 2024",
          birthday: "Mar 15, 1990",
          membership: "Gold",
          points: "150 pt",
        },
        {
          id: "customer3",
          name: "Bob Johnson",
          email: "<EMAIL>",
          phone: null,
          avatar: "BJ",
          customerSince: "Sep 08, 2023",
          lastVisit: "Nov 30, 2024",
          birthday: "Jul 22, 1985",
          membership: "Silver",
          points: "85 pt",
        },
      ]);
    }
  };

  // ========== Event Handlers ==========

  // Handle filter condition changes
  const handleFilterChange = (key, value) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Handle advanced filter condition changes
  const handleAdvancedFilterChange = (key, value) => {
    setAdvancedFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  // Run report
  const handleRunReport = async () => {
    setLoading(true);
    setError(null);
    setHasRunReport(true);

    try {
      // Simulate loading delay
      await new Promise((resolve) => setTimeout(resolve, 1000));

      // Temporarily skip API call and show report directly
      // Build API query parameters
      const queryParams = {
        startDate: filters.startDate.toISOString().split("T")[0],
        endDate: filters.endDate.toISOString().split("T")[0],
        employee: filters.employee !== "all" ? filters.employee : undefined,
      };

      // Handle multi-select customers
      if (filters.customer && filters.customer.length > 0) {
        queryParams.customers = filters.customer;
      }

      // Add advanced filter parameters
      if (advancedFilters.serviceType !== "all") {
        queryParams.serviceType = advancedFilters.serviceType;
      }
      if (advancedFilters.serviceCategory !== "all") {
        queryParams.serviceCategory = advancedFilters.serviceCategory;
      }
      if (advancedFilters.location !== "all") {
        queryParams.location = advancedFilters.location;
      }
      if (advancedFilters.priceRange !== "all") {
        queryParams.priceRange = advancedFilters.priceRange;
      }
      if (advancedFilters.duration !== "all") {
        queryParams.duration = advancedFilters.duration;
      }
      if (advancedFilters.minAmount) {
        queryParams.minAmount = advancedFilters.minAmount;
      }
      if (advancedFilters.maxAmount) {
        queryParams.maxAmount = advancedFilters.maxAmount;
      }

      queryParams.includeRefunds = advancedFilters.includeRefunds;
      queryParams.includeTax = advancedFilters.includeTax;
      queryParams.pricesIncludePointDeduction =
        advancedFilters.pricesIncludePointDeduction;
      queryParams.includePastEmployees = advancedFilters.includePastEmployees;

      // Temporarily comment out API call and complete directly
      // const response = await transactionApi.getServicesReport(queryParams);
      // setServicesData(response);

      // Simulate successful response
      console.log("Services report query params:", queryParams);
    } catch (err) {
      setError(err.message || "Failed to load services report");
    } finally {
      setLoading(false);
    }
  };

  // Export report
  const handleExport = async () => {
    try {
      // Temporarily use mock export
      alert("Export functionality will be implemented when API is ready");
    } catch (err) {
      setError(err.message || "Failed to export services report");
    }
  };

  // Print report
  const handlePrint = () => {
    window.print();
  };

  return (
    <div className="container mx-auto px-4 py-6">
      {/* Filter panel */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
          {/* Transaction date */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Transaction Date:
            </label>
            <DateRangePicker
              startDate={filters.startDate}
              endDate={filters.endDate}
              onChange={({ startDate, endDate }) => {
                setFilters((prev) => ({
                  ...prev,
                  startDate,
                  endDate,
                }));
              }}
              placeholder="Select date range"
            />
          </div>

          {/* Employees */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Employees:
            </label>
            <div className="relative">
              <select
                value={filters.employee}
                onChange={(e) => handleFilterChange("employee", e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none"
              >
                {employees.map((employee) => (
                  <option key={employee.id} value={employee.id}>
                    {employee.name}
                  </option>
                ))}
              </select>
              <ChevronDownIcon className="absolute right-3 top-2.5 h-5 w-5 text-gray-400 pointer-events-none" />
            </div>
          </div>

          {/* Customer */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Customer:
            </label>
            <CustomerSelector
              value={filters.customer}
              onChange={(selectedCustomers) =>
                handleFilterChange("customer", selectedCustomers)
              }
              customers={customers}
              placeholder="All Customers"
            />
          </div>

          {/* Run report button */}
          <div>
            <Button
              onClick={handleRunReport}
              disabled={loading}
              className="w-full bg-red-600 text-white hover:bg-red-700 focus:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 py-2 px-4 rounded-md disabled:opacity-50 disabled:cursor-not-allowed font-medium shadow-sm transition-colors duration-200"
            >
              {loading ? "Loading..." : "Run Report"}
            </Button>
          </div>
        </div>
      </div>

      {/* Service/Class Sales Summary title and advanced filters */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-xl font-semibold text-gray-800">
          Service/Class Sales Summary
        </h2>
        <button
          onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
          className="text-blue-600 hover:text-blue-800 text-sm flex items-center"
        >
          Advanced Filters {showAdvancedFilters ? "▲" : "▼"}
        </button>
      </div>

      {/* Advanced filter panel */}
      {showAdvancedFilters && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-6 mb-6">
          <div className="flex gap-8">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={advancedFilters.pricesIncludePointDeduction}
                onChange={(e) =>
                  handleAdvancedFilterChange(
                    "pricesIncludePointDeduction",
                    e.target.checked
                  )
                }
                className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">
                Prices Shown Include Point Deduction
              </span>
            </label>

            <label className="flex items-center">
              <input
                type="checkbox"
                checked={advancedFilters.includePastEmployees}
                onChange={(e) =>
                  handleAdvancedFilterChange(
                    "includePastEmployees",
                    e.target.checked
                  )
                }
                className="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              />
              <span className="text-sm text-gray-700">
                Include Past Employees
              </span>
            </label>
          </div>
        </div>
      )}

      {/* Main content area */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200">
        {!hasRunReport ? (
          // Placeholder content
          <div className="flex flex-col items-center justify-center py-20">
            <div className="text-6xl text-gray-400 mb-4">↑</div>
            <p className="text-lg text-gray-600 font-medium">
              Select filters above, then press Run Report.
            </p>
          </div>
        ) : (
          // Report results area
          <div className="p-6">
            {loading ? (
              <div className="flex items-center justify-center py-20">
                <div className="text-lg text-gray-600">Loading...</div>
              </div>
            ) : error ? (
              <div className="flex items-center justify-center py-20">
                <div className="text-lg text-red-600">Error: {error}</div>
              </div>
            ) : (
              // Services report content
              <div>
                {/* Description text */}
                <p className="text-sm text-gray-600 mb-6">
                  * All sales figures are after discount
                </p>

                {/* Services table */}
                <div className="bg-white border border-gray-200 rounded-lg overflow-hidden mb-8">
                  <table className="w-full">
                    <thead className="bg-gray-50">
                      <tr>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                          Services/Classes
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                          No of Appointments
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                          No of Attendees
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                          Service Sale
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                          Service Add-on Sale
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                          Class Sale
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                          Class Add-on Sale
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                          Cost To Business
                        </th>
                        <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-b">
                          Average Sale
                        </th>
                      </tr>
                    </thead>
                    <tbody className="bg-white">
                      <tr>
                        <td
                          className="px-6 py-8 text-center text-gray-500 text-sm"
                          colSpan="9"
                        >
                          No records to display.
                        </td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Bottom buttons */}
      {hasRunReport && !loading && !error && (
        <div className="flex justify-end gap-4 mt-6">
          <button
            onClick={handleExport}
            className="px-4 py-2 bg-white border border-gray-300 rounded-md hover:bg-gray-50 text-gray-700 flex items-center font-medium shadow-sm"
          >
            Export
            <span className="ml-1">▼</span>
          </button>
          <button
            onClick={handlePrint}
            className="px-6 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 font-medium"
          >
            Print
          </button>
        </div>
      )}
    </div>
  );
}

export default Services;
