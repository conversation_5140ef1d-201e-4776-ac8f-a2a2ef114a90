import React, { useState, useEffect } from "react";

/**
 * FormPreviewFrame — wraps the <FormPreview> component and displays
 * either a desktop preview or a framed iPhone‑style mobile preview.
 */
const FormPreviewFrame = ({ children, initialMode = "mobile" }) => {
  // Allow parent component to control the preview mode (mobile/desktop).
  const [mode, setMode] = useState(initialMode); // 'mobile' | 'desktop'

  // If the parent changes the initialMode prop (by re-mounting with a new key
  // or prop update), keep the internal state in sync.
  useEffect(() => {
    setMode(initialMode);
  }, [initialMode]);

  /* ------------------------------------------------------------ */
  /* Helper renderers                                             */
  /* ------------------------------------------------------------ */

  /** iPhone 12-style shell */
  const renderDeviceFrame = () => (
    <div className="mx-auto w-[430px]">
      {/* Device body */}
      <div className="relative rounded-[40px] bg-white border border-gray-200 shadow-xl overflow-hidden" 
           style={{ boxShadow: "0 10px 25px -5px rgba(0, 0, 0, 0.15), 0 5px 12px -3px rgba(0, 0, 0, 0.1)" }}>
        {/* Notch at top */}
        <div className="absolute top-0 left-1/2 -translate-x-1/2 w-[40%] h-7 bg-black rounded-b-2xl z-10"></div>
        
        {/* Status bar */}
        <div className="w-full h-10 bg-black"></div>

        {/* Scrollable screen */}
        <div
          className="h-[700px] overflow-y-auto text-[15px] leading-6"
          style={{
            paddingTop: "1.5rem",
            paddingLeft: "1.75rem",
            paddingRight: "1.75rem",
            paddingBottom: "2rem",
          }}
        >
          {children}
        </div>

        {/* Bottom indicator bar (replaces home button) */}
        <div className="w-full h-10 bg-black flex items-center justify-center">
          <div className="w-[30%] h-1 bg-gray-400 rounded-full"></div>
        </div>
      </div>
    </div>
  );

  /** Desktop preview simply fills the available width */
  const renderDesktop = () => <div className="w-full">{children}</div>;

  return (
    <section>
      {/* Preview content */}
      <div>
        {mode === "mobile" ? renderDeviceFrame() : renderDesktop()}
      </div>
    </section>
  );
};

export default FormPreviewFrame;
