{% extends "admin/edit_inline/tabular.html" %}
{% load static %}

{% block extrastyle %}
{{ block.super }}
<style>
  .copy-hours-btn {
    margin: 0 4px;
    padding: 4px 8px;
    background-color: #79aec8;
    border: none;
    border-radius: 4px;
    color: white;
    cursor: pointer;
    transition: background-color 0.3s ease;
    font-size: 12px;
  }

  .copy-hours-btn:hover:not(:disabled) {
    background-color: #417690;
  }

  .copy-hours-btn:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    opacity: 0.7;
  }

  .error-message {
    color: #ba2121;
    margin: 4px 0;
    display: none;
    font-size: 12px;
  }

  .inline-group .tabular td.original p {
    margin-top: 0;
  }
</style>
{% endblock %}

{% block after_related_objects %}
<div id="error-message" class="error-message"></div>
{% endblock %}

{% block field_sets %}
{{ block.super }}
<script src="{% static 'admin/js/working_hours.js' %}"></script>
{% endblock %} 