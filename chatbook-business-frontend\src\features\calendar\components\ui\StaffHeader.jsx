import React, { useState, useCallback } from 'react'


const StaffHeader = ({ employeeIds, onStaffHeader<PERSON>lick }) => {
  const [clickedEmployee, setClickedEmployee] = useState(null)
  
  if (!employeeIds?.length) return null
  const list = employeeIds
    .map(id => EMPLOYEES.find(e => e.id === id))
    .filter(Boolean)

  const handleEmployeeClick = useCallback((employee) => {
    setClickedEmployee(employee.id)
    
    // Visual feedback - remove highlight after animation
    setTimeout(() => setClickedEmployee(null), 200)
    
    // Call parent handler if provided
    if (onStaffHeaderClick) {
      onStaffHeader<PERSON>lick(employee)
    }
  }, [onStaffHeaderClick])

  return (
    <div className="flex gap-4 items-center px-4 py-3 border-b border-gray-200 bg-white sticky top-0 z-20 shadow-sm">
      {list.map(emp => (
        <div 
          key={emp.id} 
          className={`flex items-center gap-2 transition-all duration-200 cursor-pointer rounded-lg px-2 py-1 ${
            clickedEmployee === emp.id 
              ? 'bg-blue-100 scale-105 shadow-md' 
              : 'hover:bg-gray-50 hover:scale-102'
          }`}
          onClick={() => handleEmployeeClick(emp)}
          title={`View ${emp.name}'s calendar`}
        >
          <div
            className={`w-8 h-8 rounded-full flex items-center justify-center text-white text-xs font-semibold transition-transform duration-200 ${
              clickedEmployee === emp.id ? 'scale-110 ring-2 ring-blue-300' : ''
            }`}
            style={{ backgroundColor: emp.color }}
          >
            {emp.avatar}
          </div>
          <span className="text-sm font-medium text-gray-700 truncate max-w-[120px]">{emp.name}</span>
        </div>
      ))}
    </div>
  )
}

export default StaffHeader 