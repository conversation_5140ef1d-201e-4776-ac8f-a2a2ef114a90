from django.db import models
from django.contrib.auth.models import Permission

class Role(models.Model):
    """
    Defines different roles a user can have in the system.
    """
    class Choices(models.TextChoices):
        ADMIN = 'admin', 'Business Administrator'
        EMPLOYEE = 'employee', 'Employee/Stylist' 
        CUSTOMER = 'customer', 'Customer'
    
    name = models.CharField(max_length=8, choices=Choices.choices, unique=True)
    description = models.TextField(blank=True)
    permissions = models.ManyToManyField(Permission, blank=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        verbose_name = 'Role'
        verbose_name_plural = 'Roles'
        ordering = ['name']

    def __str__(self):
        return self.get_name_display() 