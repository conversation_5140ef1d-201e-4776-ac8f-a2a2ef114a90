import { useState, useCallback, useReducer, useMemo } from 'react'

/**
 * Initial state for appointment creation form
 */
const initialState = {
  selectedCustomer: null,
  selectedEmployee: null,
  selectedService: null,
  selectedAddOns: [],
  selectedDate: null,
  notes: '',
  depositPercentage: 0,
  isRepeating: false,
  notifyCustomer: true,
  // Computed values
  totalPrice: 0,
  totalDuration: 0,
  depositAmount: 0,
  // Creation metadata
  status: 'requested', // ✅ Fixed: Use 'requested' instead of 'pending' 
  source: 'admin'
}

/**
 * Creation steps for the appointment wizard
 */
export const CREATION_STEPS = ['customer', 'service', 'datetime', 'confirmation']

/**
 * Appointment form reducer for managing complex form state
 */
export const appointmentFormReducer = (state, action) => {
  switch (action.type) {
    case 'INITIALIZE':
      return { 
        ...initialState, 
        ...action.payload,
        // Ensure date is properly formatted
        selectedDate: action.payload.selectedDate ? new Date(action.payload.selectedDate) : null
      }
      
    case 'SELECT_CUSTOMER':
      return { ...state, selectedCustomer: action.payload }
      
    case 'SELECT_SERVICE':
      return {
        ...state,
        selectedService: action.payload,
        selectedAddOns: [], // Reset add-ons when service changes
        ...calculatePriceAndDuration(action.payload, [])
      }
      
    case 'SELECT_ADD_ONS':
      return {
        ...state,
        selectedAddOns: action.payload,
        ...calculatePriceAndDuration(state.selectedService, action.payload)
      }
      
    case 'UPDATE_DATE':
      return { 
        ...state, 
        selectedDate: action.payload ? new Date(action.payload) : null 
      }
      
    case 'UPDATE_EMPLOYEE':
      return { ...state, selectedEmployee: action.payload }
      
    case 'UPDATE_NOTES':
      return { ...state, notes: action.payload.slice(0, 500) } // Limit to 500 chars
      
    case 'UPDATE_DEPOSIT':
      return {
        ...state,
        depositPercentage: action.payload,
        depositAmount: (state.totalPrice * action.payload) / 100
      }
      
    case 'UPDATE_NOTIFICATION':
      return { ...state, notifyCustomer: action.payload }
      
    case 'RESET':
      return initialState
      
    default:
      return state
  }
}

/**
 * Helper function to calculate totals based on service and add-ons
 */
const calculatePriceAndDuration = (service, addOns) => {
  if (!service) return { totalPrice: 0, totalDuration: 0, depositAmount: 0 }
  
  const servicePrice = parseFloat(service.price || 0)
  const serviceDuration = (service.duration || 60) + (service.bufferTime || 0)
  
  const addOnPrice = addOns.reduce((sum, addOn) => sum + parseFloat(addOn.price || 0), 0)
  const addOnDuration = addOns.reduce((sum, addOn) => sum + (addOn.duration || 0), 0)
  
  const totalPrice = servicePrice + addOnPrice
  const totalDuration = serviceDuration + addOnDuration
  
  return { totalPrice, totalDuration, depositAmount: 0 }
}

/**
 * Custom hook for managing appointment creation flow
 * Provides state management, validation, and step navigation
 */
export const useAppointmentCreation = () => {
  const [isOpen, setIsOpen] = useState(false)
  const [currentStep, setCurrentStep] = useState('customer')
  const [formData, dispatch] = useReducer(appointmentFormReducer, initialState)
  const [validationErrors, setValidationErrors] = useState({})
  const [isCreating, setIsCreating] = useState(false)

  /**
   * Start the appointment creation flow
   */
  const startCreation = useCallback(({ date, employee }) => {
    dispatch({
      type: 'INITIALIZE',
      payload: {
        selectedDate: date,
        selectedEmployee: employee,
        status: 'requested', // ✅ Fixed: Use 'requested' instead of 'pending'
        source: 'admin'
      }
    })
    setCurrentStep('customer')
    setValidationErrors({})
    setIsOpen(true)
  }, [])

  /**
   * Navigate to next step
   */
  const nextStep = useCallback(() => {
    const currentIndex = CREATION_STEPS.indexOf(currentStep)
    if (currentIndex < CREATION_STEPS.length - 1) {
      setCurrentStep(CREATION_STEPS[currentIndex + 1])
      setValidationErrors({}) // Clear errors when moving to next step
    }
  }, [currentStep])

  /**
   * Navigate to previous step
   */
  const previousStep = useCallback(() => {
    const currentIndex = CREATION_STEPS.indexOf(currentStep)
    if (currentIndex > 0) {
      setCurrentStep(CREATION_STEPS[currentIndex - 1])
      setValidationErrors({}) // Clear errors when going back
    }
  }, [currentStep])

  /**
   * Go to specific step
   */
  const goToStep = useCallback((step) => {
    if (CREATION_STEPS.includes(step)) {
      setCurrentStep(step)
      setValidationErrors({})
    }
  }, [])

  /**
   * Validate current step (for checking, doesn't set state)
   */
  const isCurrentStepValid = useCallback(() => {
    const errors = validateStep(currentStep, formData)
    return Object.keys(errors).length === 0
  }, [currentStep, formData])

  /**
   * Validate current step and set errors
   */
  const validateCurrentStep = useCallback(() => {
    const errors = validateStep(currentStep, formData)
    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }, [currentStep, formData])

  /**
   * Update form data
   */
  const updateFormData = useCallback((action) => {
    dispatch(action)
  }, [])

  /**
   * Close modal and reset state
   */
  const closeModal = useCallback(() => {
    setIsOpen(false)
    setCurrentStep('customer')
    setValidationErrors({})
    setIsCreating(false)
    // Reset form data after a short delay to allow modal to close smoothly
    setTimeout(() => {
      dispatch({ type: 'RESET' })
    }, 300)
  }, [])

  /**
   * Check if we can proceed to next step (doesn't trigger state updates)
   */
  const canProceed = useMemo(() => {
    return isCurrentStepValid()
  }, [isCurrentStepValid])

  /**
   * Get current step index
   */
  const getCurrentStepIndex = useMemo(() => {
    return CREATION_STEPS.indexOf(currentStep)
  }, [currentStep])

  /**
   * Check if it's the last step
   */
  const isLastStep = useMemo(() => {
    return currentStep === CREATION_STEPS[CREATION_STEPS.length - 1]
  }, [currentStep])

  /**
   * Check if it's the first step
   */
  const isFirstStep = useMemo(() => {
    return currentStep === CREATION_STEPS[0]
  }, [currentStep])

  return {
    // State
    isOpen,
    currentStep,
    formData,
    validationErrors,
    isCreating,
    
    // Actions
    startCreation,
    nextStep,
    previousStep,
    goToStep,
    validateCurrentStep,
    updateFormData,
    closeModal,
    
    // Setters for external control
    setIsCreating,
    
    // Computed
    canProceed,
    getCurrentStepIndex,
    isLastStep,
    isFirstStep,
    
    // Constants
    totalSteps: CREATION_STEPS.length,
    steps: CREATION_STEPS
  }
}

/**
 * Validation schema for appointment creation
 */
export const appointmentValidationSchema = {
  customer: {
    required: true,
    validate: (customer) => customer && customer.id,
    message: 'Customer selection is required'
  },
  
  service: {
    required: true,
    validate: (service) => service && service.id,
    message: 'Service selection is required'
  },
  
  employee: {
    required: true,
    validate: (employee) => employee && employee.id,
    message: 'Employee assignment is required'
  },
  
  date: {
    required: true,
    validate: (date) => {
      if (!date) return false
      const now = new Date()
      const appointmentDate = new Date(date)
      // Allow appointments today or in the future
      return appointmentDate >= new Date(now.getFullYear(), now.getMonth(), now.getDate())
    },
    message: 'Date must be today or in the future'
  },
  
  notes: {
    maxLength: 500,
    validate: (notes) => !notes || notes.length <= 500,
    message: 'Notes cannot exceed 500 characters'
  }
}

/**
 * Validate a specific step of the appointment creation
 */
export const validateStep = (step, formData) => {
  const errors = {}
  
  switch (step) {
    case 'customer':
      if (!appointmentValidationSchema.customer.validate(formData.selectedCustomer)) {
        errors.customer = appointmentValidationSchema.customer.message
      }
      break
      
    case 'service':
      if (!appointmentValidationSchema.service.validate(formData.selectedService)) {
        errors.service = appointmentValidationSchema.service.message
      }
      break
      
    case 'datetime':
      if (!appointmentValidationSchema.employee.validate(formData.selectedEmployee)) {
        errors.employee = appointmentValidationSchema.employee.message
      }
      if (!appointmentValidationSchema.date.validate(formData.selectedDate)) {
        errors.date = appointmentValidationSchema.date.message
      }
      break
      
    case 'confirmation':
      // Validate all fields for final step
      if (!appointmentValidationSchema.customer.validate(formData.selectedCustomer)) {
        errors.customer = appointmentValidationSchema.customer.message
      }
      if (!appointmentValidationSchema.service.validate(formData.selectedService)) {
        errors.service = appointmentValidationSchema.service.message
      }
      if (!appointmentValidationSchema.employee.validate(formData.selectedEmployee)) {
        errors.employee = appointmentValidationSchema.employee.message
      }
      if (!appointmentValidationSchema.date.validate(formData.selectedDate)) {
        errors.date = appointmentValidationSchema.date.message
      }
      if (!appointmentValidationSchema.notes.validate(formData.notes)) {
        errors.notes = appointmentValidationSchema.notes.message
      }
      break
  }
  
  return errors
} 