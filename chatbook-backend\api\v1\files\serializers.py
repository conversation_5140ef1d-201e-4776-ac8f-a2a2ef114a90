from rest_framework import serializers
from django.conf import settings

# We keep the original model reference for DRF introspection, but serializer
# now supports objects from apps.files.models.UploadedFile as well.
from customers.models import UploadedFile


class FileUploadSerializer(serializers.Serializer):
    """Simple serializer for file upload requests"""
    
    file = serializers.FileField(required=True)
    file_type = serializers.CharField(default='customer_import')
    description = serializers.CharField(required=False, allow_blank=True)
    skip_duplicates = serializers.BooleanField(default=True)
    update_existing = serializers.BooleanField(default=False)

    # NEW → whether the uploaded asset is a customer-signed copy
    is_signed = serializers.BooleanField(default=False)
    
    # Optional fields for advanced folder hierarchy
    customer_uuid = serializers.UUIDField(required=False, allow_null=True)
    category = serializers.ChoiceField(
        choices=[
            ('forms', 'Forms'),
            ('photos', 'Photos'),
            ('notes', 'Notes'),
            ('shared', 'Shared'),
            ('signatures', 'Signatures'),
        ],
        required=False,
    )
    
    def validate_file(self, value):
        """Basic file validation"""
        if not value:
            raise serializers.ValidationError("No file provided")
        
        # Check file extension – allow common document and image types in addition to CSV / Excel
        # These extensions should stay in sync with the server-side processing logic
        allowed_extensions = [
            '.csv', '.xlsx', '.xls',         # spreadsheet / import files
            '.pdf',                          # generated signature or other PDFs
            '.jpg', '.jpeg', '.png'          # image uploads
        ]
        filename = value.name.lower()
        if not any(filename.endswith(ext) for ext in allowed_extensions):
            raise serializers.ValidationError(
                f"File type not supported. Please upload CSV, Excel, PDF, or image files."
            )
        
        return value


class FileProcessSerializer(serializers.Serializer):
    """Serializer for file processing requests - supports both legacy and AWS CDK formats"""
    
    # Legacy format support (backward compatibility)
    skip_duplicates = serializers.BooleanField(default=True)
    update_existing = serializers.BooleanField(default=False)
    validate_emails = serializers.BooleanField(default=True)
    custom_field_mapping = serializers.DictField(
        child=serializers.CharField(),
        required=False,
        allow_empty=True,
        help_text="Custom field mapping override (optional)"
    )
    
    # AWS CDK format support (according to API_INTEGRATION.md)
    s3_key = serializers.CharField(required=False, help_text="S3 object key where the file is stored")
    user_email = serializers.EmailField(required=False, help_text="Email address for processing notifications")
    tenant_id = serializers.CharField(required=False, help_text="Tenant identifier for multi-tenant setups")
    uploaded_by = serializers.CharField(required=False, help_text="User ID who uploaded the file")
    processing_options = serializers.DictField(required=False, help_text="Configuration for file processing")
    metadata = serializers.DictField(required=False, help_text="Additional file metadata")
    
    def validate_custom_field_mapping(self, value):
        """Validate custom field mapping if provided"""
        if value:
            # Basic validation - ensure it's a dictionary with string keys and values
            if not isinstance(value, dict):
                raise serializers.ValidationError("Field mapping must be a dictionary")
            
            for key, val in value.items():
                if not isinstance(key, str) or not isinstance(val, str):
                    raise serializers.ValidationError("Field mapping keys and values must be strings")
        
        return value


class UploadedFileResponseSerializer(serializers.ModelSerializer):
    """Response serializer for uploaded files"""
    
    file_id = serializers.CharField(read_only=True)
    file_name = serializers.CharField(read_only=True)
    file_size = serializers.IntegerField(read_only=True)
    file_path = serializers.SerializerMethodField()
    uploaded_at = serializers.DateTimeField(read_only=True)
    
    class Meta:
        model = UploadedFile
        fields = [
            'file_id', 'file_name', 'file_size', 'file_path', 
            'uploaded_at', 'status', 'file_type'
        ]
    
    def get_file_path(self, obj):
        """Return a path/URL for the uploaded file.

        Supports both customers.models.UploadedFile (has a FileField `file`)
        and apps.files.models.UploadedFile (stores S3 bucket + key).
        """
        # Legacy model path
        if hasattr(obj, "file") and obj.file:
            return obj.file.url

        # New S3-backed model path
        if hasattr(obj, "s3_key"):
            bucket = getattr(settings, "AWS_STORAGE_BUCKET_NAME", getattr(obj, "s3_bucket", ""))
            # Return an S3 URI – front-end can request a presigned URL if required
            return f"s3://{bucket}/{obj.s3_key}"

        return None 
