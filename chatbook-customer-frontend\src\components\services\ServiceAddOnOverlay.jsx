import { useState } from 'react';
import './Services.css';

const ServiceAddOnOverlay = ({ service, onClose, onContinue, initialAddOns = [] }) => {
    // Ensure service price is numeric
    const servicePrice = typeof service.price === 'number' 
        ? service.price 
        : parseFloat(service.price || 0);
    
    // Initialize with any previously selected add-ons
    const [selectedAddOns, setSelectedAddOns] = useState(initialAddOns);
    
    // Calculate initial total based on service price and any pre-selected add-ons
    const initialTotal = initialAddOns.reduce((total, addOn) => {
        const addOnPrice = typeof addOn.price === 'number' 
            ? addOn.price 
            : parseFloat(addOn.price || 0);
        return total + addOnPrice;
    }, servicePrice);
    
    const [total, setTotal] = useState(initialTotal);

    // Mock add-on services - in a real app, these would come from the API
    const addOns = [
        {
            id: 101,
            name: 'Bottom Lash',
            description: 'This service will complete your eyelash look by applying one artificial lash to one of your natural lower lashes, to match the upper lash. Bottom Lash cannot be refilled.',
            price: 29,
            image: '/images/bottom-lash.jpg'
        },
        {
            id: 102,
            name: 'Lash Removal',
            description: '15 minutes. This service will remove all the faux lashes and clean the entire eye area. If you have lashes from another salon please add this service to the lash fullset appointment.',
            price: 0,
            image: '/images/lash-removal.jpg'
        },
        {
            id: 103,
            name: 'Consulting Session',
            description: 'Get advice from our lash expert on the best lash style for your face shape and preferences.',
            price: 25,
            image: '/images/consulting.jpg'
        }
    ];

    const handleAddOnToggle = (addOn) => {
        // Ensure add-on price is numeric
        const addOnPrice = typeof addOn.price === 'number' 
            ? addOn.price 
            : parseFloat(addOn.price || 0);
            
        // Check if this add-on is already selected
        const alreadySelected = selectedAddOns.findIndex(item => item.id === addOn.id) >= 0;
        
        if (alreadySelected) {
            // Remove from selected list
            const updatedAddOns = selectedAddOns.filter(item => item.id !== addOn.id);
            setSelectedAddOns(updatedAddOns);
            setTotal(prevTotal => prevTotal - addOnPrice);
        } else {
            // Add to selected list
            setSelectedAddOns([...selectedAddOns, addOn]);
            setTotal(prevTotal => prevTotal + addOnPrice);
        }
    };

    const handleOverlayClick = (e) => {
        // Close the overlay if clicking on the backdrop (not the content)
        if (e.target.className === 'add-on-overlay') {
            onClose();
        }
    };

    const handleContinue = () => {
        onContinue(service, selectedAddOns);
    };

    // Format price for display
    const formatPrice = (price) => {
        return typeof price === 'number' 
            ? price.toFixed(2) 
            : parseFloat(price || 0).toFixed(2);
    };

    // Check if an add-on is already selected
    const isAddOnSelected = (addOnId) => {
        return selectedAddOns.some(addOn => addOn.id === addOnId);
    };

    return (
        <div className="add-on-overlay" onClick={handleOverlayClick}>
            <div className="add-on-content">
                <button className="close-btn" onClick={onClose}>&times;</button>
                
                <div className="add-on-header">
                    <div className="service-image">
                        <img src={service.image || '/images/default-service.jpg'} alt={service.name} />
                    </div>
                    <div className="service-info">
                        <h2>{service.name}</h2>
                        <p className="service-price">${formatPrice(service.price)}</p>
                        <p className="service-description">{service.description}</p>
                    </div>
                </div>

                <h3>Suggested Add-Ons Available</h3>
                
                <div className="add-on-services">
                    <div className="optional-label">Optional</div>
                    
                    {addOns.map(addOn => (
                        <div key={addOn.id} className="add-on-item">
                            <label htmlFor={`addon-${addOn.id}`} className="add-on-label">
                                <input 
                                    type="checkbox" 
                                    id={`addon-${addOn.id}`}
                                    checked={isAddOnSelected(addOn.id)}
                                    onChange={() => handleAddOnToggle(addOn)} 
                                />
                                <div className="add-on-image">
                                    <img src={addOn.image} alt={addOn.name} />
                                </div>
                                <div className="add-on-details">
                                    <h4>{addOn.name}</h4>
                                    <p>{addOn.description}</p>
                                </div>
                                <div className="add-on-price">
                                    {addOn.price === 0 ? 'FREE' : `+$${formatPrice(addOn.price)}`}
                                </div>
                            </label>
                        </div>
                    ))}
                </div>
                
                <div className="add-on-footer">
                    <div className="total-price">
                        <span>Total</span>
                        <span>${formatPrice(total)}</span>
                    </div>
                    <button className="continue-btn" onClick={handleContinue}>Continue</button>
                </div>
            </div>
        </div>
    );
};

export default ServiceAddOnOverlay;
