import { useState, useEffect } from 'react';

const NetworkDebugger = () => {
  const [logs, setLogs] = useState([]);
  const [isVisible, setIsVisible] = useState(false);

  useEffect(() => {
    // Override console.log to capture network logs
    const originalLog = console.log;
    const originalError = console.error;

    console.log = (...args) => {
      if (args[0] && (args[0].includes('📤') || args[0].includes('📥'))) {
        setLogs(prev => [...prev.slice(-20), {
          type: 'log',
          timestamp: new Date().toLocaleTimeString(),
          message: args
        }]);
      }
      originalLog.apply(console, args);
    };

    console.error = (...args) => {
      if (args[0] && (args[0].includes('📤') || args[0].includes('📥') || args[0].includes('❌'))) {
        setLogs(prev => [...prev.slice(-20), {
          type: 'error',
          timestamp: new Date().toLocaleTimeString(),
          message: args
        }]);
      }
      originalError.apply(console, args);
    };

    return () => {
      console.log = originalLog;
      console.error = originalError;
    };
  }, []);

  if (!isVisible) {
    return (
      <button
        onClick={() => setIsVisible(true)}
        className="fixed bottom-4 right-4 bg-blue-600 text-white px-3 py-2 rounded-lg text-sm z-50"
      >
        Debug Network
      </button>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 w-96 max-h-96 bg-black text-green-400 p-4 rounded-lg overflow-auto text-xs font-mono z-50">
      <div className="flex justify-between items-center mb-2">
        <h3 className="text-white font-bold">Network Debug</h3>
        <div>
          <button
            onClick={() => setLogs([])}
            className="bg-red-600 text-white px-2 py-1 rounded mr-2"
          >
            Clear
          </button>
          <button
            onClick={() => setIsVisible(false)}
            className="bg-gray-600 text-white px-2 py-1 rounded"
          >
            Hide
          </button>
        </div>
      </div>
      
      <div className="space-y-2">
        {logs.map((log, index) => (
          <div key={index} className={`p-2 rounded ${log.type === 'error' ? 'bg-red-900' : 'bg-gray-800'}`}>
            <div className="text-gray-400 text-xs">{log.timestamp}</div>
            <div className="whitespace-pre-wrap">
              {log.message.map((msg, i) => (
                <div key={i}>
                  {typeof msg === 'object' ? JSON.stringify(msg, null, 2) : String(msg)}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default NetworkDebugger;
