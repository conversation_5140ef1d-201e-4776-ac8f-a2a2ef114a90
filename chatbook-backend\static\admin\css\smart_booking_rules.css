/* Smart Booking Rules Admin Styles */

/* Hide the tolerance field initially - will be shown by JavaScript when needed */
.form-row.field-tentative_hold_tolerance {
    display: none;
}

/* Ensure proper alignment for smart booking rule fields */
.form-row.field-enable_bookend_slots,
.form-row.field-enable_gapless_booking,
.form-row.field-enable_tentative_hold {
    display: flex !important;
    align-items: center !important;
    justify-content: space-between !important;
    padding: 12px 0 !important;
    border-bottom: 1px solid #eee;
}

.form-row.field-enable_bookend_slots:last-of-type,
.form-row.field-enable_gapless_booking:last-of-type,
.form-row.field-enable_tentative_hold:last-of-type {
    border-bottom: none;
}

/* Style the labels */
.form-row.field-enable_bookend_slots > label,
.form-row.field-enable_gapless_booking > label,
.form-row.field-enable_tentative_hold > label {
    margin: 0 !important;
    font-weight: 500 !important;
    color: #333 !important;
    flex: 1 !important;
    font-size: 14px !important;
}

/* Ensure the widget container aligns properly */
.form-row.field-enable_bookend_slots > div,
.form-row.field-enable_gapless_booking > div,
.form-row.field-enable_tentative_hold > div {
    display: flex !important;
    align-items: center !important;
    gap: 8px !important;
}

/* Smart booking rules container */
.smart-booking-rules {
    margin: 20px 0;
    padding: 0;
}

.smart-booking-rules h3 {
    margin: 0 0 20px 0;
    color: #333;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 1px solid #e1e5e9;
    padding-bottom: 10px;
}

/* Rule row styling */
.smart-rule-row {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding: 0;
    background: none;
    border: none;
}

.smart-rule-content {
    display: flex;
    align-items: center;
    flex: 1;
}

.smart-rule-label {
    font-weight: 500;
    color: #333;
    font-size: 14px;
    margin-right: 10px;
}

.smart-rule-description {
    color: #666;
    font-size: 13px;
    font-style: italic;
}

/* Toggle Switch Styling */
.toggle-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 30px;
    margin-left: auto;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: 0.3s;
    border-radius: 30px;
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 22px;
    width: 22px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: 0.3s;
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0,0,0,0.2);
}

.toggle-switch input:checked + .toggle-slider {
    background-color: #4CAF50;
}

.toggle-switch input:checked + .toggle-slider:before {
    transform: translateX(30px);
}

.toggle-switch input:focus + .toggle-slider {
    box-shadow: 0 0 1px #4CAF50;
}

/* Tolerance field styling */
.tolerance-field {
    margin-top: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border: 1px solid #e9ecef;
    border-radius: 4px;
    display: none;
}

.tolerance-field.show {
    display: block;
}

.tolerance-field label {
    font-weight: 500;
    color: #333;
    margin-right: 10px;
    font-size: 14px;
}

.tolerance-field input {
    width: 80px;
    padding: 6px 10px;
    border: 1px solid #ced4da;
    border-radius: 4px;
    font-size: 14px;
}

.tolerance-field .help-text {
    display: block;
    font-size: 12px;
    color: #6c757d;
    margin-top: 8px;
    font-style: italic;
}

/* Help icon styling */
.smart-rule-help {
    position: relative;
    display: inline-block;
    margin-left: 8px;
    cursor: help;
}

.smart-rule-help-icon {
    display: inline-block;
    width: 18px;
    height: 18px;
    background-color: #6c757d;
    color: white;
    border-radius: 50%;
    text-align: center;
    line-height: 18px;
    font-size: 12px;
    font-weight: bold;
}

.smart-rule-help:hover .smart-rule-help-icon {
    background-color: #495057;
}

.smart-rule-tooltip {
    visibility: hidden;
    width: 320px;
    background-color: #333;
    color: #fff;
    text-align: left;
    border-radius: 6px;
    padding: 12px 16px;
    position: absolute;
    z-index: 1000;
    bottom: 125%;
    left: 50%;
    margin-left: -160px;
    opacity: 0;
    transition: opacity 0.3s;
    font-size: 13px;
    line-height: 1.4;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
}

.smart-rule-tooltip::after {
    content: "";
    position: absolute;
    top: 100%;
    left: 50%;
    margin-left: -5px;
    border-width: 5px;
    border-style: solid;
    border-color: #333 transparent transparent transparent;
}

.smart-rule-help:hover .smart-rule-tooltip {
    visibility: visible;
    opacity: 1;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .smart-rule-tooltip {
        width: 280px;
        margin-left: -140px;
    }

    .smart-rule-row {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .toggle-switch {
        margin-left: 0;
    }
}
