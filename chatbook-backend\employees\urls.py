from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from . import views

# Create a router and register our viewsets with it.
router = DefaultRouter()

# The API URLs are now determined automatically by the router.
urlpatterns = [
    path('', include(router.urls)),
    # ICS Calendar Feed for Employees
    path('calendar/<uuid:ics_token>.ics', views.employee_ics_feed, name='employee_ics_feed'),
] 