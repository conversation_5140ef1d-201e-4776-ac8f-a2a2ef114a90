export const getAppointmentMockData = () => [
  {
    id: 1,
    appointmentDate: '2024-06-15',
    checkoutDate: '2024-06-15',
    status: 'completed',
    serviceProvider: '<PERSON>',
    service: 'Hair Cut & Styling',
    lastVisit: '2024-06-15',
    transaction: {
      products: [
        { name: 'Hair Cut & Styling', price: 85.00 },
        { name: 'Hair Treatment', price: 45.00 },
        { name: 'Hair Product (Shampoo)', price: 25.00 }
      ],
      subtotal: 155.00,
      discountAmount: 15.50,
      discountPercentage: 10,
      tipAmount: 20.00,
      tipPercentage: 12.9,
      tax: 12.40,
      total: 171.90,
      paid: true,
      status: 'completed',
      paymentMethod: 'Credit Card'
    },
    modificationHistory: [
      {
        id: 1,
        timestamp: '2024-06-10T09:00:00Z',
        action: 'Appointment Created',
        user: '<PERSON>',
        details: 'Initial appointment scheduled for Hair Cut & Styling',
        type: 'creation'
      },
      {
        id: 2,
        timestamp: '2024-06-12T14:30:00Z',
        action: 'Service Added',
        user: '<PERSON>',
        details: 'Added Hair Treatment service',
        type: 'modification'
      },
      {
        id: 3,
        timestamp: '2024-06-15T10:15:00Z',
        action: 'Customer Checked In',
        user: 'Reception Staff',
        details: 'Customer arrived and checked in',
        type: 'status'
      },
      {
        id: 4,
        timestamp: '2024-06-15T11:45:00Z',
        action: 'Service Completed',
        user: 'Sarah Johnson',
        details: 'All services completed successfully',
        type: 'completion'
      },
      {
        id: 5,
        timestamp: '2024-06-15T12:00:00Z',
        action: 'Payment Processed',
        user: 'Reception Staff',
        details: 'Payment of $171.90 processed via card',
        type: 'payment'
      }
    ]
  },
  {
    id: 2,
    appointmentDate: '2024-05-20',
    checkoutDate: '2024-05-20',
    status: 'completed',
    serviceProvider: 'Mike Davis',
    service: 'Massage Therapy',
    lastVisit: '2024-05-20',
    transaction: {
      products: [
        { name: 'Deep Tissue Massage (60min)', price: 120.00 },
        { name: 'Aromatherapy Add-on', price: 25.00 }
      ],
      subtotal: 145.00,
      discountAmount: 0,
      discountPercentage: 0,
      tipAmount: 25.00,
      tipPercentage: 17.2,
      tax: 11.60,
      total: 181.60,
      paid: true,
      status: 'refunded',
      paymentMethod: 'Credit Card',
      refundAmount: 181.60,
      refundDate: '2024-05-22T10:30:00Z',
      refundReason: 'Customer dissatisfied with service quality'
    },
    modificationHistory: [
      {
        id: 1,
        timestamp: '2024-05-18T10:00:00Z',
        action: 'Appointment Created',
        user: 'Mike Davis',
        details: 'Initial appointment scheduled for Massage Therapy',
        type: 'creation'
      },
      {
        id: 2,
        timestamp: '2024-05-20T09:30:00Z',
        action: 'Customer Checked In',
        user: 'Reception Staff',
        details: 'Customer arrived for massage appointment',
        type: 'status'
      },
      {
        id: 3,
        timestamp: '2024-05-20T11:00:00Z',
        action: 'Service Completed',
        user: 'Mike Davis',
        details: 'Deep tissue massage completed',
        type: 'completion'
      },
      {
        id: 4,
        timestamp: '2024-05-20T11:15:00Z',
        action: 'Payment Processed',
        user: 'Reception Staff',
        details: 'Payment of $181.60 processed via cash',
        type: 'payment'
      }
    ]
  },
  {
    id: 3,
    appointmentDate: '2024-04-10',
    checkoutDate: '2024-04-10',
    status: 'completed',
    serviceProvider: 'Lisa Chen',
    service: 'Facial Treatment',
    lastVisit: '2024-04-10',
    transaction: {
      products: [
        { name: 'Anti-Aging Facial', price: 95.00 },
        { name: 'Eye Treatment', price: 35.00 },
        { name: 'Skincare Kit', price: 65.00 }
      ],
      subtotal: 195.00,
      discountAmount: 19.50,
      discountPercentage: 10,
      tipAmount: 30.00,
      tipPercentage: 17.1,
      tax: 15.60,
      total: 221.10,
      paid: true,
      status: 'completed',
      paymentMethod: 'Credit Card'
    },
    modificationHistory: [
      {
        id: 1,
        timestamp: '2024-04-08T11:00:00Z',
        action: 'Appointment Created',
        user: 'Lisa Chen',
        details: 'Initial appointment scheduled for Facial Treatment',
        type: 'creation'
      },
      {
        id: 2,
        timestamp: '2024-04-09T16:20:00Z',
        action: 'Service Modified',
        user: 'Lisa Chen',
        details: 'Added Eye Treatment to appointment',
        type: 'modification'
      },
      {
        id: 3,
        timestamp: '2024-04-10T13:00:00Z',
        action: 'Customer Checked In',
        user: 'Reception Staff',
        details: 'Customer arrived for facial appointment',
        type: 'status'
      },
      {
        id: 4,
        timestamp: '2024-04-10T15:30:00Z',
        action: 'Service Completed',
        user: 'Lisa Chen',
        details: 'Facial treatment and eye treatment completed',
        type: 'completion'
      },
      {
        id: 5,
        timestamp: '2024-04-10T15:45:00Z',
        action: 'Payment Processed',
        user: 'Reception Staff',
        details: 'Payment of $221.10 processed via card',
        type: 'payment'
      }
    ]
  },
  {
    id: 4,
    appointmentDate: '2024-03-25',
    checkoutDate: null,
    status: 'cancelled',
    serviceProvider: 'Sarah Johnson',
    service: 'Hair Color',
    lastVisit: null,
    transaction: {
      products: [
        { name: 'Hair Color Treatment', price: 150.00 },
        { name: 'Color Protection Product', price: 35.00 }
      ],
      subtotal: 185.00,
      discountAmount: 0,
      discountPercentage: 0,
      tipAmount: 0,
      tipPercentage: 0,
      tax: 14.80,
      total: 199.80,
      paid: false,
      status: 'failed',
      paymentMethod: 'Credit Card',
      failureReason: 'Insufficient funds'
    },
    modificationHistory: [
      {
        id: 1,
        timestamp: '2024-03-20T14:00:00Z',
        action: 'Appointment Created',
        user: 'Sarah Johnson',
        details: 'Initial appointment scheduled for Hair Color',
        type: 'creation'
      },
      {
        id: 2,
        timestamp: '2024-03-25T08:30:00Z',
        action: 'Appointment Cancelled',
        user: 'Customer',
        details: 'Customer cancelled appointment due to scheduling conflict',
        type: 'cancellation'
      }
    ]
  },
  {
    id: 5,
    appointmentDate: '2024-07-02',
    checkoutDate: null,
    status: 'scheduled',
    serviceProvider: 'Mike Davis',
    service: 'Sports Massage',
    lastVisit: null,
    transaction: {
      products: [
        { name: 'Sports Massage (90min)', price: 140.00 },
        { name: 'Recovery Kit', price: 45.00 }
      ],
      subtotal: 185.00,
      discountAmount: 18.50,
      discountPercentage: 10,
      tipAmount: 0,
      tipPercentage: 0,
      tax: 13.32,
      total: 179.82,
      paid: false,
      status: 'pending',
      paymentMethod: 'Cash',
      pendingReason: 'Appointment scheduled - payment due at service'
    },
    modificationHistory: [
      {
        id: 1,
        timestamp: '2024-06-25T16:00:00Z',
        action: 'Appointment Created',
        user: 'Mike Davis',
        details: 'Initial appointment scheduled for Sports Massage',
        type: 'creation'
      }
    ]
  }
] 