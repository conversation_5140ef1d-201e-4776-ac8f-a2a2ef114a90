(function($) {
    $(document).ready(function() {
        // Set source field to 'admin' on page load
        if ($('input[name="source"]').length) {
            $('input[name="source"]').val('admin');
        } else {
            // If no source field exists, create one
            $('form').append('<input type="hidden" name="source" value="admin">');
        }
        
        // Also handle form submission to ensure source is set
        $('form').on('submit', function() {
            var sourceField = $('input[name="source"]');
            if (sourceField.length) {
                sourceField.val('admin');
            } else {
                $(this).append('<input type="hidden" name="source" value="admin">');
            }
            return true;
        });
        
        // Add timepicker functionality to time input fields
        if ($.fn.timepicker) {
            // If jQuery timepicker is available, use it
            $('.vTimeField').timepicker({
                timeFormat: 'HH:mm',
                interval: 15,
                minTime: '7:00',
                maxTime: '22:00'
            });
        } else {
            // Otherwise add a time format hint to help users
            $('.field-start_datetime').append(
                '<div class="time-format-hint" style="margin-top: 5px; color: #666;">'+
                'Enter time in 24-hour format (e.g., 14:30 for 2:30 PM)</div>'
            );
        }

        // Add format instructions
        var $timeField = $('input.vTimeField');
        if ($timeField.length && !$timeField.attr('placeholder')) {
            $timeField.attr('placeholder', 'HH:MM (24h)');
        }
        
        // When employee is changed, update available services
        $('#id_employee').change(function() {
            var employeeId = $(this).val();
            if (employeeId) {
                // Make AJAX call to get services for this employee
                $.ajax({
                    url: '/api/v1/employees/' + employeeId + '/services/',
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        updateServiceOptions(data);
                    },
                    error: function(xhr, status, error) {
                        console.error("Error loading services: " + error);
                    }
                });
            }
        });

        // Update service options in all service selects
        function updateServiceOptions(services) {
            $('.service-select').each(function() {
                var select = $(this);
                var currentValue = select.val();
                
                // Clear existing options except the first empty one
                select.find('option:not(:first)').remove();
                
                // Add new options
                $.each(services, function(index, service) {
                    var option = $('<option></option>')
                        .attr('value', service.service_id)
                        .text(service.service_name);
                    
                    // Store service data as data attributes for easy access
                    option.data('price', service.price);
                    option.data('duration', service.duration);
                    
                    select.append(option);
                });
                
                // Try to restore previous selection
                if (currentValue) {
                    select.val(currentValue);
                    select.trigger('change'); // Trigger change to update price/duration
                }
            });
        }

        // When a service is selected, update price and duration fields
        $(document).on('change', '.service-select', function() {
            var row = $(this).closest('tr');
            var serviceId = $(this).val();
            var $selectedOption = $(this).find('option:selected');
            
            if (serviceId) {
                // First try to get data from the option data attributes
                var price = $selectedOption.data('price');
                var duration = $selectedOption.data('duration');
                
                if (price !== undefined && duration !== undefined) {
                    // If data is available in the option, use it directly
                    updateServiceFields(row, price, duration);
                } else {
                    // Otherwise make an API call
                    var employeeId = $('#id_employee').val();
                    
                    if (employeeId) {
                        $.ajax({
                            url: '/api/v1/employees/' + employeeId + '/services/',
                            type: 'GET',
                            dataType: 'json',
                            success: function(data) {
                                // Find the selected service
                                var service = data.find(function(s) {
                                    return s.service_id == serviceId;
                                });
                                
                                if (service) {
                                    updateServiceFields(row, service.price, service.duration);
                                }
                            },
                            error: function(xhr, status, error) {
                                console.error("Error loading service details: " + error);
                            }
                        });
                    }
                }
            }
        });
        
        // Helper function to update service price and duration fields
        function updateServiceFields(row, price, duration) {
            row.find('input[name$="-base_price"]').val(price);
            row.find('input[name$="-duration"]').val(duration);
        }

        // Initialize with existing employee's services if editing
        if ($('#id_employee').val()) {
            $('#id_employee').trigger('change');
        }
        
        // Initialize existing service rows
        $('.service-select').each(function() {
            if ($(this).val()) {
                $(this).trigger('change');
            }
        });
    });
})(django.jQuery); 