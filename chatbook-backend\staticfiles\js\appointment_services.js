(function($) {
    $(document).ready(function() {
        // Highlight required fields
        $('.field-service .required').closest('td').css('background-color', '#fff8e5');
        
        // Add required indicator to service field
        $('div.inline-group h2:contains("Appointment Services")').append(' <span class="required" style="color: #ba2121;">*</span>');
        $('.service-select').closest('td').find('label').append(' <span class="required" style="color: #ba2121;">*</span>');
        
        // Track if we're setting up the form initially
        var initialSetup = true;
        
        // Function to validate the form
        function validateAppointmentForm() {
            var isValid = true;
            
            // Check if at least one service is selected
            var hasService = false;
            $('.service-select').each(function() {
                if ($(this).val()) {
                    hasService = true;
                    return false; // Break the loop
                }
            });
            
            if (!hasService) {
                isValid = false;
                
                // Add a warning if no services
                if (!$('.service-warning').length) {
                    $('div.inline-group h2:contains("Appointment Services")').after(
                        '<div class="service-warning" style="margin: 10px 0; color: #ba2121; padding: 10px; background-color: #f8d7da; border-radius: 4px;">'+
                        '<strong>Error:</strong> At least one service is required for this appointment.</div>'
                    );
                }
                
                // Highlight the service row
                $('.service-select').each(function() {
                    if (!$(this).val()) {
                        $(this).closest('tr').find('td').first().css('background-color', '#f8d7da');
                    }
                });
            } else {
                // Remove warning if services are selected
                $('.service-warning').remove();
                $('.service-select').closest('tr').find('td').first().css('background-color', '');
            }
            
            return isValid;
        }
        
        // Function to get service details directly if AJAX fails
        function getDefaultServiceValues(serviceId, row) {
            // Set some default values to ensure form submissions work
            // These will be overridden by the backend if needed
            var defaultPrice = 0.01;
            var defaultDuration = 30;
            
            // Set these as fallback values
            row.find('input[name$="-base_price"]').val(defaultPrice);
            row.find('input[name$="-duration"]').val(defaultDuration);
            
            // Make sure the hidden service ID field is properly set
            row.find('input[name$="-service"]').val(serviceId);
            
            // Show warning to user that defaults are being used
            row.find('td:first').append(
                '<div class="warninglist" style="display:none; color: #856404; background-color: #fff3cd; font-size: 11px; margin-top: 5px; padding: 5px;">'+
                'Using default values. The server will update with correct values on save.</div>'
            );
            row.find('.warninglist').slideDown().delay(3000).slideUp();
        }
        
        // Handle service selection to update price and duration
        $(document).on('change', '.service-select', function() {
            var row = $(this).closest('tr');
            var serviceId = $(this).val();
            var employeeId = $('#id_employee').val();
            
            // Remove the highlight when a service is selected
            if (serviceId) {
                row.find('td').first().css('background-color', '');
                $('.service-warning').remove();
            }
            
            if (serviceId && employeeId) {
                // Show loading indicator
                row.find('input[name$="-base_price"]').css('background-color', '#f8f8f8');
                row.find('input[name$="-duration"]').css('background-color', '#f8f8f8');
                
                // Ensure the service_id is set in the hidden field
                var hiddenServiceField = row.find('input[name$="-service"]');
                if (hiddenServiceField.length && !hiddenServiceField.val()) {
                    hiddenServiceField.val(serviceId);
                }
                
                $.ajax({
                    url: '/api/v1/employees/' + employeeId + '/services/',
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        // Find the selected service in the response
                        var service = data.find(function(s) {
                            return s.service_id == serviceId;
                        });
                        
                        if (service) {
                            // Update price and duration fields
                            row.find('input[name$="-base_price"]').val(service.price).css('background-color', '#eaffea');
                            row.find('input[name$="-duration"]').val(service.duration).css('background-color', '#eaffea');
                            
                            // Make sure the hidden service ID field is properly set
                            row.find('input[name$="-service"]').val(serviceId);
                            
                            // Fade back to normal after a moment
                            setTimeout(function() {
                                row.find('input[name$="-base_price"]').css('background-color', '');
                                row.find('input[name$="-duration"]').css('background-color', '');
                            }, 1000);
                            
                            // Run validation check
                            validateAppointmentForm();
                        } else {
                            // Service not found in response, use default values
                            console.warn("Service not found in employee services, using defaults");
                            getDefaultServiceValues(serviceId, row);
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("Error loading service details: " + error);
                        row.find('input[name$="-base_price"]').css('background-color', '');
                        row.find('input[name$="-duration"]').css('background-color', '');
                        
                        // Set default values to ensure form submission works
                        getDefaultServiceValues(serviceId, row);
                        
                        // Show an error message
                        row.find('td:first').append(
                            '<div class="errorlist" style="display:none; color: #ba2121; font-size: 11px; margin-top: 5px;">'+
                            'Could not load service details. Using default values.</div>'
                        );
                        row.find('.errorlist').slideDown().delay(3000).slideUp();
                    }
                });
            } else if (!employeeId) {
                // Alert user to select an employee first
                if (!$('.service-hint').length) {
                    $('.inline-group:contains("Appointment Services")').prepend(
                        '<div class="service-hint" style="margin: 10px 0; color: #ba2121; padding: 10px; background-color: #fff8e5; border-radius: 4px;">'+
                        '<strong>Note:</strong> Please select an employee first to load available services</div>'
                    );
                }
                
                // Reset the service dropdown
                $(this).val('');
            }
        });

        // Add form validation on submit
        $('form').submit(function(e) {
            if (!validateAppointmentForm()) {
                e.preventDefault();
                // Scroll to the service section
                $('html, body').animate({
                    scrollTop: $('div.inline-group h2:contains("Appointment Services")').offset().top - 50
                }, 500);
                return false;
            }
        });

        // Handle quantity changes to recalculate totals
        $(document).on('change', '.quantity-input', function() {
            var row = $(this).closest('tr');
            var quantity = parseInt($(this).val()) || 1;
            var basePrice = parseFloat(row.find('input[name$="-base_price"]').val()) || 0;
            var duration = parseInt(row.find('input[name$="-duration"]').val()) || 0;
            
            // If we had a way to display the total, we would update it here
            console.log("Service total: $" + (basePrice * quantity).toFixed(2));
            console.log("Duration total: " + (duration * quantity) + " minutes");
        });

        // Initialize services on page load for existing rows
        setTimeout(function() {
            $('.service-select').each(function() {
                if ($(this).val()) {
                    // Make sure the hidden input has the service ID
                    var row = $(this).closest('tr');
                    var serviceId = $(this).val();
                    var hiddenServiceField = row.find('input[name$="-service"]');
                    
                    if (hiddenServiceField.length) {
                        hiddenServiceField.val(serviceId);
                    }
                    
                    // Check if we need to set price and duration
                    var basePrice = parseFloat(row.find('input[name$="-base_price"]').val());
                    var duration = parseInt(row.find('input[name$="-duration"]').val());
                    
                    if (isNaN(basePrice) || basePrice <= 0 || isNaN(duration) || duration <= 0) {
                        // If price or duration is not set, trigger the change event to fetch them
                        $(this).trigger('change');
                    }
                }
            });
            
            // Validate the form on load
            validateAppointmentForm();
            
            // Set initialSetup to false after initialization
            initialSetup = false;
        }, 500); // Small delay to ensure DOM is ready
        
        // Add a hint about selecting an employee first
        if ($('.service-select').length && !$('#id_employee').val()) {
            $('.inline-group:contains("Appointment Services")').prepend(
                '<div class="service-hint" style="margin: 10px 0; color: #ba2121; padding: 10px; background-color: #fff8e5; border-radius: 4px;">'+
                '<strong>Note:</strong> Please select an employee first to load available services</div>'
            );
        }
        
        // When employee is changed, prompt user to select services
        $('#id_employee').change(function() {
            // Remove existing hints
            $('.service-hint').remove();
            
            // Add a new hint if there are no services
            if ($('.service-select:first').val() === '') {
                $('.inline-group:contains("Appointment Services")').prepend(
                    '<div class="service-hint" style="margin: 10px 0; color: #0c5460; padding: 10px; background-color: #d1ecf1; border-radius: 4px;">'+
                    '<strong>Ready:</strong> Now you can select services for this appointment</div>'
                );
            }
        });
    });
})(django.jQuery); 