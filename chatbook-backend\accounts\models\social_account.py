from django.db import models
from django.utils import timezone
import uuid
from typing import Optional
import re
from django.core.exceptions import ValidationError


class SocialAccountManager(models.Manager):
    """
    Custom manager for SocialAccount model to handle active accounts
    """
    def get_queryset(self):
        # Properly passing any arguments to super() implementation
        return super().get_queryset()
    
    def active(self):
        """Return only active social accounts"""
        return self.get_queryset().filter(is_active=True)
    
    def valid_tokens(self):
        """Return accounts with valid tokens"""
        return self.active().filter(token_expiry__gt=timezone.now())
    
    def for_provider(self, provider):
        """Get accounts for a specific provider"""
        return self.get_queryset().filter(provider=provider)
    
    def get_by_provider_id(self, provider, provider_id) -> Optional['SocialAccount']:
        """
        Get account by provider and provider_id with proper error handling
        Returns None if not found instead of raising exception
        """
        try:
            return self.get_queryset().get(provider=provider, provider_id=provider_id)
        except self.model.DoesNotExist:
            return None
    
    def get_by_email(self, email) -> list['SocialAccount']:
        """Get all social accounts matching a specific email"""
        return self.get_queryset().filter(provider_email=email)


class SocialAccount(models.Model):
    """
    Model for storing social authentication accounts
    """
    class ProviderChoices(models.TextChoices):
        GOOGLE = 'google', 'Google'
        FACEBOOK = 'facebook', 'Facebook'
        APPLE = 'apple', 'Apple'

    # Security-related constants
    TOKEN_REFRESH_THRESHOLD = 3600  # seconds (1 hour) before expiry to refresh token

    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user = models.ForeignKey('accounts.User', on_delete=models.CASCADE, related_name='social_auth_accounts')
    provider = models.CharField(max_length=50, choices=ProviderChoices.choices, db_index=True)  # Added index for provider field
    provider_id = models.CharField(max_length=255, db_index=True)  # Added index for provider_id field
    provider_email = models.EmailField(db_index=True)  # Added index for email lookups
    access_token = models.TextField(blank=True)
    refresh_token = models.TextField(blank=True)
    token_expiry = models.DateTimeField(null=True, blank=True, db_index=True)
    is_active = models.BooleanField(default=True, db_index=True)  # Added index for filtered queries
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    # Set the custom manager
    objects = SocialAccountManager()

    class Meta:
        verbose_name = 'Social Account'
        verbose_name_plural = 'Social Accounts'
        ordering = ['-created_at']
        constraints = [
            models.UniqueConstraint(
                fields=['provider', 'provider_id'],
                name='unique_provider_account'
            )
        ]
        # Adding index for common combined lookups
        indexes = [
            models.Index(fields=['provider', 'is_active'], name='provider_active_idx'),
            models.Index(fields=['user', 'provider'], name='user_provider_idx'),
        ]

    def __str__(self):
        return f"{self.provider} account for {self.user.email}"

    def clean(self):
        """Validate model data"""
        # Validate provider is one of the supported choices
        if self.provider not in [choice[0] for choice in self.ProviderChoices.choices]:
            raise ValidationError(f"Unsupported provider: {self.provider}")
            
        # Basic email validation
        if self.provider_email and not re.match(r'^[^@]+@[^@]+\.[^@]+$', self.provider_email):
            raise ValidationError("Invalid email format")
            
        # Ensure token_expiry is timezone-aware
        if self.token_expiry and timezone.is_naive(self.token_expiry):
            self.token_expiry = timezone.make_aware(self.token_expiry)

    def save(self, *args, **kwargs):
        """Override save to ensure data validation"""
        self.clean()
        super().save(*args, **kwargs)

    def is_token_valid(self):
        """Check if the token is still valid based on expiry time"""
        if not self.token_expiry:
            return False
        
        # Ensure comparison uses timezone-aware datetime
        now = timezone.now()
        expiry = self.token_expiry
        
        # Make token_expiry timezone-aware if it's naive
        if timezone.is_naive(expiry):
            expiry = timezone.make_aware(expiry)
            
        return now < expiry
    
    def needs_refresh(self):
        """Check if token needs to be refreshed soon (within threshold of expiry)"""
        if not self.token_expiry:
            return True
            
        # Ensure comparison uses timezone-aware datetime
        now = timezone.now()
        expiry = self.token_expiry
        
        # Make token_expiry timezone-aware if it's naive
        if timezone.is_naive(expiry):
            expiry = timezone.make_aware(expiry)
            
        return (expiry - now).total_seconds() < self.TOKEN_REFRESH_THRESHOLD
    
    def update_token(self, access_token, refresh_token=None, token_expiry=None):
        """Update token information with proper validation"""
        if not access_token:
            raise ValueError("Access token cannot be empty")
            
        self.access_token = access_token
        
        if refresh_token:
            self.refresh_token = refresh_token
            
        if token_expiry:
            # Ensure token_expiry is timezone-aware
            if isinstance(token_expiry, str):
                try:
                    # Handle ISO format strings
                    token_expiry = timezone.datetime.fromisoformat(token_expiry.replace('Z', '+00:00'))
                except (ValueError, TypeError):
                    # Try parsing other datetime formats if ISO fails
                    try:
                        from dateutil import parser
                        token_expiry = parser.parse(token_expiry)
                    except (ImportError, ValueError, TypeError):
                        token_expiry = None
            
            # Make token_expiry timezone-aware if it's naive
            if token_expiry and timezone.is_naive(token_expiry):
                token_expiry = timezone.make_aware(token_expiry)
                
            self.token_expiry = token_expiry
            
        # Use update_fields to optimize database write
        update_fields = ['access_token', 'updated_at']
        if refresh_token:
            update_fields.append('refresh_token')
        if token_expiry:
            update_fields.append('token_expiry')
            
        self.save(update_fields=update_fields) 