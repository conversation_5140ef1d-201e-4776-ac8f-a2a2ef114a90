import { useState, useEffect } from 'react'
import { generateSampleRows, calculateFileStats, validateRowData, normalizeRowData } from '../utils/fileUtils'

function ImportPreview({ file, data, onMappingComplete }) {
  const [fieldMappings, setFieldMappings] = useState({})
  const [validationResults, setValidationResults] = useState([])
  const [fileStats, setFileStats] = useState(null)
  const [isValidating, setIsValidating] = useState(false)
  const [showAdvanced, setShowAdvanced] = useState(false)

  // Available customer fields for mapping
  const customerFields = [
    // Required Basic Information
    { key: 'firstName', label: 'First Name', required: true, description: 'Customer first name', category: 'Basic Info' },
    { key: 'lastName', label: 'Last Name', required: true, description: 'Customer last name', category: 'Basic Info' },
    { key: 'email', label: 'Email', required: true, description: 'Primary email address', category: 'Basic Info' },
    
    // Personal Details
    { key: 'birthdate', label: 'Birthdate', required: false, description: 'Date of birth', category: 'Personal' },
    { key: 'gender', label: 'Gender', required: false, description: 'Customer gender', category: 'Personal' },
    
    // Contact Information
    { key: 'mobile', label: 'Mobile', required: false, description: 'Mobile phone number', category: 'Contact' },
    { key: 'day', label: 'Day Phone', required: false, description: 'Daytime phone number', category: 'Contact' },
    { key: 'night', label: 'Night Phone', required: false, description: 'Evening phone number', category: 'Contact' },
    
    // Address Information
    { key: 'address', label: 'Address', required: false, description: 'Street address', category: 'Address' },
    { key: 'aptSuite', label: 'Apt/Suite', required: false, description: 'Apartment or suite number', category: 'Address' },
    { key: 'city', label: 'City', required: false, description: 'City name', category: 'Address' },
    { key: 'state', label: 'State', required: false, description: 'State or province', category: 'Address' },
    { key: 'zip', label: 'Zip', required: false, description: 'ZIP or postal code', category: 'Address' },
    
    // Business Information
    { key: 'customerSince', label: 'Customer Since', required: false, description: 'Date customer started', category: 'Business' },
    { key: 'lastVisited', label: 'Last Visited', required: false, description: 'Last visit date', category: 'Business' },
    { key: 'membership', label: 'Membership', required: false, description: 'Membership type or level', category: 'Business' },
    { key: 'referedBy', label: 'Referred By', required: false, description: 'Who referred this customer', category: 'Business' },
    { key: 'onlineBooking', label: 'Online Booking', required: false, description: 'Online booking preference', category: 'Business' },
    
    // Payment Information
    { key: 'creditCard', label: 'Credit Card', required: false, description: 'Credit card on file', category: 'Payment' },
    { key: 'bank', label: 'Bank', required: false, description: 'Bank information', category: 'Payment' },
    { key: 'amountPaid', label: 'Amount Paid', required: false, description: 'Total amount paid', category: 'Payment' },
    
    // Activity & Analytics
    { key: 'appointmentsBooked', label: 'Appointments Booked', required: false, description: 'Total appointments booked', category: 'Activity' },
    { key: 'classesBooked', label: 'Classes Booked', required: false, description: 'Total classes booked', category: 'Activity' },
    { key: 'checkIns', label: 'Check-Ins', required: false, description: 'Total check-ins', category: 'Activity' },
    { key: 'pointsEarned', label: 'Points Earned', required: false, description: 'Loyalty points earned', category: 'Activity' },
    { key: 'noShowsCancellations', label: 'No Shows/Cancellations', required: false, description: 'Total no-shows and cancellations', category: 'Activity' },
    { key: 'employeeSeen', label: 'Employee Seen', required: false, description: 'Primary employee or therapist', category: 'Activity' },
    
    // Other
    { key: 'tags', label: 'Tags', required: false, description: 'Customer tags or labels', category: 'Other' }
  ]

  // Initialize with smart mapping suggestions
  useEffect(() => {
    if (data.headers) {
      const smartMappings = {}
      
      data.headers.forEach(header => {
        const lowerHeader = header.toLowerCase().replace(/[^a-z0-9]/g, '') // Remove special chars for matching
        
        // Smart mapping based on your specific field names
        if (lowerHeader.includes('firstname') || lowerHeader === 'first') {
          smartMappings.firstName = header
        } else if (lowerHeader.includes('lastname') || lowerHeader === 'last') {
          smartMappings.lastName = header
        } else if (lowerHeader.includes('email') || lowerHeader.includes('mail')) {
          smartMappings.email = header
        } else if (lowerHeader.includes('birthdate') || lowerHeader.includes('birth') || lowerHeader.includes('dob')) {
          smartMappings.birthdate = header
        } else if (lowerHeader.includes('gender') || lowerHeader.includes('sex')) {
          smartMappings.gender = header
        } else if (lowerHeader.includes('mobile') || lowerHeader.includes('cell')) {
          smartMappings.mobile = header
        } else if (lowerHeader.includes('day') && lowerHeader.includes('phone')) {
          smartMappings.day = header
        } else if (lowerHeader.includes('night') && lowerHeader.includes('phone')) {
          smartMappings.night = header
        } else if (lowerHeader.includes('address') || lowerHeader.includes('street')) {
          smartMappings.address = header
        } else if (lowerHeader.includes('apt') || lowerHeader.includes('suite') || lowerHeader.includes('unit')) {
          smartMappings.aptSuite = header
        } else if (lowerHeader.includes('city')) {
          smartMappings.city = header
        } else if (lowerHeader.includes('state') || lowerHeader.includes('province')) {
          smartMappings.state = header
        } else if (lowerHeader.includes('zip') || lowerHeader.includes('postal')) {
          smartMappings.zip = header
        } else if (lowerHeader.includes('customersince') || lowerHeader.includes('since')) {
          smartMappings.customerSince = header
        } else if (lowerHeader.includes('lastvisited') || lowerHeader.includes('lastvisit')) {
          smartMappings.lastVisited = header
        } else if (lowerHeader.includes('membership') || lowerHeader.includes('member')) {
          smartMappings.membership = header
        } else if (lowerHeader.includes('refered') || lowerHeader.includes('referred') || lowerHeader.includes('referral')) {
          smartMappings.referedBy = header
        } else if (lowerHeader.includes('onlinebooking') || lowerHeader.includes('booking')) {
          smartMappings.onlineBooking = header
        } else if (lowerHeader.includes('creditcard') || lowerHeader.includes('card')) {
          smartMappings.creditCard = header
        } else if (lowerHeader.includes('bank')) {
          smartMappings.bank = header
        } else if (lowerHeader.includes('amountpaid') || lowerHeader.includes('paid') || lowerHeader.includes('amount')) {
          smartMappings.amountPaid = header
        } else if (lowerHeader.includes('appointmentsbooked') || lowerHeader.includes('appointments')) {
          smartMappings.appointmentsBooked = header
        } else if (lowerHeader.includes('classesbooked') || lowerHeader.includes('classes')) {
          smartMappings.classesBooked = header
        } else if (lowerHeader.includes('checkins') || lowerHeader.includes('checkin')) {
          smartMappings.checkIns = header
        } else if (lowerHeader.includes('pointsearned') || lowerHeader.includes('points')) {
          smartMappings.pointsEarned = header
        } else if (lowerHeader.includes('noshows') || lowerHeader.includes('cancellations')) {
          smartMappings.noShowsCancellations = header
        } else if (lowerHeader.includes('employeeseen') || lowerHeader.includes('employee')) {
          smartMappings.employeeSeen = header
        } else if (lowerHeader.includes('tags') || lowerHeader.includes('tag')) {
          smartMappings.tags = header
        }
      })
      
      setFieldMappings(smartMappings)
    }
  }, [data.headers])

  // Calculate file statistics
  useEffect(() => {
    if (data.data) {
      setFileStats(calculateFileStats(data.data))
    }
  }, [data.data])

  const handleMappingChange = (customerField, sourceField) => {
    setFieldMappings(prev => ({
      ...prev,
      [customerField]: sourceField || ''
    }))
  }

  const validateAndProceed = async () => {
    setIsValidating(true)
    
    // Check if required fields are mapped
    const requiredFields = customerFields.filter(field => field.required)
    const mappedRequiredFields = requiredFields.filter(field => fieldMappings[field.key]?.trim())
    
    if (mappedRequiredFields.length < requiredFields.length) {
      alert('Please map all required fields (First Name, Last Name, and Email)')
      setIsValidating(false)
      return
    }

    // Validate and normalize all records
    const validatedRecords = []
    const validationErrors = []
    
    data.data.forEach((row, index) => {
      const validation = validateRowData(row, fieldMappings, {
        firstName: { required: true },
        lastName: { required: true },
        email: { required: true }
      })
      
      const normalizedData = normalizeRowData(row, fieldMappings)
      
      validatedRecords.push({
        ...normalizedData,
        rowIndex: index + 1,
        validation
      })
      
      if (!validation.isValid) {
        validationErrors.push({
          row: index + 1,
          errors: validation.errors,
          warnings: validation.warnings
        })
      }
    })

    setValidationResults(validationErrors)
    setIsValidating(false)
    
    // Proceed to import
    onMappingComplete(fieldMappings, validatedRecords)
  }

  const sampleRows = generateSampleRows(data.data, 3)

  return (
    <div className="space-y-6">
      {/* File Information */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
          <div>
            <span className="text-gray-500">File:</span>
            <div className="font-medium">{file.name}</div>
          </div>
          <div>
            <span className="text-gray-500">Total Rows:</span>
            <div className="font-medium">{data.totalRows}</div>
          </div>
          <div>
            <span className="text-gray-500">Columns:</span>
            <div className="font-medium">{data.headers.length}</div>
          </div>
          <div>
            <span className="text-gray-500">File Size:</span>
            <div className="font-medium">{(file.size / 1024).toFixed(1)} KB</div>
          </div>
        </div>
      </div>

      {/* File Statistics */}
      {fileStats && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-900 mb-3">Data Quality Overview</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <div className="text-lg font-bold text-blue-600">{fileStats.totalRows}</div>
              <div className="text-blue-800">Total Records</div>
            </div>
            <div className="text-center">
              <div className={`text-lg font-bold ${fileStats.emptyRows > 0 ? 'text-yellow-600' : 'text-green-600'}`}>
                {fileStats.emptyRows}
              </div>
              <div className="text-blue-800">Empty Rows</div>
            </div>
            <div className="text-center">
              <div className={`text-lg font-bold ${fileStats.incompleteRows > 0 ? 'text-yellow-600' : 'text-green-600'}`}>
                {fileStats.incompleteRows}
              </div>
              <div className="text-blue-800">Incomplete Rows</div>
            </div>
            <div className="text-center">
              <div className={`text-lg font-bold ${fileStats.duplicateEmails > 0 ? 'text-red-600' : 'text-green-600'}`}>
                {fileStats.duplicateEmails}
              </div>
              <div className="text-blue-800">Duplicate Emails</div>
            </div>
          </div>
        </div>
      )}

      {/* Field Mapping Section */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-medium text-gray-900">Field Mapping</h3>
          <button
            onClick={() => setShowAdvanced(!showAdvanced)}
            className="text-sm text-blue-600 hover:text-blue-800"
          >
            {showAdvanced ? 'Hide' : 'Show'} Advanced Options
          </button>
        </div>
        
        <div className="space-y-6">
          {/* Group fields by category */}
          {['Basic Info', 'Personal', 'Contact', 'Address', 'Business', 'Payment', 'Activity', 'Other'].map(category => {
            const categoryFields = customerFields.filter(field => field.category === category)
            if (categoryFields.length === 0) return null
            
            return (
              <div key={category} className="space-y-3">
                <h4 className="text-sm font-medium text-gray-700 border-b border-gray-200 pb-2">
                  {category}
                </h4>
                <div className="space-y-3">
                  {categoryFields.map(field => (
                    <div key={field.key} className="flex items-center space-x-4 p-3 border border-gray-200 rounded-lg bg-gray-50">
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2">
                          <label className="text-sm font-medium text-gray-900">
                            {field.label}
                          </label>
                          {field.required && (
                            <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">Required</span>
                          )}
                        </div>
                        <p className="text-xs text-gray-500 mt-1">{field.description}</p>
                      </div>
                      
                      <div className="flex-1">
                        <select
                          value={fieldMappings[field.key] || ''}
                          onChange={(e) => handleMappingChange(field.key, e.target.value)}
                          className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-sm bg-white"
                        >
                          <option value="">Select column...</option>
                          {data.headers.map(header => (
                            <option key={header} value={header}>
                              {header}
                            </option>
                          ))}
                        </select>
                      </div>
                      
                      {fieldMappings[field.key] && (
                        <div className="flex-1 text-xs text-gray-600">
                          <span className="bg-green-100 text-green-800 px-2 py-1 rounded">
                            ✓ {fieldMappings[field.key]}
                          </span>
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </div>
            )
          })}
        </div>
      </div>

      {/* Data Preview */}
      <div>
        <h3 className="text-lg font-medium text-gray-900 mb-4">Data Preview</h3>
        <div className="overflow-x-auto border border-gray-200 rounded-lg">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Row
                </th>
                {data.headers.map(header => (
                  <th key={header} className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    <div className="flex flex-col">
                      <span>{header}</span>
                      {Object.entries(fieldMappings).find(([_, sourceField]) => sourceField === header) && (
                        <span className="text-blue-600 font-normal normal-case text-xs mt-1">
                          → {customerFields.find(f => f.key === Object.entries(fieldMappings).find(([_, sourceField]) => sourceField === header)[0])?.label}
                        </span>
                      )}
                    </div>
                  </th>
                ))}
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {sampleRows.map((row, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-4 py-3 text-sm text-gray-500">
                    {index + 1}
                  </td>
                  {data.headers.map(header => (
                    <td key={header} className="px-4 py-3 text-sm text-gray-900">
                      <div className="max-w-xs truncate">
                        {row[header] || '-'}
                      </div>
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
        
        {data.totalRows > 3 && (
          <p className="text-sm text-gray-500 mt-2">
            Showing first 3 rows of {data.totalRows} total rows
          </p>
        )}
      </div>

      {/* Validation Results */}
      {validationResults.length > 0 && (
        <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-yellow-900 mb-3">Validation Issues Found</h4>
          <div className="space-y-2 max-h-40 overflow-y-auto">
            {validationResults.slice(0, 10).map((result, index) => (
              <div key={index} className="text-sm">
                <span className="font-medium">Row {result.row}:</span>
                {result.errors.map((error, i) => (
                  <span key={i} className="text-red-700 ml-2">• {error}</span>
                ))}
                {result.warnings.map((warning, i) => (
                  <span key={i} className="text-yellow-700 ml-2">• {warning}</span>
                ))}
              </div>
            ))}
          </div>
          {validationResults.length > 10 && (
            <p className="text-xs text-yellow-700 mt-2">
              And {validationResults.length - 10} more issues...
            </p>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row justify-end gap-3 pt-6 border-t border-gray-200">
        <button
          onClick={() => window.location.reload()}
          className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
        >
          Cancel
        </button>
        <button
          onClick={validateAndProceed}
          disabled={Boolean(isValidating || !fieldMappings.firstName?.trim() || !fieldMappings.lastName?.trim() || !fieldMappings.email?.trim())}
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium flex items-center"
        >
          {isValidating ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Validating...
            </>
          ) : (
            'Proceed to Import'
          )}
        </button>
      </div>
    </div>
  )
}

export default ImportPreview 