from django.contrib import admin
from django.contrib.auth.admin import UserAdmin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from django.contrib.admin import AdminSite
from .models.user import User
from .models.user_settings import UserSettings
from .models.role import Role
from .models.recovery_token import RecoveryToken
from .models.user_device import UserDevice
from .models.social_account import SocialAccount

# User Settings Management
class UserSettingsAdmin(admin.ModelAdmin):
    list_display = ('user', 'email_notifications', 'sms_notifications', 'push_notifications')
    search_fields = ('user__email', 'user__first_name', 'user__last_name')
    list_filter = ('email_notifications', 'sms_notifications', 'push_notifications')

# User Management
class CustomUserAdmin(UserAdmin):
    list_display = ('email', 'phone_number', 'first_name', 'last_name', 'is_staff', 'is_active', 'user_type', 'created_at')
    list_filter = ('is_staff', 'is_active', 'is_superuser', 'roles', 'created_at', 'mfa_enabled')
    search_fields = ('email', 'phone_number', 'first_name', 'last_name')
    ordering = ('email',)
    date_hierarchy = 'created_at'
    
    fieldsets = (
        (None, {'fields': ('email', 'phone_number', 'password')}),
        (_('Personal info'), {'fields': ('first_name', 'last_name')}),
        (_('Roles'), {'fields': ('roles',)}),
        (_('MFA Settings'), {'fields': ('mfa_enabled', 'preferred_mfa_method')}),
        (_('Permissions'), {'fields': ('is_active', 'is_staff', 'is_superuser',
                                       'groups', 'user_permissions')}),
        (_('Important dates'), {'fields': ('last_login', 'created_at')}),
        (_('Security info'), {'fields': ('last_login_ip', 'last_login_device'),
                             'classes': ('collapse',)}),
    )
    
    add_fieldsets = (
        (None, {
            'classes': ('wide',),
            'fields': ('email', 'phone_number', 'password1', 'password2', 'first_name', 'last_name'),
        }),
        (_('Permissions'), {
            'classes': ('collapse',),
            'fields': ('is_staff', 'is_superuser'),
        }),
    )
    
    readonly_fields = ('created_at', 'last_login', 'last_login_ip', 'last_login_device')
    filter_horizontal = ('roles', 'groups', 'user_permissions')
    
    def get_queryset(self, request):
        """Optimize queryset for admin list view by prefetching related objects"""
        return super().get_queryset(request).prefetch_related('roles')
    
    def user_type(self, obj):
        return obj.user_type

    user_type.short_description = 'User Type'
    user_type.admin_order_field = 'roles__name'  # Enable sorting

# Role Admin
class RoleAdmin(admin.ModelAdmin):
    list_display = ('name', 'description', 'is_active')
    list_filter = ('is_active',)
    search_fields = ('name', 'description')
    filter_horizontal = ('permissions',)

# Recovery Token Admin
class RecoveryTokenAdmin(admin.ModelAdmin):
    list_display = ('user', 'created_at', 'expires_at', 'used', 'used_at')
    list_filter = ('used', 'created_at')
    search_fields = ('user__email', 'token_hash')
    readonly_fields = ('token_hash', 'created_at', 'expires_at', 'used_at', 'ip_address', 'user_agent')

# User Device Admin
class UserDeviceAdmin(admin.ModelAdmin):
    list_display = ('user', 'device_name', 'device_type', 'is_active', 'last_used')
    list_filter = ('device_type', 'is_active')
    search_fields = ('user__email', 'device_name', 'device_id')
    readonly_fields = ('created_at', 'last_used')

# Social Account Admin
class SocialAccountAdmin(admin.ModelAdmin):
    list_display = ('user', 'provider', 'is_active', 'created_at')
    list_filter = ('provider', 'is_active')
    search_fields = ('user__email', 'provider_id', 'provider_email')
    readonly_fields = ('created_at', 'updated_at')

# Register models with the admin site
admin.site.register(User, CustomUserAdmin)
admin.site.register(UserSettings, UserSettingsAdmin)
admin.site.register(Role, RoleAdmin)
admin.site.register(RecoveryToken, RecoveryTokenAdmin)
admin.site.register(UserDevice, UserDeviceAdmin)
admin.site.register(SocialAccount, SocialAccountAdmin)
