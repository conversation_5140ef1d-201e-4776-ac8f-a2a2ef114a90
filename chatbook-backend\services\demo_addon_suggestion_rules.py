#!/usr/bin/env python
"""
Demo script showing how to use the AddonSuggestionRule model.

This script demonstrates creating various types of addon suggestion rules
and how they would work in practice.

Run this script with: python manage.py shell < services/demo_addon_suggestion_rules.py
"""

from datetime import timedelta
from django.contrib.auth import get_user_model
from business.models import Business
from services.models import AddOn, AddonSuggestionRule

User = get_user_model()

def create_demo_data():
    """Create demo business, user, and addons for testing"""
    print("Creating demo data...")
    
    # Create a user
    user, created = User.objects.get_or_create(
        email="<EMAIL>",
        defaults={
            'phone_number': '+***********',
            'first_name': 'Demo',
            'last_name': 'Owner',
        }
    )
    if created:
        user.set_password('demo123')
        user.save()
        print(f"Created user: {user}")
    else:
        print(f"Using existing user: {user}")
    
    # Create a business
    business, created = Business.objects.get_or_create(
        name="Demo Hair Salon",
        defaults={
            'owner': user,
            'email': '<EMAIL>',
            'phone': '555-0123'
        }
    )
    if created:
        print(f"Created business: {business}")
    else:
        print(f"Using existing business: {business}")
    
    # Create some addons
    addons = [
        {
            'name': 'Deep Conditioning Treatment',
            'base_price': 35.00,
            'base_duration': timedelta(minutes=30)
        },
        {
            'name': 'Hair Gloss',
            'base_price': 25.00,
            'base_duration': timedelta(minutes=20)
        },
        {
            'name': 'Scalp Massage',
            'base_price': 15.00,
            'base_duration': timedelta(minutes=15)
        },
        {
            'name': 'Hair Mask',
            'base_price': 40.00,
            'base_duration': timedelta(minutes=25)
        }
    ]
    
    created_addons = []
    for addon_data in addons:
        addon, created = AddOn.objects.get_or_create(
            business=business,
            name=addon_data['name'],
            defaults={
                'base_price': addon_data['base_price'],
                'base_duration': addon_data['base_duration']
            }
        )
        created_addons.append(addon)
        if created:
            print(f"Created addon: {addon}")
        else:
            print(f"Using existing addon: {addon}")
    
    return business, created_addons

def create_suggestion_rules(business, addons):
    """Create various types of suggestion rules"""
    print("\nCreating suggestion rules...")
    
    deep_conditioning, hair_gloss, scalp_massage, hair_mask = addons
    
    # Rule 1: Suggest deep conditioning to new clients only
    rule1, created = AddonSuggestionRule.objects.get_or_create(
        business=business,
        suggested_addon=deep_conditioning,
        new_client_only=True,
        defaults={}
    )
    if created:
        print(f"Created rule: {rule1}")
    else:
        print(f"Using existing rule: {rule1}")
    
    # Rule 2: Suggest hair gloss 30-60 days after deep conditioning
    rule2, created = AddonSuggestionRule.objects.get_or_create(
        business=business,
        suggested_addon=hair_gloss,
        last_addon=deep_conditioning,
        defaults={
            'min_days_since': 30,
            'max_days_since': 60
        }
    )
    if created:
        print(f"Created rule: {rule2}")
    else:
        print(f"Using existing rule: {rule2}")
    
    # Rule 3: Suggest scalp massage 14+ days after any hair mask
    rule3, created = AddonSuggestionRule.objects.get_or_create(
        business=business,
        suggested_addon=scalp_massage,
        last_addon=hair_mask,
        defaults={
            'min_days_since': 14
        }
    )
    if created:
        print(f"Created rule: {rule3}")
    else:
        print(f"Using existing rule: {rule3}")
    
    # Rule 4: Suggest hair mask within 90 days of hair gloss
    rule4, created = AddonSuggestionRule.objects.get_or_create(
        business=business,
        suggested_addon=hair_mask,
        last_addon=hair_gloss,
        defaults={
            'max_days_since': 90
        }
    )
    if created:
        print(f"Created rule: {rule4}")
    else:
        print(f"Using existing rule: {rule4}")
    
    return [rule1, rule2, rule3, rule4]

def demonstrate_rules(rules):
    """Show how the rules would work"""
    print("\n" + "="*60)
    print("ADDON SUGGESTION RULES DEMONSTRATION")
    print("="*60)
    
    for i, rule in enumerate(rules, 1):
        print(f"\nRule {i}: {rule}")
        print(f"  Business: {rule.business.name}")
        print(f"  Suggested Add-on: {rule.suggested_addon.name} (${rule.suggested_addon.base_price})")
        
        if rule.new_client_only:
            print("  Trigger: New clients only")
        
        if rule.last_addon:
            print(f"  Trigger: After customer had '{rule.last_addon.name}'")
            
            if rule.min_days_since and rule.max_days_since:
                print(f"  Time Range: {rule.min_days_since}-{rule.max_days_since-1} days ago")
            elif rule.min_days_since:
                print(f"  Time Range: {rule.min_days_since}+ days ago")
            elif rule.max_days_since:
                print(f"  Time Range: Within {rule.max_days_since} days")
            else:
                print("  Time Range: Any time")

def main():
    """Main demo function"""
    print("AddonSuggestionRule Model Demonstration")
    print("="*50)
    
    # Create demo data
    business, addons = create_demo_data()
    
    # Create suggestion rules
    rules = create_suggestion_rules(business, addons)
    
    # Demonstrate the rules
    demonstrate_rules(rules)
    
    print("\n" + "="*60)
    print("SUMMARY")
    print("="*60)
    print(f"Created {len(addons)} add-ons and {len(rules)} suggestion rules")
    print("You can now view and manage these rules in the Django admin at:")
    print("http://localhost:8000/admin/services/addonsuggestionsrule/")
    print("\nThe admin interface allows you to:")
    print("- Create new suggestion rules")
    print("- Edit existing rules")
    print("- Filter rules by business, criteria, etc.")
    print("- View rule details in a user-friendly format")

if __name__ == "__main__":
    main()
