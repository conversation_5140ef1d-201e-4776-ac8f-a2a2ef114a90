from django.contrib import admin
from django.utils.translation import gettext_lazy as _
from django.utils.html import format_html
from .models import CustomerProfile, CustomerTag, CustomerFile, SignedForm

# Customer Management
class CustomerFileInline(admin.TabularInline):
    model = CustomerFile
    extra = 1
    fields = ('title', 'file', 'description', 'uploaded_by')
    verbose_name = "Customer Document"
    verbose_name_plural = "Customer Documents"

class SignedFormInline(admin.TabularInline):
    model = SignedForm
    extra = 1
    fields = ('title', 'form_type', 'content', 'signature', 'ip_address')
    readonly_fields = ('signed_at',)
    verbose_name = "Signed Form"
    verbose_name_plural = "Signed Forms"

@admin.register(CustomerProfile)
class CustomerProfileAdmin(admin.ModelAdmin):
    list_display = ('get_customer_name', 'get_email', 'get_phone', 'card_on_file', 'created_at')
    search_fields = ('user__email', 'user__phone_number', 'user__first_name', 'user__last_name')
    list_filter = ('card_on_file',)
    inlines = [CustomerFileInline, SignedFormInline]
    readonly_fields = ('get_customer_info',)
    
    def get_customer_name(self, obj):
        return f"{obj.user.first_name} {obj.user.last_name}"
    get_customer_name.short_description = 'Customer Name'
    
    def get_email(self, obj):
        return obj.user.email
    get_email.short_description = 'Email'
    
    def get_phone(self, obj):
        return obj.user.phone_number
    get_phone.short_description = 'Phone'
    
    def get_customer_info(self, obj):
        """Display formatted customer contact information."""
        if not obj or not obj.user:
            return "-"
            
        html = f"""
        <div style="padding: 10px; margin-bottom: 20px;">
            <h3 style="margin-top: 0; color: #444;">{obj.user.first_name} {obj.user.last_name}</h3>
            <div style="display: flex; margin-top: 10px;">
                <div style="margin-right: 40px;">
                    <p style="margin: 5px 0;"><strong>Email:</strong></p>
                    <p style="margin: 5px 0;"><a href="mailto:{obj.user.email}">{obj.user.email}</a></p>
                </div>
                <div>
                    <p style="margin: 5px 0;"><strong>Phone:</strong></p>
                    <p style="margin: 5px 0;"><a href="tel:{obj.user.phone_number}">{obj.user.phone_number}</a></p>
                </div>
            </div>
        </div>
        """
        return format_html(html)
    get_customer_info.short_description = ""
    
    fieldsets = (
        (_('Customer Information'), {
            'fields': ('get_customer_info', 'user'),
            'classes': ('wide',),
        }),
        (_('Payment Information'), {
            'fields': ('card_on_file',),
        }),
    )

@admin.register(CustomerTag)
class CustomerTagAdmin(admin.ModelAdmin):
    list_display = ('name', 'color', 'customer_count')
    search_fields = ('name',)
    
    def customer_count(self, obj):
        return obj.business_customers.count()
    customer_count.short_description = 'Customers'

@admin.register(CustomerFile)
class CustomerFileAdmin(admin.ModelAdmin):
    list_display = ('title', 'customer', 'uploaded_by', 'uploaded_at')
    search_fields = ('title', 'customer__user__email', 'customer__user__first_name', 'customer__user__last_name')
    list_filter = ('uploaded_at', 'uploaded_by')

@admin.register(SignedForm)
class SignedFormAdmin(admin.ModelAdmin):
    list_display = ('title', 'customer', 'signed_at', 'ip_address')
    search_fields = ('title', 'customer__user__email', 'customer__user__first_name', 'customer__user__last_name')
    list_filter = ('signed_at',)
    date_hierarchy = 'signed_at'
    readonly_fields = ('signed_at',)
