// Save and restore sidebar scroll position
document.addEventListener('DOMContentLoaded', function() {
    const sidebar = document.querySelector('.main-sidebar .sidebar');
    
    if (sidebar) {
        // Restore scroll position when page loads
        const savedPosition = localStorage.getItem('adminSidebarScrollPosition');
        if (savedPosition) {
            sidebar.scrollTop = parseInt(savedPosition);
        }
        
        // Save scroll position when scrolling the sidebar
        sidebar.addEventListener('scroll', function() {
            localStorage.setItem('adminSidebarScrollPosition', sidebar.scrollTop.toString());
        });
        
        // Save scroll position before page unloads (navigation)
        window.addEventListener('beforeunload', function() {
            localStorage.setItem('adminSidebarScrollPosition', sidebar.scrollTop.toString());
        });
        
        // Handle links in the sidebar
        const sidebarLinks = sidebar.querySelectorAll('a');
        sidebarLinks.forEach(link => {
            link.addEventListener('click', function() {
                localStorage.setItem('adminSidebarScrollPosition', sidebar.scrollTop.toString());
            });
        });
    }
}); 