from rest_framework import viewsets, permissions, status, filters
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from django.shortcuts import get_object_or_404
from datetime import datetime, timedelta
import logging

from services.models import Service, ServiceCategory, AddOn, ServiceAddOn, EmployeeService, StylistLevelService, EmployeeAddOn
from employees.models import Employee
from appointments.models import Appointment
from .serializers import (
    ServiceSerializer, ServiceCategorySerializer, ServiceDetailSerializer,
    EmployeeBasicSerializer, EmployeeWithServicesSerializer, EmployeeServiceSerializer,
    AppointmentSerializer, StylistLevelServiceSerializer, EmployeeServiceWithDetailsSerializer,
    EmployeeAddOnSerializer
)
from ..employees.serializers import (
    EmployeeSimplifiedSerializer, EmployeeDetailSimplifiedSerializer
)
from rest_framework.permissions import IsAdminUser, IsAuthenticated, AllowAny
from api.permissions import PublicReadOnlyPrivateWrite
from django.db import connection

logger = logging.getLogger(__name__)

class ServiceCategoryViewSet(viewsets.ModelViewSet):
    """
    API endpoint for service categories.
    """
    queryset = ServiceCategory.objects.all()
    serializer_class = ServiceCategorySerializer
    permission_classes = [PublicReadOnlyPrivateWrite]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description']
    ordering_fields = ['name', 'order']
    ordering = ['order', 'name']
    pagination_class = PageNumberPagination  # Enable pagination for categories
    pagination_class.page_size = 10  # Set page size

    def get_queryset(self):
        queryset = super().get_queryset()
        if self.action == 'list':
            return queryset.filter(is_active=True)
        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return Response({
            'success': True,
            'message': f"Category '{instance.name}' was deleted successfully"
        })


class ServiceViewSet(viewsets.ModelViewSet):
    """
    API endpoint for salon services.
    
    Supports filtering via query parameters:
    - category: Filter services by category ID(s)
      Examples: 
        - Single: /api/v1/services/?category=5
        - Multiple (comma-separated): /api/v1/services/?category=5,6,7
        - Multiple (repeated params): /api/v1/services/?category=5&category=6
    
    - show_online: Filter services by their online visibility (true/false)
      Example: /api/v1/services/?show_online=true
    
    - Combined filters: /api/v1/services/?category=5,6&show_online=true
    
    Also supports ordering:
    - /api/v1/services/?ordering=name
    - /api/v1/services/?ordering=-base_price (descending price)
    
    And searching:
    - /api/v1/services/?search=haircut
    """
    queryset = Service.objects.all()
    serializer_class = ServiceDetailSerializer
    permission_classes = [PublicReadOnlyPrivateWrite]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name', 'description', 'category__name']
    ordering_fields = ['name', 'base_price', 'base_duration', 'display_order']
    ordering = ['category', 'display_order', 'name']
    pagination_class = PageNumberPagination  # Enable pagination for services
    pagination_class.page_size = 10  # Set page size

    def get_queryset(self):
        queryset = super().get_queryset().filter(is_active=True)
        
        # Handle category filtering - supporting both comma-separated and repeated params
        categories = []
        
        # Get comma-separated values
        category_param = self.request.query_params.get('category')
        if category_param:
            categories.extend([cat.strip() for cat in category_param.split(',') if cat.strip()])
        
        # Also check for multiple category parameters
        category_params = self.request.query_params.getlist('category')
        if len(category_params) > 1:  # More than one category parameter
            for param in category_params:
                if ',' in param:  # Handle comma-separated values in repeated params too
                    categories.extend([cat.strip() for cat in param.split(',') if cat.strip()])
                else:
                    categories.append(param.strip())
        
        # Apply category filter if we have categories
        if categories:
            queryset = queryset.filter(category_id__in=categories)
        
        # Filter by show_online if provided
        show_online = self.request.query_params.get('show_online')
        if show_online is not None:
            show_online_bool = show_online.lower() == 'true'
            queryset = queryset.filter(show_online=show_online_bool)
            
        return queryset

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        return Response(serializer.data, status=status.HTTP_201_CREATED)

    def partial_update(self, request, *args, **kwargs):
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        self.perform_update(serializer)
        return Response(serializer.data)

    def destroy(self, request, *args, **kwargs):
        instance = self.get_object()
        self.perform_destroy(instance)
        return Response({
            'success': True,
            'message': f"Service '{instance.name}' was deleted successfully"
        })

    @action(detail=True, methods=['get'])
    def employees(self, request, pk=None):
        """
        Return all employees who offer this service
        """
        service = self.get_object()
        employee_services = EmployeeService.objects.filter(
            service=service, 
            is_active=True,
            employee__is_active=True
        )
        serializer = EmployeeServiceSerializer(employee_services, many=True)
        return Response(serializer.data)


class EmployeeViewSet(viewsets.ReadOnlyModelViewSet):
    """
    API endpoint for employees.
    """
    queryset = Employee.objects.filter(is_active=True)
    permission_classes = [PublicReadOnlyPrivateWrite]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['user__first_name', 'user__last_name', 'user__email']
    ordering_fields = ['user__first_name', 'user__last_name']
    ordering = ['user__first_name', 'user__last_name']
    pagination_class = PageNumberPagination  # Enable pagination for employees
    pagination_class.page_size = 10  # Set page size

    def get_serializer_class(self):
        if self.action == 'retrieve':
            return EmployeeDetailSimplifiedSerializer
        elif self.action == 'services':
            return EmployeeWithServicesSerializer
        return EmployeeSimplifiedSerializer

    def get_queryset(self):
        """Filter employees based on query parameters"""
        logger.debug("EmployeeViewSet.get_queryset called")
        queryset = Employee.objects.filter(is_active=True)
        
        # Filter by business if provided
        business_id = self.request.query_params.get('business_id')
        if business_id:
            queryset = queryset.filter(business_id=business_id)
            
        return queryset

    def list(self, request, *args, **kwargs):
        """
        Override list method to format response according to requirements
        """
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            data = serializer.data
            return self.get_paginated_response(data)
        
        serializer = self.get_serializer(queryset, many=True)
        data = serializer.data
        
        # Sort employees by full_name for consistent ordering
        data = sorted(data, key=lambda x: x.get('full_name', ''))
        
        return Response({
            'count': len(data),
            'results': data
        })

    def _get_display_value(self, value, choices):
        """Helper method to get display value from choices"""
        for choice_value, choice_display in choices:
            if choice_value == value:
                return choice_display
        return value

    @action(detail=True, methods=['get'])
    def services(self, request, pk=None):
        """
        Return all services and add-ons an employee can offer based on their stylist level,
        with correct price/duration logic.
        
        This endpoint handles both:
        1. Services explicitly assigned to the employee (EmployeeService)
        2. Services available to the employee's stylist level (StylistLevelService)
        3. Add-ons explicitly assigned to the employee (EmployeeAddOn)
        4. Add-ons available to the employee through their stylist level
        
        Supports filtering via query parameters:
        - category: Filter services by category ID(s)
          Examples: 
            - Single: /api/v1/employees/1/services/?category=5
            - Multiple (comma-separated): /api/v1/employees/1/services/?category=5,6,7
        
        Results are sorted by category_display_order and then service_display_order.
        """
        employee = self.get_object()
        
        # Parse category filter
        categories = []
        category_param = self.request.query_params.get('category')
        if category_param:
            categories.extend([cat.strip() for cat in category_param.split(',') if cat.strip()])
        
        # First, get all employee-specific service customizations
        employee_services_query = EmployeeService.objects.filter(
            employee=employee,
            is_active=True
        ).select_related('service', 'service__category')
        
        # Apply category filter if specified
        if categories:
            employee_services_query = employee_services_query.filter(service__category_id__in=categories)
            
        employee_services = list(employee_services_query)
        
        # Get all services based on stylist level that aren't already covered by employee services
        employee_service_ids = [es.service_id for es in employee_services]
        
        # Only attempt to get stylist level services if employee has a stylist level
        stylist_level_services = []
        if employee.stylist_level:
            # Get services that this stylist level can offer
            level_services_query = Service.objects.filter(
                stylist_level_services__stylist_level=employee.stylist_level,
                stylist_level_services__is_active=True,
                stylist_level_services__is_offered=True,
                is_active=True
            ).exclude(id__in=employee_service_ids)
            
            # Apply category filter if specified
            if categories:
                level_services_query = level_services_query.filter(category_id__in=categories)
            
            # Create temporary EmployeeService objects for these services
            # (they will not be saved to the database)
            for service in level_services_query:
                temp_es = EmployeeService(
                    employee=employee,
                    service=service,
                    business=employee.business
                )
                stylist_level_services.append(temp_es)
        
        # Combine both lists of services
        all_services = employee_services + stylist_level_services
        
        # Serialize the combined services results
        service_serializer = EmployeeServiceWithDetailsSerializer(all_services, many=True)
        service_data = service_serializer.data
        
        # Sort by category_display_order and then by service_display_order
        service_data = sorted(service_data, key=lambda x: (x.get('category_display_order', 0), x.get('service_display_order', 0)))
        
        # Return only the services data for backward compatibility
        return Response(service_data)

    @action(detail=True, methods=['get'], url_path='service-addons')
    def service_addons(self, request, pk=None):
        """
        Return all add-ons an employee can offer based on their stylist level,
        with correct price/duration logic.
        
        This endpoint handles both:
        1. Add-ons explicitly assigned to the employee (EmployeeAddOn)
        2. Add-ons available to the employee through their stylist level
        
        Supports filtering via query parameters:
        - category: Filter add-ons by category ID(s)
          Examples: 
            - Single: /api/v1/employees/1/service-addons/?category=5
            - Multiple (comma-separated): /api/v1/employees/1/service-addons/?category=5,6,7
        
        Results are sorted by category_display_order and then addon_display_order.
        """
        employee = self.get_object()
        
        # Parse category filter
        categories = []
        category_param = self.request.query_params.get('category')
        if category_param:
            categories.extend([cat.strip() for cat in category_param.split(',') if cat.strip()])
        
        # Get employee add-ons
        employee_addons_query = EmployeeAddOn.objects.filter(
            employee=employee,
            is_active=True
        ).select_related('addon', 'addon__category')
        
        # Apply category filter if specified
        if categories:
            employee_addons_query = employee_addons_query.filter(addon__category_id__in=categories)
            
        employee_addons = list(employee_addons_query)
        
        # Get add-ons through stylist level
        employee_addon_ids = [ea.addon_id for ea in employee_addons]
        
        # Only attempt to get stylist level add-ons if employee has a stylist level
        stylist_level_addons = []
        if employee.stylist_level:
            # Get add-ons that this stylist level can offer
            level_addons_query = AddOn.objects.filter(
                stylist_level_addons__stylist_level=employee.stylist_level,
                stylist_level_addons__is_active=True,
                stylist_level_addons__is_offered=True,
                is_active=True
            ).exclude(id__in=employee_addon_ids)
            
            # Apply category filter if specified
            if categories:
                level_addons_query = level_addons_query.filter(category_id__in=categories)
            
            # Create temporary EmployeeAddOn objects for these add-ons
            # (they will not be saved to the database)
            for addon in level_addons_query:
                temp_ea = EmployeeAddOn(
                    employee=employee,
                    addon=addon,
                    business=employee.business
                )
                stylist_level_addons.append(temp_ea)
        
        # Combine both lists of add-ons
        all_addons = employee_addons + stylist_level_addons
        
        # Serialize the combined add-ons results
        addon_serializer = EmployeeAddOnSerializer(all_addons, many=True)
        addon_data = addon_serializer.data
        
        # Sort add-ons by category_display_order and then by addon_display_order
        addon_data = sorted(addon_data, key=lambda x: (
            x.get('category_display_order', 999) or 999,  # Handle None values with default 999
            x.get('addon_display_order', 0)
        ))
        
        return Response(addon_data)

    @action(detail=True, methods=['get'], url_path='services/(?P<service_id>[^/.]+)/addons')
    def service_specific_addons(self, request, pk=None, service_id=None):
        """
        Return all add-ons for a specific employee and service combination.
        This returns the intersection of:
        1. Add-ons associated with the specified service (via ServiceAddOn)
        2. Add-ons available to the specified employee 
        
        Example URL: /api/v1/employees/1/services/2/addons/
        
        Results are sorted by category_display_order and then addon_display_order.
        """
        employee = self.get_object()
        
        # Get the service or return 404
        service = get_object_or_404(Service, pk=service_id)
        
        # Get service add-ons
        service_addon_ids = ServiceAddOn.objects.filter(
            service=service,
            is_active=True
        ).values_list('addon_id', flat=True)
        
        # Get employee add-ons that match the service add-ons
        employee_addons_query = EmployeeAddOn.objects.filter(
            employee=employee,
            addon_id__in=service_addon_ids,
            is_active=True
        ).select_related('addon', 'addon__category')
        
        employee_addons = list(employee_addons_query)
        
        # Get stylist level add-ons that match the service add-ons
        employee_addon_ids = [ea.addon_id for ea in employee_addons]
        
        stylist_level_addons = []
        if employee.stylist_level:
            # Get add-ons that this stylist level can offer and that are service add-ons
            level_addons_query = AddOn.objects.filter(
                id__in=service_addon_ids,
                stylist_level_addons__stylist_level=employee.stylist_level,
                stylist_level_addons__is_active=True,
                stylist_level_addons__is_offered=True,
                is_active=True
            ).exclude(id__in=employee_addon_ids)
            
            # Create temporary EmployeeAddOn objects
            for addon in level_addons_query:
                temp_ea = EmployeeAddOn(
                    employee=employee,
                    addon=addon,
                    business=employee.business
                )
                stylist_level_addons.append(temp_ea)
        
        # Combine both lists of add-ons
        all_addons = employee_addons + stylist_level_addons
        
        # Serialize the combined add-ons results
        addon_serializer = EmployeeAddOnSerializer(all_addons, many=True)
        addon_data = addon_serializer.data
        
        # Sort add-ons by category_display_order and then by addon_display_order
        addon_data = sorted(addon_data, key=lambda x: (
            x.get('category_display_order', 999) or 999,  # Handle None values with default 999
            x.get('addon_display_order', 0)
        ))
        
        return Response(addon_data)


class EmployeeServiceViewSet(viewsets.ModelViewSet):
    """
    API endpoint for employee services.
    Allows viewing which employees offer specific services and their customizations.
    """
    queryset = EmployeeService.objects.filter(is_active=True)
    serializer_class = EmployeeServiceSerializer
    permission_classes = [PublicReadOnlyPrivateWrite]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['employee__user__first_name', 'employee__user__last_name', 'service__name']
    ordering_fields = ['employee__user__first_name', 'employee__user__last_name', 'service__name']
    ordering = ['employee__user__first_name', 'service__name']
    pagination_class = PageNumberPagination
    pagination_class.page_size = 20

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by employee if provided
        employee_id = self.request.query_params.get('employee_id')
        if employee_id:
            queryset = queryset.filter(employee_id=employee_id)
        
        # Filter by service if provided
        service_id = self.request.query_params.get('service_id')
        if service_id:
            queryset = queryset.filter(service_id=service_id)
        
        # Filter by business if provided
        business_id = self.request.query_params.get('business_id')
        if business_id:
            queryset = queryset.filter(business_id=business_id)
            
        return queryset

class StylistLevelServiceViewSet(viewsets.ModelViewSet):
    """
    API endpoint for stylist level services.
    Allows defining default prices and durations for services based on stylist level.
    These act as defaults for EmployeeService if no custom values are set.
    """
    queryset = StylistLevelService.objects.filter(is_active=True)
    serializer_class = StylistLevelServiceSerializer
    permission_classes = [PublicReadOnlyPrivateWrite]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['service__name', 'stylist_level']
    ordering_fields = ['service__name', 'stylist_level', 'price']
    ordering = ['service__name', 'stylist_level']
    pagination_class = PageNumberPagination
    pagination_class.page_size = 20

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by service if provided
        service_id = self.request.query_params.get('service_id')
        if service_id:
            queryset = queryset.filter(service_id=service_id)
        
        # Filter by stylist level if provided
        stylist_level = self.request.query_params.get('stylist_level')
        if stylist_level:
            queryset = queryset.filter(stylist_level=stylist_level)
        
        # Filter by business if provided
        business_id = self.request.query_params.get('business_id')
        if business_id:
            queryset = queryset.filter(business_id=business_id)
            
        return queryset 