# Salon Booking System – Database Schema

Multi-tenant, role-based appointment system for salons and beauty service providers.

---

## 1. Design Rationale

| Topic | Explanation |
|-------|-------------|
| **Users & Roles** | Each person has a single `User` record. Roles like `Employee` or `BusinessAdmin` are linked via role-specific tables. A person can hold both roles within the **same** business. |
| **Access Levels** | Roles like “<PERSON>ylist,” “Manager,” or “Owner” are managed in a shared `AccessLevel` table. Both employees and admins reference this to define permissions. |
| **EmployeeWorkingHours** | Modeled separately because each employee may work different days/times. Enables support for recurring availability blocks and precise scheduling. |
| **BusinessCustomer** | Manages the relationship between a global customer and individual businesses. Stores business-specific fields like notes, preferences, and loyalty points. |
| **EmployeeService** | Stores employee-specific price/duration overrides for services. Prevents duplication of services and supports personalized booking experiences. |
| **RecurringPattern** | Captures recurrence rules for repeated appointments (e.g., every 2 weeks). Referenced by appointments to avoid duplicate logic or data.

---

## 2. Core Tables and Relationships

<small>*PK = Primary Key, FK = Foreign Key, UQ = Unique Constraint*</small>

### 2.1 Business & Location

| Table | Fields |
|-------|--------|
| **Business** | BusinessID PK, Name, Email, Phone, Industry, CreatedAt |
| **Location** | LocationID PK, BusinessID FK, Address, Phone, TimeZone |
| **BusinessSettings** | SettingID PK, BusinessID FK, Key, Value |

---

### 2.2 Users, Roles & Access

| Table | Fields |
|-------|--------|
| **User** | UserID PK, Email UQ, Phone UQ, PasswordHash, FirstName, LastName, CreatedAt |
| **AccessLevel** | AccessLevelID PK, Name (e.g., Stylist, Manager), Description, PermissionsJSON |
| **Customer** | CustomerID PK, UserID FK UQ |
| **BusinessCustomer** | BusinessCustomerID PK, BusinessID FK, CustomerID FK, Notes, LoyaltyPoints, OptInMarketing |
| **Employee** | EmployeeID PK, UserID FK, BusinessID FK, AccessLevelID FK, Title, HireDate, Active, UNIQUE (UserID, BusinessID) |
| **EmployeeWorkingHours** | WorkingHoursID PK, EmployeeID FK, StartTime, EndTime, RecurrenceRule |
| **BusinessAdmin** | AdminID PK, UserID FK, BusinessID FK, AccessLevelID FK, UNIQUE (UserID, BusinessID) |

---

### 2.3 Service Catalog

| Table | Fields |
|-------|--------|
| **ServiceCategory** | CategoryID PK, BusinessID FK, Name, SortOrder |
| **Service** | ServiceID PK, BusinessID FK, CategoryID FK, Name, Description, BasePrice, BaseDurationMin |
| **AddOn** | AddOnID PK, BusinessID FK, Name, Price, DurationMin |
| **ServiceAddOn** | Composite PK (ServiceID, AddOnID) |

---

### 2.4 Employee Service Mapping

| Table | Fields |
|-------|--------|
| **EmployeeService** | Composite PK (EmployeeID, ServiceID), CustomPrice, CustomDurationMin, Active |

---

### 2.5 Booking & Recurrence

| Table | Fields |
|-------|--------|
| **RecurringPattern** | RecurrenceID PK, Frequency (daily/weekly/etc.), Interval, ByDay, StartDate, EndDate |
| **Appointment** | AppointmentID PK, BusinessID FK, LocationID FK, CustomerID FK, EmployeeID FK, ServiceID FK, StartTime, EndTime, Status, PriceCharged, RecurrenceID FK (nullable), Notes |
| **AppointmentAddOn** | Composite PK (AppointmentID, AddOnID), AddOnPrice |

---

## 3. Materialized Views / Developer Helpers

| View | Description | Key Fields |
|------|-------------|------------|
| **vAppointmentDetails** | Full appointment info with joins | AppointmentID, StartTime, ServiceName, EmployeeName, CustomerName, LocationName |
| **vUserRoles** | Lists all roles for a user | UserID, IsCustomer, IsEmployee, IsAdmin, BusinessesJSON |
| **vEmployeeServices** | Employee-service mapping with pricing | EmployeeID, ServiceID, CustomPrice, CustomDuration |
| **vBusinessCustomers** | Customers scoped per business | BusinessID, CustomerID, LoyaltyPoints, Notes |

---
