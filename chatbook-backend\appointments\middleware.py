"""
Middleware to capture user context for appointment history tracking.
"""
from threading import local
from django.contrib.auth import get_user_model
from rest_framework_simplejwt.authentication import JW<PERSON>uthentication
from rest_framework.authentication import SessionAuthentication

User = get_user_model()

# Thread-local storage for user context
_thread_local = local()


class AppointmentHistoryMiddleware:
    """
    Middleware to capture the current user and request context for appointment history tracking.
    This middleware runs after Django's AuthenticationMiddleware but before DRF's authentication,
    so we need to manually authenticate API requests.
    """

    def __init__(self, get_response):
        self.get_response = get_response
        self.jwt_auth = JWTAuthentication()
        self.session_auth = SessionAuthentication()

    def __call__(self, request):
        # Get the authenticated user - try multiple authentication methods
        user = self.get_authenticated_user(request)



        # Store user context in thread-local storage
        set_current_user_context(
            user=user,
            ip_address=self.get_client_ip(request),
            user_agent=request.META.get('HTTP_USER_AGENT', '')
        )

        try:
            response = self.get_response(request)



            # Clean up thread-local storage after response is generated
            # This ensures that model signals (like post_save) can still access the context
            clear_current_user_context()

            return response
        except Exception:
            # Clean up on exception
            clear_current_user_context()
            raise

    def get_authenticated_user(self, request):
        """
        Try to authenticate the user using multiple authentication methods.
        This ensures we capture the user regardless of authentication method.
        """
        # First, check if user is already authenticated (session auth)
        if hasattr(request, 'user') and request.user.is_authenticated:
            return request.user

        # Try JWT authentication for API requests
        if request.path.startswith('/api/'):
            try:
                # Try JWT authentication
                jwt_result = self.jwt_auth.authenticate(request)
                if jwt_result is not None:
                    user, _ = jwt_result  # token not needed
                    return user
            except Exception:
                pass

            try:
                # Try session authentication as fallback
                session_result = self.session_auth.authenticate(request)
                if session_result is not None:
                    user, _ = session_result  # auth not needed
                    return user
            except Exception:
                pass

        # Return the request user (might be AnonymousUser) or None
        return getattr(request, 'user', None)
    
    def get_client_ip(self, request):
        """Get the client's IP address from the request."""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


def set_current_user_context(user=None, ip_address=None, user_agent=None):
    """Set the current user context in thread-local storage."""
    _thread_local.user = user
    _thread_local.ip_address = ip_address
    _thread_local.user_agent = user_agent


def clear_current_user_context():
    """Clear the current user context from thread-local storage."""
    if hasattr(_thread_local, 'user'):
        del _thread_local.user
    if hasattr(_thread_local, 'ip_address'):
        del _thread_local.ip_address
    if hasattr(_thread_local, 'user_agent'):
        del _thread_local.user_agent


def get_current_user_context():
    """
    Get the current user context from thread-local storage.
    Returns a dictionary with user information for history tracking.
    """
    user = getattr(_thread_local, 'user', None)
    ip_address = getattr(_thread_local, 'ip_address', None)
    user_agent = getattr(_thread_local, 'user_agent', None)

    # Determine user type and related objects
    user_type = 'system'
    employee = None
    customer = None

    if user and user.is_authenticated:
        # Check if user is an employee (using correct related_name)
        if hasattr(user, 'employee_profile'):
            user_type = 'employee'
            employee = user.employee_profile
        # Check if user is a customer
        elif hasattr(user, 'customer'):
            user_type = 'customer'
            customer = user.customer
        else:
            user_type = 'user'

    return {
        'user': user if user and user.is_authenticated else None,
        'user_type': user_type,
        'employee': employee,
        'customer': customer,
        'ip_address': ip_address,
        'user_agent': user_agent,
    }
