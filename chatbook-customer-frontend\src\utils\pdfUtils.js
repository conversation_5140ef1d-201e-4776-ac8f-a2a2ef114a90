import { api } from '../hooks/useApi'

/**
 * Generate a PDF (A4) from free-form HTML (rendered via html2canvas) and
 * upload it to S3 via the backend `/files/upload/` endpoint. Returns the
 * response JSON from the backend upload serializer.
 *
 * @param {String} title             – Title to appear at top of PDF
 * @param {Array}  questions         – Array of question blocks; supports
 *                                     same schema used by template builder
 * @param {String} filename          – Name for the uploaded PDF
 * @param {Object} opts              – Extra options { documentType, category }
 */
export const saveFormToPdf = async (
  title = 'Document',
  questions = [],
  filename = 'document.pdf',
  opts = {},
) => {
  const {
    documentType = 'Consent Form',
    category = 'forms',
  } = opts

  try {
    /* -------------------------------------------------------------- */
    /* Dynamically import heavy libs                                  */
    /* -------------------------------------------------------------- */
    const [jsPDF, html2canvas] = await Promise.all([
      import('jspdf').then(m => m.default),
      import('html2canvas').then(m => m.default),
    ])

    /* -------------------------------------------------------------- */
    /* Build hidden DOM container with the form content               */
    /* -------------------------------------------------------------- */
    const container = document.createElement('div')
    Object.assign(container.style, {
      position: 'absolute',
      left: '-9999px',
      top: '-9999px',
      padding: '20px',
      width: '800px',
      fontFamily: 'Arial, sans-serif',
      background: '#ffffff',
      color: '#000000',
    })
    document.body.appendChild(container)

    // helper to render arbitrary HTML -> PNG data + height (A4 width)
    const renderHtmlBlock = async (html) => {
      container.innerHTML = html
      const canvas = await html2canvas(container, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        logging: false,
      })
      const dataUrl = canvas.toDataURL('image/png')
      const imgWidth = 210 // mm
      const imgHeight = (canvas.height * imgWidth) / canvas.width
      return { dataUrl, imgWidth, imgHeight }
    }

    const pdf = new jsPDF({ orientation: 'portrait', unit: 'mm', format: 'a4' })
    const pageHeight = pdf.internal.pageSize.getHeight()
    let cursorY = 0

    // Title
    const titleInfo = await renderHtmlBlock(`<h1 style="text-align:center;margin-bottom:24px;">${title}</h1>`)
    pdf.addImage(titleInfo.dataUrl, 'PNG', 0, cursorY, titleInfo.imgWidth, titleInfo.imgHeight)
    cursorY += titleInfo.imgHeight

    // Render each question block
    const buildQuestionHtml = (q, idx) => {
      if (!q) return ''
      switch (q.type) {
        case 'text': {
          const tag = q.textStyle === 'Heading' ? 'h2' : q.textStyle === 'Subheading' ? 'h3' : 'p'
          return `<${tag} style="margin:12px 0;">${q.textContent || ''}</${tag}>`
        }
        case 'short':
        case 'long': {
          const h = q.type === 'short' ? 30 : 60
          return `
            <div style="margin:14px 0;">
              <p style="margin:0 0 6px;"><strong>Q${idx + 1}: ${q.label || ''}</strong></p>
              <div style="height:${h}px;border-bottom:1px solid #bbb;"></div>
            </div>
          `
        }
        case 'multiple': {
          const opts = (q.options || []).map(o => `<li>${o}</li>`).join('')
          return `
            <div style="margin:14px 0;">
              <p style="margin:0 0 6px;"><strong>Q${idx + 1}: ${q.label || ''}</strong></p>
              <ul style="margin:0 0 0 18px;padding:0;list-style-type:disc;">${opts}</ul>
            </div>
          `
        }
        case 'signature': {
          const imgHtml = q.signatureData?.customer
            ? `<img src="${q.signatureData.customer}" style="max-width:100%;max-height:200px;" />`
            : '<div style="height:100px;border:1px dashed #bbb;"></div>'
          return `
            <div style="margin:18px 0;">
              <p style="margin:0 0 6px;"><strong>Customer Signature</strong></p>
              ${imgHtml}
            </div>
          `
        }
        default:
          return ''
      }
    }

    for (let i = 0; i < questions.length; i++) {
      const html = buildQuestionHtml(questions[i], i)
      if (!html) continue
      const info = await renderHtmlBlock(html)
      if (cursorY + info.imgHeight > pageHeight) {
        pdf.addPage()
        cursorY = 0
      }
      pdf.addImage(info.dataUrl, 'PNG', 0, cursorY, info.imgWidth, info.imgHeight)
      cursorY += info.imgHeight
    }

    // Date footer
    const dateInfo = await renderHtmlBlock(`<p style="margin-top:24px;text-align:right;">Date: ${new Date().toLocaleDateString()}</p>`)
    if (cursorY + dateInfo.imgHeight > pageHeight) {
      pdf.addPage()
      cursorY = 0
    }
    pdf.addImage(dateInfo.dataUrl, 'PNG', 0, cursorY, dateInfo.imgWidth, dateInfo.imgHeight)

    // Clean up DOM container
    document.body.removeChild(container)

    /* -------------------------------------------------------------- */
    /* Upload                                                         */
    /* -------------------------------------------------------------- */
    const blob = pdf.output('blob')
    const file = new File([blob], filename, { type: 'application/pdf' })

    const uploadRes = await api.uploadForm(file, {
      category,
      is_signed: true,
      file_type: 'document',
    })

    return uploadRes
  } catch (err) {
    console.error('saveFormToPdf error:', err)
    throw err
  }
} 