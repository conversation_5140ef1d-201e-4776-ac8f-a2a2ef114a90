function DashboardStats({ stats }) {
  const defaultStats = {
    totalBookings: 42,
    upcomingBookings: 8,
    completedBookings: 34,
    cancellationRate: 5,
    averageRating: 4.8
  }
  
  const data = stats || defaultStats
  
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
      <StatCard 
        title="Total Bookings" 
        value={data.totalBookings}
        icon="📊"
      />
      <StatCard 
        title="Upcoming Bookings" 
        value={data.upcomingBookings}
        icon="📅"
        highlight
      />
      <StatCard 
        title="Completed Bookings" 
        value={data.completedBookings}
        icon="✓"
      />
      <StatCard 
        title="Average Rating" 
        value={`${data.averageRating}/5`}
        icon="⭐"
      />
    </div>
  )
}

function StatCard({ title, value, icon, highlight = false }) {
  return (
    <div className={`p-6 rounded-lg shadow-md ${highlight ? 'bg-primary-50 border border-primary-100' : 'bg-white'}`}>
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-medium text-gray-700">{title}</h3>
        <span className="text-2xl">{icon}</span>
      </div>
      <p className={`text-3xl font-bold ${highlight ? 'text-primary-700' : 'text-gray-900'}`}>
        {value}
      </p>
    </div>
  )
}

export default DashboardStats 