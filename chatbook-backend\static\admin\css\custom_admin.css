/* Custom Admin Theme */
:root {
    --primary-color: #2c3e50;
    --secondary-color: #3498db;
    --accent-color: #e74c3c;
    --text-color: #2c3e50;
    --background-color: #f5f6fa;
    --header-height: 60px;
}

/* Header Styling */
#header {
    background: var(--primary-color);
    color: white;
    height: var(--header-height);
    line-height: var(--header-height);
    padding: 0 2rem;
}

#header a:link, #header a:visited {
    color: white;
}

#branding h1 {
    color: white;
    font-weight: 600;
}

/* Content Area */
#content {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    margin: 2rem;
    padding: 2rem;
}

/* Module Headers */
.module h2, .module caption {
    background: var(--primary-color);
    color: white;
    font-weight: 500;
    padding: 1rem;
}

/* Links and Buttons */
a:link, a:visited {
    color: var(--secondary-color);
    text-decoration: none;
}

a:hover {
    color: var(--accent-color);
}

.button, input[type=submit], input[type=button], .submit-row input {
    background: var(--secondary-color);
    border: none;
    border-radius: 4px;
    color: white;
    padding: 0.5rem 1rem;
    transition: background 0.3s ease;
}

.button:hover, input[type=submit]:hover, input[type=button]:hover {
    background: var(--accent-color);
}

/* Form Elements */
input[type=text], input[type=password], input[type=email], textarea, select {
    border: 1px solid #ddd;
    border-radius: 4px;
    padding: 0.5rem;
}

/* List View */
#changelist-form .results {
    border-radius: 4px;
    overflow: hidden;
}

#changelist table thead th {
    background: var(--background-color);
    border-bottom: 2px solid var(--primary-color);
    color: var(--text-color);
    padding: 1rem;
}

/* Messages */
.success {
    background: #2ecc71;
    color: white;
}

.warning {
    background: #f1c40f;
    color: white;
}

.error {
    background: var(--accent-color);
    color: white;
}

/* Fix for sidebar scrolling issue */
.main-sidebar {
    position: fixed !important;
    height: 100vh;
    overflow-y: auto;
}

.layout-fixed .brand-link {
    position: fixed;
    width: 250px;
}

.layout-fixed .main-sidebar .sidebar {
    height: calc(100vh - 57px);
    margin-top: 57px;
    padding-bottom: 100px;
}

.nav-sidebar {
    height: 100%;
    overflow-y: auto;
    overflow-x: hidden;
}

.content-wrapper {
    margin-left: 250px !important;
}

@media (max-width: 992px) {
    .content-wrapper {
        margin-left: 0 !important;
    }
}

/* Save sidebar scroll position */
html {
    scroll-behavior: smooth;
}

/* Responsive Design */
@media (max-width: 1024px) {
    #content {
        margin: 1rem;
        padding: 1rem;
    }
}

@media (max-width: 767px) {
    #header {
        padding: 0 1rem;
    }
    
    #content {
        margin: 0.5rem;
        padding: 0.5rem;
    }
} 