import { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { Toaster } from 'react-hot-toast';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import { queryClient } from './lib/queryClient';
import { useAuthStore } from './stores/authStore';
import { setupDebugEnvironment } from './utils/flowValidation';
import Layout from './components/layout/Layout.jsx';
import MainContentWrapper from './components/MainContentWrapper.jsx';
import ConsentFormPage from './pages/ConsentFormPage.jsx';
import ReviewBookingPage from './pages/ReviewBookingPage.jsx';
import BookingConfirmationPage from './pages/BookingConfirmationPage.jsx';
import LoginPage from './pages/Auth/LoginPage.jsx';
import ProfilePage from './pages/Auth/ProfilePage.jsx';
import AppointmentsPage from './pages/AppointmentsPage.jsx';
import RequireAuth from './components/RequireAuth.jsx';
import './App.css';

// App initialization component
const AppInitializer = ({ children }) => {
    const initializeAuth = useAuthStore(state => state.initializeAuth);

    useEffect(() => {
        // Initialize auth state on app startup
        initializeAuth();

        // Setup debug environment in development
        if (process.env.NODE_ENV === 'development') {
            setupDebugEnvironment();
        }
    }, [initializeAuth]);

    return children;
};

const App = () => {
    const [activeTab, setActiveTab] = useState('services');

    const businessInfo = {
        name: 'Clément Lash',
        location: 'Bellevue, WA',
        logo: '/images/logo.png'
    };

    const handleTabChange = (tab) => {
        setActiveTab(tab);
    };

    return (
        <QueryClientProvider client={queryClient}>
            <AppInitializer>
                <Router>
                    <Routes>
                        <Route
                            path="/"
                            element={
                                <Layout
                                    businessInfo={businessInfo}
                                    activeTab={activeTab}
                                    onTabChange={handleTabChange}
                                >
                                    <div className="app-container">
                                        <MainContentWrapper
                                            activeTab={activeTab}
                                            setActiveTab={setActiveTab}
                                        />
                                    </div>
                                </Layout>
                            }
                        />
                        <Route
                            path="/login"
                            element={<LoginPage />}
                        />
                        <Route
                            path="/profile"
                            element={
                                <RequireAuth>
                                    <ProfilePage />
                                </RequireAuth>
                            }
                        />
                        <Route
                            path="/appointments"
                            element={
                                <RequireAuth>
                                    <AppointmentsPage />
                                </RequireAuth>
                            }
                        />
                        <Route
                            path="/consent-form"
                            element={<ConsentFormPage />}
                        />
                        <Route
                            path="/review-booking"
                            element={<ReviewBookingPage />}
                        />
                        <Route
                            path="/booking-confirmation"
                            element={<BookingConfirmationPage />}
                        />
                    </Routes>
                    <Toaster
                        position="top-right"
                        toastOptions={{
                            duration: 4000,
                            style: {
                                background: '#363636',
                                color: '#fff',
                            },
                        }}
                    />
                </Router>
            </AppInitializer>
            <ReactQueryDevtools initialIsOpen={false} />
        </QueryClientProvider>
    );
};

export default App;
