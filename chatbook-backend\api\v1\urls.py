"""
API v1 URLs

Main URL configuration for API version 1.
Files are reorganized but all original endpoints are preserved exactly as they were.
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from rest_framework_simplejwt.views import TokenRefreshView
from django.views.decorators.csrf import csrf_exempt

# Import views from reorganized modules (same functionality, just organized in folders)
from .services.views import ServiceCategoryViewSet, ServiceViewSet, EmployeeViewSet, EmployeeServiceViewSet, StylistLevelServiceViewSet
from .business.views import BusinessCustomerViewSet, BusinessViewSet, LocationViewSet, OnlineBookingRulesViewSet, StylistLevelViewSet
from .business.views import BusinessCustomerCurrentUserView, BusinessCustomerDetailCurrentUserView, BusinessCustomerServiceSuggestionsView
from .business.views import CustomerFormCompletionView, BusinessCustomerFormCompletionView
from .employees.views import EmployeeCurrentUserView, EmployeeWorkingHoursView, EmployeePermissionsView, EmployeeCalendarConfigView, EmployeeAppointmentsView
from .forms.views import FormTemplateViewSet, FormSubmissionViewSet, SignatureViewSet
from .views.users import ProfileView, SettingsView
from .views.bff import AppBootstrapView
from .views.health import GeneralHealthCheckView, AWSHealthCheckView
from .customers.views import CustomerImportView, ImportStatusView, ImportResultsView, FieldMappingSuggestionsView
from .files.views import FileUploadView, FileProcessView, FilePreviewView, FileStatusView, FileResultsView, FileProcessStatusView, FileProcessResultsView
from .appointments.views import AppointmentViewSet
from .waitlist.views import WaitlistEntryViewSet

app_name = 'v1'

# Create a router and register our viewsets with it - EXACT SAME AS ORIGINAL
router = DefaultRouter()
router.register(r'service-categories', ServiceCategoryViewSet)
router.register(r'services', ServiceViewSet)
router.register(r'employees', EmployeeViewSet)
router.register(r'appointments', AppointmentViewSet)
router.register(r'employee-services', EmployeeServiceViewSet)
router.register(r'stylist-level-services', StylistLevelServiceViewSet)
router.register(r'business/customer', BusinessCustomerViewSet, basename='business-customer')
router.register(r'businesses', BusinessViewSet)
router.register(r'locations', LocationViewSet)
router.register(r'booking-rules', OnlineBookingRulesViewSet)
router.register(r'stylist-levels', StylistLevelViewSet)
router.register(r'waitlist', WaitlistEntryViewSet)

# Forms router - consolidated forms endpoints
forms_router = DefaultRouter()
forms_router.register(r'templates', FormTemplateViewSet, basename='form-template')
forms_router.register(r'submissions', FormSubmissionViewSet, basename='form-submission')
forms_router.register(r'signatures', SignatureViewSet, basename='form-signature')

# The API URLs are determined automatically by the router and manually specified paths - EXACT SAME AS ORIGINAL
urlpatterns = [
    # Health check endpoints
    path('health/', GeneralHealthCheckView.as_view(), name='health_check'),
    path('health/aws/', AWSHealthCheckView.as_view(), name='aws_health_check'),

    # Auth endpoints - explicitly mark as CSRF exempt at the URL level
    path('auth/', include('api.v1.auth.urls')),

    # User endpoints
    path('users/profile/', ProfileView.as_view(), name='user_profile'),
    path('users/settings/', SettingsView.as_view(), name='user_settings'),

    # Employee endpoints - updated for the new structure
    path('employees/me/', EmployeeCurrentUserView.as_view(), name='employee_current_user'),
    path('employees/me/working-hours/', EmployeeWorkingHoursView.as_view(), name='employee_working_hours'),
    path('employees/me/permissions/', EmployeePermissionsView.as_view(), name='employee_permissions'),
    path('employees/me/calendar-configs/', EmployeeCalendarConfigView.as_view(), name='employee_calendar_config'),
    path('employees/me/appointments/', EmployeeAppointmentsView.as_view(), name='employee_appointments'),

    # Business customer endpoints for current user's business
    path('business-customers/me/', BusinessCustomerCurrentUserView.as_view(), name='business_customer_current_user'),
    path('business-customers/me/<int:customer_id>/', BusinessCustomerDetailCurrentUserView.as_view(), name='business_customer_detail_current_user'),
    path('business-customers/me/<int:customer_id>/service-suggestions/', BusinessCustomerServiceSuggestionsView.as_view(), name='business_customer_service_suggestions'),
    path('business-customers/me/forms/', CustomerFormCompletionView.as_view(), name='customer_forms'),
    
    # Business staff endpoints for checking customer forms
    path('business-customers/forms/', BusinessCustomerFormCompletionView.as_view(), name='business_customer_forms'),
    # BFF endpoint for app bootstrapping
    path('app/bootstrap/', AppBootstrapView.as_view(), name='app_bootstrap'),

    # Customer import endpoints
    path('customers/import/', CustomerImportView.as_view(), name='customer_import'),
    path('customers/import/status/<str:job_id>/', ImportStatusView.as_view(), name='import_status'),
    path('customers/import/results/<str:job_id>/', ImportResultsView.as_view(), name='import_results'),
    path('customers/import/field-mapping-suggestions/', FieldMappingSuggestionsView.as_view(), name='field_mapping_suggestions'),

    # File upload endpoints
    path('files/upload/', csrf_exempt(FileUploadView.as_view()), name='file_upload'),
    path('files/<str:file_id>/process/', csrf_exempt(FileProcessView.as_view()), name='file_process'),
    path('files/<str:file_id>/preview/', csrf_exempt(FilePreviewView.as_view()), name='file_preview'),
    path('files/<str:file_id>/status/', csrf_exempt(FileStatusView.as_view()), name='file_status'),
    path('files/<str:file_id>/results/', csrf_exempt(FileResultsView.as_view()), name='file_results'),
    path('files/processing/<str:job_id>/status/', csrf_exempt(FileProcessStatusView.as_view()), name='file_process_status'),
    path('files/processing/<str:job_id>/results/', csrf_exempt(FileProcessResultsView.as_view()), name='file_process_results'),

    # Notification endpoints
    path('notifications/', include('notifications.urls')),

    # Forms endpoints - consolidated under /api/v1/forms/
    path('forms/', include(forms_router.urls)),

    # Reports endpoints
    path('reports/', include('api.v1.reports.urls')),
    
    # Calendar endpoints
    path('calendar/', include('api.v1.calendar.urls')),

    # Business customer endpoints - flattened structure with business-id as query param
    path('business-customers/', BusinessCustomerViewSet.as_view({
        'get': 'list',
        'post': 'create'
    }), name='business_customer_list'),
    path('business-customers/<int:pk>/', BusinessCustomerViewSet.as_view({
        'get': 'retrieve',
        'put': 'update',
        'patch': 'partial_update',
        'delete': 'destroy'
    }), name='business_customer_detail'),

    # Router URLs for services and other viewsets
    path('', include(router.urls)),
]
