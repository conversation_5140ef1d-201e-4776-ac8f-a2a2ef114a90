from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase
from rest_framework import status
from .models import WaitlistEntry, PreferredWindow
from business.models import Business
from services.models import Service, AddOn, ServiceCategory
from employees.models import Employee
from django.contrib.auth import get_user_model
from datetime import datetime, timedelta
from django.utils import timezone

User = get_user_model()


class WaitlistEntryModelTest(TestCase):
    def setUp(self):
        # Create a user and business first
        self.user = User.objects.create_user(
            identifier="<EMAIL>",
            email="<EMAIL>",
            first_name="Test",
            last_name="Owner"
        )
        self.business = Business.objects.create(
            name="Test Business",
            owner=self.user
        )

        # Create an employee user and employee
        self.employee_user = User.objects.create_user(
            identifier="<EMAIL>",
            email="<EMAIL>",
            first_name="<PERSON>",
            last_name="Stylist",
            phone_number="555-9876"  # Unique phone number
        )
        self.employee = Employee.objects.create(
            user=self.employee_user,
            business=self.business
        )

        # Create a service category and service for testing
        self.category = ServiceCategory.objects.create(
            name="Test Category",
            business=self.business
        )
        self.service = Service.objects.create(
            business=self.business,
            category=self.category,
            name="Test Service",
            base_price=100.00,
            base_duration=timedelta(hours=1)
        )
        self.addon = AddOn.objects.create(
            business=self.business,
            name="Test Add-on",
            base_price=25.00,
            base_duration=timedelta(minutes=30)
        )

        self.entry = WaitlistEntry.objects.create(
            business=self.business,
            customer_name="John Doe",
            phone_number="555-1234",
            email="<EMAIL>",
            notes="Test notes for waitlist entry",
            status="current",
            priority_rule="manual"
        )

        # Add services, add-ons, and employees using ManyToMany
        self.entry.services.add(self.service)
        self.entry.add_ons.add(self.addon)
        self.entry.employees.add(self.employee)

    def test_waitlist_entry_creation(self):
        self.assertEqual(self.entry.business, self.business)
        self.assertEqual(self.entry.customer_name, "John Doe")
        self.assertEqual(self.entry.phone_number, "555-1234")
        self.assertEqual(self.entry.email, "<EMAIL>")
        self.assertEqual(self.entry.status, "current")
        self.assertEqual(self.entry.priority_rule, "manual")

    def test_waitlist_entry_str(self):
        expected = f"{self.entry.customer_name} - {self.entry.status}"
        self.assertEqual(str(self.entry), expected)

    def test_waitlist_services_relationship(self):
        self.assertIn(self.service, self.entry.services.all())
        self.assertEqual(self.entry.services.count(), 1)

    def test_waitlist_addons_relationship(self):
        self.assertIn(self.addon, self.entry.add_ons.all())
        self.assertEqual(self.entry.add_ons.count(), 1)

    def test_waitlist_employees_relationship(self):
        self.assertIn(self.employee, self.entry.employees.all())
        self.assertEqual(self.entry.employees.count(), 1)

    def test_waitlist_notes(self):
        self.assertEqual(self.entry.notes, "Test notes for waitlist entry")

    def test_total_price_calculation(self):
        # Test that total_price correctly sums services and add-ons
        expected_total = self.service.base_price + self.addon.base_price
        self.assertEqual(self.entry.total_price, expected_total)

    def test_total_duration_calculation(self):
        # Test that total_duration correctly sums services (including buffer time) and add-ons in minutes
        service_minutes = int(self.service.base_duration.total_seconds() // 60)
        buffer_minutes = int(self.service.buffer_time.total_seconds() // 60)
        addon_minutes = int(self.addon.base_duration.total_seconds() // 60)
        expected_total = service_minutes + buffer_minutes + addon_minutes
        self.assertEqual(self.entry.total_duration, expected_total)


class PreferredWindowModelTest(TestCase):
    def setUp(self):
        # Create a user and business first
        self.user = User.objects.create_user(
            identifier="<EMAIL>",
            email="<EMAIL>",
            first_name="Test",
            last_name="Owner2"
        )
        self.business = Business.objects.create(
            name="Test Business 2",
            owner=self.user
        )

        self.entry = WaitlistEntry.objects.create(
            business=self.business,
            customer_name="Jane Doe",
            phone_number="555-5678",
            email="<EMAIL>",
            status="current",
            priority_rule="manual"
        )
        self.window = PreferredWindow.objects.create(
            entry=self.entry,
            start_datetime=timezone.now(),
            end_datetime=timezone.now() + timedelta(hours=2)
        )

    def test_preferred_window_creation(self):
        self.assertEqual(self.window.entry, self.entry)
        self.assertIsNotNone(self.window.start_datetime)
        self.assertIsNotNone(self.window.end_datetime)

    def test_preferred_window_str(self):
        expected = f"{self.entry.customer_name} - {self.window.start_datetime} to {self.window.end_datetime}"
        self.assertEqual(str(self.window), expected)


class WaitlistAPITest(APITestCase):
    def setUp(self):
        # Clear any existing entries
        WaitlistEntry.objects.all().delete()

        # Create a user and business for API tests
        self.user = User.objects.create_user(
            identifier="<EMAIL>",
            email="<EMAIL>",
            first_name="API",
            last_name="User"
        )
        self.business = Business.objects.create(
            name="API Test Business",
            owner=self.user
        )

        self.entry_data = {
            'business': self.business.id,
            'customer_name': 'Test Customer',
            'phone_number': '555-9999',
            'email': '<EMAIL>',
            'services': [1, 2],
            'status': 'current',
            'priority_rule': 'manual'
        }

    def test_create_waitlist_entry(self):
        url = reverse('v1:waitlistentry-list')
        response = self.client.post(url, self.entry_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(WaitlistEntry.objects.count(), 1)
        self.assertEqual(WaitlistEntry.objects.get().customer_name, 'Test Customer')

    def test_list_waitlist_entries(self):
        # Create entry with business object, not just ID
        entry_data_for_creation = self.entry_data.copy()
        entry_data_for_creation['business'] = self.business
        WaitlistEntry.objects.create(**entry_data_for_creation)
        url = reverse('v1:waitlistentry-list')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
