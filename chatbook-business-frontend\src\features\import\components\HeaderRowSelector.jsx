import { useState, useEffect } from 'react'
import { detectHeaderRows, parseCSVFile, parseExcelFile } from '../utils/fileUtils'

function HeaderRowSelector({ file, rawData, onHeaderRowSelected }) {
  const [selectedHeaderRow, setSelectedHeaderRow] = useState(0)
  const [potentialHeaders, setPotentialHeaders] = useState([])
  const [isProcessing, setIsProcessing] = useState(false)

  // Detect potential header rows when component mounts
  useEffect(() => {
    if (rawData && rawData.rawData) {
      const detected = detectHeaderRows(rawData.rawData)
      setPotentialHeaders(detected)
      
      // Auto-select the most likely header row
      if (detected.length > 0) {
        setSelectedHeaderRow(detected[0].rowIndex)
      }
    }
  }, [rawData])

  const handleConfirmHeaderRow = async () => {
    setIsProcessing(true)
    
    try {
      let parsedData
      if (file.name.toLowerCase().endsWith('.csv')) {
        parsedData = await parseCSVFile(file, selectedHeaderRow)
      } else {
        parsedData = await parseExcelFile(file, selectedHeaderRow)
      }
      
      onHeaderRowSelected(parsedData)
    } catch (error) {
      console.error('Error parsing with selected header row:', error)
      // Could add error handling here
    } finally {
      setIsProcessing(false)
    }
  }

  const getRowPreview = (rowIndex) => {
    if (!rawData.rawData || rowIndex >= rawData.rawData.length) return []
    return rawData.rawData[rowIndex] || []
  }

  const getConfidenceColor = (score) => {
    if (score >= 10) return 'text-green-600 bg-green-50'
    if (score >= 5) return 'text-yellow-600 bg-yellow-50'
    return 'text-red-600 bg-red-50'
  }

  const getConfidenceLabel = (score) => {
    if (score >= 10) return 'High'
    if (score >= 5) return 'Medium'
    return 'Low'
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center">
        <h3 className="text-lg font-medium text-gray-900 mb-2">Select Header Row</h3>
        <p className="text-sm text-gray-600">
          Choose which row contains your column headers. We've analyzed the first few rows to help you decide.
        </p>
      </div>

      {/* File Info */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-500">File:</span>
          <span className="font-medium">{file.name}</span>
        </div>
      </div>

      {/* Row Selection */}
      <div className="space-y-4">
        <h4 className="text-sm font-medium text-gray-900">Available Rows:</h4>
        
        <div className="space-y-3">
          {potentialHeaders.slice(0, 5).map((headerCandidate) => {
            const preview = getRowPreview(headerCandidate.rowIndex)
            const isSelected = selectedHeaderRow === headerCandidate.rowIndex
            
            return (
              <div
                key={headerCandidate.rowIndex}
                className={`border rounded-lg p-4 cursor-pointer transition-all ${
                  isSelected 
                    ? 'border-blue-500 bg-blue-50 ring-2 ring-blue-500 ring-opacity-20' 
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
                onClick={() => setSelectedHeaderRow(headerCandidate.rowIndex)}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="flex items-center space-x-2">
                        <div className={`w-4 h-4 rounded-full border-2 ${
                          isSelected ? 'border-blue-500 bg-blue-500' : 'border-gray-300'
                        }`}>
                          {isSelected && (
                            <div className="w-full h-full rounded-full bg-white scale-50"></div>
                          )}
                        </div>
                        <span className="text-sm font-medium text-gray-900">
                          Row {headerCandidate.rowIndex + 1}
                        </span>
                      </div>
                      
                      <span className={`text-xs px-2 py-1 rounded-full ${getConfidenceColor(headerCandidate.score)}`}>
                        {getConfidenceLabel(headerCandidate.score)} Confidence
                      </span>
                      
                      <span className="text-xs text-gray-500">
                        {headerCandidate.columnCount} columns
                      </span>
                    </div>
                    
                    {/* Row Preview */}
                    <div className="bg-white border border-gray-200 rounded p-3">
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-2">
                        {preview.slice(0, 5).map((cell, cellIndex) => (
                          <div key={cellIndex} className="text-xs">
                            <div className="font-medium text-gray-500 mb-1">Col {cellIndex + 1}</div>
                            <div className="bg-gray-50 p-2 rounded border text-gray-900 truncate">
                              {cell || '-'}
                            </div>
                          </div>
                        ))}
                        {preview.length > 5 && (
                          <div className="text-xs text-gray-500 flex items-center">
                            +{preview.length - 5} more...
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )
          })}
        </div>

        {/* Custom Row Selection */}
        <div className="border-t border-gray-200 pt-4">
          <div className="flex items-center space-x-4">
            <label className="text-sm font-medium text-gray-700">
              Or select a specific row:
            </label>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">Row</span>
              <input
                type="number"
                min="1"
                max={rawData.rawData ? rawData.rawData.length : 1}
                value={selectedHeaderRow + 1}
                onChange={(e) => setSelectedHeaderRow(Math.max(0, parseInt(e.target.value) - 1) || 0)}
                className="w-20 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
              <span className="text-sm text-gray-500">
                of {rawData.rawData ? rawData.rawData.length : 0}
              </span>
            </div>
          </div>
        </div>
      </div>

      {/* Preview of Selected Headers */}
      {selectedHeaderRow !== undefined && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-blue-900 mb-3">
            Preview of Selected Headers (Row {selectedHeaderRow + 1}):
          </h4>
          <div className="flex flex-wrap gap-2">
            {getRowPreview(selectedHeaderRow).map((header, index) => (
              <span
                key={index}
                className="inline-flex items-center px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
              >
                {header || `Column ${index + 1}`}
              </span>
            ))}
          </div>
        </div>
      )}

      {/* Action Buttons */}
      <div className="flex flex-col sm:flex-row justify-end gap-3 pt-6 border-t border-gray-200">
        <button
          onClick={() => window.location.reload()}
          className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
        >
          Cancel
        </button>
        <button
          onClick={handleConfirmHeaderRow}
          disabled={isProcessing}
          className="px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors font-medium flex items-center"
        >
          {isProcessing ? (
            <>
              <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              Processing...
            </>
          ) : (
            'Continue with Selected Headers'
          )}
        </button>
      </div>
    </div>
  )
}

export default HeaderRowSelector 