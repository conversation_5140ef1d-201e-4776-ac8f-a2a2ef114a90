import { useState } from 'react'
import axios from 'axios'

// Base API configuration
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1',
  headers: {
    'Content-Type': 'application/json',
  },
})

// Add request interceptor for authentication
// Note: This is for authenticated endpoints only
// Public booking endpoints use a separate publicApi instance
api.interceptors.request.use((config) => {
  const token = localStorage.getItem('auth_token')
  if (token) {
    config.headers.Authorization = `Bearer ${token}`
  }
  return config
})

// Add response interceptor for global error handling
api.interceptors.response.use(
  (response) => response,
  (error) => {
    // Handle 401 errors globally (but only if not already handled)
    if (error.response?.status === 401) {
      const logoutTriggered = sessionStorage.getItem('auth_logout_triggered');

      if (!logoutTriggered) {
        console.log('🔒 Global 401 interceptor - clearing auth and redirecting');

        // Set flag to prevent loops
        sessionStorage.setItem('auth_logout_triggered', 'true');

        // Clear auth data
        localStorage.removeItem('auth_token');
        localStorage.removeItem('user_data');

        // Redirect to login (clean URL for user)
        if (window.location.pathname !== '/login') {
          window.location.href = '/login';
        }
      } else {
        console.log('🚫 Global 401 interceptor - logout already triggered, not redirecting');
      }
    }

    return Promise.reject(error);
  }
)

// -----------------------------------------------------------------
// Helper: uploadForm – multipart upload for signed / template forms
// -----------------------------------------------------------------
/**
 * Upload a form (blank template or signed copy) to the backend.
 *
 * @param {File|Blob} file     The file object to upload
 * @param {Object} opts        Extra fields for backend serializer
 * @param {String} opts.category         Folder category inside tenant bucket (forms/photos/notes/shared/signatures)
 * @param {Boolean} opts.is_signed       Whether this is a customer-signed form
 * @param {String} [opts.customer_uuid]  UUID of the customer (required when is_signed)
 * @param {Boolean} [opts.skip_duplicates=true]
 * @param {String} [opts.file_type="document"]      Logical type stored in DB
 * @param {String} [opts.description=""]            Optional description
 */
api.uploadForm = async (
  file,
  {
    category = "forms",
    is_signed = false,
    customer_uuid,
    skip_duplicates = true,
    file_type = "document",
    description = "",
    update_existing = false,
  } = {}
) => {
  const formData = new FormData();
  formData.append("file", file);
  formData.append("file_type", file_type);
  formData.append("category", category);
  formData.append("description", description);
  formData.append("skip_duplicates", String(skip_duplicates));
  formData.append("update_existing", String(update_existing));
  formData.append("is_signed", String(is_signed));
  if (customer_uuid) formData.append("customer_uuid", customer_uuid);

  const res = await api.post("/files/upload/", formData, {
    headers: { "Content-Type": "multipart/form-data" },
  });
  return res.data;
};

/**
 * Custom hook for API interactions
 * @param {string} endpoint - API endpoint
 * @returns {Object} - API interaction methods and state
 */
export function useApi(endpoint) {
  const [data, setData] = useState(null)
  const [error, setError] = useState(null)
  const [loading, setLoading] = useState(false)

  // Fetch data from the API
  const fetchData = async (params = {}) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await api.get(endpoint, { params })
      setData(response.data)
      return response.data
    } catch (err) {
      setError(err.response?.data || { message: err.message })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Create a new resource
  const createData = async (payload) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await api.post(endpoint, payload)
      return response.data
    } catch (err) {
      setError(err.response?.data || { message: err.message })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Update an existing resource
  const updateData = async (id, payload) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await api.put(`${endpoint}/${id}`, payload)
      return response.data
    } catch (err) {
      setError(err.response?.data || { message: err.message })
      return null
    } finally {
      setLoading(false)
    }
  }

  // Delete a resource
  const deleteData = async (id) => {
    setLoading(true)
    setError(null)
    
    try {
      const response = await api.delete(`${endpoint}/${id}`)
      return response.data
    } catch (err) {
      setError(err.response?.data || { message: err.message })
      return null
    } finally {
      setLoading(false)
    }
  }

  return {
    data,
    error,
    loading,
    fetchData,
    createData,
    updateData,
    deleteData,
  }
}

export default useApi

// Named export so other modules can import the configured axios instance directly
export { api }
