import React, { useState, useEffect, useRef } from 'react'
import { 
  CalendarIcon, 
  ClockIcon, 
  ExclamationTriangleIcon, 
  PencilIcon,
  XMarkIcon,
  UserGroupIcon,
  BookmarkIcon,
  PlusIcon
} from '@heroicons/react/24/outline'

/**
 * CalendarActionSheet - Enhanced context menu for time slots and appointments
 * Desktop-optimized with better positioning and UX
 */
const CalendarActionSheet = ({
  isOpen,
  onClose,
  date,
  appointment = null,
  employee = null,
  onNewAppointment,
  onAddToWaitlist,
  onPersonalTask,
  onEditWorkingHours,
  onEditAppointment,
  onCancelAppointment,
  onMarkNoShow,
  onMarkCompleted,
  position = { x: 0, y: 0 },
  timeSlotOccupied = false,
  conflictingAppointments = [],
  allowCustomerWaitlistAddition = true // Default to enabled for backwards compatibility
}) => {
  const [isClosing, setIsClosing] = useState(false)
  const [computedPosition, setComputedPosition] = useState(position)
  const sheetRef = useRef(null)

  // Enhanced positioning for desktop screens
  useEffect(() => {
    if (!isOpen || !sheetRef.current) return

    const sheet = sheetRef.current
    const rect = sheet.getBoundingClientRect()
    const viewport = {
      width: window.innerWidth,
      height: window.innerHeight
    }

    let { x, y } = position
    const padding = 16

    // Horizontal positioning - prefer right side but flip if needed
    if (x + rect.width + padding > viewport.width) {
      x = Math.max(padding, x - rect.width)
    }

    // Vertical positioning - center on click point but stay in viewport
    y = Math.max(padding, Math.min(y - rect.height / 2, viewport.height - rect.height - padding))

    setComputedPosition({ x, y })
  }, [isOpen, position])

  if (!isOpen) return null

  const handleClose = () => {
    setIsClosing(true)
    setTimeout(() => {
      setIsClosing(false)
      onClose()
    }, 150)
  }

  const handleAction = (actionFn) => {
    handleClose()
    setTimeout(() => actionFn?.(), 100)
  }

  // Enhanced time slot occupied detection
  const isTimeSlotOccupied = () => {
    return timeSlotOccupied || conflictingAppointments.length > 0
  }

  const formatDateTime = (date) => {
    const dateObj = new Date(date)
    return {
      date: dateObj.toLocaleDateString('en-US', { 
        weekday: 'long', 
        month: 'long', 
        day: 'numeric', 
        year: 'numeric' 
      }),
      time: dateObj.toLocaleTimeString('en-US', { 
        hour: '2-digit', 
        minute: '2-digit' 
      })
    }
  }

  const { date: formattedDate, time: formattedTime } = formatDateTime(date)

  // Enhanced time slot actions with occupancy detection
  const getTimeSlotActions = () => {
    const baseActions = []

    // Primary action - New Appointment (always available, but shows conflict warning)
    baseActions.push({
      icon: CalendarIcon,
      label: isTimeSlotOccupied() ? 'New Appointment (Conflict)' : 'New Appointment',
      description: isTimeSlotOccupied() 
        ? 'Create appointment despite existing booking' 
        : 'Book a new appointment',
      action: () => handleAction(onNewAppointment),
      primary: !isTimeSlotOccupied(),
      warning: isTimeSlotOccupied()
    })

    // Show conflict information if slot is occupied
    if (conflictingAppointments.length > 0) {
      baseActions.push({
        icon: ExclamationTriangleIcon,
        label: `${conflictingAppointments.length} Existing Booking${conflictingAppointments.length > 1 ? 's' : ''}`,
        description: conflictingAppointments.map(apt => apt.title || apt.clientName).join(', '),
        action: () => {}, // No action - just informational
        disabled: true,
        conflictInfo: true
      })
    }

    // Additional actions
    // Add to Waitlist - only available when enabled in settings
    if (allowCustomerWaitlistAddition) {
      baseActions.push({
        icon: ClockIcon,
        label: 'Add to Waitlist',
        description: 'Add client to waitlist',
        action: () => handleAction(onAddToWaitlist)
      })
    }

    // Block Time - only available when slot is not occupied
    if (!isTimeSlotOccupied()) {
      baseActions.push({
        icon: BookmarkIcon,
        label: 'Block Time',
        description: 'Block time for personal task',
        action: () => handleAction(onPersonalTask)
      })
    }

    baseActions.push({
      icon: PencilIcon,
      label: 'Edit Working Hours',
      description: 'Modify business hours',
      action: () => handleAction(onEditWorkingHours)
    })

    return baseActions
  }

  const timeSlotActions = getTimeSlotActions()

  // Appointment actions (when appointment exists)
  const appointmentActions = [
    {
      icon: PencilIcon,
      label: 'Edit Appointment',
      description: 'Modify appointment details',
      action: () => handleAction(onEditAppointment),
      primary: true
    },
    {
      icon: ExclamationTriangleIcon,
      label: 'Cancel Appointment',
      description: 'Cancel this appointment',
      action: () => handleAction(onCancelAppointment),
      destructive: true
    },
    {
      icon: XMarkIcon,
      label: 'Mark No Show',
      description: 'Customer did not show up',
      action: () => handleAction(onMarkNoShow),
      destructive: true
    },
    {
      icon: CalendarIcon,
      label: 'Mark Completed',
      description: 'Mark appointment as completed',
      action: () => handleAction(onMarkCompleted),
      positive: true
    }
  ]

  const actions = appointment ? appointmentActions : timeSlotActions

  return (
    <div className="fixed inset-0 z-50">
      {/* Backdrop */}
      <div 
        className="absolute inset-0 bg-black bg-opacity-30 transition-opacity"
        onClick={handleClose}
      />
      
      {/* Action Sheet */}
      <div 
        ref={sheetRef}
        className={`
          absolute bg-white rounded-lg shadow-2xl w-80 border border-gray-200
          transform transition-all duration-200 ease-out
          ${isClosing 
            ? 'scale-95 opacity-0' 
            : 'scale-100 opacity-100'
          }
        `}
        style={{
          left: `${computedPosition.x}px`,
          top: `${computedPosition.y}px`,
        }}
      >
        {/* Header */}
        <div className="p-4 border-b border-gray-200">
          <div className="text-lg font-semibold text-gray-900">
            {appointment ? appointment.title : 'Available Time Slot'}
          </div>
          <div className="text-sm text-gray-600 mt-1">
            {formattedDate}
          </div>
          <div className="text-sm text-gray-600">
            {formattedTime}
            {employee && (
              <span className="ml-2 text-blue-600 font-medium">
                • {employee.name}
              </span>
            )}
          </div>
          
          {appointment && (
            <div className="mt-2 text-xs text-gray-500">
              {appointment.description}
            </div>
          )}
        </div>

        {/* Actions */}
        <div className="py-1">
          {actions.map((action, index) => (
            <button
              key={index}
              onClick={action.action}
              disabled={Boolean(action.disabled)}
              className={`
                w-full flex items-center px-4 py-3 text-left transition-colors
                ${action.disabled 
                  ? 'cursor-not-allowed bg-gray-50' 
                  : 'hover:bg-blue-50 cursor-pointer'
                }
                ${action.primary ? 'text-blue-600 font-medium' : ''}
                ${action.warning ? 'text-orange-600' : ''}
                ${action.destructive ? 'text-red-600' : ''}
                ${action.positive ? 'text-green-600' : ''}
                ${action.conflictInfo ? 'text-orange-700 bg-orange-50' : ''}
                ${!action.primary && !action.destructive && !action.positive && !action.warning && !action.conflictInfo ? 'text-gray-700' : ''}
              `}
            >
              <action.icon className={`h-5 w-5 mr-3 flex-shrink-0 ${action.conflictInfo ? 'text-orange-500' : ''}`} />
              <div className="flex-1 min-w-0">
                <div className={`font-medium truncate ${action.primary ? 'font-semibold' : ''}`}>
                  {action.label}
                </div>
                <div className="text-xs text-gray-500 mt-0.5 line-clamp-2">
                  {action.description}
                </div>
              </div>
              {action.primary && (
                <PlusIcon className="h-4 w-4 ml-2 text-blue-500 flex-shrink-0" />
              )}
            </button>
          ))}
        </div>

        {/* Cancel Button */}
        <div className="border-t border-gray-200">
          <button
            onClick={handleClose}
            className="w-full py-3 px-4 text-gray-600 font-medium hover:bg-gray-50 transition-colors"
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  )
}

export default CalendarActionSheet 