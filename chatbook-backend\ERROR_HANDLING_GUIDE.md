# Error Handling and Logging Guide

## Overview

This guide explains the comprehensive error handling and logging system implemented for the Django backend to provide production-grade error management and debugging capabilities.

## What Was Implemented

### 1. Enhanced Logging Configuration

**Location**: `settings.py` - LOGGING configuration

**Features**:
- **Multiple log levels**: DEBUG, INFO, WARNING, ERROR
- **Rotating file handlers**: Prevents log files from growing too large (50MB max, 5 backups)
- **Separate log files**:
  - `logs/debug.log` - All debug information and application logs
  - `logs/error.log` - Error-level logs only
  - `logs/requests.log` - All HTTP requests and responses
- **Structured logging**: Consistent format with timestamps, modules, and context
- **Environment-aware**: Different log levels for development vs production

### 2. Custom Error Handling Middleware

**Location**: `api/middleware/error_handling.py`

**Components**:

#### ErrorHandlingMiddleware
- Catches all unhandled exceptions
- Generates unique error IDs for tracking
- Logs full context (user, request details, stack trace)
- Returns structured JSON error responses
- Filters sensitive data from logs

#### RequestResponseLoggingMiddleware
- Logs all incoming requests
- Logs all outgoing responses
- Tracks response times and status codes
- Filters out static files and health checks

### 3. Custom DRF Exception Handler

**Location**: `api/exceptions.py`

**Features**:
- Standardized error response format
- User-friendly error messages
- Detailed logging with context
- Debug information in development mode
- Proper HTTP status codes

### 4. Enhanced File Upload Logging

**Location**: `api/views/files.py`

**Improvements**:
- Detailed logging at each step of file upload
- S3 operation tracking
- User context in all log messages
- Error tracking with unique IDs

## Error Response Format

All API errors now return a consistent format:

```json
{
  "error": true,
  "error_id": "7503c614",
  "timestamp": "2025-06-18T05:28:42.392457",
  "status_code": 401,
  "message": "Authentication credentials were not provided.",
  "details": {
    "detail": "Authentication credentials were not provided."
  },
  "debug_info": {
    "exception_type": "NotAuthenticated",
    "exception_message": "Authentication credentials were not provided.",
    "original_data": {...}
  }
}
```

## Benefits

### For Developers
1. **Detailed debugging**: Full stack traces and context in logs
2. **Request tracking**: Complete audit trail of all API calls
3. **Error correlation**: Unique error IDs to track issues across logs
4. **Performance monitoring**: Response times and status codes

### For Users
1. **Clear error messages**: User-friendly explanations instead of technical jargon
2. **Consistent format**: Predictable error structure for frontend handling
3. **Actionable feedback**: Specific guidance on how to resolve issues

### For Operations
1. **Production monitoring**: Structured logs for log aggregation tools
2. **Error tracking**: Unique IDs for support ticket correlation
3. **Security**: Sensitive data filtering in logs
4. **Scalability**: Rotating logs prevent disk space issues

## Log File Locations

```
logs/
├── debug.log      # All application logs (DEBUG level and above)
├── error.log      # Error-level logs only
└── requests.log   # HTTP request/response logs
```

## How to Use Error IDs

When users report issues:
1. Ask for the `error_id` from the error response
2. Search logs using: `grep "error_id" logs/*.log`
3. Find the full context and stack trace for debugging

## Frontend Error Handling Best Practices

### 1. Parse Error Responses
```javascript
try {
  const response = await fetch('/api/v1/files/upload/', options);
  const data = await response.json();
  
  if (!response.ok) {
    // Show user-friendly message
    showError(data.message || 'An error occurred');
    
    // Log error ID for support
    console.error('Error ID:', data.error_id);
  }
} catch (error) {
  showError('Network error. Please try again.');
}
```

### 2. Handle Specific Error Types
```javascript
if (data.status_code === 401) {
  // Redirect to login
  redirectToLogin();
} else if (data.status_code === 403) {
  // Show permission denied message
  showError('You do not have permission to perform this action');
} else if (data.status_code >= 500) {
  // Show generic server error
  showError('Server error. Please try again later.');
}
```

### 3. Display User-Friendly Messages
Instead of showing technical errors like:
- "The config profile (524752462460_SystemAdministrator) could not be found"

Show user-friendly messages like:
- "Upload failed. Please check your file and try again."
- "Authentication required. Please log in to continue."
- "Server error. Please try again later."

## Monitoring and Alerting

### Log Monitoring
- Set up log aggregation (ELK stack, Splunk, etc.)
- Monitor error rates and patterns
- Alert on critical errors (500 status codes)

### Key Metrics to Track
- Error rate by endpoint
- Response times
- Authentication failures
- File upload success/failure rates

## Security Considerations

### Sensitive Data Filtering
The middleware automatically filters sensitive fields from logs:
- Passwords
- Tokens
- API keys
- Session data
- CSRF tokens

### Error Information Disclosure
- Production mode hides technical details from users
- Debug information only shown in development
- Stack traces logged but not exposed to users

## Troubleshooting Common Issues

### 1. Authentication Errors (401)
- **User sees**: "Authentication credentials were not provided"
- **Developer checks**: JWT token validity, session status
- **Log location**: `logs/debug.log` with error ID

### 2. File Upload Errors
- **User sees**: "Upload failed. Please try again."
- **Developer checks**: S3 configuration, file size limits, permissions
- **Log location**: `logs/debug.log` with detailed S3 operation logs

### 3. Permission Errors (403)
- **User sees**: "You do not have permission to perform this action"
- **Developer checks**: User roles, access levels
- **Log location**: `logs/debug.log` with user context

## Next Steps

1. **Set up log rotation** in production
2. **Configure log aggregation** tools
3. **Set up monitoring alerts** for error rates
4. **Train support team** on using error IDs
5. **Update frontend** to handle new error format
6. **Add performance monitoring** for slow requests
