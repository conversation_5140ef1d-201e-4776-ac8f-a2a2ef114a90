import { getHistoryTypeIcon, downloadHistory, getStatusBadge } from '../../utils/appointmentUtils.jsx'

function ModificationHistoryModal({ 
  isOpen, 
  appointment, 
  customer, 
  onClose 
}) {
  if (!isOpen || !appointment) return null

  const handleDownload = () => {
    downloadHistory(appointment, customer)
  }

  const getStatusBadgeComponent = (status) => {
    const className = getStatusBadge(status)
    return (
      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${className}`}>
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </span>
    )
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Modal Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">Modification History</h3>
            <p className="text-sm text-gray-600 mt-1">
              Appointment #{appointment.id} - {appointment.service}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleDownload}
              className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors"
            >
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Download
            </button>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Modal Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-120px)]">
          {/* Appointment Summary */}
          <div className="bg-gray-50 rounded-lg p-4 mb-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div>
                <span className="font-medium text-gray-700">Date:</span>
                <p className="text-gray-900">{new Date(appointment.appointmentDate).toLocaleDateString()}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Service Provider:</span>
                <p className="text-gray-900">{appointment.serviceProvider}</p>
              </div>
              <div>
                <span className="font-medium text-gray-700">Status:</span>
                <div className="mt-1">{getStatusBadgeComponent(appointment.status)}</div>
              </div>
              <div>
                <span className="font-medium text-gray-700">Customer:</span>
                <p className="text-gray-900">{customer.name}</p>
              </div>
            </div>
          </div>

          {/* Timeline */}
          <div className="space-y-4">
            <h4 className="text-sm font-medium text-gray-700 uppercase tracking-wide">Activity Timeline</h4>
            <div className="flow-root">
              <ul className="-mb-8">
                {appointment.modificationHistory.map((entry, index) => (
                  <li key={entry.id}>
                    <div className="relative pb-8">
                      {index !== appointment.modificationHistory.length - 1 && (
                        <span className="absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200" aria-hidden="true" />
                      )}
                      <div className="relative flex items-start space-x-3">
                        <div className="relative">
                          <div className="h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center ring-8 ring-white">
                            {getHistoryTypeIcon(entry.type)}
                          </div>
                        </div>
                        <div className="min-w-0 flex-1">
                          <div className="text-sm">
                            <div className="flex items-center justify-between">
                              <span className="font-medium text-gray-900">{entry.action}</span>
                              <time className="text-xs text-gray-500">
                                {new Date(entry.timestamp).toLocaleString()}
                              </time>
                            </div>
                            <p className="mt-1 text-gray-600">{entry.details}</p>
                            <p className="mt-1 text-xs text-gray-500">by {entry.user}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </li>
                ))}
              </ul>
            </div>
          </div>


        </div>
      </div>
    </div>
  )
}

export default ModificationHistoryModal 