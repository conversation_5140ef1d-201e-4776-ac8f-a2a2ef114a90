import React from 'react'
import { UserIcon, WrenchScrewdriverIcon, PlusIcon, ClockIcon } from '@heroicons/react/24/outline'
import { cn } from '../../../../../utils'

/**
 * AppointmentZones - Handles rendering of service and buffer zones
 */
export const AppointmentZones = ({
  appointment,
  bufferLayout,
  getServiceZoneClasses,
  getBufferZoneClasses,
  contentClasses,
  statusIndicator,
  employeeStatusIndicator,
  dragIndicator,
  isHovered,
  dragState,
  resizeState,
  appointmentHeight,
  resizeTopRef,
  resizeBottomRef,
  handleResizeMouseDown
}) => {
  return (
    <>
      {/* SERVICE ZONE - Main appointment content with original styling */}
      <div 
        className={getServiceZoneClasses(bufferLayout)}
        style={{
          height: `${bufferLayout.serviceHeight}px`,
          minHeight: '30px',
          backgroundColor: 'inherit',
          borderColor: 'inherit',
        }}
      >
        <div className="space-y-0.5 pl-0 pr-1">
          <div className={cn(contentClasses.title, "leading-tight flex items-start gap-1")}>
            <UserIcon className="w-3 h-3 flex-shrink-0 mt-0.5" />
            {appointment.title || `${appointment.clientName || 'Customer'}`}
          </div>
          <div className={cn(contentClasses.client, "leading-tight flex items-start gap-1")}>
            <WrenchScrewdriverIcon className="w-3 h-3 flex-shrink-0 mt-0.5" />
            {appointment.serviceShortName || appointment.serviceName || appointment.location || 'Service'}
          </div>
          {/* Add-ons display - adaptive based on available space */}
          {appointment.addOns && appointment.addOns.length > 0 && (
            <>
              {/* Show add-on names when there's enough space */}
              {bufferLayout.serviceHeight > 55 && (
                <div className={cn(contentClasses.client, "leading-tight text-xs opacity-80 flex items-start gap-1")}>
                  <PlusIcon className="w-3 h-3 flex-shrink-0 mt-0.5" />
                  {appointment.addOns.map(addOn => addOn.add_on_short_name || addOn.add_on_name || addOn.name).join(', ')}
                </div>
              )}
              {/* Show add-on count when space is limited */}
              {bufferLayout.serviceHeight > 35 && bufferLayout.serviceHeight <= 55 && (
                <div className={cn(contentClasses.client, "leading-tight text-xs opacity-70 flex items-start gap-1")}>
                  <PlusIcon className="w-3 h-3 flex-shrink-0 mt-0.5" />
                  {appointment.addOns.length} add-on{appointment.addOns.length > 1 ? 's' : ''}
                </div>
              )}
            </>
          )}
          {bufferLayout.serviceHeight > 35 && (
            <div className={cn(contentClasses.duration, "leading-tight mt-0.5 flex items-start gap-1")}>
              <ClockIcon className="w-3 h-3 flex-shrink-0 mt-0.5" />
              {bufferLayout.totalDuration}min
            </div>
          )}
        </div>
      </div>

      {/* BUFFER ZONE - Visual indicator only */}
      {bufferLayout.hasBuffer && bufferLayout.bufferHeight > 15 && (
        <div 
          className={getBufferZoneClasses()}
          style={{
            height: `${bufferLayout.bufferHeight}px`,
            minHeight: '20px'
          }}
        />
      )}


      
      {/* Drag indicator */}
      {dragIndicator && (
        <div className={cn(
          dragIndicator.className, 
          "absolute inset-0 pointer-events-none z-20"
        )} />
      )}

      {/* Resize handles - positioned on the entire appointment */}
      {isHovered && !dragState.isDragging && !resizeState.isResizing && appointmentHeight > 30 && (
        <>
          <div
            ref={resizeTopRef}
            className={cn(
              "absolute top-0 left-0 right-0 h-2 z-30",
              "cursor-ns-resize",
              "hover:bg-blue-500 hover:bg-opacity-20",
              "transition-colors duration-200"
            )}
            onMouseDown={(e) => handleResizeMouseDown(e, 'start')}
          />
          
          <div
            ref={resizeBottomRef}
            className={cn(
              "absolute bottom-0 left-0 right-0 h-2 z-30",
              "cursor-ns-resize", 
              "hover:bg-blue-500 hover:bg-opacity-20",
              "transition-colors duration-200"
            )}
            onMouseDown={(e) => handleResizeMouseDown(e, 'end')}
          />
        </>
      )}
    </>
  )
} 