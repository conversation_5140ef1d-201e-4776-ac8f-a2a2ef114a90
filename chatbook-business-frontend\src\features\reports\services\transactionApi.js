// transactionApi.js - API service for transaction reports

const API_BASE_URL =
  import.meta.env.VITE_API_URL || "http://localhost:8000/api";

class TransactionApiService {
  constructor() {
    this.baseURL = API_BASE_URL;
  }

  // Helper method to get auth token
  getAuthToken() {
    return (
      localStorage.getItem("auth_token") || sessionStorage.getItem("auth_token")
    );
  }

  // Helper method to make API requests
  async makeRequest(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;

    const authToken = this.getAuthToken();
    const defaultOptions = {
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${authToken}`,
      },
      ...options,
    };

    try {
      const response = await fetch(url, defaultOptions);

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(
          errorData.message ||
            errorData.error ||
            `HTTP error! status: ${response.status}`
        );
      }

      return await response.json();
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);
      throw error;
    }
  }

  // ========== 交易报告 API ==========

  // 获取交易列表
  async getTransactions(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.startDate) queryParams.append("start_date", params.startDate);
    if (params.endDate) queryParams.append("end_date", params.endDate);
    if (params.serviceProvider && params.serviceProvider !== "all") {
      queryParams.append("service_provider", params.serviceProvider);
    }
    if (params.customer && params.customer !== "all") {
      queryParams.append("customer", params.customer);
    }
    if (params.page) queryParams.append("page", params.page);
    if (params.limit) queryParams.append("limit", params.limit);

    const queryString = queryParams.toString();
    const endpoint = `/reports/transactions/${
      queryString ? `?${queryString}` : ""
    }`;

    return await this.makeRequest(endpoint);
  }

  // 获取交易统计
  async getTransactionSummary(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.startDate) queryParams.append("start_date", params.startDate);
    if (params.endDate) queryParams.append("end_date", params.endDate);
    if (params.serviceProvider && params.serviceProvider !== "all") {
      queryParams.append("service_provider", params.serviceProvider);
    }
    if (params.customer && params.customer !== "all") {
      queryParams.append("customer", params.customer);
    }

    const queryString = queryParams.toString();
    const endpoint = `/reports/transactions/summary/${
      queryString ? `?${queryString}` : ""
    }`;

    return await this.makeRequest(endpoint);
  }

  // 获取交易详情
  async getTransactionDetails(transactionId) {
    return await this.makeRequest(`/transactions/${transactionId}/`);
  }

  // 导出交易报告
  async exportTransactions(params = {}, format = "csv") {
    const queryParams = new URLSearchParams();

    if (params.startDate) queryParams.append("start_date", params.startDate);
    if (params.endDate) queryParams.append("end_date", params.endDate);
    if (params.serviceProvider && params.serviceProvider !== "all") {
      queryParams.append("service_provider", params.serviceProvider);
    }
    if (params.customer && params.customer !== "all") {
      queryParams.append("customer", params.customer);
    }
    queryParams.append("format", format);

    const queryString = queryParams.toString();
    const authToken = this.getAuthToken();
    const url = `${this.baseURL}/reports/transactions/export/?${queryString}`;

    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to export transactions: ${response.status}`);
    }

    return response.blob();
  }

  // ========== 服务提供者和客户数据 ==========

  // 获取服务提供者列表（用于筛选）
  async getServiceProviders() {
    return await this.makeRequest("/staff/?active=true");
  }

  // 获取客户列表（用于筛选）
  async getCustomers(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.search) queryParams.append("search", params.search);
    if (params.limit) queryParams.append("limit", params.limit);

    const queryString = queryParams.toString();
    const endpoint = `/business-customers/me/${
      queryString ? `?${queryString}` : ""
    }`;

    return await this.makeRequest(endpoint);
  }

  // ========== 高级筛选 ==========

  // 获取高级筛选选项
  async getAdvancedFilters() {
    return await this.makeRequest("/reports/transactions/filters/");
  }

  // 应用高级筛选
  async getTransactionsWithAdvancedFilters(filters = {}) {
    return await this.makeRequest("/reports/transactions/advanced/", {
      method: "POST",
      body: JSON.stringify(filters),
    });
  }

  // ========== 收银机余额 ==========

  // 获取收银机余额
  async getDrawerBalance(date = null) {
    const params = date ? `?date=${date}` : "";
    return await this.makeRequest(`/reports/drawer-balance/${params}`);
  }

  // 获取收银机历史记录
  async getDrawerHistory(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.startDate) queryParams.append("start_date", params.startDate);
    if (params.endDate) queryParams.append("end_date", params.endDate);

    const queryString = queryParams.toString();
    const endpoint = `/reports/drawer-history/${
      queryString ? `?${queryString}` : ""
    }`;

    return await this.makeRequest(endpoint);
  }

  // ========== 销售统计 ==========

  // 获取销售统计
  async getSalesStats(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.startDate) queryParams.append("start_date", params.startDate);
    if (params.endDate) queryParams.append("end_date", params.endDate);
    if (params.groupBy) queryParams.append("group_by", params.groupBy); // day, week, month

    const queryString = queryParams.toString();
    const endpoint = `/reports/sales-stats/${
      queryString ? `?${queryString}` : ""
    }`;

    return await this.makeRequest(endpoint);
  }

  // 获取支付方式统计
  async getPaymentMethodStats(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.startDate) queryParams.append("start_date", params.startDate);
    if (params.endDate) queryParams.append("end_date", params.endDate);

    const queryString = queryParams.toString();
    const endpoint = `/reports/payment-methods/${
      queryString ? `?${queryString}` : ""
    }`;

    return await this.makeRequest(endpoint);
  }

  // 获取员工销售统计
  async getStaffSalesStats(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.startDate) queryParams.append("start_date", params.startDate);
    if (params.endDate) queryParams.append("end_date", params.endDate);
    if (params.staffId) queryParams.append("staff_id", params.staffId);

    const queryString = queryParams.toString();
    const endpoint = `/reports/staff-sales/${
      queryString ? `?${queryString}` : ""
    }`;

    return await this.makeRequest(endpoint);
  }

  // ========== 销售摘要 ==========

  // 获取销售摘要数据
  async getSalesSummary(params = {}) {
    const queryParams = new URLSearchParams();

    if (params.startDate) queryParams.append("start_date", params.startDate);
    if (params.endDate) queryParams.append("end_date", params.endDate);
    if (params.employee && params.employee !== "all") {
      queryParams.append("employee", params.employee);
    }
    if (params.customers && params.customers.length > 0) {
      queryParams.append("customers", params.customers.join(","));
    }
    if (params.serviceType && params.serviceType !== "all") {
      queryParams.append("service_type", params.serviceType);
    }
    if (params.paymentMethod && params.paymentMethod !== "all") {
      queryParams.append("payment_method", params.paymentMethod);
    }
    if (params.location && params.location !== "all") {
      queryParams.append("location", params.location);
    }
    if (params.productCategory && params.productCategory !== "all") {
      queryParams.append("product_category", params.productCategory);
    }
    if (params.minAmount) queryParams.append("min_amount", params.minAmount);
    if (params.maxAmount) queryParams.append("max_amount", params.maxAmount);
    if (params.includeRefunds) queryParams.append("include_refunds", "true");
    if (params.excludeTax) queryParams.append("exclude_tax", "true");

    const queryString = queryParams.toString();
    const endpoint = `/reports/sales-summary/${
      queryString ? `?${queryString}` : ""
    }`;

    return await this.makeRequest(endpoint);
  }

  // 导出销售摘要报告
  async exportSalesSummary(params = {}, format = "csv") {
    const queryParams = new URLSearchParams();

    if (params.startDate) queryParams.append("start_date", params.startDate);
    if (params.endDate) queryParams.append("end_date", params.endDate);
    if (params.employee && params.employee !== "all") {
      queryParams.append("employee", params.employee);
    }
    if (params.customers && params.customers.length > 0) {
      queryParams.append("customers", params.customers.join(","));
    }
    if (params.serviceType && params.serviceType !== "all") {
      queryParams.append("service_type", params.serviceType);
    }
    if (params.paymentMethod && params.paymentMethod !== "all") {
      queryParams.append("payment_method", params.paymentMethod);
    }
    if (params.location && params.location !== "all") {
      queryParams.append("location", params.location);
    }
    if (params.productCategory && params.productCategory !== "all") {
      queryParams.append("product_category", params.productCategory);
    }
    if (params.minAmount) queryParams.append("min_amount", params.minAmount);
    if (params.maxAmount) queryParams.append("max_amount", params.maxAmount);
    if (params.includeRefunds) queryParams.append("include_refunds", "true");
    if (params.excludeTax) queryParams.append("exclude_tax", "true");
    queryParams.append("format", format);

    const queryString = queryParams.toString();
    const authToken = this.getAuthToken();
    const url = `${this.baseURL}/reports/sales-summary/export/?${queryString}`;

    const response = await fetch(url, {
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to export sales summary: ${response.status}`);
    }

    return response.blob();
  }

  // ========== 报告生成 ==========

  // 生成日报
  async generateDailyReport(date) {
    return await this.makeRequest("/reports/daily/", {
      method: "POST",
      body: JSON.stringify({ date }),
    });
  }

  // 生成周报
  async generateWeeklyReport(startDate, endDate) {
    return await this.makeRequest("/reports/weekly/", {
      method: "POST",
      body: JSON.stringify({ start_date: startDate, end_date: endDate }),
    });
  }

  // 生成月报
  async generateMonthlyReport(month, year) {
    return await this.makeRequest("/reports/monthly/", {
      method: "POST",
      body: JSON.stringify({ month, year }),
    });
  }
}

// Export singleton instance
export const transactionApi = new TransactionApiService();

// Export individual methods for easier importing
export const {
  getTransactions,
  getTransactionSummary,
  getTransactionDetails,
  exportTransactions,
  getServiceProviders,
  getCustomers,
  getAdvancedFilters,
  getTransactionsWithAdvancedFilters,
  getDrawerBalance,
  getDrawerHistory,
  getSalesStats,
  getPaymentMethodStats,
  getStaffSalesStats,
  getSalesSummary,
  exportSalesSummary,
  generateDailyReport,
  generateWeeklyReport,
  generateMonthlyReport,
} = transactionApi;
