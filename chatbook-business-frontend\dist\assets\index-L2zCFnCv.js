function Rh(s,a){for(var o=0;o<a.length;o++){const i=a[o];if(typeof i!="string"&&!Array.isArray(i)){for(const u in i)if(u!=="default"&&!(u in s)){const m=Object.getOwnPropertyDescriptor(i,u);m&&Object.defineProperty(s,u,m.get?m:{enumerable:!0,get:()=>i[u]})}}}return Object.freeze(Object.defineProperty(s,Symbol.toStringTag,{value:"Module"}))}(function(){const a=document.createElement("link").relList;if(a&&a.supports&&a.supports("modulepreload"))return;for(const u of document.querySelectorAll('link[rel="modulepreload"]'))i(u);new MutationObserver(u=>{for(const m of u)if(m.type==="childList")for(const f of m.addedNodes)f.tagName==="LINK"&&f.rel==="modulepreload"&&i(f)}).observe(document,{childList:!0,subtree:!0});function o(u){const m={};return u.integrity&&(m.integrity=u.integrity),u.referrerPolicy&&(m.referrerPolicy=u.referrerPolicy),u.crossOrigin==="use-credentials"?m.credentials="include":u.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function i(u){if(u.ep)return;u.ep=!0;const m=o(u);fetch(u.href,m)}})();var av=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Ud(s){return s&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s}var Bl={exports:{}},os={},$l={exports:{}},ve={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var nd;function Ah(){if(nd)return ve;nd=1;var s=Symbol.for("react.element"),a=Symbol.for("react.portal"),o=Symbol.for("react.fragment"),i=Symbol.for("react.strict_mode"),u=Symbol.for("react.profiler"),m=Symbol.for("react.provider"),f=Symbol.for("react.context"),p=Symbol.for("react.forward_ref"),y=Symbol.for("react.suspense"),x=Symbol.for("react.memo"),g=Symbol.for("react.lazy"),v=Symbol.iterator;function j(T){return T===null||typeof T!="object"?null:(T=v&&T[v]||T["@@iterator"],typeof T=="function"?T:null)}var P={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},k=Object.assign,E={};function S(T,$,xe){this.props=T,this.context=$,this.refs=E,this.updater=xe||P}S.prototype.isReactComponent={},S.prototype.setState=function(T,$){if(typeof T!="object"&&typeof T!="function"&&T!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,T,$,"setState")},S.prototype.forceUpdate=function(T){this.updater.enqueueForceUpdate(this,T,"forceUpdate")};function _(){}_.prototype=S.prototype;function B(T,$,xe){this.props=T,this.context=$,this.refs=E,this.updater=xe||P}var H=B.prototype=new _;H.constructor=B,k(H,S.prototype),H.isPureReactComponent=!0;var F=Array.isArray,I=Object.prototype.hasOwnProperty,z={current:null},U={key:!0,ref:!0,__self:!0,__source:!0};function oe(T,$,xe){var pe,je={},be=null,De=null;if($!=null)for(pe in $.ref!==void 0&&(De=$.ref),$.key!==void 0&&(be=""+$.key),$)I.call($,pe)&&!U.hasOwnProperty(pe)&&(je[pe]=$[pe]);var Se=arguments.length-2;if(Se===1)je.children=xe;else if(1<Se){for(var Te=Array(Se),nt=0;nt<Se;nt++)Te[nt]=arguments[nt+2];je.children=Te}if(T&&T.defaultProps)for(pe in Se=T.defaultProps,Se)je[pe]===void 0&&(je[pe]=Se[pe]);return{$$typeof:s,type:T,key:be,ref:De,props:je,_owner:z.current}}function fe(T,$){return{$$typeof:s,type:T.type,key:$,ref:T.ref,props:T.props,_owner:T._owner}}function Z(T){return typeof T=="object"&&T!==null&&T.$$typeof===s}function re(T){var $={"=":"=0",":":"=2"};return"$"+T.replace(/[=:]/g,function(xe){return $[xe]})}var de=/\/+/g;function ie(T,$){return typeof T=="object"&&T!==null&&T.key!=null?re(""+T.key):$.toString(36)}function J(T,$,xe,pe,je){var be=typeof T;(be==="undefined"||be==="boolean")&&(T=null);var De=!1;if(T===null)De=!0;else switch(be){case"string":case"number":De=!0;break;case"object":switch(T.$$typeof){case s:case a:De=!0}}if(De)return De=T,je=je(De),T=pe===""?"."+ie(De,0):pe,F(je)?(xe="",T!=null&&(xe=T.replace(de,"$&/")+"/"),J(je,$,xe,"",function(nt){return nt})):je!=null&&(Z(je)&&(je=fe(je,xe+(!je.key||De&&De.key===je.key?"":(""+je.key).replace(de,"$&/")+"/")+T)),$.push(je)),1;if(De=0,pe=pe===""?".":pe+":",F(T))for(var Se=0;Se<T.length;Se++){be=T[Se];var Te=pe+ie(be,Se);De+=J(be,$,xe,Te,je)}else if(Te=j(T),typeof Te=="function")for(T=Te.call(T),Se=0;!(be=T.next()).done;)be=be.value,Te=pe+ie(be,Se++),De+=J(be,$,xe,Te,je);else if(be==="object")throw $=String(T),Error("Objects are not valid as a React child (found: "+($==="[object Object]"?"object with keys {"+Object.keys(T).join(", ")+"}":$)+"). If you meant to render a collection of children, use an array instead.");return De}function D(T,$,xe){if(T==null)return T;var pe=[],je=0;return J(T,pe,"","",function(be){return $.call(xe,be,je++)}),pe}function q(T){if(T._status===-1){var $=T._result;$=$(),$.then(function(xe){(T._status===0||T._status===-1)&&(T._status=1,T._result=xe)},function(xe){(T._status===0||T._status===-1)&&(T._status=2,T._result=xe)}),T._status===-1&&(T._status=0,T._result=$)}if(T._status===1)return T._result.default;throw T._result}var me={current:null},Y={transition:null},ee={ReactCurrentDispatcher:me,ReactCurrentBatchConfig:Y,ReactCurrentOwner:z};function X(){throw Error("act(...) is not supported in production builds of React.")}return ve.Children={map:D,forEach:function(T,$,xe){D(T,function(){$.apply(this,arguments)},xe)},count:function(T){var $=0;return D(T,function(){$++}),$},toArray:function(T){return D(T,function($){return $})||[]},only:function(T){if(!Z(T))throw Error("React.Children.only expected to receive a single React element child.");return T}},ve.Component=S,ve.Fragment=o,ve.Profiler=u,ve.PureComponent=B,ve.StrictMode=i,ve.Suspense=y,ve.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ee,ve.act=X,ve.cloneElement=function(T,$,xe){if(T==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+T+".");var pe=k({},T.props),je=T.key,be=T.ref,De=T._owner;if($!=null){if($.ref!==void 0&&(be=$.ref,De=z.current),$.key!==void 0&&(je=""+$.key),T.type&&T.type.defaultProps)var Se=T.type.defaultProps;for(Te in $)I.call($,Te)&&!U.hasOwnProperty(Te)&&(pe[Te]=$[Te]===void 0&&Se!==void 0?Se[Te]:$[Te])}var Te=arguments.length-2;if(Te===1)pe.children=xe;else if(1<Te){Se=Array(Te);for(var nt=0;nt<Te;nt++)Se[nt]=arguments[nt+2];pe.children=Se}return{$$typeof:s,type:T.type,key:je,ref:be,props:pe,_owner:De}},ve.createContext=function(T){return T={$$typeof:f,_currentValue:T,_currentValue2:T,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},T.Provider={$$typeof:m,_context:T},T.Consumer=T},ve.createElement=oe,ve.createFactory=function(T){var $=oe.bind(null,T);return $.type=T,$},ve.createRef=function(){return{current:null}},ve.forwardRef=function(T){return{$$typeof:p,render:T}},ve.isValidElement=Z,ve.lazy=function(T){return{$$typeof:g,_payload:{_status:-1,_result:T},_init:q}},ve.memo=function(T,$){return{$$typeof:x,type:T,compare:$===void 0?null:$}},ve.startTransition=function(T){var $=Y.transition;Y.transition={};try{T()}finally{Y.transition=$}},ve.unstable_act=X,ve.useCallback=function(T,$){return me.current.useCallback(T,$)},ve.useContext=function(T){return me.current.useContext(T)},ve.useDebugValue=function(){},ve.useDeferredValue=function(T){return me.current.useDeferredValue(T)},ve.useEffect=function(T,$){return me.current.useEffect(T,$)},ve.useId=function(){return me.current.useId()},ve.useImperativeHandle=function(T,$,xe){return me.current.useImperativeHandle(T,$,xe)},ve.useInsertionEffect=function(T,$){return me.current.useInsertionEffect(T,$)},ve.useLayoutEffect=function(T,$){return me.current.useLayoutEffect(T,$)},ve.useMemo=function(T,$){return me.current.useMemo(T,$)},ve.useReducer=function(T,$,xe){return me.current.useReducer(T,$,xe)},ve.useRef=function(T){return me.current.useRef(T)},ve.useState=function(T){return me.current.useState(T)},ve.useSyncExternalStore=function(T,$,xe){return me.current.useSyncExternalStore(T,$,xe)},ve.useTransition=function(){return me.current.useTransition()},ve.version="18.3.1",ve}var sd;function oi(){return sd||(sd=1,$l.exports=Ah()),$l.exports}/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ad;function _h(){if(ad)return os;ad=1;var s=oi(),a=Symbol.for("react.element"),o=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,u=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,m={key:!0,ref:!0,__self:!0,__source:!0};function f(p,y,x){var g,v={},j=null,P=null;x!==void 0&&(j=""+x),y.key!==void 0&&(j=""+y.key),y.ref!==void 0&&(P=y.ref);for(g in y)i.call(y,g)&&!m.hasOwnProperty(g)&&(v[g]=y[g]);if(p&&p.defaultProps)for(g in y=p.defaultProps,y)v[g]===void 0&&(v[g]=y[g]);return{$$typeof:a,type:p,key:j,ref:P,props:v,_owner:u.current}}return os.Fragment=o,os.jsx=f,os.jsxs=f,os}var od;function Fh(){return od||(od=1,Bl.exports=_h()),Bl.exports}var r=Fh(),b=oi();const Wd=Ud(b),zh=Rh({__proto__:null,default:Wd},[b]);var Ea={},Ul={exports:{}},mt={},Wl={exports:{}},Hl={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ld;function Ih(){return ld||(ld=1,function(s){function a(Y,ee){var X=Y.length;Y.push(ee);e:for(;0<X;){var T=X-1>>>1,$=Y[T];if(0<u($,ee))Y[T]=ee,Y[X]=$,X=T;else break e}}function o(Y){return Y.length===0?null:Y[0]}function i(Y){if(Y.length===0)return null;var ee=Y[0],X=Y.pop();if(X!==ee){Y[0]=X;e:for(var T=0,$=Y.length,xe=$>>>1;T<xe;){var pe=2*(T+1)-1,je=Y[pe],be=pe+1,De=Y[be];if(0>u(je,X))be<$&&0>u(De,je)?(Y[T]=De,Y[be]=X,T=be):(Y[T]=je,Y[pe]=X,T=pe);else if(be<$&&0>u(De,X))Y[T]=De,Y[be]=X,T=be;else break e}}return ee}function u(Y,ee){var X=Y.sortIndex-ee.sortIndex;return X!==0?X:Y.id-ee.id}if(typeof performance=="object"&&typeof performance.now=="function"){var m=performance;s.unstable_now=function(){return m.now()}}else{var f=Date,p=f.now();s.unstable_now=function(){return f.now()-p}}var y=[],x=[],g=1,v=null,j=3,P=!1,k=!1,E=!1,S=typeof setTimeout=="function"?setTimeout:null,_=typeof clearTimeout=="function"?clearTimeout:null,B=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function H(Y){for(var ee=o(x);ee!==null;){if(ee.callback===null)i(x);else if(ee.startTime<=Y)i(x),ee.sortIndex=ee.expirationTime,a(y,ee);else break;ee=o(x)}}function F(Y){if(E=!1,H(Y),!k)if(o(y)!==null)k=!0,q(I);else{var ee=o(x);ee!==null&&me(F,ee.startTime-Y)}}function I(Y,ee){k=!1,E&&(E=!1,_(oe),oe=-1),P=!0;var X=j;try{for(H(ee),v=o(y);v!==null&&(!(v.expirationTime>ee)||Y&&!re());){var T=v.callback;if(typeof T=="function"){v.callback=null,j=v.priorityLevel;var $=T(v.expirationTime<=ee);ee=s.unstable_now(),typeof $=="function"?v.callback=$:v===o(y)&&i(y),H(ee)}else i(y);v=o(y)}if(v!==null)var xe=!0;else{var pe=o(x);pe!==null&&me(F,pe.startTime-ee),xe=!1}return xe}finally{v=null,j=X,P=!1}}var z=!1,U=null,oe=-1,fe=5,Z=-1;function re(){return!(s.unstable_now()-Z<fe)}function de(){if(U!==null){var Y=s.unstable_now();Z=Y;var ee=!0;try{ee=U(!0,Y)}finally{ee?ie():(z=!1,U=null)}}else z=!1}var ie;if(typeof B=="function")ie=function(){B(de)};else if(typeof MessageChannel<"u"){var J=new MessageChannel,D=J.port2;J.port1.onmessage=de,ie=function(){D.postMessage(null)}}else ie=function(){S(de,0)};function q(Y){U=Y,z||(z=!0,ie())}function me(Y,ee){oe=S(function(){Y(s.unstable_now())},ee)}s.unstable_IdlePriority=5,s.unstable_ImmediatePriority=1,s.unstable_LowPriority=4,s.unstable_NormalPriority=3,s.unstable_Profiling=null,s.unstable_UserBlockingPriority=2,s.unstable_cancelCallback=function(Y){Y.callback=null},s.unstable_continueExecution=function(){k||P||(k=!0,q(I))},s.unstable_forceFrameRate=function(Y){0>Y||125<Y?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):fe=0<Y?Math.floor(1e3/Y):5},s.unstable_getCurrentPriorityLevel=function(){return j},s.unstable_getFirstCallbackNode=function(){return o(y)},s.unstable_next=function(Y){switch(j){case 1:case 2:case 3:var ee=3;break;default:ee=j}var X=j;j=ee;try{return Y()}finally{j=X}},s.unstable_pauseExecution=function(){},s.unstable_requestPaint=function(){},s.unstable_runWithPriority=function(Y,ee){switch(Y){case 1:case 2:case 3:case 4:case 5:break;default:Y=3}var X=j;j=Y;try{return ee()}finally{j=X}},s.unstable_scheduleCallback=function(Y,ee,X){var T=s.unstable_now();switch(typeof X=="object"&&X!==null?(X=X.delay,X=typeof X=="number"&&0<X?T+X:T):X=T,Y){case 1:var $=-1;break;case 2:$=250;break;case 5:$=**********;break;case 4:$=1e4;break;default:$=5e3}return $=X+$,Y={id:g++,callback:ee,priorityLevel:Y,startTime:X,expirationTime:$,sortIndex:-1},X>T?(Y.sortIndex=X,a(x,Y),o(y)===null&&Y===o(x)&&(E?(_(oe),oe=-1):E=!0,me(F,X-T))):(Y.sortIndex=$,a(y,Y),k||P||(k=!0,q(I))),Y},s.unstable_shouldYield=re,s.unstable_wrapCallback=function(Y){var ee=j;return function(){var X=j;j=ee;try{return Y.apply(this,arguments)}finally{j=X}}}}(Hl)),Hl}var id;function Bh(){return id||(id=1,Wl.exports=Ih()),Wl.exports}/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var cd;function $h(){if(cd)return mt;cd=1;var s=oi(),a=Bh();function o(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var i=new Set,u={};function m(e,t){f(e,t),f(e+"Capture",t)}function f(e,t){for(u[e]=t,e=0;e<t.length;e++)i.add(t[e])}var p=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),y=Object.prototype.hasOwnProperty,x=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,g={},v={};function j(e){return y.call(v,e)?!0:y.call(g,e)?!1:x.test(e)?v[e]=!0:(g[e]=!0,!1)}function P(e,t,n,l){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return l?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function k(e,t,n,l){if(t===null||typeof t>"u"||P(e,t,n,l))return!0;if(l)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function E(e,t,n,l,c,d,h){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=l,this.attributeNamespace=c,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=d,this.removeEmptyString=h}var S={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){S[e]=new E(e,0,!1,e,null,!1,!1)}),[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];S[t]=new E(t,1,!1,e[1],null,!1,!1)}),["contentEditable","draggable","spellCheck","value"].forEach(function(e){S[e]=new E(e,2,!1,e.toLowerCase(),null,!1,!1)}),["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){S[e]=new E(e,2,!1,e,null,!1,!1)}),"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){S[e]=new E(e,3,!1,e.toLowerCase(),null,!1,!1)}),["checked","multiple","muted","selected"].forEach(function(e){S[e]=new E(e,3,!0,e,null,!1,!1)}),["capture","download"].forEach(function(e){S[e]=new E(e,4,!1,e,null,!1,!1)}),["cols","rows","size","span"].forEach(function(e){S[e]=new E(e,6,!1,e,null,!1,!1)}),["rowSpan","start"].forEach(function(e){S[e]=new E(e,5,!1,e.toLowerCase(),null,!1,!1)});var _=/[\-:]([a-z])/g;function B(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(_,B);S[t]=new E(t,1,!1,e,null,!1,!1)}),"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(_,B);S[t]=new E(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)}),["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(_,B);S[t]=new E(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)}),["tabIndex","crossOrigin"].forEach(function(e){S[e]=new E(e,1,!1,e.toLowerCase(),null,!1,!1)}),S.xlinkHref=new E("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1),["src","href","action","formAction"].forEach(function(e){S[e]=new E(e,1,!1,e.toLowerCase(),null,!0,!0)});function H(e,t,n,l){var c=S.hasOwnProperty(t)?S[t]:null;(c!==null?c.type!==0:l||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(k(t,n,c,l)&&(n=null),l||c===null?j(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):c.mustUseProperty?e[c.propertyName]=n===null?c.type===3?!1:"":n:(t=c.attributeName,l=c.attributeNamespace,n===null?e.removeAttribute(t):(c=c.type,n=c===3||c===4&&n===!0?"":""+n,l?e.setAttributeNS(l,t,n):e.setAttribute(t,n))))}var F=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,I=Symbol.for("react.element"),z=Symbol.for("react.portal"),U=Symbol.for("react.fragment"),oe=Symbol.for("react.strict_mode"),fe=Symbol.for("react.profiler"),Z=Symbol.for("react.provider"),re=Symbol.for("react.context"),de=Symbol.for("react.forward_ref"),ie=Symbol.for("react.suspense"),J=Symbol.for("react.suspense_list"),D=Symbol.for("react.memo"),q=Symbol.for("react.lazy"),me=Symbol.for("react.offscreen"),Y=Symbol.iterator;function ee(e){return e===null||typeof e!="object"?null:(e=Y&&e[Y]||e["@@iterator"],typeof e=="function"?e:null)}var X=Object.assign,T;function $(e){if(T===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);T=t&&t[1]||""}return`
`+T+e}var xe=!1;function pe(e,t){if(!e||xe)return"";xe=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(M){var l=M}Reflect.construct(e,[],t)}else{try{t.call()}catch(M){l=M}e.call(t.prototype)}else{try{throw Error()}catch(M){l=M}e()}}catch(M){if(M&&l&&typeof M.stack=="string"){for(var c=M.stack.split(`
`),d=l.stack.split(`
`),h=c.length-1,w=d.length-1;1<=h&&0<=w&&c[h]!==d[w];)w--;for(;1<=h&&0<=w;h--,w--)if(c[h]!==d[w]){if(h!==1||w!==1)do if(h--,w--,0>w||c[h]!==d[w]){var N=`
`+c[h].replace(" at new "," at ");return e.displayName&&N.includes("<anonymous>")&&(N=N.replace("<anonymous>",e.displayName)),N}while(1<=h&&0<=w);break}}}finally{xe=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?$(e):""}function je(e){switch(e.tag){case 5:return $(e.type);case 16:return $("Lazy");case 13:return $("Suspense");case 19:return $("SuspenseList");case 0:case 2:case 15:return e=pe(e.type,!1),e;case 11:return e=pe(e.type.render,!1),e;case 1:return e=pe(e.type,!0),e;default:return""}}function be(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case U:return"Fragment";case z:return"Portal";case fe:return"Profiler";case oe:return"StrictMode";case ie:return"Suspense";case J:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case re:return(e.displayName||"Context")+".Consumer";case Z:return(e._context.displayName||"Context")+".Provider";case de:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case D:return t=e.displayName||null,t!==null?t:be(e.type)||"Memo";case q:t=e._payload,e=e._init;try{return be(e(t))}catch{}}return null}function De(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return be(t);case 8:return t===oe?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function Se(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Te(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function nt(e){var t=Te(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var c=n.get,d=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return c.call(this)},set:function(h){l=""+h,d.call(this,h)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return l},setValue:function(h){l=""+h},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function Wr(e){e._valueTracker||(e._valueTracker=nt(e))}function ys(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),l="";return e&&(l=Te(e)?e.checked?"true":"false":e.value),e=l,e!==n?(t.setValue(e),!0):!1}function Hr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function A(e,t){var n=t.checked;return X({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function le(e,t){var n=t.defaultValue==null?"":t.defaultValue,l=t.checked!=null?t.checked:t.defaultChecked;n=Se(t.value!=null?t.value:n),e._wrapperState={initialChecked:l,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function we(e,t){t=t.checked,t!=null&&H(e,"checked",t,!1)}function ge(e,t){we(e,t);var n=Se(t.value),l=t.type;if(n!=null)l==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(l==="submit"||l==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?Me(e,t.type,n):t.hasOwnProperty("defaultValue")&&Me(e,t.type,Se(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function Le(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var l=t.type;if(!(l!=="submit"&&l!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function Me(e,t,n){(t!=="number"||Hr(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var At=Array.isArray;function er(e,t,n,l){if(e=e.options,t){t={};for(var c=0;c<n.length;c++)t["$"+n[c]]=!0;for(n=0;n<e.length;n++)c=t.hasOwnProperty("$"+e[n].value),e[n].selected!==c&&(e[n].selected=c),c&&l&&(e[n].defaultSelected=!0)}else{for(n=""+Se(n),t=null,c=0;c<e.length;c++){if(e[c].value===n){e[c].selected=!0,l&&(e[c].defaultSelected=!0);return}t!==null||e[c].disabled||(t=e[c])}t!==null&&(t.selected=!0)}}function kr(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(o(91));return X({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function fi(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(o(92));if(At(n)){if(1<n.length)throw Error(o(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:Se(n)}}function hi(e,t){var n=Se(t.value),l=Se(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),l!=null&&(e.defaultValue=""+l)}function pi(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function xi(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Ka(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?xi(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var vs,gi=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,l,c){MSApp.execUnsafeLocalFunction(function(){return e(t,n,l,c)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(vs=vs||document.createElement("div"),vs.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=vs.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function bn(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var Nn={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},Fm=["Webkit","ms","Moz","O"];Object.keys(Nn).forEach(function(e){Fm.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),Nn[t]=Nn[e]})});function yi(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||Nn.hasOwnProperty(e)&&Nn[e]?(""+t).trim():t+"px"}function vi(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var l=n.indexOf("--")===0,c=yi(n,t[n],l);n==="float"&&(n="cssFloat"),l?e.setProperty(n,c):e[n]=c}}var zm=X({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function Xa(e,t){if(t){if(zm[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(o(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(o(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(o(61))}if(t.style!=null&&typeof t.style!="object")throw Error(o(62))}}function Ga(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Za=null;function eo(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var to=null,Vr=null,Qr=null;function wi(e){if(e=Vn(e)){if(typeof to!="function")throw Error(o(280));var t=e.stateNode;t&&(t=Us(t),to(e.stateNode,e.type,t))}}function ji(e){Vr?Qr?Qr.push(e):Qr=[e]:Vr=e}function bi(){if(Vr){var e=Vr,t=Qr;if(Qr=Vr=null,wi(e),t)for(e=0;e<t.length;e++)wi(t[e])}}function Ni(e,t){return e(t)}function ki(){}var ro=!1;function Si(e,t,n){if(ro)return e(t,n);ro=!0;try{return Ni(e,t,n)}finally{ro=!1,(Vr!==null||Qr!==null)&&(ki(),bi())}}function kn(e,t){var n=e.stateNode;if(n===null)return null;var l=Us(n);if(l===null)return null;n=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(o(231,t,typeof n));return n}var no=!1;if(p)try{var Sn={};Object.defineProperty(Sn,"passive",{get:function(){no=!0}}),window.addEventListener("test",Sn,Sn),window.removeEventListener("test",Sn,Sn)}catch{no=!1}function Im(e,t,n,l,c,d,h,w,N){var M=Array.prototype.slice.call(arguments,3);try{t.apply(n,M)}catch(V){this.onError(V)}}var Cn=!1,ws=null,js=!1,so=null,Bm={onError:function(e){Cn=!0,ws=e}};function $m(e,t,n,l,c,d,h,w,N){Cn=!1,ws=null,Im.apply(Bm,arguments)}function Um(e,t,n,l,c,d,h,w,N){if($m.apply(this,arguments),Cn){if(Cn){var M=ws;Cn=!1,ws=null}else throw Error(o(198));js||(js=!0,so=M)}}function Sr(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Ci(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ei(e){if(Sr(e)!==e)throw Error(o(188))}function Wm(e){var t=e.alternate;if(!t){if(t=Sr(e),t===null)throw Error(o(188));return t!==e?null:e}for(var n=e,l=t;;){var c=n.return;if(c===null)break;var d=c.alternate;if(d===null){if(l=c.return,l!==null){n=l;continue}break}if(c.child===d.child){for(d=c.child;d;){if(d===n)return Ei(c),e;if(d===l)return Ei(c),t;d=d.sibling}throw Error(o(188))}if(n.return!==l.return)n=c,l=d;else{for(var h=!1,w=c.child;w;){if(w===n){h=!0,n=c,l=d;break}if(w===l){h=!0,l=c,n=d;break}w=w.sibling}if(!h){for(w=d.child;w;){if(w===n){h=!0,n=d,l=c;break}if(w===l){h=!0,l=d,n=c;break}w=w.sibling}if(!h)throw Error(o(189))}}if(n.alternate!==l)throw Error(o(190))}if(n.tag!==3)throw Error(o(188));return n.stateNode.current===n?e:t}function Pi(e){return e=Wm(e),e!==null?Di(e):null}function Di(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Di(e);if(t!==null)return t;e=e.sibling}return null}var Ti=a.unstable_scheduleCallback,Li=a.unstable_cancelCallback,Hm=a.unstable_shouldYield,Vm=a.unstable_requestPaint,Ue=a.unstable_now,Qm=a.unstable_getCurrentPriorityLevel,ao=a.unstable_ImmediatePriority,Oi=a.unstable_UserBlockingPriority,bs=a.unstable_NormalPriority,Ym=a.unstable_LowPriority,Mi=a.unstable_IdlePriority,Ns=null,_t=null;function Jm(e){if(_t&&typeof _t.onCommitFiberRoot=="function")try{_t.onCommitFiberRoot(Ns,e,void 0,(e.current.flags&128)===128)}catch{}}var St=Math.clz32?Math.clz32:Xm,qm=Math.log,Km=Math.LN2;function Xm(e){return e>>>=0,e===0?32:31-(qm(e)/Km|0)|0}var ks=64,Ss=4194304;function En(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function Cs(e,t){var n=e.pendingLanes;if(n===0)return 0;var l=0,c=e.suspendedLanes,d=e.pingedLanes,h=n&268435455;if(h!==0){var w=h&~c;w!==0?l=En(w):(d&=h,d!==0&&(l=En(d)))}else h=n&~c,h!==0?l=En(h):d!==0&&(l=En(d));if(l===0)return 0;if(t!==0&&t!==l&&(t&c)===0&&(c=l&-l,d=t&-t,c>=d||c===16&&(d&4194240)!==0))return t;if((l&4)!==0&&(l|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=l;0<t;)n=31-St(t),c=1<<n,l|=e[n],t&=~c;return l}function Gm(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Zm(e,t){for(var n=e.suspendedLanes,l=e.pingedLanes,c=e.expirationTimes,d=e.pendingLanes;0<d;){var h=31-St(d),w=1<<h,N=c[h];N===-1?((w&n)===0||(w&l)!==0)&&(c[h]=Gm(w,t)):N<=t&&(e.expiredLanes|=w),d&=~w}}function oo(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Ri(){var e=ks;return ks<<=1,(ks&4194240)===0&&(ks=64),e}function lo(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function Pn(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-St(t),e[t]=n}function ef(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var l=e.eventTimes;for(e=e.expirationTimes;0<n;){var c=31-St(n),d=1<<c;t[c]=0,l[c]=-1,e[c]=-1,n&=~d}}function io(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var l=31-St(n),c=1<<l;c&t|e[l]&t&&(e[l]|=t),n&=~c}}var Ee=0;function Ai(e){return e&=-e,1<e?4<e?(e&268435455)!==0?16:536870912:4:1}var _i,co,Fi,zi,Ii,uo=!1,Es=[],tr=null,rr=null,nr=null,Dn=new Map,Tn=new Map,sr=[],tf="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function Bi(e,t){switch(e){case"focusin":case"focusout":tr=null;break;case"dragenter":case"dragleave":rr=null;break;case"mouseover":case"mouseout":nr=null;break;case"pointerover":case"pointerout":Dn.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Tn.delete(t.pointerId)}}function Ln(e,t,n,l,c,d){return e===null||e.nativeEvent!==d?(e={blockedOn:t,domEventName:n,eventSystemFlags:l,nativeEvent:d,targetContainers:[c]},t!==null&&(t=Vn(t),t!==null&&co(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,c!==null&&t.indexOf(c)===-1&&t.push(c),e)}function rf(e,t,n,l,c){switch(t){case"focusin":return tr=Ln(tr,e,t,n,l,c),!0;case"dragenter":return rr=Ln(rr,e,t,n,l,c),!0;case"mouseover":return nr=Ln(nr,e,t,n,l,c),!0;case"pointerover":var d=c.pointerId;return Dn.set(d,Ln(Dn.get(d)||null,e,t,n,l,c)),!0;case"gotpointercapture":return d=c.pointerId,Tn.set(d,Ln(Tn.get(d)||null,e,t,n,l,c)),!0}return!1}function $i(e){var t=Cr(e.target);if(t!==null){var n=Sr(t);if(n!==null){if(t=n.tag,t===13){if(t=Ci(n),t!==null){e.blockedOn=t,Ii(e.priority,function(){Fi(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Ps(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=fo(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var l=new n.constructor(n.type,n);Za=l,n.target.dispatchEvent(l),Za=null}else return t=Vn(n),t!==null&&co(t),e.blockedOn=n,!1;t.shift()}return!0}function Ui(e,t,n){Ps(e)&&n.delete(t)}function nf(){uo=!1,tr!==null&&Ps(tr)&&(tr=null),rr!==null&&Ps(rr)&&(rr=null),nr!==null&&Ps(nr)&&(nr=null),Dn.forEach(Ui),Tn.forEach(Ui)}function On(e,t){e.blockedOn===t&&(e.blockedOn=null,uo||(uo=!0,a.unstable_scheduleCallback(a.unstable_NormalPriority,nf)))}function Mn(e){function t(c){return On(c,e)}if(0<Es.length){On(Es[0],e);for(var n=1;n<Es.length;n++){var l=Es[n];l.blockedOn===e&&(l.blockedOn=null)}}for(tr!==null&&On(tr,e),rr!==null&&On(rr,e),nr!==null&&On(nr,e),Dn.forEach(t),Tn.forEach(t),n=0;n<sr.length;n++)l=sr[n],l.blockedOn===e&&(l.blockedOn=null);for(;0<sr.length&&(n=sr[0],n.blockedOn===null);)$i(n),n.blockedOn===null&&sr.shift()}var Yr=F.ReactCurrentBatchConfig,Ds=!0;function sf(e,t,n,l){var c=Ee,d=Yr.transition;Yr.transition=null;try{Ee=1,mo(e,t,n,l)}finally{Ee=c,Yr.transition=d}}function af(e,t,n,l){var c=Ee,d=Yr.transition;Yr.transition=null;try{Ee=4,mo(e,t,n,l)}finally{Ee=c,Yr.transition=d}}function mo(e,t,n,l){if(Ds){var c=fo(e,t,n,l);if(c===null)To(e,t,l,Ts,n),Bi(e,l);else if(rf(c,e,t,n,l))l.stopPropagation();else if(Bi(e,l),t&4&&-1<tf.indexOf(e)){for(;c!==null;){var d=Vn(c);if(d!==null&&_i(d),d=fo(e,t,n,l),d===null&&To(e,t,l,Ts,n),d===c)break;c=d}c!==null&&l.stopPropagation()}else To(e,t,l,null,n)}}var Ts=null;function fo(e,t,n,l){if(Ts=null,e=eo(l),e=Cr(e),e!==null)if(t=Sr(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Ci(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return Ts=e,null}function Wi(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(Qm()){case ao:return 1;case Oi:return 4;case bs:case Ym:return 16;case Mi:return 536870912;default:return 16}default:return 16}}var ar=null,ho=null,Ls=null;function Hi(){if(Ls)return Ls;var e,t=ho,n=t.length,l,c="value"in ar?ar.value:ar.textContent,d=c.length;for(e=0;e<n&&t[e]===c[e];e++);var h=n-e;for(l=1;l<=h&&t[n-l]===c[d-l];l++);return Ls=c.slice(e,1<l?1-l:void 0)}function Os(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Ms(){return!0}function Vi(){return!1}function pt(e){function t(n,l,c,d,h){this._reactName=n,this._targetInst=c,this.type=l,this.nativeEvent=d,this.target=h,this.currentTarget=null;for(var w in e)e.hasOwnProperty(w)&&(n=e[w],this[w]=n?n(d):d[w]);return this.isDefaultPrevented=(d.defaultPrevented!=null?d.defaultPrevented:d.returnValue===!1)?Ms:Vi,this.isPropagationStopped=Vi,this}return X(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=Ms)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=Ms)},persist:function(){},isPersistent:Ms}),t}var Jr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},po=pt(Jr),Rn=X({},Jr,{view:0,detail:0}),of=pt(Rn),xo,go,An,Rs=X({},Rn,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:vo,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==An&&(An&&e.type==="mousemove"?(xo=e.screenX-An.screenX,go=e.screenY-An.screenY):go=xo=0,An=e),xo)},movementY:function(e){return"movementY"in e?e.movementY:go}}),Qi=pt(Rs),lf=X({},Rs,{dataTransfer:0}),cf=pt(lf),uf=X({},Rn,{relatedTarget:0}),yo=pt(uf),df=X({},Jr,{animationName:0,elapsedTime:0,pseudoElement:0}),mf=pt(df),ff=X({},Jr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),hf=pt(ff),pf=X({},Jr,{data:0}),Yi=pt(pf),xf={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},gf={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},yf={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function vf(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=yf[e])?!!t[e]:!1}function vo(){return vf}var wf=X({},Rn,{key:function(e){if(e.key){var t=xf[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Os(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?gf[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:vo,charCode:function(e){return e.type==="keypress"?Os(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Os(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),jf=pt(wf),bf=X({},Rs,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Ji=pt(bf),Nf=X({},Rn,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:vo}),kf=pt(Nf),Sf=X({},Jr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Cf=pt(Sf),Ef=X({},Rs,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Pf=pt(Ef),Df=[9,13,27,32],wo=p&&"CompositionEvent"in window,_n=null;p&&"documentMode"in document&&(_n=document.documentMode);var Tf=p&&"TextEvent"in window&&!_n,qi=p&&(!wo||_n&&8<_n&&11>=_n),Ki=" ",Xi=!1;function Gi(e,t){switch(e){case"keyup":return Df.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Zi(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var qr=!1;function Lf(e,t){switch(e){case"compositionend":return Zi(t);case"keypress":return t.which!==32?null:(Xi=!0,Ki);case"textInput":return e=t.data,e===Ki&&Xi?null:e;default:return null}}function Of(e,t){if(qr)return e==="compositionend"||!wo&&Gi(e,t)?(e=Hi(),Ls=ho=ar=null,qr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return qi&&t.locale!=="ko"?null:t.data;default:return null}}var Mf={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function ec(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!Mf[e.type]:t==="textarea"}function tc(e,t,n,l){ji(l),t=Is(t,"onChange"),0<t.length&&(n=new po("onChange","change",null,n,l),e.push({event:n,listeners:t}))}var Fn=null,zn=null;function Rf(e){vc(e,0)}function As(e){var t=en(e);if(ys(t))return e}function Af(e,t){if(e==="change")return t}var rc=!1;if(p){var jo;if(p){var bo="oninput"in document;if(!bo){var nc=document.createElement("div");nc.setAttribute("oninput","return;"),bo=typeof nc.oninput=="function"}jo=bo}else jo=!1;rc=jo&&(!document.documentMode||9<document.documentMode)}function sc(){Fn&&(Fn.detachEvent("onpropertychange",ac),zn=Fn=null)}function ac(e){if(e.propertyName==="value"&&As(zn)){var t=[];tc(t,zn,e,eo(e)),Si(Rf,t)}}function _f(e,t,n){e==="focusin"?(sc(),Fn=t,zn=n,Fn.attachEvent("onpropertychange",ac)):e==="focusout"&&sc()}function Ff(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return As(zn)}function zf(e,t){if(e==="click")return As(t)}function If(e,t){if(e==="input"||e==="change")return As(t)}function Bf(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Ct=typeof Object.is=="function"?Object.is:Bf;function In(e,t){if(Ct(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),l=Object.keys(t);if(n.length!==l.length)return!1;for(l=0;l<n.length;l++){var c=n[l];if(!y.call(t,c)||!Ct(e[c],t[c]))return!1}return!0}function oc(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function lc(e,t){var n=oc(e);e=0;for(var l;n;){if(n.nodeType===3){if(l=e+n.textContent.length,e<=t&&l>=t)return{node:n,offset:t-e};e=l}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=oc(n)}}function ic(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?ic(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function cc(){for(var e=window,t=Hr();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Hr(e.document)}return t}function No(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function $f(e){var t=cc(),n=e.focusedElem,l=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&ic(n.ownerDocument.documentElement,n)){if(l!==null&&No(n)){if(t=l.start,e=l.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var c=n.textContent.length,d=Math.min(l.start,c);l=l.end===void 0?d:Math.min(l.end,c),!e.extend&&d>l&&(c=l,l=d,d=c),c=lc(n,d);var h=lc(n,l);c&&h&&(e.rangeCount!==1||e.anchorNode!==c.node||e.anchorOffset!==c.offset||e.focusNode!==h.node||e.focusOffset!==h.offset)&&(t=t.createRange(),t.setStart(c.node,c.offset),e.removeAllRanges(),d>l?(e.addRange(t),e.extend(h.node,h.offset)):(t.setEnd(h.node,h.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var Uf=p&&"documentMode"in document&&11>=document.documentMode,Kr=null,ko=null,Bn=null,So=!1;function uc(e,t,n){var l=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;So||Kr==null||Kr!==Hr(l)||(l=Kr,"selectionStart"in l&&No(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),Bn&&In(Bn,l)||(Bn=l,l=Is(ko,"onSelect"),0<l.length&&(t=new po("onSelect","select",null,t,n),e.push({event:t,listeners:l}),t.target=Kr)))}function _s(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var Xr={animationend:_s("Animation","AnimationEnd"),animationiteration:_s("Animation","AnimationIteration"),animationstart:_s("Animation","AnimationStart"),transitionend:_s("Transition","TransitionEnd")},Co={},dc={};p&&(dc=document.createElement("div").style,"AnimationEvent"in window||(delete Xr.animationend.animation,delete Xr.animationiteration.animation,delete Xr.animationstart.animation),"TransitionEvent"in window||delete Xr.transitionend.transition);function Fs(e){if(Co[e])return Co[e];if(!Xr[e])return e;var t=Xr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in dc)return Co[e]=t[n];return e}var mc=Fs("animationend"),fc=Fs("animationiteration"),hc=Fs("animationstart"),pc=Fs("transitionend"),xc=new Map,gc="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function or(e,t){xc.set(e,t),m(t,[e])}for(var Eo=0;Eo<gc.length;Eo++){var Po=gc[Eo],Wf=Po.toLowerCase(),Hf=Po[0].toUpperCase()+Po.slice(1);or(Wf,"on"+Hf)}or(mc,"onAnimationEnd"),or(fc,"onAnimationIteration"),or(hc,"onAnimationStart"),or("dblclick","onDoubleClick"),or("focusin","onFocus"),or("focusout","onBlur"),or(pc,"onTransitionEnd"),f("onMouseEnter",["mouseout","mouseover"]),f("onMouseLeave",["mouseout","mouseover"]),f("onPointerEnter",["pointerout","pointerover"]),f("onPointerLeave",["pointerout","pointerover"]),m("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),m("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),m("onBeforeInput",["compositionend","keypress","textInput","paste"]),m("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),m("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),m("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var $n="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Vf=new Set("cancel close invalid load scroll toggle".split(" ").concat($n));function yc(e,t,n){var l=e.type||"unknown-event";e.currentTarget=n,Um(l,t,void 0,e),e.currentTarget=null}function vc(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var l=e[n],c=l.event;l=l.listeners;e:{var d=void 0;if(t)for(var h=l.length-1;0<=h;h--){var w=l[h],N=w.instance,M=w.currentTarget;if(w=w.listener,N!==d&&c.isPropagationStopped())break e;yc(c,w,M),d=N}else for(h=0;h<l.length;h++){if(w=l[h],N=w.instance,M=w.currentTarget,w=w.listener,N!==d&&c.isPropagationStopped())break e;yc(c,w,M),d=N}}}if(js)throw e=so,js=!1,so=null,e}function Re(e,t){var n=t[_o];n===void 0&&(n=t[_o]=new Set);var l=e+"__bubble";n.has(l)||(wc(t,e,2,!1),n.add(l))}function Do(e,t,n){var l=0;t&&(l|=4),wc(n,e,l,t)}var zs="_reactListening"+Math.random().toString(36).slice(2);function Un(e){if(!e[zs]){e[zs]=!0,i.forEach(function(n){n!=="selectionchange"&&(Vf.has(n)||Do(n,!1,e),Do(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[zs]||(t[zs]=!0,Do("selectionchange",!1,t))}}function wc(e,t,n,l){switch(Wi(t)){case 1:var c=sf;break;case 4:c=af;break;default:c=mo}n=c.bind(null,t,n,e),c=void 0,!no||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(c=!0),l?c!==void 0?e.addEventListener(t,n,{capture:!0,passive:c}):e.addEventListener(t,n,!0):c!==void 0?e.addEventListener(t,n,{passive:c}):e.addEventListener(t,n,!1)}function To(e,t,n,l,c){var d=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var h=l.tag;if(h===3||h===4){var w=l.stateNode.containerInfo;if(w===c||w.nodeType===8&&w.parentNode===c)break;if(h===4)for(h=l.return;h!==null;){var N=h.tag;if((N===3||N===4)&&(N=h.stateNode.containerInfo,N===c||N.nodeType===8&&N.parentNode===c))return;h=h.return}for(;w!==null;){if(h=Cr(w),h===null)return;if(N=h.tag,N===5||N===6){l=d=h;continue e}w=w.parentNode}}l=l.return}Si(function(){var M=d,V=eo(n),Q=[];e:{var W=xc.get(e);if(W!==void 0){var G=po,ne=e;switch(e){case"keypress":if(Os(n)===0)break e;case"keydown":case"keyup":G=jf;break;case"focusin":ne="focus",G=yo;break;case"focusout":ne="blur",G=yo;break;case"beforeblur":case"afterblur":G=yo;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":G=Qi;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":G=cf;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":G=kf;break;case mc:case fc:case hc:G=mf;break;case pc:G=Cf;break;case"scroll":G=of;break;case"wheel":G=Pf;break;case"copy":case"cut":case"paste":G=hf;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":G=Ji}var se=(t&4)!==0,We=!se&&e==="scroll",L=se?W!==null?W+"Capture":null:W;se=[];for(var C=M,O;C!==null;){O=C;var K=O.stateNode;if(O.tag===5&&K!==null&&(O=K,L!==null&&(K=kn(C,L),K!=null&&se.push(Wn(C,K,O)))),We)break;C=C.return}0<se.length&&(W=new G(W,ne,null,n,V),Q.push({event:W,listeners:se}))}}if((t&7)===0){e:{if(W=e==="mouseover"||e==="pointerover",G=e==="mouseout"||e==="pointerout",W&&n!==Za&&(ne=n.relatedTarget||n.fromElement)&&(Cr(ne)||ne[Wt]))break e;if((G||W)&&(W=V.window===V?V:(W=V.ownerDocument)?W.defaultView||W.parentWindow:window,G?(ne=n.relatedTarget||n.toElement,G=M,ne=ne?Cr(ne):null,ne!==null&&(We=Sr(ne),ne!==We||ne.tag!==5&&ne.tag!==6)&&(ne=null)):(G=null,ne=M),G!==ne)){if(se=Qi,K="onMouseLeave",L="onMouseEnter",C="mouse",(e==="pointerout"||e==="pointerover")&&(se=Ji,K="onPointerLeave",L="onPointerEnter",C="pointer"),We=G==null?W:en(G),O=ne==null?W:en(ne),W=new se(K,C+"leave",G,n,V),W.target=We,W.relatedTarget=O,K=null,Cr(V)===M&&(se=new se(L,C+"enter",ne,n,V),se.target=O,se.relatedTarget=We,K=se),We=K,G&&ne)t:{for(se=G,L=ne,C=0,O=se;O;O=Gr(O))C++;for(O=0,K=L;K;K=Gr(K))O++;for(;0<C-O;)se=Gr(se),C--;for(;0<O-C;)L=Gr(L),O--;for(;C--;){if(se===L||L!==null&&se===L.alternate)break t;se=Gr(se),L=Gr(L)}se=null}else se=null;G!==null&&jc(Q,W,G,se,!1),ne!==null&&We!==null&&jc(Q,We,ne,se,!0)}}e:{if(W=M?en(M):window,G=W.nodeName&&W.nodeName.toLowerCase(),G==="select"||G==="input"&&W.type==="file")var ae=Af;else if(ec(W))if(rc)ae=If;else{ae=Ff;var ce=_f}else(G=W.nodeName)&&G.toLowerCase()==="input"&&(W.type==="checkbox"||W.type==="radio")&&(ae=zf);if(ae&&(ae=ae(e,M))){tc(Q,ae,n,V);break e}ce&&ce(e,W,M),e==="focusout"&&(ce=W._wrapperState)&&ce.controlled&&W.type==="number"&&Me(W,"number",W.value)}switch(ce=M?en(M):window,e){case"focusin":(ec(ce)||ce.contentEditable==="true")&&(Kr=ce,ko=M,Bn=null);break;case"focusout":Bn=ko=Kr=null;break;case"mousedown":So=!0;break;case"contextmenu":case"mouseup":case"dragend":So=!1,uc(Q,n,V);break;case"selectionchange":if(Uf)break;case"keydown":case"keyup":uc(Q,n,V)}var ue;if(wo)e:{switch(e){case"compositionstart":var he="onCompositionStart";break e;case"compositionend":he="onCompositionEnd";break e;case"compositionupdate":he="onCompositionUpdate";break e}he=void 0}else qr?Gi(e,n)&&(he="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(he="onCompositionStart");he&&(qi&&n.locale!=="ko"&&(qr||he!=="onCompositionStart"?he==="onCompositionEnd"&&qr&&(ue=Hi()):(ar=V,ho="value"in ar?ar.value:ar.textContent,qr=!0)),ce=Is(M,he),0<ce.length&&(he=new Yi(he,e,null,n,V),Q.push({event:he,listeners:ce}),ue?he.data=ue:(ue=Zi(n),ue!==null&&(he.data=ue)))),(ue=Tf?Lf(e,n):Of(e,n))&&(M=Is(M,"onBeforeInput"),0<M.length&&(V=new Yi("onBeforeInput","beforeinput",null,n,V),Q.push({event:V,listeners:M}),V.data=ue))}vc(Q,t)})}function Wn(e,t,n){return{instance:e,listener:t,currentTarget:n}}function Is(e,t){for(var n=t+"Capture",l=[];e!==null;){var c=e,d=c.stateNode;c.tag===5&&d!==null&&(c=d,d=kn(e,n),d!=null&&l.unshift(Wn(e,d,c)),d=kn(e,t),d!=null&&l.push(Wn(e,d,c))),e=e.return}return l}function Gr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function jc(e,t,n,l,c){for(var d=t._reactName,h=[];n!==null&&n!==l;){var w=n,N=w.alternate,M=w.stateNode;if(N!==null&&N===l)break;w.tag===5&&M!==null&&(w=M,c?(N=kn(n,d),N!=null&&h.unshift(Wn(n,N,w))):c||(N=kn(n,d),N!=null&&h.push(Wn(n,N,w)))),n=n.return}h.length!==0&&e.push({event:t,listeners:h})}var Qf=/\r\n?/g,Yf=/\u0000|\uFFFD/g;function bc(e){return(typeof e=="string"?e:""+e).replace(Qf,`
`).replace(Yf,"")}function Bs(e,t,n){if(t=bc(t),bc(e)!==t&&n)throw Error(o(425))}function $s(){}var Lo=null,Oo=null;function Mo(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Ro=typeof setTimeout=="function"?setTimeout:void 0,Jf=typeof clearTimeout=="function"?clearTimeout:void 0,Nc=typeof Promise=="function"?Promise:void 0,qf=typeof queueMicrotask=="function"?queueMicrotask:typeof Nc<"u"?function(e){return Nc.resolve(null).then(e).catch(Kf)}:Ro;function Kf(e){setTimeout(function(){throw e})}function Ao(e,t){var n=t,l=0;do{var c=n.nextSibling;if(e.removeChild(n),c&&c.nodeType===8)if(n=c.data,n==="/$"){if(l===0){e.removeChild(c),Mn(t);return}l--}else n!=="$"&&n!=="$?"&&n!=="$!"||l++;n=c}while(n);Mn(t)}function lr(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function kc(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Zr=Math.random().toString(36).slice(2),Ft="__reactFiber$"+Zr,Hn="__reactProps$"+Zr,Wt="__reactContainer$"+Zr,_o="__reactEvents$"+Zr,Xf="__reactListeners$"+Zr,Gf="__reactHandles$"+Zr;function Cr(e){var t=e[Ft];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Wt]||n[Ft]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=kc(e);e!==null;){if(n=e[Ft])return n;e=kc(e)}return t}e=n,n=e.parentNode}return null}function Vn(e){return e=e[Ft]||e[Wt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function en(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(o(33))}function Us(e){return e[Hn]||null}var Fo=[],tn=-1;function ir(e){return{current:e}}function Ae(e){0>tn||(e.current=Fo[tn],Fo[tn]=null,tn--)}function Oe(e,t){tn++,Fo[tn]=e.current,e.current=t}var cr={},Ge=ir(cr),lt=ir(!1),Er=cr;function rn(e,t){var n=e.type.contextTypes;if(!n)return cr;var l=e.stateNode;if(l&&l.__reactInternalMemoizedUnmaskedChildContext===t)return l.__reactInternalMemoizedMaskedChildContext;var c={},d;for(d in n)c[d]=t[d];return l&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=c),c}function it(e){return e=e.childContextTypes,e!=null}function Ws(){Ae(lt),Ae(Ge)}function Sc(e,t,n){if(Ge.current!==cr)throw Error(o(168));Oe(Ge,t),Oe(lt,n)}function Cc(e,t,n){var l=e.stateNode;if(t=t.childContextTypes,typeof l.getChildContext!="function")return n;l=l.getChildContext();for(var c in l)if(!(c in t))throw Error(o(108,De(e)||"Unknown",c));return X({},n,l)}function Hs(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||cr,Er=Ge.current,Oe(Ge,e),Oe(lt,lt.current),!0}function Ec(e,t,n){var l=e.stateNode;if(!l)throw Error(o(169));n?(e=Cc(e,t,Er),l.__reactInternalMemoizedMergedChildContext=e,Ae(lt),Ae(Ge),Oe(Ge,e)):Ae(lt),Oe(lt,n)}var Ht=null,Vs=!1,zo=!1;function Pc(e){Ht===null?Ht=[e]:Ht.push(e)}function Zf(e){Vs=!0,Pc(e)}function ur(){if(!zo&&Ht!==null){zo=!0;var e=0,t=Ee;try{var n=Ht;for(Ee=1;e<n.length;e++){var l=n[e];do l=l(!0);while(l!==null)}Ht=null,Vs=!1}catch(c){throw Ht!==null&&(Ht=Ht.slice(e+1)),Ti(ao,ur),c}finally{Ee=t,zo=!1}}return null}var nn=[],sn=0,Qs=null,Ys=0,vt=[],wt=0,Pr=null,Vt=1,Qt="";function Dr(e,t){nn[sn++]=Ys,nn[sn++]=Qs,Qs=e,Ys=t}function Dc(e,t,n){vt[wt++]=Vt,vt[wt++]=Qt,vt[wt++]=Pr,Pr=e;var l=Vt;e=Qt;var c=32-St(l)-1;l&=~(1<<c),n+=1;var d=32-St(t)+c;if(30<d){var h=c-c%5;d=(l&(1<<h)-1).toString(32),l>>=h,c-=h,Vt=1<<32-St(t)+c|n<<c|l,Qt=d+e}else Vt=1<<d|n<<c|l,Qt=e}function Io(e){e.return!==null&&(Dr(e,1),Dc(e,1,0))}function Bo(e){for(;e===Qs;)Qs=nn[--sn],nn[sn]=null,Ys=nn[--sn],nn[sn]=null;for(;e===Pr;)Pr=vt[--wt],vt[wt]=null,Qt=vt[--wt],vt[wt]=null,Vt=vt[--wt],vt[wt]=null}var xt=null,gt=null,_e=!1,Et=null;function Tc(e,t){var n=kt(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function Lc(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,xt=e,gt=lr(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,xt=e,gt=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Pr!==null?{id:Vt,overflow:Qt}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=kt(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,xt=e,gt=null,!0):!1;default:return!1}}function $o(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Uo(e){if(_e){var t=gt;if(t){var n=t;if(!Lc(e,t)){if($o(e))throw Error(o(418));t=lr(n.nextSibling);var l=xt;t&&Lc(e,t)?Tc(l,n):(e.flags=e.flags&-4097|2,_e=!1,xt=e)}}else{if($o(e))throw Error(o(418));e.flags=e.flags&-4097|2,_e=!1,xt=e}}}function Oc(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;xt=e}function Js(e){if(e!==xt)return!1;if(!_e)return Oc(e),_e=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Mo(e.type,e.memoizedProps)),t&&(t=gt)){if($o(e))throw Mc(),Error(o(418));for(;t;)Tc(e,t),t=lr(t.nextSibling)}if(Oc(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(o(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){gt=lr(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}gt=null}}else gt=xt?lr(e.stateNode.nextSibling):null;return!0}function Mc(){for(var e=gt;e;)e=lr(e.nextSibling)}function an(){gt=xt=null,_e=!1}function Wo(e){Et===null?Et=[e]:Et.push(e)}var eh=F.ReactCurrentBatchConfig;function Qn(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(o(309));var l=n.stateNode}if(!l)throw Error(o(147,e));var c=l,d=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===d?t.ref:(t=function(h){var w=c.refs;h===null?delete w[d]:w[d]=h},t._stringRef=d,t)}if(typeof e!="string")throw Error(o(284));if(!n._owner)throw Error(o(290,e))}return e}function qs(e,t){throw e=Object.prototype.toString.call(t),Error(o(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Rc(e){var t=e._init;return t(e._payload)}function Ac(e){function t(L,C){if(e){var O=L.deletions;O===null?(L.deletions=[C],L.flags|=16):O.push(C)}}function n(L,C){if(!e)return null;for(;C!==null;)t(L,C),C=C.sibling;return null}function l(L,C){for(L=new Map;C!==null;)C.key!==null?L.set(C.key,C):L.set(C.index,C),C=C.sibling;return L}function c(L,C){return L=yr(L,C),L.index=0,L.sibling=null,L}function d(L,C,O){return L.index=O,e?(O=L.alternate,O!==null?(O=O.index,O<C?(L.flags|=2,C):O):(L.flags|=2,C)):(L.flags|=1048576,C)}function h(L){return e&&L.alternate===null&&(L.flags|=2),L}function w(L,C,O,K){return C===null||C.tag!==6?(C=Rl(O,L.mode,K),C.return=L,C):(C=c(C,O),C.return=L,C)}function N(L,C,O,K){var ae=O.type;return ae===U?V(L,C,O.props.children,K,O.key):C!==null&&(C.elementType===ae||typeof ae=="object"&&ae!==null&&ae.$$typeof===q&&Rc(ae)===C.type)?(K=c(C,O.props),K.ref=Qn(L,C,O),K.return=L,K):(K=va(O.type,O.key,O.props,null,L.mode,K),K.ref=Qn(L,C,O),K.return=L,K)}function M(L,C,O,K){return C===null||C.tag!==4||C.stateNode.containerInfo!==O.containerInfo||C.stateNode.implementation!==O.implementation?(C=Al(O,L.mode,K),C.return=L,C):(C=c(C,O.children||[]),C.return=L,C)}function V(L,C,O,K,ae){return C===null||C.tag!==7?(C=Fr(O,L.mode,K,ae),C.return=L,C):(C=c(C,O),C.return=L,C)}function Q(L,C,O){if(typeof C=="string"&&C!==""||typeof C=="number")return C=Rl(""+C,L.mode,O),C.return=L,C;if(typeof C=="object"&&C!==null){switch(C.$$typeof){case I:return O=va(C.type,C.key,C.props,null,L.mode,O),O.ref=Qn(L,null,C),O.return=L,O;case z:return C=Al(C,L.mode,O),C.return=L,C;case q:var K=C._init;return Q(L,K(C._payload),O)}if(At(C)||ee(C))return C=Fr(C,L.mode,O,null),C.return=L,C;qs(L,C)}return null}function W(L,C,O,K){var ae=C!==null?C.key:null;if(typeof O=="string"&&O!==""||typeof O=="number")return ae!==null?null:w(L,C,""+O,K);if(typeof O=="object"&&O!==null){switch(O.$$typeof){case I:return O.key===ae?N(L,C,O,K):null;case z:return O.key===ae?M(L,C,O,K):null;case q:return ae=O._init,W(L,C,ae(O._payload),K)}if(At(O)||ee(O))return ae!==null?null:V(L,C,O,K,null);qs(L,O)}return null}function G(L,C,O,K,ae){if(typeof K=="string"&&K!==""||typeof K=="number")return L=L.get(O)||null,w(C,L,""+K,ae);if(typeof K=="object"&&K!==null){switch(K.$$typeof){case I:return L=L.get(K.key===null?O:K.key)||null,N(C,L,K,ae);case z:return L=L.get(K.key===null?O:K.key)||null,M(C,L,K,ae);case q:var ce=K._init;return G(L,C,O,ce(K._payload),ae)}if(At(K)||ee(K))return L=L.get(O)||null,V(C,L,K,ae,null);qs(C,K)}return null}function ne(L,C,O,K){for(var ae=null,ce=null,ue=C,he=C=0,qe=null;ue!==null&&he<O.length;he++){ue.index>he?(qe=ue,ue=null):qe=ue.sibling;var ke=W(L,ue,O[he],K);if(ke===null){ue===null&&(ue=qe);break}e&&ue&&ke.alternate===null&&t(L,ue),C=d(ke,C,he),ce===null?ae=ke:ce.sibling=ke,ce=ke,ue=qe}if(he===O.length)return n(L,ue),_e&&Dr(L,he),ae;if(ue===null){for(;he<O.length;he++)ue=Q(L,O[he],K),ue!==null&&(C=d(ue,C,he),ce===null?ae=ue:ce.sibling=ue,ce=ue);return _e&&Dr(L,he),ae}for(ue=l(L,ue);he<O.length;he++)qe=G(ue,L,he,O[he],K),qe!==null&&(e&&qe.alternate!==null&&ue.delete(qe.key===null?he:qe.key),C=d(qe,C,he),ce===null?ae=qe:ce.sibling=qe,ce=qe);return e&&ue.forEach(function(vr){return t(L,vr)}),_e&&Dr(L,he),ae}function se(L,C,O,K){var ae=ee(O);if(typeof ae!="function")throw Error(o(150));if(O=ae.call(O),O==null)throw Error(o(151));for(var ce=ae=null,ue=C,he=C=0,qe=null,ke=O.next();ue!==null&&!ke.done;he++,ke=O.next()){ue.index>he?(qe=ue,ue=null):qe=ue.sibling;var vr=W(L,ue,ke.value,K);if(vr===null){ue===null&&(ue=qe);break}e&&ue&&vr.alternate===null&&t(L,ue),C=d(vr,C,he),ce===null?ae=vr:ce.sibling=vr,ce=vr,ue=qe}if(ke.done)return n(L,ue),_e&&Dr(L,he),ae;if(ue===null){for(;!ke.done;he++,ke=O.next())ke=Q(L,ke.value,K),ke!==null&&(C=d(ke,C,he),ce===null?ae=ke:ce.sibling=ke,ce=ke);return _e&&Dr(L,he),ae}for(ue=l(L,ue);!ke.done;he++,ke=O.next())ke=G(ue,L,he,ke.value,K),ke!==null&&(e&&ke.alternate!==null&&ue.delete(ke.key===null?he:ke.key),C=d(ke,C,he),ce===null?ae=ke:ce.sibling=ke,ce=ke);return e&&ue.forEach(function(Mh){return t(L,Mh)}),_e&&Dr(L,he),ae}function We(L,C,O,K){if(typeof O=="object"&&O!==null&&O.type===U&&O.key===null&&(O=O.props.children),typeof O=="object"&&O!==null){switch(O.$$typeof){case I:e:{for(var ae=O.key,ce=C;ce!==null;){if(ce.key===ae){if(ae=O.type,ae===U){if(ce.tag===7){n(L,ce.sibling),C=c(ce,O.props.children),C.return=L,L=C;break e}}else if(ce.elementType===ae||typeof ae=="object"&&ae!==null&&ae.$$typeof===q&&Rc(ae)===ce.type){n(L,ce.sibling),C=c(ce,O.props),C.ref=Qn(L,ce,O),C.return=L,L=C;break e}n(L,ce);break}else t(L,ce);ce=ce.sibling}O.type===U?(C=Fr(O.props.children,L.mode,K,O.key),C.return=L,L=C):(K=va(O.type,O.key,O.props,null,L.mode,K),K.ref=Qn(L,C,O),K.return=L,L=K)}return h(L);case z:e:{for(ce=O.key;C!==null;){if(C.key===ce)if(C.tag===4&&C.stateNode.containerInfo===O.containerInfo&&C.stateNode.implementation===O.implementation){n(L,C.sibling),C=c(C,O.children||[]),C.return=L,L=C;break e}else{n(L,C);break}else t(L,C);C=C.sibling}C=Al(O,L.mode,K),C.return=L,L=C}return h(L);case q:return ce=O._init,We(L,C,ce(O._payload),K)}if(At(O))return ne(L,C,O,K);if(ee(O))return se(L,C,O,K);qs(L,O)}return typeof O=="string"&&O!==""||typeof O=="number"?(O=""+O,C!==null&&C.tag===6?(n(L,C.sibling),C=c(C,O),C.return=L,L=C):(n(L,C),C=Rl(O,L.mode,K),C.return=L,L=C),h(L)):n(L,C)}return We}var on=Ac(!0),_c=Ac(!1),Ks=ir(null),Xs=null,ln=null,Ho=null;function Vo(){Ho=ln=Xs=null}function Qo(e){var t=Ks.current;Ae(Ks),e._currentValue=t}function Yo(e,t,n){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===n)break;e=e.return}}function cn(e,t){Xs=e,Ho=ln=null,e=e.dependencies,e!==null&&e.firstContext!==null&&((e.lanes&t)!==0&&(ct=!0),e.firstContext=null)}function jt(e){var t=e._currentValue;if(Ho!==e)if(e={context:e,memoizedValue:t,next:null},ln===null){if(Xs===null)throw Error(o(308));ln=e,Xs.dependencies={lanes:0,firstContext:e}}else ln=ln.next=e;return t}var Tr=null;function Jo(e){Tr===null?Tr=[e]:Tr.push(e)}function Fc(e,t,n,l){var c=t.interleaved;return c===null?(n.next=n,Jo(t)):(n.next=c.next,c.next=n),t.interleaved=n,Yt(e,l)}function Yt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var dr=!1;function qo(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function zc(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Jt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function mr(e,t,n){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(Ne&2)!==0){var c=l.pending;return c===null?t.next=t:(t.next=c.next,c.next=t),l.pending=t,Yt(e,n)}return c=l.interleaved,c===null?(t.next=t,Jo(l)):(t.next=c.next,c.next=t),l.interleaved=t,Yt(e,n)}function Gs(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,io(e,n)}}function Ic(e,t){var n=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,n===l)){var c=null,d=null;if(n=n.firstBaseUpdate,n!==null){do{var h={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};d===null?c=d=h:d=d.next=h,n=n.next}while(n!==null);d===null?c=d=t:d=d.next=t}else c=d=t;n={baseState:l.baseState,firstBaseUpdate:c,lastBaseUpdate:d,shared:l.shared,effects:l.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function Zs(e,t,n,l){var c=e.updateQueue;dr=!1;var d=c.firstBaseUpdate,h=c.lastBaseUpdate,w=c.shared.pending;if(w!==null){c.shared.pending=null;var N=w,M=N.next;N.next=null,h===null?d=M:h.next=M,h=N;var V=e.alternate;V!==null&&(V=V.updateQueue,w=V.lastBaseUpdate,w!==h&&(w===null?V.firstBaseUpdate=M:w.next=M,V.lastBaseUpdate=N))}if(d!==null){var Q=c.baseState;h=0,V=M=N=null,w=d;do{var W=w.lane,G=w.eventTime;if((l&W)===W){V!==null&&(V=V.next={eventTime:G,lane:0,tag:w.tag,payload:w.payload,callback:w.callback,next:null});e:{var ne=e,se=w;switch(W=t,G=n,se.tag){case 1:if(ne=se.payload,typeof ne=="function"){Q=ne.call(G,Q,W);break e}Q=ne;break e;case 3:ne.flags=ne.flags&-65537|128;case 0:if(ne=se.payload,W=typeof ne=="function"?ne.call(G,Q,W):ne,W==null)break e;Q=X({},Q,W);break e;case 2:dr=!0}}w.callback!==null&&w.lane!==0&&(e.flags|=64,W=c.effects,W===null?c.effects=[w]:W.push(w))}else G={eventTime:G,lane:W,tag:w.tag,payload:w.payload,callback:w.callback,next:null},V===null?(M=V=G,N=Q):V=V.next=G,h|=W;if(w=w.next,w===null){if(w=c.shared.pending,w===null)break;W=w,w=W.next,W.next=null,c.lastBaseUpdate=W,c.shared.pending=null}}while(!0);if(V===null&&(N=Q),c.baseState=N,c.firstBaseUpdate=M,c.lastBaseUpdate=V,t=c.shared.interleaved,t!==null){c=t;do h|=c.lane,c=c.next;while(c!==t)}else d===null&&(c.shared.lanes=0);Mr|=h,e.lanes=h,e.memoizedState=Q}}function Bc(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var l=e[t],c=l.callback;if(c!==null){if(l.callback=null,l=n,typeof c!="function")throw Error(o(191,c));c.call(l)}}}var Yn={},zt=ir(Yn),Jn=ir(Yn),qn=ir(Yn);function Lr(e){if(e===Yn)throw Error(o(174));return e}function Ko(e,t){switch(Oe(qn,t),Oe(Jn,e),Oe(zt,Yn),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Ka(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Ka(t,e)}Ae(zt),Oe(zt,t)}function un(){Ae(zt),Ae(Jn),Ae(qn)}function $c(e){Lr(qn.current);var t=Lr(zt.current),n=Ka(t,e.type);t!==n&&(Oe(Jn,e),Oe(zt,n))}function Xo(e){Jn.current===e&&(Ae(zt),Ae(Jn))}var Fe=ir(0);function ea(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var Go=[];function Zo(){for(var e=0;e<Go.length;e++)Go[e]._workInProgressVersionPrimary=null;Go.length=0}var ta=F.ReactCurrentDispatcher,el=F.ReactCurrentBatchConfig,Or=0,ze=null,Ve=null,Ye=null,ra=!1,Kn=!1,Xn=0,th=0;function Ze(){throw Error(o(321))}function tl(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!Ct(e[n],t[n]))return!1;return!0}function rl(e,t,n,l,c,d){if(Or=d,ze=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,ta.current=e===null||e.memoizedState===null?ah:oh,e=n(l,c),Kn){d=0;do{if(Kn=!1,Xn=0,25<=d)throw Error(o(301));d+=1,Ye=Ve=null,t.updateQueue=null,ta.current=lh,e=n(l,c)}while(Kn)}if(ta.current=aa,t=Ve!==null&&Ve.next!==null,Or=0,Ye=Ve=ze=null,ra=!1,t)throw Error(o(300));return e}function nl(){var e=Xn!==0;return Xn=0,e}function It(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ye===null?ze.memoizedState=Ye=e:Ye=Ye.next=e,Ye}function bt(){if(Ve===null){var e=ze.alternate;e=e!==null?e.memoizedState:null}else e=Ve.next;var t=Ye===null?ze.memoizedState:Ye.next;if(t!==null)Ye=t,Ve=e;else{if(e===null)throw Error(o(310));Ve=e,e={memoizedState:Ve.memoizedState,baseState:Ve.baseState,baseQueue:Ve.baseQueue,queue:Ve.queue,next:null},Ye===null?ze.memoizedState=Ye=e:Ye=Ye.next=e}return Ye}function Gn(e,t){return typeof t=="function"?t(e):t}function sl(e){var t=bt(),n=t.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=e;var l=Ve,c=l.baseQueue,d=n.pending;if(d!==null){if(c!==null){var h=c.next;c.next=d.next,d.next=h}l.baseQueue=c=d,n.pending=null}if(c!==null){d=c.next,l=l.baseState;var w=h=null,N=null,M=d;do{var V=M.lane;if((Or&V)===V)N!==null&&(N=N.next={lane:0,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null}),l=M.hasEagerState?M.eagerState:e(l,M.action);else{var Q={lane:V,action:M.action,hasEagerState:M.hasEagerState,eagerState:M.eagerState,next:null};N===null?(w=N=Q,h=l):N=N.next=Q,ze.lanes|=V,Mr|=V}M=M.next}while(M!==null&&M!==d);N===null?h=l:N.next=w,Ct(l,t.memoizedState)||(ct=!0),t.memoizedState=l,t.baseState=h,t.baseQueue=N,n.lastRenderedState=l}if(e=n.interleaved,e!==null){c=e;do d=c.lane,ze.lanes|=d,Mr|=d,c=c.next;while(c!==e)}else c===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function al(e){var t=bt(),n=t.queue;if(n===null)throw Error(o(311));n.lastRenderedReducer=e;var l=n.dispatch,c=n.pending,d=t.memoizedState;if(c!==null){n.pending=null;var h=c=c.next;do d=e(d,h.action),h=h.next;while(h!==c);Ct(d,t.memoizedState)||(ct=!0),t.memoizedState=d,t.baseQueue===null&&(t.baseState=d),n.lastRenderedState=d}return[d,l]}function Uc(){}function Wc(e,t){var n=ze,l=bt(),c=t(),d=!Ct(l.memoizedState,c);if(d&&(l.memoizedState=c,ct=!0),l=l.queue,ol(Qc.bind(null,n,l,e),[e]),l.getSnapshot!==t||d||Ye!==null&&Ye.memoizedState.tag&1){if(n.flags|=2048,Zn(9,Vc.bind(null,n,l,c,t),void 0,null),Je===null)throw Error(o(349));(Or&30)!==0||Hc(n,t,c)}return c}function Hc(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=ze.updateQueue,t===null?(t={lastEffect:null,stores:null},ze.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function Vc(e,t,n,l){t.value=n,t.getSnapshot=l,Yc(t)&&Jc(e)}function Qc(e,t,n){return n(function(){Yc(t)&&Jc(e)})}function Yc(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!Ct(e,n)}catch{return!0}}function Jc(e){var t=Yt(e,1);t!==null&&Lt(t,e,1,-1)}function qc(e){var t=It();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:Gn,lastRenderedState:e},t.queue=e,e=e.dispatch=sh.bind(null,ze,e),[t.memoizedState,e]}function Zn(e,t,n,l){return e={tag:e,create:t,destroy:n,deps:l,next:null},t=ze.updateQueue,t===null?(t={lastEffect:null,stores:null},ze.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(l=n.next,n.next=e,e.next=l,t.lastEffect=e)),e}function Kc(){return bt().memoizedState}function na(e,t,n,l){var c=It();ze.flags|=e,c.memoizedState=Zn(1|t,n,void 0,l===void 0?null:l)}function sa(e,t,n,l){var c=bt();l=l===void 0?null:l;var d=void 0;if(Ve!==null){var h=Ve.memoizedState;if(d=h.destroy,l!==null&&tl(l,h.deps)){c.memoizedState=Zn(t,n,d,l);return}}ze.flags|=e,c.memoizedState=Zn(1|t,n,d,l)}function Xc(e,t){return na(8390656,8,e,t)}function ol(e,t){return sa(2048,8,e,t)}function Gc(e,t){return sa(4,2,e,t)}function Zc(e,t){return sa(4,4,e,t)}function eu(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function tu(e,t,n){return n=n!=null?n.concat([e]):null,sa(4,4,eu.bind(null,t,e),n)}function ll(){}function ru(e,t){var n=bt();t=t===void 0?null:t;var l=n.memoizedState;return l!==null&&t!==null&&tl(t,l[1])?l[0]:(n.memoizedState=[e,t],e)}function nu(e,t){var n=bt();t=t===void 0?null:t;var l=n.memoizedState;return l!==null&&t!==null&&tl(t,l[1])?l[0]:(e=e(),n.memoizedState=[e,t],e)}function su(e,t,n){return(Or&21)===0?(e.baseState&&(e.baseState=!1,ct=!0),e.memoizedState=n):(Ct(n,t)||(n=Ri(),ze.lanes|=n,Mr|=n,e.baseState=!0),t)}function rh(e,t){var n=Ee;Ee=n!==0&&4>n?n:4,e(!0);var l=el.transition;el.transition={};try{e(!1),t()}finally{Ee=n,el.transition=l}}function au(){return bt().memoizedState}function nh(e,t,n){var l=xr(e);if(n={lane:l,action:n,hasEagerState:!1,eagerState:null,next:null},ou(e))lu(t,n);else if(n=Fc(e,t,n,l),n!==null){var c=at();Lt(n,e,l,c),iu(n,t,l)}}function sh(e,t,n){var l=xr(e),c={lane:l,action:n,hasEagerState:!1,eagerState:null,next:null};if(ou(e))lu(t,c);else{var d=e.alternate;if(e.lanes===0&&(d===null||d.lanes===0)&&(d=t.lastRenderedReducer,d!==null))try{var h=t.lastRenderedState,w=d(h,n);if(c.hasEagerState=!0,c.eagerState=w,Ct(w,h)){var N=t.interleaved;N===null?(c.next=c,Jo(t)):(c.next=N.next,N.next=c),t.interleaved=c;return}}catch{}finally{}n=Fc(e,t,c,l),n!==null&&(c=at(),Lt(n,e,l,c),iu(n,t,l))}}function ou(e){var t=e.alternate;return e===ze||t!==null&&t===ze}function lu(e,t){Kn=ra=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function iu(e,t,n){if((n&4194240)!==0){var l=t.lanes;l&=e.pendingLanes,n|=l,t.lanes=n,io(e,n)}}var aa={readContext:jt,useCallback:Ze,useContext:Ze,useEffect:Ze,useImperativeHandle:Ze,useInsertionEffect:Ze,useLayoutEffect:Ze,useMemo:Ze,useReducer:Ze,useRef:Ze,useState:Ze,useDebugValue:Ze,useDeferredValue:Ze,useTransition:Ze,useMutableSource:Ze,useSyncExternalStore:Ze,useId:Ze,unstable_isNewReconciler:!1},ah={readContext:jt,useCallback:function(e,t){return It().memoizedState=[e,t===void 0?null:t],e},useContext:jt,useEffect:Xc,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,na(4194308,4,eu.bind(null,t,e),n)},useLayoutEffect:function(e,t){return na(4194308,4,e,t)},useInsertionEffect:function(e,t){return na(4,2,e,t)},useMemo:function(e,t){var n=It();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var l=It();return t=n!==void 0?n(t):t,l.memoizedState=l.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},l.queue=e,e=e.dispatch=nh.bind(null,ze,e),[l.memoizedState,e]},useRef:function(e){var t=It();return e={current:e},t.memoizedState=e},useState:qc,useDebugValue:ll,useDeferredValue:function(e){return It().memoizedState=e},useTransition:function(){var e=qc(!1),t=e[0];return e=rh.bind(null,e[1]),It().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var l=ze,c=It();if(_e){if(n===void 0)throw Error(o(407));n=n()}else{if(n=t(),Je===null)throw Error(o(349));(Or&30)!==0||Hc(l,t,n)}c.memoizedState=n;var d={value:n,getSnapshot:t};return c.queue=d,Xc(Qc.bind(null,l,d,e),[e]),l.flags|=2048,Zn(9,Vc.bind(null,l,d,n,t),void 0,null),n},useId:function(){var e=It(),t=Je.identifierPrefix;if(_e){var n=Qt,l=Vt;n=(l&~(1<<32-St(l)-1)).toString(32)+n,t=":"+t+"R"+n,n=Xn++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=th++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},oh={readContext:jt,useCallback:ru,useContext:jt,useEffect:ol,useImperativeHandle:tu,useInsertionEffect:Gc,useLayoutEffect:Zc,useMemo:nu,useReducer:sl,useRef:Kc,useState:function(){return sl(Gn)},useDebugValue:ll,useDeferredValue:function(e){var t=bt();return su(t,Ve.memoizedState,e)},useTransition:function(){var e=sl(Gn)[0],t=bt().memoizedState;return[e,t]},useMutableSource:Uc,useSyncExternalStore:Wc,useId:au,unstable_isNewReconciler:!1},lh={readContext:jt,useCallback:ru,useContext:jt,useEffect:ol,useImperativeHandle:tu,useInsertionEffect:Gc,useLayoutEffect:Zc,useMemo:nu,useReducer:al,useRef:Kc,useState:function(){return al(Gn)},useDebugValue:ll,useDeferredValue:function(e){var t=bt();return Ve===null?t.memoizedState=e:su(t,Ve.memoizedState,e)},useTransition:function(){var e=al(Gn)[0],t=bt().memoizedState;return[e,t]},useMutableSource:Uc,useSyncExternalStore:Wc,useId:au,unstable_isNewReconciler:!1};function Pt(e,t){if(e&&e.defaultProps){t=X({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function il(e,t,n,l){t=e.memoizedState,n=n(l,t),n=n==null?t:X({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var oa={isMounted:function(e){return(e=e._reactInternals)?Sr(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var l=at(),c=xr(e),d=Jt(l,c);d.payload=t,n!=null&&(d.callback=n),t=mr(e,d,c),t!==null&&(Lt(t,e,c,l),Gs(t,e,c))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var l=at(),c=xr(e),d=Jt(l,c);d.tag=1,d.payload=t,n!=null&&(d.callback=n),t=mr(e,d,c),t!==null&&(Lt(t,e,c,l),Gs(t,e,c))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=at(),l=xr(e),c=Jt(n,l);c.tag=2,t!=null&&(c.callback=t),t=mr(e,c,l),t!==null&&(Lt(t,e,l,n),Gs(t,e,l))}};function cu(e,t,n,l,c,d,h){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,d,h):t.prototype&&t.prototype.isPureReactComponent?!In(n,l)||!In(c,d):!0}function uu(e,t,n){var l=!1,c=cr,d=t.contextType;return typeof d=="object"&&d!==null?d=jt(d):(c=it(t)?Er:Ge.current,l=t.contextTypes,d=(l=l!=null)?rn(e,c):cr),t=new t(n,d),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=oa,e.stateNode=t,t._reactInternals=e,l&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=c,e.__reactInternalMemoizedMaskedChildContext=d),t}function du(e,t,n,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,l),t.state!==e&&oa.enqueueReplaceState(t,t.state,null)}function cl(e,t,n,l){var c=e.stateNode;c.props=n,c.state=e.memoizedState,c.refs={},qo(e);var d=t.contextType;typeof d=="object"&&d!==null?c.context=jt(d):(d=it(t)?Er:Ge.current,c.context=rn(e,d)),c.state=e.memoizedState,d=t.getDerivedStateFromProps,typeof d=="function"&&(il(e,t,d,n),c.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof c.getSnapshotBeforeUpdate=="function"||typeof c.UNSAFE_componentWillMount!="function"&&typeof c.componentWillMount!="function"||(t=c.state,typeof c.componentWillMount=="function"&&c.componentWillMount(),typeof c.UNSAFE_componentWillMount=="function"&&c.UNSAFE_componentWillMount(),t!==c.state&&oa.enqueueReplaceState(c,c.state,null),Zs(e,n,c,l),c.state=e.memoizedState),typeof c.componentDidMount=="function"&&(e.flags|=4194308)}function dn(e,t){try{var n="",l=t;do n+=je(l),l=l.return;while(l);var c=n}catch(d){c=`
Error generating stack: `+d.message+`
`+d.stack}return{value:e,source:t,stack:c,digest:null}}function ul(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function dl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var ih=typeof WeakMap=="function"?WeakMap:Map;function mu(e,t,n){n=Jt(-1,n),n.tag=3,n.payload={element:null};var l=t.value;return n.callback=function(){fa||(fa=!0,Cl=l),dl(e,t)},n}function fu(e,t,n){n=Jt(-1,n),n.tag=3;var l=e.type.getDerivedStateFromError;if(typeof l=="function"){var c=t.value;n.payload=function(){return l(c)},n.callback=function(){dl(e,t)}}var d=e.stateNode;return d!==null&&typeof d.componentDidCatch=="function"&&(n.callback=function(){dl(e,t),typeof l!="function"&&(hr===null?hr=new Set([this]):hr.add(this));var h=t.stack;this.componentDidCatch(t.value,{componentStack:h!==null?h:""})}),n}function hu(e,t,n){var l=e.pingCache;if(l===null){l=e.pingCache=new ih;var c=new Set;l.set(t,c)}else c=l.get(t),c===void 0&&(c=new Set,l.set(t,c));c.has(n)||(c.add(n),e=bh.bind(null,e,t,n),t.then(e,e))}function pu(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function xu(e,t,n,l,c){return(e.mode&1)===0?(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Jt(-1,1),t.tag=2,mr(n,t,1))),n.lanes|=1),e):(e.flags|=65536,e.lanes=c,e)}var ch=F.ReactCurrentOwner,ct=!1;function st(e,t,n,l){t.child=e===null?_c(t,null,n,l):on(t,e.child,n,l)}function gu(e,t,n,l,c){n=n.render;var d=t.ref;return cn(t,c),l=rl(e,t,n,l,d,c),n=nl(),e!==null&&!ct?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~c,qt(e,t,c)):(_e&&n&&Io(t),t.flags|=1,st(e,t,l,c),t.child)}function yu(e,t,n,l,c){if(e===null){var d=n.type;return typeof d=="function"&&!Ml(d)&&d.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=d,vu(e,t,d,l,c)):(e=va(n.type,null,l,t,t.mode,c),e.ref=t.ref,e.return=t,t.child=e)}if(d=e.child,(e.lanes&c)===0){var h=d.memoizedProps;if(n=n.compare,n=n!==null?n:In,n(h,l)&&e.ref===t.ref)return qt(e,t,c)}return t.flags|=1,e=yr(d,l),e.ref=t.ref,e.return=t,t.child=e}function vu(e,t,n,l,c){if(e!==null){var d=e.memoizedProps;if(In(d,l)&&e.ref===t.ref)if(ct=!1,t.pendingProps=l=d,(e.lanes&c)!==0)(e.flags&131072)!==0&&(ct=!0);else return t.lanes=e.lanes,qt(e,t,c)}return ml(e,t,n,l,c)}function wu(e,t,n){var l=t.pendingProps,c=l.children,d=e!==null?e.memoizedState:null;if(l.mode==="hidden")if((t.mode&1)===0)t.memoizedState={baseLanes:0,cachePool:null,transitions:null},Oe(fn,yt),yt|=n;else{if((n&1073741824)===0)return e=d!==null?d.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,Oe(fn,yt),yt|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},l=d!==null?d.baseLanes:n,Oe(fn,yt),yt|=l}else d!==null?(l=d.baseLanes|n,t.memoizedState=null):l=n,Oe(fn,yt),yt|=l;return st(e,t,c,n),t.child}function ju(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function ml(e,t,n,l,c){var d=it(n)?Er:Ge.current;return d=rn(t,d),cn(t,c),n=rl(e,t,n,l,d,c),l=nl(),e!==null&&!ct?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~c,qt(e,t,c)):(_e&&l&&Io(t),t.flags|=1,st(e,t,n,c),t.child)}function bu(e,t,n,l,c){if(it(n)){var d=!0;Hs(t)}else d=!1;if(cn(t,c),t.stateNode===null)ia(e,t),uu(t,n,l),cl(t,n,l,c),l=!0;else if(e===null){var h=t.stateNode,w=t.memoizedProps;h.props=w;var N=h.context,M=n.contextType;typeof M=="object"&&M!==null?M=jt(M):(M=it(n)?Er:Ge.current,M=rn(t,M));var V=n.getDerivedStateFromProps,Q=typeof V=="function"||typeof h.getSnapshotBeforeUpdate=="function";Q||typeof h.UNSAFE_componentWillReceiveProps!="function"&&typeof h.componentWillReceiveProps!="function"||(w!==l||N!==M)&&du(t,h,l,M),dr=!1;var W=t.memoizedState;h.state=W,Zs(t,l,h,c),N=t.memoizedState,w!==l||W!==N||lt.current||dr?(typeof V=="function"&&(il(t,n,V,l),N=t.memoizedState),(w=dr||cu(t,n,w,l,W,N,M))?(Q||typeof h.UNSAFE_componentWillMount!="function"&&typeof h.componentWillMount!="function"||(typeof h.componentWillMount=="function"&&h.componentWillMount(),typeof h.UNSAFE_componentWillMount=="function"&&h.UNSAFE_componentWillMount()),typeof h.componentDidMount=="function"&&(t.flags|=4194308)):(typeof h.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=N),h.props=l,h.state=N,h.context=M,l=w):(typeof h.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{h=t.stateNode,zc(e,t),w=t.memoizedProps,M=t.type===t.elementType?w:Pt(t.type,w),h.props=M,Q=t.pendingProps,W=h.context,N=n.contextType,typeof N=="object"&&N!==null?N=jt(N):(N=it(n)?Er:Ge.current,N=rn(t,N));var G=n.getDerivedStateFromProps;(V=typeof G=="function"||typeof h.getSnapshotBeforeUpdate=="function")||typeof h.UNSAFE_componentWillReceiveProps!="function"&&typeof h.componentWillReceiveProps!="function"||(w!==Q||W!==N)&&du(t,h,l,N),dr=!1,W=t.memoizedState,h.state=W,Zs(t,l,h,c);var ne=t.memoizedState;w!==Q||W!==ne||lt.current||dr?(typeof G=="function"&&(il(t,n,G,l),ne=t.memoizedState),(M=dr||cu(t,n,M,l,W,ne,N)||!1)?(V||typeof h.UNSAFE_componentWillUpdate!="function"&&typeof h.componentWillUpdate!="function"||(typeof h.componentWillUpdate=="function"&&h.componentWillUpdate(l,ne,N),typeof h.UNSAFE_componentWillUpdate=="function"&&h.UNSAFE_componentWillUpdate(l,ne,N)),typeof h.componentDidUpdate=="function"&&(t.flags|=4),typeof h.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof h.componentDidUpdate!="function"||w===e.memoizedProps&&W===e.memoizedState||(t.flags|=4),typeof h.getSnapshotBeforeUpdate!="function"||w===e.memoizedProps&&W===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=ne),h.props=l,h.state=ne,h.context=N,l=M):(typeof h.componentDidUpdate!="function"||w===e.memoizedProps&&W===e.memoizedState||(t.flags|=4),typeof h.getSnapshotBeforeUpdate!="function"||w===e.memoizedProps&&W===e.memoizedState||(t.flags|=1024),l=!1)}return fl(e,t,n,l,d,c)}function fl(e,t,n,l,c,d){ju(e,t);var h=(t.flags&128)!==0;if(!l&&!h)return c&&Ec(t,n,!1),qt(e,t,d);l=t.stateNode,ch.current=t;var w=h&&typeof n.getDerivedStateFromError!="function"?null:l.render();return t.flags|=1,e!==null&&h?(t.child=on(t,e.child,null,d),t.child=on(t,null,w,d)):st(e,t,w,d),t.memoizedState=l.state,c&&Ec(t,n,!0),t.child}function Nu(e){var t=e.stateNode;t.pendingContext?Sc(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Sc(e,t.context,!1),Ko(e,t.containerInfo)}function ku(e,t,n,l,c){return an(),Wo(c),t.flags|=256,st(e,t,n,l),t.child}var hl={dehydrated:null,treeContext:null,retryLane:0};function pl(e){return{baseLanes:e,cachePool:null,transitions:null}}function Su(e,t,n){var l=t.pendingProps,c=Fe.current,d=!1,h=(t.flags&128)!==0,w;if((w=h)||(w=e!==null&&e.memoizedState===null?!1:(c&2)!==0),w?(d=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(c|=1),Oe(Fe,c&1),e===null)return Uo(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?((t.mode&1)===0?t.lanes=1:e.data==="$!"?t.lanes=8:t.lanes=1073741824,null):(h=l.children,e=l.fallback,d?(l=t.mode,d=t.child,h={mode:"hidden",children:h},(l&1)===0&&d!==null?(d.childLanes=0,d.pendingProps=h):d=wa(h,l,0,null),e=Fr(e,l,n,null),d.return=t,e.return=t,d.sibling=e,t.child=d,t.child.memoizedState=pl(n),t.memoizedState=hl,e):xl(t,h));if(c=e.memoizedState,c!==null&&(w=c.dehydrated,w!==null))return uh(e,t,h,l,w,c,n);if(d){d=l.fallback,h=t.mode,c=e.child,w=c.sibling;var N={mode:"hidden",children:l.children};return(h&1)===0&&t.child!==c?(l=t.child,l.childLanes=0,l.pendingProps=N,t.deletions=null):(l=yr(c,N),l.subtreeFlags=c.subtreeFlags&14680064),w!==null?d=yr(w,d):(d=Fr(d,h,n,null),d.flags|=2),d.return=t,l.return=t,l.sibling=d,t.child=l,l=d,d=t.child,h=e.child.memoizedState,h=h===null?pl(n):{baseLanes:h.baseLanes|n,cachePool:null,transitions:h.transitions},d.memoizedState=h,d.childLanes=e.childLanes&~n,t.memoizedState=hl,l}return d=e.child,e=d.sibling,l=yr(d,{mode:"visible",children:l.children}),(t.mode&1)===0&&(l.lanes=n),l.return=t,l.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=l,t.memoizedState=null,l}function xl(e,t){return t=wa({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function la(e,t,n,l){return l!==null&&Wo(l),on(t,e.child,null,n),e=xl(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function uh(e,t,n,l,c,d,h){if(n)return t.flags&256?(t.flags&=-257,l=ul(Error(o(422))),la(e,t,h,l)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(d=l.fallback,c=t.mode,l=wa({mode:"visible",children:l.children},c,0,null),d=Fr(d,c,h,null),d.flags|=2,l.return=t,d.return=t,l.sibling=d,t.child=l,(t.mode&1)!==0&&on(t,e.child,null,h),t.child.memoizedState=pl(h),t.memoizedState=hl,d);if((t.mode&1)===0)return la(e,t,h,null);if(c.data==="$!"){if(l=c.nextSibling&&c.nextSibling.dataset,l)var w=l.dgst;return l=w,d=Error(o(419)),l=ul(d,l,void 0),la(e,t,h,l)}if(w=(h&e.childLanes)!==0,ct||w){if(l=Je,l!==null){switch(h&-h){case 4:c=2;break;case 16:c=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:c=32;break;case 536870912:c=268435456;break;default:c=0}c=(c&(l.suspendedLanes|h))!==0?0:c,c!==0&&c!==d.retryLane&&(d.retryLane=c,Yt(e,c),Lt(l,e,c,-1))}return Ol(),l=ul(Error(o(421))),la(e,t,h,l)}return c.data==="$?"?(t.flags|=128,t.child=e.child,t=Nh.bind(null,e),c._reactRetry=t,null):(e=d.treeContext,gt=lr(c.nextSibling),xt=t,_e=!0,Et=null,e!==null&&(vt[wt++]=Vt,vt[wt++]=Qt,vt[wt++]=Pr,Vt=e.id,Qt=e.overflow,Pr=t),t=xl(t,l.children),t.flags|=4096,t)}function Cu(e,t,n){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Yo(e.return,t,n)}function gl(e,t,n,l,c){var d=e.memoizedState;d===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:n,tailMode:c}:(d.isBackwards=t,d.rendering=null,d.renderingStartTime=0,d.last=l,d.tail=n,d.tailMode=c)}function Eu(e,t,n){var l=t.pendingProps,c=l.revealOrder,d=l.tail;if(st(e,t,l.children,n),l=Fe.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Cu(e,n,t);else if(e.tag===19)Cu(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}if(Oe(Fe,l),(t.mode&1)===0)t.memoizedState=null;else switch(c){case"forwards":for(n=t.child,c=null;n!==null;)e=n.alternate,e!==null&&ea(e)===null&&(c=n),n=n.sibling;n=c,n===null?(c=t.child,t.child=null):(c=n.sibling,n.sibling=null),gl(t,!1,c,n,d);break;case"backwards":for(n=null,c=t.child,t.child=null;c!==null;){if(e=c.alternate,e!==null&&ea(e)===null){t.child=c;break}e=c.sibling,c.sibling=n,n=c,c=e}gl(t,!0,n,null,d);break;case"together":gl(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function ia(e,t){(t.mode&1)===0&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function qt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Mr|=t.lanes,(n&t.childLanes)===0)return null;if(e!==null&&t.child!==e.child)throw Error(o(153));if(t.child!==null){for(e=t.child,n=yr(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=yr(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function dh(e,t,n){switch(t.tag){case 3:Nu(t),an();break;case 5:$c(t);break;case 1:it(t.type)&&Hs(t);break;case 4:Ko(t,t.stateNode.containerInfo);break;case 10:var l=t.type._context,c=t.memoizedProps.value;Oe(Ks,l._currentValue),l._currentValue=c;break;case 13:if(l=t.memoizedState,l!==null)return l.dehydrated!==null?(Oe(Fe,Fe.current&1),t.flags|=128,null):(n&t.child.childLanes)!==0?Su(e,t,n):(Oe(Fe,Fe.current&1),e=qt(e,t,n),e!==null?e.sibling:null);Oe(Fe,Fe.current&1);break;case 19:if(l=(n&t.childLanes)!==0,(e.flags&128)!==0){if(l)return Eu(e,t,n);t.flags|=128}if(c=t.memoizedState,c!==null&&(c.rendering=null,c.tail=null,c.lastEffect=null),Oe(Fe,Fe.current),l)break;return null;case 22:case 23:return t.lanes=0,wu(e,t,n)}return qt(e,t,n)}var Pu,yl,Du,Tu;Pu=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}},yl=function(){},Du=function(e,t,n,l){var c=e.memoizedProps;if(c!==l){e=t.stateNode,Lr(zt.current);var d=null;switch(n){case"input":c=A(e,c),l=A(e,l),d=[];break;case"select":c=X({},c,{value:void 0}),l=X({},l,{value:void 0}),d=[];break;case"textarea":c=kr(e,c),l=kr(e,l),d=[];break;default:typeof c.onClick!="function"&&typeof l.onClick=="function"&&(e.onclick=$s)}Xa(n,l);var h;n=null;for(M in c)if(!l.hasOwnProperty(M)&&c.hasOwnProperty(M)&&c[M]!=null)if(M==="style"){var w=c[M];for(h in w)w.hasOwnProperty(h)&&(n||(n={}),n[h]="")}else M!=="dangerouslySetInnerHTML"&&M!=="children"&&M!=="suppressContentEditableWarning"&&M!=="suppressHydrationWarning"&&M!=="autoFocus"&&(u.hasOwnProperty(M)?d||(d=[]):(d=d||[]).push(M,null));for(M in l){var N=l[M];if(w=c!=null?c[M]:void 0,l.hasOwnProperty(M)&&N!==w&&(N!=null||w!=null))if(M==="style")if(w){for(h in w)!w.hasOwnProperty(h)||N&&N.hasOwnProperty(h)||(n||(n={}),n[h]="");for(h in N)N.hasOwnProperty(h)&&w[h]!==N[h]&&(n||(n={}),n[h]=N[h])}else n||(d||(d=[]),d.push(M,n)),n=N;else M==="dangerouslySetInnerHTML"?(N=N?N.__html:void 0,w=w?w.__html:void 0,N!=null&&w!==N&&(d=d||[]).push(M,N)):M==="children"?typeof N!="string"&&typeof N!="number"||(d=d||[]).push(M,""+N):M!=="suppressContentEditableWarning"&&M!=="suppressHydrationWarning"&&(u.hasOwnProperty(M)?(N!=null&&M==="onScroll"&&Re("scroll",e),d||w===N||(d=[])):(d=d||[]).push(M,N))}n&&(d=d||[]).push("style",n);var M=d;(t.updateQueue=M)&&(t.flags|=4)}},Tu=function(e,t,n,l){n!==l&&(t.flags|=4)};function es(e,t){if(!_e)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var l=null;n!==null;)n.alternate!==null&&(l=n),n=n.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function et(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,l=0;if(t)for(var c=e.child;c!==null;)n|=c.lanes|c.childLanes,l|=c.subtreeFlags&14680064,l|=c.flags&14680064,c.return=e,c=c.sibling;else for(c=e.child;c!==null;)n|=c.lanes|c.childLanes,l|=c.subtreeFlags,l|=c.flags,c.return=e,c=c.sibling;return e.subtreeFlags|=l,e.childLanes=n,t}function mh(e,t,n){var l=t.pendingProps;switch(Bo(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return et(t),null;case 1:return it(t.type)&&Ws(),et(t),null;case 3:return l=t.stateNode,un(),Ae(lt),Ae(Ge),Zo(),l.pendingContext&&(l.context=l.pendingContext,l.pendingContext=null),(e===null||e.child===null)&&(Js(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,Et!==null&&(Dl(Et),Et=null))),yl(e,t),et(t),null;case 5:Xo(t);var c=Lr(qn.current);if(n=t.type,e!==null&&t.stateNode!=null)Du(e,t,n,l,c),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!l){if(t.stateNode===null)throw Error(o(166));return et(t),null}if(e=Lr(zt.current),Js(t)){l=t.stateNode,n=t.type;var d=t.memoizedProps;switch(l[Ft]=t,l[Hn]=d,e=(t.mode&1)!==0,n){case"dialog":Re("cancel",l),Re("close",l);break;case"iframe":case"object":case"embed":Re("load",l);break;case"video":case"audio":for(c=0;c<$n.length;c++)Re($n[c],l);break;case"source":Re("error",l);break;case"img":case"image":case"link":Re("error",l),Re("load",l);break;case"details":Re("toggle",l);break;case"input":le(l,d),Re("invalid",l);break;case"select":l._wrapperState={wasMultiple:!!d.multiple},Re("invalid",l);break;case"textarea":fi(l,d),Re("invalid",l)}Xa(n,d),c=null;for(var h in d)if(d.hasOwnProperty(h)){var w=d[h];h==="children"?typeof w=="string"?l.textContent!==w&&(d.suppressHydrationWarning!==!0&&Bs(l.textContent,w,e),c=["children",w]):typeof w=="number"&&l.textContent!==""+w&&(d.suppressHydrationWarning!==!0&&Bs(l.textContent,w,e),c=["children",""+w]):u.hasOwnProperty(h)&&w!=null&&h==="onScroll"&&Re("scroll",l)}switch(n){case"input":Wr(l),Le(l,d,!0);break;case"textarea":Wr(l),pi(l);break;case"select":case"option":break;default:typeof d.onClick=="function"&&(l.onclick=$s)}l=c,t.updateQueue=l,l!==null&&(t.flags|=4)}else{h=c.nodeType===9?c:c.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=xi(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=h.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof l.is=="string"?e=h.createElement(n,{is:l.is}):(e=h.createElement(n),n==="select"&&(h=e,l.multiple?h.multiple=!0:l.size&&(h.size=l.size))):e=h.createElementNS(e,n),e[Ft]=t,e[Hn]=l,Pu(e,t,!1,!1),t.stateNode=e;e:{switch(h=Ga(n,l),n){case"dialog":Re("cancel",e),Re("close",e),c=l;break;case"iframe":case"object":case"embed":Re("load",e),c=l;break;case"video":case"audio":for(c=0;c<$n.length;c++)Re($n[c],e);c=l;break;case"source":Re("error",e),c=l;break;case"img":case"image":case"link":Re("error",e),Re("load",e),c=l;break;case"details":Re("toggle",e),c=l;break;case"input":le(e,l),c=A(e,l),Re("invalid",e);break;case"option":c=l;break;case"select":e._wrapperState={wasMultiple:!!l.multiple},c=X({},l,{value:void 0}),Re("invalid",e);break;case"textarea":fi(e,l),c=kr(e,l),Re("invalid",e);break;default:c=l}Xa(n,c),w=c;for(d in w)if(w.hasOwnProperty(d)){var N=w[d];d==="style"?vi(e,N):d==="dangerouslySetInnerHTML"?(N=N?N.__html:void 0,N!=null&&gi(e,N)):d==="children"?typeof N=="string"?(n!=="textarea"||N!=="")&&bn(e,N):typeof N=="number"&&bn(e,""+N):d!=="suppressContentEditableWarning"&&d!=="suppressHydrationWarning"&&d!=="autoFocus"&&(u.hasOwnProperty(d)?N!=null&&d==="onScroll"&&Re("scroll",e):N!=null&&H(e,d,N,h))}switch(n){case"input":Wr(e),Le(e,l,!1);break;case"textarea":Wr(e),pi(e);break;case"option":l.value!=null&&e.setAttribute("value",""+Se(l.value));break;case"select":e.multiple=!!l.multiple,d=l.value,d!=null?er(e,!!l.multiple,d,!1):l.defaultValue!=null&&er(e,!!l.multiple,l.defaultValue,!0);break;default:typeof c.onClick=="function"&&(e.onclick=$s)}switch(n){case"button":case"input":case"select":case"textarea":l=!!l.autoFocus;break e;case"img":l=!0;break e;default:l=!1}}l&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return et(t),null;case 6:if(e&&t.stateNode!=null)Tu(e,t,e.memoizedProps,l);else{if(typeof l!="string"&&t.stateNode===null)throw Error(o(166));if(n=Lr(qn.current),Lr(zt.current),Js(t)){if(l=t.stateNode,n=t.memoizedProps,l[Ft]=t,(d=l.nodeValue!==n)&&(e=xt,e!==null))switch(e.tag){case 3:Bs(l.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Bs(l.nodeValue,n,(e.mode&1)!==0)}d&&(t.flags|=4)}else l=(n.nodeType===9?n:n.ownerDocument).createTextNode(l),l[Ft]=t,t.stateNode=l}return et(t),null;case 13:if(Ae(Fe),l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(_e&&gt!==null&&(t.mode&1)!==0&&(t.flags&128)===0)Mc(),an(),t.flags|=98560,d=!1;else if(d=Js(t),l!==null&&l.dehydrated!==null){if(e===null){if(!d)throw Error(o(318));if(d=t.memoizedState,d=d!==null?d.dehydrated:null,!d)throw Error(o(317));d[Ft]=t}else an(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;et(t),d=!1}else Et!==null&&(Dl(Et),Et=null),d=!0;if(!d)return t.flags&65536?t:null}return(t.flags&128)!==0?(t.lanes=n,t):(l=l!==null,l!==(e!==null&&e.memoizedState!==null)&&l&&(t.child.flags|=8192,(t.mode&1)!==0&&(e===null||(Fe.current&1)!==0?Qe===0&&(Qe=3):Ol())),t.updateQueue!==null&&(t.flags|=4),et(t),null);case 4:return un(),yl(e,t),e===null&&Un(t.stateNode.containerInfo),et(t),null;case 10:return Qo(t.type._context),et(t),null;case 17:return it(t.type)&&Ws(),et(t),null;case 19:if(Ae(Fe),d=t.memoizedState,d===null)return et(t),null;if(l=(t.flags&128)!==0,h=d.rendering,h===null)if(l)es(d,!1);else{if(Qe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(h=ea(e),h!==null){for(t.flags|=128,es(d,!1),l=h.updateQueue,l!==null&&(t.updateQueue=l,t.flags|=4),t.subtreeFlags=0,l=n,n=t.child;n!==null;)d=n,e=l,d.flags&=14680066,h=d.alternate,h===null?(d.childLanes=0,d.lanes=e,d.child=null,d.subtreeFlags=0,d.memoizedProps=null,d.memoizedState=null,d.updateQueue=null,d.dependencies=null,d.stateNode=null):(d.childLanes=h.childLanes,d.lanes=h.lanes,d.child=h.child,d.subtreeFlags=0,d.deletions=null,d.memoizedProps=h.memoizedProps,d.memoizedState=h.memoizedState,d.updateQueue=h.updateQueue,d.type=h.type,e=h.dependencies,d.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return Oe(Fe,Fe.current&1|2),t.child}e=e.sibling}d.tail!==null&&Ue()>hn&&(t.flags|=128,l=!0,es(d,!1),t.lanes=4194304)}else{if(!l)if(e=ea(h),e!==null){if(t.flags|=128,l=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),es(d,!0),d.tail===null&&d.tailMode==="hidden"&&!h.alternate&&!_e)return et(t),null}else 2*Ue()-d.renderingStartTime>hn&&n!==1073741824&&(t.flags|=128,l=!0,es(d,!1),t.lanes=4194304);d.isBackwards?(h.sibling=t.child,t.child=h):(n=d.last,n!==null?n.sibling=h:t.child=h,d.last=h)}return d.tail!==null?(t=d.tail,d.rendering=t,d.tail=t.sibling,d.renderingStartTime=Ue(),t.sibling=null,n=Fe.current,Oe(Fe,l?n&1|2:n&1),t):(et(t),null);case 22:case 23:return Ll(),l=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==l&&(t.flags|=8192),l&&(t.mode&1)!==0?(yt&1073741824)!==0&&(et(t),t.subtreeFlags&6&&(t.flags|=8192)):et(t),null;case 24:return null;case 25:return null}throw Error(o(156,t.tag))}function fh(e,t){switch(Bo(t),t.tag){case 1:return it(t.type)&&Ws(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return un(),Ae(lt),Ae(Ge),Zo(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 5:return Xo(t),null;case 13:if(Ae(Fe),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(o(340));an()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Ae(Fe),null;case 4:return un(),null;case 10:return Qo(t.type._context),null;case 22:case 23:return Ll(),null;case 24:return null;default:return null}}var ca=!1,tt=!1,hh=typeof WeakSet=="function"?WeakSet:Set,te=null;function mn(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(l){$e(e,t,l)}else n.current=null}function vl(e,t,n){try{n()}catch(l){$e(e,t,l)}}var Lu=!1;function ph(e,t){if(Lo=Ds,e=cc(),No(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var l=n.getSelection&&n.getSelection();if(l&&l.rangeCount!==0){n=l.anchorNode;var c=l.anchorOffset,d=l.focusNode;l=l.focusOffset;try{n.nodeType,d.nodeType}catch{n=null;break e}var h=0,w=-1,N=-1,M=0,V=0,Q=e,W=null;t:for(;;){for(var G;Q!==n||c!==0&&Q.nodeType!==3||(w=h+c),Q!==d||l!==0&&Q.nodeType!==3||(N=h+l),Q.nodeType===3&&(h+=Q.nodeValue.length),(G=Q.firstChild)!==null;)W=Q,Q=G;for(;;){if(Q===e)break t;if(W===n&&++M===c&&(w=h),W===d&&++V===l&&(N=h),(G=Q.nextSibling)!==null)break;Q=W,W=Q.parentNode}Q=G}n=w===-1||N===-1?null:{start:w,end:N}}else n=null}n=n||{start:0,end:0}}else n=null;for(Oo={focusedElem:e,selectionRange:n},Ds=!1,te=t;te!==null;)if(t=te,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,te=e;else for(;te!==null;){t=te;try{var ne=t.alternate;if((t.flags&1024)!==0)switch(t.tag){case 0:case 11:case 15:break;case 1:if(ne!==null){var se=ne.memoizedProps,We=ne.memoizedState,L=t.stateNode,C=L.getSnapshotBeforeUpdate(t.elementType===t.type?se:Pt(t.type,se),We);L.__reactInternalSnapshotBeforeUpdate=C}break;case 3:var O=t.stateNode.containerInfo;O.nodeType===1?O.textContent="":O.nodeType===9&&O.documentElement&&O.removeChild(O.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(o(163))}}catch(K){$e(t,t.return,K)}if(e=t.sibling,e!==null){e.return=t.return,te=e;break}te=t.return}return ne=Lu,Lu=!1,ne}function ts(e,t,n){var l=t.updateQueue;if(l=l!==null?l.lastEffect:null,l!==null){var c=l=l.next;do{if((c.tag&e)===e){var d=c.destroy;c.destroy=void 0,d!==void 0&&vl(t,n,d)}c=c.next}while(c!==l)}}function ua(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var l=n.create;n.destroy=l()}n=n.next}while(n!==t)}}function wl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function Ou(e){var t=e.alternate;t!==null&&(e.alternate=null,Ou(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[Ft],delete t[Hn],delete t[_o],delete t[Xf],delete t[Gf])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function Mu(e){return e.tag===5||e.tag===3||e.tag===4}function Ru(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Mu(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function jl(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=$s));else if(l!==4&&(e=e.child,e!==null))for(jl(e,t,n),e=e.sibling;e!==null;)jl(e,t,n),e=e.sibling}function bl(e,t,n){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(l!==4&&(e=e.child,e!==null))for(bl(e,t,n),e=e.sibling;e!==null;)bl(e,t,n),e=e.sibling}var Ke=null,Dt=!1;function fr(e,t,n){for(n=n.child;n!==null;)Au(e,t,n),n=n.sibling}function Au(e,t,n){if(_t&&typeof _t.onCommitFiberUnmount=="function")try{_t.onCommitFiberUnmount(Ns,n)}catch{}switch(n.tag){case 5:tt||mn(n,t);case 6:var l=Ke,c=Dt;Ke=null,fr(e,t,n),Ke=l,Dt=c,Ke!==null&&(Dt?(e=Ke,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):Ke.removeChild(n.stateNode));break;case 18:Ke!==null&&(Dt?(e=Ke,n=n.stateNode,e.nodeType===8?Ao(e.parentNode,n):e.nodeType===1&&Ao(e,n),Mn(e)):Ao(Ke,n.stateNode));break;case 4:l=Ke,c=Dt,Ke=n.stateNode.containerInfo,Dt=!0,fr(e,t,n),Ke=l,Dt=c;break;case 0:case 11:case 14:case 15:if(!tt&&(l=n.updateQueue,l!==null&&(l=l.lastEffect,l!==null))){c=l=l.next;do{var d=c,h=d.destroy;d=d.tag,h!==void 0&&((d&2)!==0||(d&4)!==0)&&vl(n,t,h),c=c.next}while(c!==l)}fr(e,t,n);break;case 1:if(!tt&&(mn(n,t),l=n.stateNode,typeof l.componentWillUnmount=="function"))try{l.props=n.memoizedProps,l.state=n.memoizedState,l.componentWillUnmount()}catch(w){$e(n,t,w)}fr(e,t,n);break;case 21:fr(e,t,n);break;case 22:n.mode&1?(tt=(l=tt)||n.memoizedState!==null,fr(e,t,n),tt=l):fr(e,t,n);break;default:fr(e,t,n)}}function _u(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new hh),t.forEach(function(l){var c=kh.bind(null,e,l);n.has(l)||(n.add(l),l.then(c,c))})}}function Tt(e,t){var n=t.deletions;if(n!==null)for(var l=0;l<n.length;l++){var c=n[l];try{var d=e,h=t,w=h;e:for(;w!==null;){switch(w.tag){case 5:Ke=w.stateNode,Dt=!1;break e;case 3:Ke=w.stateNode.containerInfo,Dt=!0;break e;case 4:Ke=w.stateNode.containerInfo,Dt=!0;break e}w=w.return}if(Ke===null)throw Error(o(160));Au(d,h,c),Ke=null,Dt=!1;var N=c.alternate;N!==null&&(N.return=null),c.return=null}catch(M){$e(c,t,M)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)Fu(t,e),t=t.sibling}function Fu(e,t){var n=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(Tt(t,e),Bt(e),l&4){try{ts(3,e,e.return),ua(3,e)}catch(se){$e(e,e.return,se)}try{ts(5,e,e.return)}catch(se){$e(e,e.return,se)}}break;case 1:Tt(t,e),Bt(e),l&512&&n!==null&&mn(n,n.return);break;case 5:if(Tt(t,e),Bt(e),l&512&&n!==null&&mn(n,n.return),e.flags&32){var c=e.stateNode;try{bn(c,"")}catch(se){$e(e,e.return,se)}}if(l&4&&(c=e.stateNode,c!=null)){var d=e.memoizedProps,h=n!==null?n.memoizedProps:d,w=e.type,N=e.updateQueue;if(e.updateQueue=null,N!==null)try{w==="input"&&d.type==="radio"&&d.name!=null&&we(c,d),Ga(w,h);var M=Ga(w,d);for(h=0;h<N.length;h+=2){var V=N[h],Q=N[h+1];V==="style"?vi(c,Q):V==="dangerouslySetInnerHTML"?gi(c,Q):V==="children"?bn(c,Q):H(c,V,Q,M)}switch(w){case"input":ge(c,d);break;case"textarea":hi(c,d);break;case"select":var W=c._wrapperState.wasMultiple;c._wrapperState.wasMultiple=!!d.multiple;var G=d.value;G!=null?er(c,!!d.multiple,G,!1):W!==!!d.multiple&&(d.defaultValue!=null?er(c,!!d.multiple,d.defaultValue,!0):er(c,!!d.multiple,d.multiple?[]:"",!1))}c[Hn]=d}catch(se){$e(e,e.return,se)}}break;case 6:if(Tt(t,e),Bt(e),l&4){if(e.stateNode===null)throw Error(o(162));c=e.stateNode,d=e.memoizedProps;try{c.nodeValue=d}catch(se){$e(e,e.return,se)}}break;case 3:if(Tt(t,e),Bt(e),l&4&&n!==null&&n.memoizedState.isDehydrated)try{Mn(t.containerInfo)}catch(se){$e(e,e.return,se)}break;case 4:Tt(t,e),Bt(e);break;case 13:Tt(t,e),Bt(e),c=e.child,c.flags&8192&&(d=c.memoizedState!==null,c.stateNode.isHidden=d,!d||c.alternate!==null&&c.alternate.memoizedState!==null||(Sl=Ue())),l&4&&_u(e);break;case 22:if(V=n!==null&&n.memoizedState!==null,e.mode&1?(tt=(M=tt)||V,Tt(t,e),tt=M):Tt(t,e),Bt(e),l&8192){if(M=e.memoizedState!==null,(e.stateNode.isHidden=M)&&!V&&(e.mode&1)!==0)for(te=e,V=e.child;V!==null;){for(Q=te=V;te!==null;){switch(W=te,G=W.child,W.tag){case 0:case 11:case 14:case 15:ts(4,W,W.return);break;case 1:mn(W,W.return);var ne=W.stateNode;if(typeof ne.componentWillUnmount=="function"){l=W,n=W.return;try{t=l,ne.props=t.memoizedProps,ne.state=t.memoizedState,ne.componentWillUnmount()}catch(se){$e(l,n,se)}}break;case 5:mn(W,W.return);break;case 22:if(W.memoizedState!==null){Bu(Q);continue}}G!==null?(G.return=W,te=G):Bu(Q)}V=V.sibling}e:for(V=null,Q=e;;){if(Q.tag===5){if(V===null){V=Q;try{c=Q.stateNode,M?(d=c.style,typeof d.setProperty=="function"?d.setProperty("display","none","important"):d.display="none"):(w=Q.stateNode,N=Q.memoizedProps.style,h=N!=null&&N.hasOwnProperty("display")?N.display:null,w.style.display=yi("display",h))}catch(se){$e(e,e.return,se)}}}else if(Q.tag===6){if(V===null)try{Q.stateNode.nodeValue=M?"":Q.memoizedProps}catch(se){$e(e,e.return,se)}}else if((Q.tag!==22&&Q.tag!==23||Q.memoizedState===null||Q===e)&&Q.child!==null){Q.child.return=Q,Q=Q.child;continue}if(Q===e)break e;for(;Q.sibling===null;){if(Q.return===null||Q.return===e)break e;V===Q&&(V=null),Q=Q.return}V===Q&&(V=null),Q.sibling.return=Q.return,Q=Q.sibling}}break;case 19:Tt(t,e),Bt(e),l&4&&_u(e);break;case 21:break;default:Tt(t,e),Bt(e)}}function Bt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(Mu(n)){var l=n;break e}n=n.return}throw Error(o(160))}switch(l.tag){case 5:var c=l.stateNode;l.flags&32&&(bn(c,""),l.flags&=-33);var d=Ru(e);bl(e,d,c);break;case 3:case 4:var h=l.stateNode.containerInfo,w=Ru(e);jl(e,w,h);break;default:throw Error(o(161))}}catch(N){$e(e,e.return,N)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function xh(e,t,n){te=e,zu(e)}function zu(e,t,n){for(var l=(e.mode&1)!==0;te!==null;){var c=te,d=c.child;if(c.tag===22&&l){var h=c.memoizedState!==null||ca;if(!h){var w=c.alternate,N=w!==null&&w.memoizedState!==null||tt;w=ca;var M=tt;if(ca=h,(tt=N)&&!M)for(te=c;te!==null;)h=te,N=h.child,h.tag===22&&h.memoizedState!==null?$u(c):N!==null?(N.return=h,te=N):$u(c);for(;d!==null;)te=d,zu(d),d=d.sibling;te=c,ca=w,tt=M}Iu(e)}else(c.subtreeFlags&8772)!==0&&d!==null?(d.return=c,te=d):Iu(e)}}function Iu(e){for(;te!==null;){var t=te;if((t.flags&8772)!==0){var n=t.alternate;try{if((t.flags&8772)!==0)switch(t.tag){case 0:case 11:case 15:tt||ua(5,t);break;case 1:var l=t.stateNode;if(t.flags&4&&!tt)if(n===null)l.componentDidMount();else{var c=t.elementType===t.type?n.memoizedProps:Pt(t.type,n.memoizedProps);l.componentDidUpdate(c,n.memoizedState,l.__reactInternalSnapshotBeforeUpdate)}var d=t.updateQueue;d!==null&&Bc(t,d,l);break;case 3:var h=t.updateQueue;if(h!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Bc(t,h,n)}break;case 5:var w=t.stateNode;if(n===null&&t.flags&4){n=w;var N=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":N.autoFocus&&n.focus();break;case"img":N.src&&(n.src=N.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var M=t.alternate;if(M!==null){var V=M.memoizedState;if(V!==null){var Q=V.dehydrated;Q!==null&&Mn(Q)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(o(163))}tt||t.flags&512&&wl(t)}catch(W){$e(t,t.return,W)}}if(t===e){te=null;break}if(n=t.sibling,n!==null){n.return=t.return,te=n;break}te=t.return}}function Bu(e){for(;te!==null;){var t=te;if(t===e){te=null;break}var n=t.sibling;if(n!==null){n.return=t.return,te=n;break}te=t.return}}function $u(e){for(;te!==null;){var t=te;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{ua(4,t)}catch(N){$e(t,n,N)}break;case 1:var l=t.stateNode;if(typeof l.componentDidMount=="function"){var c=t.return;try{l.componentDidMount()}catch(N){$e(t,c,N)}}var d=t.return;try{wl(t)}catch(N){$e(t,d,N)}break;case 5:var h=t.return;try{wl(t)}catch(N){$e(t,h,N)}}}catch(N){$e(t,t.return,N)}if(t===e){te=null;break}var w=t.sibling;if(w!==null){w.return=t.return,te=w;break}te=t.return}}var gh=Math.ceil,da=F.ReactCurrentDispatcher,Nl=F.ReactCurrentOwner,Nt=F.ReactCurrentBatchConfig,Ne=0,Je=null,He=null,Xe=0,yt=0,fn=ir(0),Qe=0,rs=null,Mr=0,ma=0,kl=0,ns=null,ut=null,Sl=0,hn=1/0,Kt=null,fa=!1,Cl=null,hr=null,ha=!1,pr=null,pa=0,ss=0,El=null,xa=-1,ga=0;function at(){return(Ne&6)!==0?Ue():xa!==-1?xa:xa=Ue()}function xr(e){return(e.mode&1)===0?1:(Ne&2)!==0&&Xe!==0?Xe&-Xe:eh.transition!==null?(ga===0&&(ga=Ri()),ga):(e=Ee,e!==0||(e=window.event,e=e===void 0?16:Wi(e.type)),e)}function Lt(e,t,n,l){if(50<ss)throw ss=0,El=null,Error(o(185));Pn(e,n,l),((Ne&2)===0||e!==Je)&&(e===Je&&((Ne&2)===0&&(ma|=n),Qe===4&&gr(e,Xe)),dt(e,l),n===1&&Ne===0&&(t.mode&1)===0&&(hn=Ue()+500,Vs&&ur()))}function dt(e,t){var n=e.callbackNode;Zm(e,t);var l=Cs(e,e===Je?Xe:0);if(l===0)n!==null&&Li(n),e.callbackNode=null,e.callbackPriority=0;else if(t=l&-l,e.callbackPriority!==t){if(n!=null&&Li(n),t===1)e.tag===0?Zf(Wu.bind(null,e)):Pc(Wu.bind(null,e)),qf(function(){(Ne&6)===0&&ur()}),n=null;else{switch(Ai(l)){case 1:n=ao;break;case 4:n=Oi;break;case 16:n=bs;break;case 536870912:n=Mi;break;default:n=bs}n=Xu(n,Uu.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function Uu(e,t){if(xa=-1,ga=0,(Ne&6)!==0)throw Error(o(327));var n=e.callbackNode;if(pn()&&e.callbackNode!==n)return null;var l=Cs(e,e===Je?Xe:0);if(l===0)return null;if((l&30)!==0||(l&e.expiredLanes)!==0||t)t=ya(e,l);else{t=l;var c=Ne;Ne|=2;var d=Vu();(Je!==e||Xe!==t)&&(Kt=null,hn=Ue()+500,Ar(e,t));do try{wh();break}catch(w){Hu(e,w)}while(!0);Vo(),da.current=d,Ne=c,He!==null?t=0:(Je=null,Xe=0,t=Qe)}if(t!==0){if(t===2&&(c=oo(e),c!==0&&(l=c,t=Pl(e,c))),t===1)throw n=rs,Ar(e,0),gr(e,l),dt(e,Ue()),n;if(t===6)gr(e,l);else{if(c=e.current.alternate,(l&30)===0&&!yh(c)&&(t=ya(e,l),t===2&&(d=oo(e),d!==0&&(l=d,t=Pl(e,d))),t===1))throw n=rs,Ar(e,0),gr(e,l),dt(e,Ue()),n;switch(e.finishedWork=c,e.finishedLanes=l,t){case 0:case 1:throw Error(o(345));case 2:_r(e,ut,Kt);break;case 3:if(gr(e,l),(l&130023424)===l&&(t=Sl+500-Ue(),10<t)){if(Cs(e,0)!==0)break;if(c=e.suspendedLanes,(c&l)!==l){at(),e.pingedLanes|=e.suspendedLanes&c;break}e.timeoutHandle=Ro(_r.bind(null,e,ut,Kt),t);break}_r(e,ut,Kt);break;case 4:if(gr(e,l),(l&4194240)===l)break;for(t=e.eventTimes,c=-1;0<l;){var h=31-St(l);d=1<<h,h=t[h],h>c&&(c=h),l&=~d}if(l=c,l=Ue()-l,l=(120>l?120:480>l?480:1080>l?1080:1920>l?1920:3e3>l?3e3:4320>l?4320:1960*gh(l/1960))-l,10<l){e.timeoutHandle=Ro(_r.bind(null,e,ut,Kt),l);break}_r(e,ut,Kt);break;case 5:_r(e,ut,Kt);break;default:throw Error(o(329))}}}return dt(e,Ue()),e.callbackNode===n?Uu.bind(null,e):null}function Pl(e,t){var n=ns;return e.current.memoizedState.isDehydrated&&(Ar(e,t).flags|=256),e=ya(e,t),e!==2&&(t=ut,ut=n,t!==null&&Dl(t)),e}function Dl(e){ut===null?ut=e:ut.push.apply(ut,e)}function yh(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var l=0;l<n.length;l++){var c=n[l],d=c.getSnapshot;c=c.value;try{if(!Ct(d(),c))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function gr(e,t){for(t&=~kl,t&=~ma,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-St(t),l=1<<n;e[n]=-1,t&=~l}}function Wu(e){if((Ne&6)!==0)throw Error(o(327));pn();var t=Cs(e,0);if((t&1)===0)return dt(e,Ue()),null;var n=ya(e,t);if(e.tag!==0&&n===2){var l=oo(e);l!==0&&(t=l,n=Pl(e,l))}if(n===1)throw n=rs,Ar(e,0),gr(e,t),dt(e,Ue()),n;if(n===6)throw Error(o(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,_r(e,ut,Kt),dt(e,Ue()),null}function Tl(e,t){var n=Ne;Ne|=1;try{return e(t)}finally{Ne=n,Ne===0&&(hn=Ue()+500,Vs&&ur())}}function Rr(e){pr!==null&&pr.tag===0&&(Ne&6)===0&&pn();var t=Ne;Ne|=1;var n=Nt.transition,l=Ee;try{if(Nt.transition=null,Ee=1,e)return e()}finally{Ee=l,Nt.transition=n,Ne=t,(Ne&6)===0&&ur()}}function Ll(){yt=fn.current,Ae(fn)}function Ar(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,Jf(n)),He!==null)for(n=He.return;n!==null;){var l=n;switch(Bo(l),l.tag){case 1:l=l.type.childContextTypes,l!=null&&Ws();break;case 3:un(),Ae(lt),Ae(Ge),Zo();break;case 5:Xo(l);break;case 4:un();break;case 13:Ae(Fe);break;case 19:Ae(Fe);break;case 10:Qo(l.type._context);break;case 22:case 23:Ll()}n=n.return}if(Je=e,He=e=yr(e.current,null),Xe=yt=t,Qe=0,rs=null,kl=ma=Mr=0,ut=ns=null,Tr!==null){for(t=0;t<Tr.length;t++)if(n=Tr[t],l=n.interleaved,l!==null){n.interleaved=null;var c=l.next,d=n.pending;if(d!==null){var h=d.next;d.next=c,l.next=h}n.pending=l}Tr=null}return e}function Hu(e,t){do{var n=He;try{if(Vo(),ta.current=aa,ra){for(var l=ze.memoizedState;l!==null;){var c=l.queue;c!==null&&(c.pending=null),l=l.next}ra=!1}if(Or=0,Ye=Ve=ze=null,Kn=!1,Xn=0,Nl.current=null,n===null||n.return===null){Qe=1,rs=t,He=null;break}e:{var d=e,h=n.return,w=n,N=t;if(t=Xe,w.flags|=32768,N!==null&&typeof N=="object"&&typeof N.then=="function"){var M=N,V=w,Q=V.tag;if((V.mode&1)===0&&(Q===0||Q===11||Q===15)){var W=V.alternate;W?(V.updateQueue=W.updateQueue,V.memoizedState=W.memoizedState,V.lanes=W.lanes):(V.updateQueue=null,V.memoizedState=null)}var G=pu(h);if(G!==null){G.flags&=-257,xu(G,h,w,d,t),G.mode&1&&hu(d,M,t),t=G,N=M;var ne=t.updateQueue;if(ne===null){var se=new Set;se.add(N),t.updateQueue=se}else ne.add(N);break e}else{if((t&1)===0){hu(d,M,t),Ol();break e}N=Error(o(426))}}else if(_e&&w.mode&1){var We=pu(h);if(We!==null){(We.flags&65536)===0&&(We.flags|=256),xu(We,h,w,d,t),Wo(dn(N,w));break e}}d=N=dn(N,w),Qe!==4&&(Qe=2),ns===null?ns=[d]:ns.push(d),d=h;do{switch(d.tag){case 3:d.flags|=65536,t&=-t,d.lanes|=t;var L=mu(d,N,t);Ic(d,L);break e;case 1:w=N;var C=d.type,O=d.stateNode;if((d.flags&128)===0&&(typeof C.getDerivedStateFromError=="function"||O!==null&&typeof O.componentDidCatch=="function"&&(hr===null||!hr.has(O)))){d.flags|=65536,t&=-t,d.lanes|=t;var K=fu(d,w,t);Ic(d,K);break e}}d=d.return}while(d!==null)}Yu(n)}catch(ae){t=ae,He===n&&n!==null&&(He=n=n.return);continue}break}while(!0)}function Vu(){var e=da.current;return da.current=aa,e===null?aa:e}function Ol(){(Qe===0||Qe===3||Qe===2)&&(Qe=4),Je===null||(Mr&268435455)===0&&(ma&268435455)===0||gr(Je,Xe)}function ya(e,t){var n=Ne;Ne|=2;var l=Vu();(Je!==e||Xe!==t)&&(Kt=null,Ar(e,t));do try{vh();break}catch(c){Hu(e,c)}while(!0);if(Vo(),Ne=n,da.current=l,He!==null)throw Error(o(261));return Je=null,Xe=0,Qe}function vh(){for(;He!==null;)Qu(He)}function wh(){for(;He!==null&&!Hm();)Qu(He)}function Qu(e){var t=Ku(e.alternate,e,yt);e.memoizedProps=e.pendingProps,t===null?Yu(e):He=t,Nl.current=null}function Yu(e){var t=e;do{var n=t.alternate;if(e=t.return,(t.flags&32768)===0){if(n=mh(n,t,yt),n!==null){He=n;return}}else{if(n=fh(n,t),n!==null){n.flags&=32767,He=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{Qe=6,He=null;return}}if(t=t.sibling,t!==null){He=t;return}He=t=e}while(t!==null);Qe===0&&(Qe=5)}function _r(e,t,n){var l=Ee,c=Nt.transition;try{Nt.transition=null,Ee=1,jh(e,t,n,l)}finally{Nt.transition=c,Ee=l}return null}function jh(e,t,n,l){do pn();while(pr!==null);if((Ne&6)!==0)throw Error(o(327));n=e.finishedWork;var c=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(o(177));e.callbackNode=null,e.callbackPriority=0;var d=n.lanes|n.childLanes;if(ef(e,d),e===Je&&(He=Je=null,Xe=0),(n.subtreeFlags&2064)===0&&(n.flags&2064)===0||ha||(ha=!0,Xu(bs,function(){return pn(),null})),d=(n.flags&15990)!==0,(n.subtreeFlags&15990)!==0||d){d=Nt.transition,Nt.transition=null;var h=Ee;Ee=1;var w=Ne;Ne|=4,Nl.current=null,ph(e,n),Fu(n,e),$f(Oo),Ds=!!Lo,Oo=Lo=null,e.current=n,xh(n),Vm(),Ne=w,Ee=h,Nt.transition=d}else e.current=n;if(ha&&(ha=!1,pr=e,pa=c),d=e.pendingLanes,d===0&&(hr=null),Jm(n.stateNode),dt(e,Ue()),t!==null)for(l=e.onRecoverableError,n=0;n<t.length;n++)c=t[n],l(c.value,{componentStack:c.stack,digest:c.digest});if(fa)throw fa=!1,e=Cl,Cl=null,e;return(pa&1)!==0&&e.tag!==0&&pn(),d=e.pendingLanes,(d&1)!==0?e===El?ss++:(ss=0,El=e):ss=0,ur(),null}function pn(){if(pr!==null){var e=Ai(pa),t=Nt.transition,n=Ee;try{if(Nt.transition=null,Ee=16>e?16:e,pr===null)var l=!1;else{if(e=pr,pr=null,pa=0,(Ne&6)!==0)throw Error(o(331));var c=Ne;for(Ne|=4,te=e.current;te!==null;){var d=te,h=d.child;if((te.flags&16)!==0){var w=d.deletions;if(w!==null){for(var N=0;N<w.length;N++){var M=w[N];for(te=M;te!==null;){var V=te;switch(V.tag){case 0:case 11:case 15:ts(8,V,d)}var Q=V.child;if(Q!==null)Q.return=V,te=Q;else for(;te!==null;){V=te;var W=V.sibling,G=V.return;if(Ou(V),V===M){te=null;break}if(W!==null){W.return=G,te=W;break}te=G}}}var ne=d.alternate;if(ne!==null){var se=ne.child;if(se!==null){ne.child=null;do{var We=se.sibling;se.sibling=null,se=We}while(se!==null)}}te=d}}if((d.subtreeFlags&2064)!==0&&h!==null)h.return=d,te=h;else e:for(;te!==null;){if(d=te,(d.flags&2048)!==0)switch(d.tag){case 0:case 11:case 15:ts(9,d,d.return)}var L=d.sibling;if(L!==null){L.return=d.return,te=L;break e}te=d.return}}var C=e.current;for(te=C;te!==null;){h=te;var O=h.child;if((h.subtreeFlags&2064)!==0&&O!==null)O.return=h,te=O;else e:for(h=C;te!==null;){if(w=te,(w.flags&2048)!==0)try{switch(w.tag){case 0:case 11:case 15:ua(9,w)}}catch(ae){$e(w,w.return,ae)}if(w===h){te=null;break e}var K=w.sibling;if(K!==null){K.return=w.return,te=K;break e}te=w.return}}if(Ne=c,ur(),_t&&typeof _t.onPostCommitFiberRoot=="function")try{_t.onPostCommitFiberRoot(Ns,e)}catch{}l=!0}return l}finally{Ee=n,Nt.transition=t}}return!1}function Ju(e,t,n){t=dn(n,t),t=mu(e,t,1),e=mr(e,t,1),t=at(),e!==null&&(Pn(e,1,t),dt(e,t))}function $e(e,t,n){if(e.tag===3)Ju(e,e,n);else for(;t!==null;){if(t.tag===3){Ju(t,e,n);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(hr===null||!hr.has(l))){e=dn(n,e),e=fu(t,e,1),t=mr(t,e,1),e=at(),t!==null&&(Pn(t,1,e),dt(t,e));break}}t=t.return}}function bh(e,t,n){var l=e.pingCache;l!==null&&l.delete(t),t=at(),e.pingedLanes|=e.suspendedLanes&n,Je===e&&(Xe&n)===n&&(Qe===4||Qe===3&&(Xe&130023424)===Xe&&500>Ue()-Sl?Ar(e,0):kl|=n),dt(e,t)}function qu(e,t){t===0&&((e.mode&1)===0?t=1:(t=Ss,Ss<<=1,(Ss&130023424)===0&&(Ss=4194304)));var n=at();e=Yt(e,t),e!==null&&(Pn(e,t,n),dt(e,n))}function Nh(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),qu(e,n)}function kh(e,t){var n=0;switch(e.tag){case 13:var l=e.stateNode,c=e.memoizedState;c!==null&&(n=c.retryLane);break;case 19:l=e.stateNode;break;default:throw Error(o(314))}l!==null&&l.delete(t),qu(e,n)}var Ku;Ku=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||lt.current)ct=!0;else{if((e.lanes&n)===0&&(t.flags&128)===0)return ct=!1,dh(e,t,n);ct=(e.flags&131072)!==0}else ct=!1,_e&&(t.flags&1048576)!==0&&Dc(t,Ys,t.index);switch(t.lanes=0,t.tag){case 2:var l=t.type;ia(e,t),e=t.pendingProps;var c=rn(t,Ge.current);cn(t,n),c=rl(null,t,l,e,c,n);var d=nl();return t.flags|=1,typeof c=="object"&&c!==null&&typeof c.render=="function"&&c.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,it(l)?(d=!0,Hs(t)):d=!1,t.memoizedState=c.state!==null&&c.state!==void 0?c.state:null,qo(t),c.updater=oa,t.stateNode=c,c._reactInternals=t,cl(t,l,e,n),t=fl(null,t,l,!0,d,n)):(t.tag=0,_e&&d&&Io(t),st(null,t,c,n),t=t.child),t;case 16:l=t.elementType;e:{switch(ia(e,t),e=t.pendingProps,c=l._init,l=c(l._payload),t.type=l,c=t.tag=Ch(l),e=Pt(l,e),c){case 0:t=ml(null,t,l,e,n);break e;case 1:t=bu(null,t,l,e,n);break e;case 11:t=gu(null,t,l,e,n);break e;case 14:t=yu(null,t,l,Pt(l.type,e),n);break e}throw Error(o(306,l,""))}return t;case 0:return l=t.type,c=t.pendingProps,c=t.elementType===l?c:Pt(l,c),ml(e,t,l,c,n);case 1:return l=t.type,c=t.pendingProps,c=t.elementType===l?c:Pt(l,c),bu(e,t,l,c,n);case 3:e:{if(Nu(t),e===null)throw Error(o(387));l=t.pendingProps,d=t.memoizedState,c=d.element,zc(e,t),Zs(t,l,null,n);var h=t.memoizedState;if(l=h.element,d.isDehydrated)if(d={element:l,isDehydrated:!1,cache:h.cache,pendingSuspenseBoundaries:h.pendingSuspenseBoundaries,transitions:h.transitions},t.updateQueue.baseState=d,t.memoizedState=d,t.flags&256){c=dn(Error(o(423)),t),t=ku(e,t,l,n,c);break e}else if(l!==c){c=dn(Error(o(424)),t),t=ku(e,t,l,n,c);break e}else for(gt=lr(t.stateNode.containerInfo.firstChild),xt=t,_e=!0,Et=null,n=_c(t,null,l,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(an(),l===c){t=qt(e,t,n);break e}st(e,t,l,n)}t=t.child}return t;case 5:return $c(t),e===null&&Uo(t),l=t.type,c=t.pendingProps,d=e!==null?e.memoizedProps:null,h=c.children,Mo(l,c)?h=null:d!==null&&Mo(l,d)&&(t.flags|=32),ju(e,t),st(e,t,h,n),t.child;case 6:return e===null&&Uo(t),null;case 13:return Su(e,t,n);case 4:return Ko(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=on(t,null,l,n):st(e,t,l,n),t.child;case 11:return l=t.type,c=t.pendingProps,c=t.elementType===l?c:Pt(l,c),gu(e,t,l,c,n);case 7:return st(e,t,t.pendingProps,n),t.child;case 8:return st(e,t,t.pendingProps.children,n),t.child;case 12:return st(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(l=t.type._context,c=t.pendingProps,d=t.memoizedProps,h=c.value,Oe(Ks,l._currentValue),l._currentValue=h,d!==null)if(Ct(d.value,h)){if(d.children===c.children&&!lt.current){t=qt(e,t,n);break e}}else for(d=t.child,d!==null&&(d.return=t);d!==null;){var w=d.dependencies;if(w!==null){h=d.child;for(var N=w.firstContext;N!==null;){if(N.context===l){if(d.tag===1){N=Jt(-1,n&-n),N.tag=2;var M=d.updateQueue;if(M!==null){M=M.shared;var V=M.pending;V===null?N.next=N:(N.next=V.next,V.next=N),M.pending=N}}d.lanes|=n,N=d.alternate,N!==null&&(N.lanes|=n),Yo(d.return,n,t),w.lanes|=n;break}N=N.next}}else if(d.tag===10)h=d.type===t.type?null:d.child;else if(d.tag===18){if(h=d.return,h===null)throw Error(o(341));h.lanes|=n,w=h.alternate,w!==null&&(w.lanes|=n),Yo(h,n,t),h=d.sibling}else h=d.child;if(h!==null)h.return=d;else for(h=d;h!==null;){if(h===t){h=null;break}if(d=h.sibling,d!==null){d.return=h.return,h=d;break}h=h.return}d=h}st(e,t,c.children,n),t=t.child}return t;case 9:return c=t.type,l=t.pendingProps.children,cn(t,n),c=jt(c),l=l(c),t.flags|=1,st(e,t,l,n),t.child;case 14:return l=t.type,c=Pt(l,t.pendingProps),c=Pt(l.type,c),yu(e,t,l,c,n);case 15:return vu(e,t,t.type,t.pendingProps,n);case 17:return l=t.type,c=t.pendingProps,c=t.elementType===l?c:Pt(l,c),ia(e,t),t.tag=1,it(l)?(e=!0,Hs(t)):e=!1,cn(t,n),uu(t,l,c),cl(t,l,c,n),fl(null,t,l,!0,e,n);case 19:return Eu(e,t,n);case 22:return wu(e,t,n)}throw Error(o(156,t.tag))};function Xu(e,t){return Ti(e,t)}function Sh(e,t,n,l){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function kt(e,t,n,l){return new Sh(e,t,n,l)}function Ml(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ch(e){if(typeof e=="function")return Ml(e)?1:0;if(e!=null){if(e=e.$$typeof,e===de)return 11;if(e===D)return 14}return 2}function yr(e,t){var n=e.alternate;return n===null?(n=kt(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function va(e,t,n,l,c,d){var h=2;if(l=e,typeof e=="function")Ml(e)&&(h=1);else if(typeof e=="string")h=5;else e:switch(e){case U:return Fr(n.children,c,d,t);case oe:h=8,c|=8;break;case fe:return e=kt(12,n,t,c|2),e.elementType=fe,e.lanes=d,e;case ie:return e=kt(13,n,t,c),e.elementType=ie,e.lanes=d,e;case J:return e=kt(19,n,t,c),e.elementType=J,e.lanes=d,e;case me:return wa(n,c,d,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case Z:h=10;break e;case re:h=9;break e;case de:h=11;break e;case D:h=14;break e;case q:h=16,l=null;break e}throw Error(o(130,e==null?e:typeof e,""))}return t=kt(h,n,t,c),t.elementType=e,t.type=l,t.lanes=d,t}function Fr(e,t,n,l){return e=kt(7,e,l,t),e.lanes=n,e}function wa(e,t,n,l){return e=kt(22,e,l,t),e.elementType=me,e.lanes=n,e.stateNode={isHidden:!1},e}function Rl(e,t,n){return e=kt(6,e,null,t),e.lanes=n,e}function Al(e,t,n){return t=kt(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Eh(e,t,n,l,c){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=lo(0),this.expirationTimes=lo(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=lo(0),this.identifierPrefix=l,this.onRecoverableError=c,this.mutableSourceEagerHydrationData=null}function _l(e,t,n,l,c,d,h,w,N){return e=new Eh(e,t,n,w,N),t===1?(t=1,d===!0&&(t|=8)):t=0,d=kt(3,null,null,t),e.current=d,d.stateNode=e,d.memoizedState={element:l,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},qo(d),e}function Ph(e,t,n){var l=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:z,key:l==null?null:""+l,children:e,containerInfo:t,implementation:n}}function Gu(e){if(!e)return cr;e=e._reactInternals;e:{if(Sr(e)!==e||e.tag!==1)throw Error(o(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(it(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(o(171))}if(e.tag===1){var n=e.type;if(it(n))return Cc(e,n,t)}return t}function Zu(e,t,n,l,c,d,h,w,N){return e=_l(n,l,!0,e,c,d,h,w,N),e.context=Gu(null),n=e.current,l=at(),c=xr(n),d=Jt(l,c),d.callback=t??null,mr(n,d,c),e.current.lanes=c,Pn(e,c,l),dt(e,l),e}function ja(e,t,n,l){var c=t.current,d=at(),h=xr(c);return n=Gu(n),t.context===null?t.context=n:t.pendingContext=n,t=Jt(d,h),t.payload={element:e},l=l===void 0?null:l,l!==null&&(t.callback=l),e=mr(c,t,h),e!==null&&(Lt(e,c,h,d),Gs(e,c,h)),h}function ba(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function ed(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Fl(e,t){ed(e,t),(e=e.alternate)&&ed(e,t)}function Dh(){return null}var td=typeof reportError=="function"?reportError:function(e){console.error(e)};function zl(e){this._internalRoot=e}Na.prototype.render=zl.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(o(409));ja(e,t,null,null)},Na.prototype.unmount=zl.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Rr(function(){ja(null,e,null,null)}),t[Wt]=null}};function Na(e){this._internalRoot=e}Na.prototype.unstable_scheduleHydration=function(e){if(e){var t=zi();e={blockedOn:null,target:e,priority:t};for(var n=0;n<sr.length&&t!==0&&t<sr[n].priority;n++);sr.splice(n,0,e),n===0&&$i(e)}};function Il(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function ka(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function rd(){}function Th(e,t,n,l,c){if(c){if(typeof l=="function"){var d=l;l=function(){var M=ba(h);d.call(M)}}var h=Zu(t,l,e,0,null,!1,!1,"",rd);return e._reactRootContainer=h,e[Wt]=h.current,Un(e.nodeType===8?e.parentNode:e),Rr(),h}for(;c=e.lastChild;)e.removeChild(c);if(typeof l=="function"){var w=l;l=function(){var M=ba(N);w.call(M)}}var N=_l(e,0,!1,null,null,!1,!1,"",rd);return e._reactRootContainer=N,e[Wt]=N.current,Un(e.nodeType===8?e.parentNode:e),Rr(function(){ja(t,N,n,l)}),N}function Sa(e,t,n,l,c){var d=n._reactRootContainer;if(d){var h=d;if(typeof c=="function"){var w=c;c=function(){var N=ba(h);w.call(N)}}ja(t,h,e,c)}else h=Th(n,t,e,c,l);return ba(h)}_i=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=En(t.pendingLanes);n!==0&&(io(t,n|1),dt(t,Ue()),(Ne&6)===0&&(hn=Ue()+500,ur()))}break;case 13:Rr(function(){var l=Yt(e,1);if(l!==null){var c=at();Lt(l,e,1,c)}}),Fl(e,1)}},co=function(e){if(e.tag===13){var t=Yt(e,134217728);if(t!==null){var n=at();Lt(t,e,134217728,n)}Fl(e,134217728)}},Fi=function(e){if(e.tag===13){var t=xr(e),n=Yt(e,t);if(n!==null){var l=at();Lt(n,e,t,l)}Fl(e,t)}},zi=function(){return Ee},Ii=function(e,t){var n=Ee;try{return Ee=e,t()}finally{Ee=n}},to=function(e,t,n){switch(t){case"input":if(ge(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var l=n[t];if(l!==e&&l.form===e.form){var c=Us(l);if(!c)throw Error(o(90));ys(l),ge(l,c)}}}break;case"textarea":hi(e,n);break;case"select":t=n.value,t!=null&&er(e,!!n.multiple,t,!1)}},Ni=Tl,ki=Rr;var Lh={usingClientEntryPoint:!1,Events:[Vn,en,Us,ji,bi,Tl]},as={findFiberByHostInstance:Cr,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},Oh={bundleType:as.bundleType,version:as.version,rendererPackageName:as.rendererPackageName,rendererConfig:as.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:F.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=Pi(e),e===null?null:e.stateNode},findFiberByHostInstance:as.findFiberByHostInstance||Dh,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Ca=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Ca.isDisabled&&Ca.supportsFiber)try{Ns=Ca.inject(Oh),_t=Ca}catch{}}return mt.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Lh,mt.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!Il(t))throw Error(o(200));return Ph(e,t,null,n)},mt.createRoot=function(e,t){if(!Il(e))throw Error(o(299));var n=!1,l="",c=td;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onRecoverableError!==void 0&&(c=t.onRecoverableError)),t=_l(e,1,!1,null,null,n,!1,l,c),e[Wt]=t.current,Un(e.nodeType===8?e.parentNode:e),new zl(t)},mt.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(o(188)):(e=Object.keys(e).join(","),Error(o(268,e)));return e=Pi(t),e=e===null?null:e.stateNode,e},mt.flushSync=function(e){return Rr(e)},mt.hydrate=function(e,t,n){if(!ka(t))throw Error(o(200));return Sa(null,e,t,!0,n)},mt.hydrateRoot=function(e,t,n){if(!Il(e))throw Error(o(405));var l=n!=null&&n.hydratedSources||null,c=!1,d="",h=td;if(n!=null&&(n.unstable_strictMode===!0&&(c=!0),n.identifierPrefix!==void 0&&(d=n.identifierPrefix),n.onRecoverableError!==void 0&&(h=n.onRecoverableError)),t=Zu(t,null,e,1,n??null,c,!1,d,h),e[Wt]=t.current,Un(e),l)for(e=0;e<l.length;e++)n=l[e],c=n._getVersion,c=c(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,c]:t.mutableSourceEagerHydrationData.push(n,c);return new Na(t)},mt.render=function(e,t,n){if(!ka(t))throw Error(o(200));return Sa(null,e,t,!1,n)},mt.unmountComponentAtNode=function(e){if(!ka(e))throw Error(o(40));return e._reactRootContainer?(Rr(function(){Sa(null,null,e,!1,function(){e._reactRootContainer=null,e[Wt]=null})}),!0):!1},mt.unstable_batchedUpdates=Tl,mt.unstable_renderSubtreeIntoContainer=function(e,t,n,l){if(!ka(n))throw Error(o(200));if(e==null||e._reactInternals===void 0)throw Error(o(38));return Sa(e,t,n,!1,l)},mt.version="18.3.1-next-f1338f8080-20240426",mt}var ud;function Hd(){if(ud)return Ul.exports;ud=1;function s(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(s)}catch(a){console.error(a)}}return s(),Ul.exports=$h(),Ul.exports}var dd;function Uh(){if(dd)return Ea;dd=1;var s=Hd();return Ea.createRoot=s.createRoot,Ea.hydrateRoot=s.hydrateRoot,Ea}var Wh=Uh();const Hh=Ud(Wh);Hd();/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ds(){return ds=Object.assign?Object.assign.bind():function(s){for(var a=1;a<arguments.length;a++){var o=arguments[a];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(s[i]=o[i])}return s},ds.apply(this,arguments)}var jr;(function(s){s.Pop="POP",s.Push="PUSH",s.Replace="REPLACE"})(jr||(jr={}));const md="popstate";function Vh(s){s===void 0&&(s={});function a(i,u){let{pathname:m,search:f,hash:p}=i.location;return Kl("",{pathname:m,search:f,hash:p},u.state&&u.state.usr||null,u.state&&u.state.key||"default")}function o(i,u){return typeof u=="string"?u:La(u)}return Yh(a,o,null,s)}function Ie(s,a){if(s===!1||s===null||typeof s>"u")throw new Error(a)}function Vd(s,a){if(!s){typeof console<"u"&&console.warn(a);try{throw new Error(a)}catch{}}}function Qh(){return Math.random().toString(36).substr(2,8)}function fd(s,a){return{usr:s.state,key:s.key,idx:a}}function Kl(s,a,o,i){return o===void 0&&(o=null),ds({pathname:typeof s=="string"?s:s.pathname,search:"",hash:""},typeof a=="string"?yn(a):a,{state:o,key:a&&a.key||i||Qh()})}function La(s){let{pathname:a="/",search:o="",hash:i=""}=s;return o&&o!=="?"&&(a+=o.charAt(0)==="?"?o:"?"+o),i&&i!=="#"&&(a+=i.charAt(0)==="#"?i:"#"+i),a}function yn(s){let a={};if(s){let o=s.indexOf("#");o>=0&&(a.hash=s.substr(o),s=s.substr(0,o));let i=s.indexOf("?");i>=0&&(a.search=s.substr(i),s=s.substr(0,i)),s&&(a.pathname=s)}return a}function Yh(s,a,o,i){i===void 0&&(i={});let{window:u=document.defaultView,v5Compat:m=!1}=i,f=u.history,p=jr.Pop,y=null,x=g();x==null&&(x=0,f.replaceState(ds({},f.state,{idx:x}),""));function g(){return(f.state||{idx:null}).idx}function v(){p=jr.Pop;let S=g(),_=S==null?null:S-x;x=S,y&&y({action:p,location:E.location,delta:_})}function j(S,_){p=jr.Push;let B=Kl(E.location,S,_);x=g()+1;let H=fd(B,x),F=E.createHref(B);try{f.pushState(H,"",F)}catch(I){if(I instanceof DOMException&&I.name==="DataCloneError")throw I;u.location.assign(F)}m&&y&&y({action:p,location:E.location,delta:1})}function P(S,_){p=jr.Replace;let B=Kl(E.location,S,_);x=g();let H=fd(B,x),F=E.createHref(B);f.replaceState(H,"",F),m&&y&&y({action:p,location:E.location,delta:0})}function k(S){let _=u.location.origin!=="null"?u.location.origin:u.location.href,B=typeof S=="string"?S:La(S);return B=B.replace(/ $/,"%20"),Ie(_,"No window.location.(origin|href) available to create URL for href: "+B),new URL(B,_)}let E={get action(){return p},get location(){return s(u,f)},listen(S){if(y)throw new Error("A history only accepts one active listener");return u.addEventListener(md,v),y=S,()=>{u.removeEventListener(md,v),y=null}},createHref(S){return a(u,S)},createURL:k,encodeLocation(S){let _=k(S);return{pathname:_.pathname,search:_.search,hash:_.hash}},push:j,replace:P,go(S){return f.go(S)}};return E}var hd;(function(s){s.data="data",s.deferred="deferred",s.redirect="redirect",s.error="error"})(hd||(hd={}));function Jh(s,a,o){return o===void 0&&(o="/"),qh(s,a,o)}function qh(s,a,o,i){let u=typeof a=="string"?yn(a):a,m=gn(u.pathname||"/",o);if(m==null)return null;let f=Qd(s);Kh(f);let p=null;for(let y=0;p==null&&y<f.length;++y){let x=lp(m);p=ap(f[y],x)}return p}function Qd(s,a,o,i){a===void 0&&(a=[]),o===void 0&&(o=[]),i===void 0&&(i="");let u=(m,f,p)=>{let y={relativePath:p===void 0?m.path||"":p,caseSensitive:m.caseSensitive===!0,childrenIndex:f,route:m};y.relativePath.startsWith("/")&&(Ie(y.relativePath.startsWith(i),'Absolute route path "'+y.relativePath+'" nested under path '+('"'+i+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),y.relativePath=y.relativePath.slice(i.length));let x=br([i,y.relativePath]),g=o.concat(y);m.children&&m.children.length>0&&(Ie(m.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+x+'".')),Qd(m.children,a,g,x)),!(m.path==null&&!m.index)&&a.push({path:x,score:np(x,m.index),routesMeta:g})};return s.forEach((m,f)=>{var p;if(m.path===""||!((p=m.path)!=null&&p.includes("?")))u(m,f);else for(let y of Yd(m.path))u(m,f,y)}),a}function Yd(s){let a=s.split("/");if(a.length===0)return[];let[o,...i]=a,u=o.endsWith("?"),m=o.replace(/\?$/,"");if(i.length===0)return u?[m,""]:[m];let f=Yd(i.join("/")),p=[];return p.push(...f.map(y=>y===""?m:[m,y].join("/"))),u&&p.push(...f),p.map(y=>s.startsWith("/")&&y===""?"/":y)}function Kh(s){s.sort((a,o)=>a.score!==o.score?o.score-a.score:sp(a.routesMeta.map(i=>i.childrenIndex),o.routesMeta.map(i=>i.childrenIndex)))}const Xh=/^:[\w-]+$/,Gh=3,Zh=2,ep=1,tp=10,rp=-2,pd=s=>s==="*";function np(s,a){let o=s.split("/"),i=o.length;return o.some(pd)&&(i+=rp),a&&(i+=Zh),o.filter(u=>!pd(u)).reduce((u,m)=>u+(Xh.test(m)?Gh:m===""?ep:tp),i)}function sp(s,a){return s.length===a.length&&s.slice(0,-1).every((i,u)=>i===a[u])?s[s.length-1]-a[a.length-1]:0}function ap(s,a,o){let{routesMeta:i}=s,u={},m="/",f=[];for(let p=0;p<i.length;++p){let y=i[p],x=p===i.length-1,g=m==="/"?a:a.slice(m.length)||"/",v=Xl({path:y.relativePath,caseSensitive:y.caseSensitive,end:x},g),j=y.route;if(!v)return null;Object.assign(u,v.params),f.push({params:u,pathname:br([m,v.pathname]),pathnameBase:dp(br([m,v.pathnameBase])),route:j}),v.pathnameBase!=="/"&&(m=br([m,v.pathnameBase]))}return f}function Xl(s,a){typeof s=="string"&&(s={path:s,caseSensitive:!1,end:!0});let[o,i]=op(s.path,s.caseSensitive,s.end),u=a.match(o);if(!u)return null;let m=u[0],f=m.replace(/(.)\/+$/,"$1"),p=u.slice(1);return{params:i.reduce((x,g,v)=>{let{paramName:j,isOptional:P}=g;if(j==="*"){let E=p[v]||"";f=m.slice(0,m.length-E.length).replace(/(.)\/+$/,"$1")}const k=p[v];return P&&!k?x[j]=void 0:x[j]=(k||"").replace(/%2F/g,"/"),x},{}),pathname:m,pathnameBase:f,pattern:s}}function op(s,a,o){a===void 0&&(a=!1),o===void 0&&(o=!0),Vd(s==="*"||!s.endsWith("*")||s.endsWith("/*"),'Route path "'+s+'" will be treated as if it were '+('"'+s.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+s.replace(/\*$/,"/*")+'".'));let i=[],u="^"+s.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(f,p,y)=>(i.push({paramName:p,isOptional:y!=null}),y?"/?([^\\/]+)?":"/([^\\/]+)"));return s.endsWith("*")?(i.push({paramName:"*"}),u+=s==="*"||s==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):o?u+="\\/*$":s!==""&&s!=="/"&&(u+="(?:(?=\\/|$))"),[new RegExp(u,a?void 0:"i"),i]}function lp(s){try{return s.split("/").map(a=>decodeURIComponent(a).replace(/\//g,"%2F")).join("/")}catch(a){return Vd(!1,'The URL path "'+s+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+a+").")),s}}function gn(s,a){if(a==="/")return s;if(!s.toLowerCase().startsWith(a.toLowerCase()))return null;let o=a.endsWith("/")?a.length-1:a.length,i=s.charAt(o);return i&&i!=="/"?null:s.slice(o)||"/"}function ip(s,a){a===void 0&&(a="/");let{pathname:o,search:i="",hash:u=""}=typeof s=="string"?yn(s):s;return{pathname:o?o.startsWith("/")?o:cp(o,a):a,search:mp(i),hash:fp(u)}}function cp(s,a){let o=a.replace(/\/+$/,"").split("/");return s.split("/").forEach(u=>{u===".."?o.length>1&&o.pop():u!=="."&&o.push(u)}),o.length>1?o.join("/"):"/"}function Vl(s,a,o,i){return"Cannot include a '"+s+"' character in a manually specified "+("`to."+a+"` field ["+JSON.stringify(i)+"].  Please separate it out to the ")+("`to."+o+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function up(s){return s.filter((a,o)=>o===0||a.route.path&&a.route.path.length>0)}function li(s,a){let o=up(s);return a?o.map((i,u)=>u===o.length-1?i.pathname:i.pathnameBase):o.map(i=>i.pathnameBase)}function ii(s,a,o,i){i===void 0&&(i=!1);let u;typeof s=="string"?u=yn(s):(u=ds({},s),Ie(!u.pathname||!u.pathname.includes("?"),Vl("?","pathname","search",u)),Ie(!u.pathname||!u.pathname.includes("#"),Vl("#","pathname","hash",u)),Ie(!u.search||!u.search.includes("#"),Vl("#","search","hash",u)));let m=s===""||u.pathname==="",f=m?"/":u.pathname,p;if(f==null)p=o;else{let v=a.length-1;if(!i&&f.startsWith("..")){let j=f.split("/");for(;j[0]==="..";)j.shift(),v-=1;u.pathname=j.join("/")}p=v>=0?a[v]:"/"}let y=ip(u,p),x=f&&f!=="/"&&f.endsWith("/"),g=(m||f===".")&&o.endsWith("/");return!y.pathname.endsWith("/")&&(x||g)&&(y.pathname+="/"),y}const br=s=>s.join("/").replace(/\/\/+/g,"/"),dp=s=>s.replace(/\/+$/,"").replace(/^\/*/,"/"),mp=s=>!s||s==="?"?"":s.startsWith("?")?s:"?"+s,fp=s=>!s||s==="#"?"":s.startsWith("#")?s:"#"+s;function hp(s){return s!=null&&typeof s.status=="number"&&typeof s.statusText=="string"&&typeof s.internal=="boolean"&&"data"in s}const Jd=["post","put","patch","delete"];new Set(Jd);const pp=["get",...Jd];new Set(pp);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function ms(){return ms=Object.assign?Object.assign.bind():function(s){for(var a=1;a<arguments.length;a++){var o=arguments[a];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(s[i]=o[i])}return s},ms.apply(this,arguments)}const Fa=b.createContext(null),qd=b.createContext(null),Xt=b.createContext(null),za=b.createContext(null),Ut=b.createContext({outlet:null,matches:[],isDataRoute:!1}),Kd=b.createContext(null);function xp(s,a){let{relative:o}=a===void 0?{}:a;vn()||Ie(!1);let{basename:i,navigator:u}=b.useContext(Xt),{hash:m,pathname:f,search:p}=Ba(s,{relative:o}),y=f;return i!=="/"&&(y=f==="/"?i:br([i,f])),u.createHref({pathname:y,search:p,hash:m})}function vn(){return b.useContext(za)!=null}function Gt(){return vn()||Ie(!1),b.useContext(za).location}function Xd(s){b.useContext(Xt).static||b.useLayoutEffect(s)}function Zt(){let{isDataRoute:s}=b.useContext(Ut);return s?Lp():gp()}function gp(){vn()||Ie(!1);let s=b.useContext(Fa),{basename:a,future:o,navigator:i}=b.useContext(Xt),{matches:u}=b.useContext(Ut),{pathname:m}=Gt(),f=JSON.stringify(li(u,o.v7_relativeSplatPath)),p=b.useRef(!1);return Xd(()=>{p.current=!0}),b.useCallback(function(x,g){if(g===void 0&&(g={}),!p.current)return;if(typeof x=="number"){i.go(x);return}let v=ii(x,JSON.parse(f),m,g.relative==="path");s==null&&a!=="/"&&(v.pathname=v.pathname==="/"?a:br([a,v.pathname])),(g.replace?i.replace:i.push)(v,g.state,g)},[a,i,f,m,s])}const yp=b.createContext(null);function vp(s){let a=b.useContext(Ut).outlet;return a&&b.createElement(yp.Provider,{value:s},a)}function Ia(){let{matches:s}=b.useContext(Ut),a=s[s.length-1];return a?a.params:{}}function Ba(s,a){let{relative:o}=a===void 0?{}:a,{future:i}=b.useContext(Xt),{matches:u}=b.useContext(Ut),{pathname:m}=Gt(),f=JSON.stringify(li(u,i.v7_relativeSplatPath));return b.useMemo(()=>ii(s,JSON.parse(f),m,o==="path"),[s,f,m,o])}function wp(s,a){return jp(s,a)}function jp(s,a,o,i){vn()||Ie(!1);let{navigator:u}=b.useContext(Xt),{matches:m}=b.useContext(Ut),f=m[m.length-1],p=f?f.params:{};f&&f.pathname;let y=f?f.pathnameBase:"/";f&&f.route;let x=Gt(),g;if(a){var v;let S=typeof a=="string"?yn(a):a;y==="/"||(v=S.pathname)!=null&&v.startsWith(y)||Ie(!1),g=S}else g=x;let j=g.pathname||"/",P=j;if(y!=="/"){let S=y.replace(/^\//,"").split("/");P="/"+j.replace(/^\//,"").split("/").slice(S.length).join("/")}let k=Jh(s,{pathname:P}),E=Cp(k&&k.map(S=>Object.assign({},S,{params:Object.assign({},p,S.params),pathname:br([y,u.encodeLocation?u.encodeLocation(S.pathname).pathname:S.pathname]),pathnameBase:S.pathnameBase==="/"?y:br([y,u.encodeLocation?u.encodeLocation(S.pathnameBase).pathname:S.pathnameBase])})),m,o,i);return a&&E?b.createElement(za.Provider,{value:{location:ms({pathname:"/",search:"",hash:"",state:null,key:"default"},g),navigationType:jr.Pop}},E):E}function bp(){let s=Tp(),a=hp(s)?s.status+" "+s.statusText:s instanceof Error?s.message:JSON.stringify(s),o=s instanceof Error?s.stack:null,u={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"};return b.createElement(b.Fragment,null,b.createElement("h2",null,"Unexpected Application Error!"),b.createElement("h3",{style:{fontStyle:"italic"}},a),o?b.createElement("pre",{style:u},o):null,null)}const Np=b.createElement(bp,null);class kp extends b.Component{constructor(a){super(a),this.state={location:a.location,revalidation:a.revalidation,error:a.error}}static getDerivedStateFromError(a){return{error:a}}static getDerivedStateFromProps(a,o){return o.location!==a.location||o.revalidation!=="idle"&&a.revalidation==="idle"?{error:a.error,location:a.location,revalidation:a.revalidation}:{error:a.error!==void 0?a.error:o.error,location:o.location,revalidation:a.revalidation||o.revalidation}}componentDidCatch(a,o){console.error("React Router caught the following error during render",a,o)}render(){return this.state.error!==void 0?b.createElement(Ut.Provider,{value:this.props.routeContext},b.createElement(Kd.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Sp(s){let{routeContext:a,match:o,children:i}=s,u=b.useContext(Fa);return u&&u.static&&u.staticContext&&(o.route.errorElement||o.route.ErrorBoundary)&&(u.staticContext._deepestRenderedBoundaryId=o.route.id),b.createElement(Ut.Provider,{value:a},i)}function Cp(s,a,o,i){var u;if(a===void 0&&(a=[]),o===void 0&&(o=null),i===void 0&&(i=null),s==null){var m;if(!o)return null;if(o.errors)s=o.matches;else if((m=i)!=null&&m.v7_partialHydration&&a.length===0&&!o.initialized&&o.matches.length>0)s=o.matches;else return null}let f=s,p=(u=o)==null?void 0:u.errors;if(p!=null){let g=f.findIndex(v=>v.route.id&&(p==null?void 0:p[v.route.id])!==void 0);g>=0||Ie(!1),f=f.slice(0,Math.min(f.length,g+1))}let y=!1,x=-1;if(o&&i&&i.v7_partialHydration)for(let g=0;g<f.length;g++){let v=f[g];if((v.route.HydrateFallback||v.route.hydrateFallbackElement)&&(x=g),v.route.id){let{loaderData:j,errors:P}=o,k=v.route.loader&&j[v.route.id]===void 0&&(!P||P[v.route.id]===void 0);if(v.route.lazy||k){y=!0,x>=0?f=f.slice(0,x+1):f=[f[0]];break}}}return f.reduceRight((g,v,j)=>{let P,k=!1,E=null,S=null;o&&(P=p&&v.route.id?p[v.route.id]:void 0,E=v.route.errorElement||Np,y&&(x<0&&j===0?(Op("route-fallback"),k=!0,S=null):x===j&&(k=!0,S=v.route.hydrateFallbackElement||null)));let _=a.concat(f.slice(0,j+1)),B=()=>{let H;return P?H=E:k?H=S:v.route.Component?H=b.createElement(v.route.Component,null):v.route.element?H=v.route.element:H=g,b.createElement(Sp,{match:v,routeContext:{outlet:g,matches:_,isDataRoute:o!=null},children:H})};return o&&(v.route.ErrorBoundary||v.route.errorElement||j===0)?b.createElement(kp,{location:o.location,revalidation:o.revalidation,component:E,error:P,children:B(),routeContext:{outlet:null,matches:_,isDataRoute:!0}}):B()},null)}var Gd=function(s){return s.UseBlocker="useBlocker",s.UseRevalidator="useRevalidator",s.UseNavigateStable="useNavigate",s}(Gd||{}),Zd=function(s){return s.UseBlocker="useBlocker",s.UseLoaderData="useLoaderData",s.UseActionData="useActionData",s.UseRouteError="useRouteError",s.UseNavigation="useNavigation",s.UseRouteLoaderData="useRouteLoaderData",s.UseMatches="useMatches",s.UseRevalidator="useRevalidator",s.UseNavigateStable="useNavigate",s.UseRouteId="useRouteId",s}(Zd||{});function Ep(s){let a=b.useContext(Fa);return a||Ie(!1),a}function Pp(s){let a=b.useContext(qd);return a||Ie(!1),a}function Dp(s){let a=b.useContext(Ut);return a||Ie(!1),a}function em(s){let a=Dp(),o=a.matches[a.matches.length-1];return o.route.id||Ie(!1),o.route.id}function Tp(){var s;let a=b.useContext(Kd),o=Pp(),i=em();return a!==void 0?a:(s=o.errors)==null?void 0:s[i]}function Lp(){let{router:s}=Ep(Gd.UseNavigateStable),a=em(Zd.UseNavigateStable),o=b.useRef(!1);return Xd(()=>{o.current=!0}),b.useCallback(function(u,m){m===void 0&&(m={}),o.current&&(typeof u=="number"?s.navigate(u):s.navigate(u,ms({fromRouteId:a},m)))},[s,a])}const xd={};function Op(s,a,o){xd[s]||(xd[s]=!0)}function Mp(s,a){s==null||s.v7_startTransition,s==null||s.v7_relativeSplatPath}function tm(s){let{to:a,replace:o,state:i,relative:u}=s;vn()||Ie(!1);let{future:m,static:f}=b.useContext(Xt),{matches:p}=b.useContext(Ut),{pathname:y}=Gt(),x=Zt(),g=ii(a,li(p,m.v7_relativeSplatPath),y,u==="path"),v=JSON.stringify(g);return b.useEffect(()=>x(JSON.parse(v),{replace:o,state:i,relative:u}),[x,v,u,o,i]),null}function Rp(s){return vp(s.context)}function Ce(s){Ie(!1)}function Ap(s){let{basename:a="/",children:o=null,location:i,navigationType:u=jr.Pop,navigator:m,static:f=!1,future:p}=s;vn()&&Ie(!1);let y=a.replace(/^\/*/,"/"),x=b.useMemo(()=>({basename:y,navigator:m,static:f,future:ms({v7_relativeSplatPath:!1},p)}),[y,p,m,f]);typeof i=="string"&&(i=yn(i));let{pathname:g="/",search:v="",hash:j="",state:P=null,key:k="default"}=i,E=b.useMemo(()=>{let S=gn(g,y);return S==null?null:{location:{pathname:S,search:v,hash:j,state:P,key:k},navigationType:u}},[y,g,v,j,P,k,u]);return E==null?null:b.createElement(Xt.Provider,{value:x},b.createElement(za.Provider,{children:o,value:E}))}function _p(s){let{children:a,location:o}=s;return wp(Gl(a),o)}new Promise(()=>{});function Gl(s,a){a===void 0&&(a=[]);let o=[];return b.Children.forEach(s,(i,u)=>{if(!b.isValidElement(i))return;let m=[...a,u];if(i.type===b.Fragment){o.push.apply(o,Gl(i.props.children,m));return}i.type!==Ce&&Ie(!1),!i.props.index||!i.props.children||Ie(!1);let f={id:i.props.id||m.join("-"),caseSensitive:i.props.caseSensitive,element:i.props.element,Component:i.props.Component,index:i.props.index,path:i.props.path,loader:i.props.loader,action:i.props.action,errorElement:i.props.errorElement,ErrorBoundary:i.props.ErrorBoundary,hasErrorBoundary:i.props.ErrorBoundary!=null||i.props.errorElement!=null,shouldRevalidate:i.props.shouldRevalidate,handle:i.props.handle,lazy:i.props.lazy};i.props.children&&(f.children=Gl(i.props.children,m)),o.push(f)}),o}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Oa(){return Oa=Object.assign?Object.assign.bind():function(s){for(var a=1;a<arguments.length;a++){var o=arguments[a];for(var i in o)Object.prototype.hasOwnProperty.call(o,i)&&(s[i]=o[i])}return s},Oa.apply(this,arguments)}function rm(s,a){if(s==null)return{};var o={},i=Object.keys(s),u,m;for(m=0;m<i.length;m++)u=i[m],!(a.indexOf(u)>=0)&&(o[u]=s[u]);return o}function Fp(s){return!!(s.metaKey||s.altKey||s.ctrlKey||s.shiftKey)}function zp(s,a){return s.button===0&&(!a||a==="_self")&&!Fp(s)}const Ip=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Bp=["aria-current","caseSensitive","className","end","style","to","viewTransition","children"],$p="6";try{window.__reactRouterVersion=$p}catch{}const Up=b.createContext({isTransitioning:!1}),Wp="startTransition",gd=zh[Wp];function Hp(s){let{basename:a,children:o,future:i,window:u}=s,m=b.useRef();m.current==null&&(m.current=Vh({window:u,v5Compat:!0}));let f=m.current,[p,y]=b.useState({action:f.action,location:f.location}),{v7_startTransition:x}=i||{},g=b.useCallback(v=>{x&&gd?gd(()=>y(v)):y(v)},[y,x]);return b.useLayoutEffect(()=>f.listen(g),[f,g]),b.useEffect(()=>Mp(i),[i]),b.createElement(Ap,{basename:a,children:o,location:p.location,navigationType:p.action,navigator:f,future:i})}const Vp=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",Qp=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,ot=b.forwardRef(function(a,o){let{onClick:i,relative:u,reloadDocument:m,replace:f,state:p,target:y,to:x,preventScrollReset:g,viewTransition:v}=a,j=rm(a,Ip),{basename:P}=b.useContext(Xt),k,E=!1;if(typeof x=="string"&&Qp.test(x)&&(k=x,Vp))try{let H=new URL(window.location.href),F=x.startsWith("//")?new URL(H.protocol+x):new URL(x),I=gn(F.pathname,P);F.origin===H.origin&&I!=null?x=I+F.search+F.hash:E=!0}catch{}let S=xp(x,{relative:u}),_=qp(x,{replace:f,state:p,target:y,preventScrollReset:g,relative:u,viewTransition:v});function B(H){i&&i(H),H.defaultPrevented||_(H)}return b.createElement("a",Oa({},j,{href:k||S,onClick:E||m?i:B,ref:o,target:y}))}),Yp=b.forwardRef(function(a,o){let{"aria-current":i="page",caseSensitive:u=!1,className:m="",end:f=!1,style:p,to:y,viewTransition:x,children:g}=a,v=rm(a,Bp),j=Ba(y,{relative:v.relative}),P=Gt(),k=b.useContext(qd),{navigator:E,basename:S}=b.useContext(Xt),_=k!=null&&Kp(j)&&x===!0,B=E.encodeLocation?E.encodeLocation(j).pathname:j.pathname,H=P.pathname,F=k&&k.navigation&&k.navigation.location?k.navigation.location.pathname:null;u||(H=H.toLowerCase(),F=F?F.toLowerCase():null,B=B.toLowerCase()),F&&S&&(F=gn(F,S)||F);const I=B!=="/"&&B.endsWith("/")?B.length-1:B.length;let z=H===B||!f&&H.startsWith(B)&&H.charAt(I)==="/",U=F!=null&&(F===B||!f&&F.startsWith(B)&&F.charAt(B.length)==="/"),oe={isActive:z,isPending:U,isTransitioning:_},fe=z?i:void 0,Z;typeof m=="function"?Z=m(oe):Z=[m,z?"active":null,U?"pending":null,_?"transitioning":null].filter(Boolean).join(" ");let re=typeof p=="function"?p(oe):p;return b.createElement(ot,Oa({},v,{"aria-current":fe,className:Z,ref:o,style:re,to:y,viewTransition:x}),typeof g=="function"?g(oe):g)});var Zl;(function(s){s.UseScrollRestoration="useScrollRestoration",s.UseSubmit="useSubmit",s.UseSubmitFetcher="useSubmitFetcher",s.UseFetcher="useFetcher",s.useViewTransitionState="useViewTransitionState"})(Zl||(Zl={}));var yd;(function(s){s.UseFetcher="useFetcher",s.UseFetchers="useFetchers",s.UseScrollRestoration="useScrollRestoration"})(yd||(yd={}));function Jp(s){let a=b.useContext(Fa);return a||Ie(!1),a}function qp(s,a){let{target:o,replace:i,state:u,preventScrollReset:m,relative:f,viewTransition:p}=a===void 0?{}:a,y=Zt(),x=Gt(),g=Ba(s,{relative:f});return b.useCallback(v=>{if(zp(v,o)){v.preventDefault();let j=i!==void 0?i:La(x)===La(g);y(s,{replace:j,state:u,preventScrollReset:m,relative:f,viewTransition:p})}},[x,y,g,i,u,o,s,m,f,p])}function Kp(s,a){a===void 0&&(a={});let o=b.useContext(Up);o==null&&Ie(!1);let{basename:i}=Jp(Zl.useViewTransitionState),u=Ba(s,{relative:a.relative});if(!o.isTransitioning)return!1;let m=gn(o.currentLocation.pathname,i)||o.currentLocation.pathname,f=gn(o.nextLocation.pathname,i)||o.nextLocation.pathname;return Xl(u.pathname,f)!=null||Xl(u.pathname,m)!=null}function Xp(){const[s,a]=b.useState(null),o=b.useRef(null),i=b.useRef({}),u=Zt(),m=[{label:"Calendar",to:"/calendar"},{label:"Checkout",to:"/checkout",hasSubmenu:!0},{label:"Customers",to:"/customers/management",hasSubmenu:!0},{label:"Marketing",to:"/marketing"},{label:"Forms",to:"/forms"},{label:"Reports",to:"/reports",hasSubmenu:!0},{label:"Settings",to:"/settings",hasSubmenu:!0}],f={Checkout:[{label:"Checkout",to:"/checkout"},{label:"Checkout Settings",to:"/checkout/settings"},{label:"Refund Customer",to:"/checkout/refunds"},{label:"Invoices",to:"/checkout/invoices"}],Customers:[{label:"Customer Management",to:"/customers/management"},{label:"Customer List",to:"/customers/list"},{label:"Import Management",to:"/customers/import"}],Reports:[{label:"All reports",to:"/reports/all"},{label:"Transaction List",to:"/reports/transactions"},{label:"Dashboard",to:"/dashboard"}],Settings:[{label:"All settings",to:"/settings/all"},{label:"Employees",to:"/settings/employees"},{label:"Service Menu",to:"/settings/services"}]},p=v=>{var j;o.current&&clearTimeout(o.current),(j=m.find(P=>P.label===v))!=null&&j.hasSubmenu&&a(v)},y=()=>{o.current=setTimeout(()=>{a(null)},150)},x=()=>{o.current&&clearTimeout(o.current)};b.useEffect(()=>{const v=j=>{s&&(Object.values(i.current).some(k=>k&&k.contains(j.target))||a(null))};return document.addEventListener("mousedown",v),()=>{document.removeEventListener("mousedown",v)}},[s]);const g=()=>{localStorage.removeItem("auth_token"),localStorage.removeItem("refresh_token"),u("/auth/login")};return r.jsx("header",{className:"sticky top-0 z-30 bg-white border-b border-gray-200 shadow-sm",children:r.jsxs("div",{className:"container flex items-center justify-between h-16 px-4 md:px-6 lg:px-8",children:[r.jsx(ot,{to:"/",className:"text-xl font-bold text-primary-700",children:"Chatbook"}),r.jsx("nav",{className:"hidden md:flex space-x-6",children:m.map(v=>r.jsxs("div",{className:"relative",ref:j=>i.current[v.label]=j,onMouseEnter:()=>p(v.label),onMouseLeave:y,children:[r.jsx(Yp,{to:v.to,className:({isActive:j})=>`text-sm font-medium transition-colors pb-4 ${j?"text-primary-700":"text-gray-600 hover:text-primary-600"}`,children:v.label}),v.hasSubmenu&&s===v.label&&r.jsx("div",{className:"absolute left-0 top-6 w-56 bg-white rounded-md shadow-lg border border-gray-200 z-40",onMouseEnter:x,onMouseLeave:y,children:r.jsx("div",{className:"py-1",children:f[v.label].map(j=>r.jsx(ot,{to:j.to,className:"block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",children:j.label},j.to))})})]},v.to))}),r.jsxs("div",{className:"flex items-center space-x-4",children:[r.jsxs("button",{className:"w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center",title:"Toggle theme",children:[r.jsx("span",{className:"sr-only",children:"Toggle theme"}),r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",className:"w-5 h-5 text-gray-600",children:r.jsx("path",{d:"M12 3.75A8.25 8.25 0 1 0 20.25 12 8.26 8.26 0 0 0 12 3.75Zm0 14.25a6 6 0 1 1 6-6 6 6 0 0 1-6 6Z"})})]}),r.jsxs("button",{onClick:g,className:"flex items-center text-sm text-gray-600 hover:text-red-600",title:"Log out",children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor",className:"w-5 h-5 mr-1",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6A2.25 2.25 0 0 0 5.25 5.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15M18 12H8.25m0 0 3-3m-3 3 3 3"})}),"Logout"]}),r.jsx("div",{className:"w-8 h-8 rounded-full bg-gray-300 overflow-hidden"})]})]})})}function Gp(){return r.jsxs("div",{className:"flex flex-col min-h-screen",children:[r.jsx(Xp,{}),r.jsx("main",{className:"flex-1",children:r.jsx(Rp,{})})]})}function Zp({children:s}){const a=Gt();return localStorage.getItem("auth_token")?s:r.jsx(tm,{to:"/auth/login",state:{from:a},replace:!0})}function nm(s,a){return function(){return s.apply(a,arguments)}}const{toString:e0}=Object.prototype,{getPrototypeOf:ci}=Object,{iterator:$a,toStringTag:sm}=Symbol,Ua=(s=>a=>{const o=e0.call(a);return s[o]||(s[o]=o.slice(8,-1).toLowerCase())})(Object.create(null)),Mt=s=>(s=s.toLowerCase(),a=>Ua(a)===s),Wa=s=>a=>typeof a===s,{isArray:wn}=Array,fs=Wa("undefined");function t0(s){return s!==null&&!fs(s)&&s.constructor!==null&&!fs(s.constructor)&&ft(s.constructor.isBuffer)&&s.constructor.isBuffer(s)}const am=Mt("ArrayBuffer");function r0(s){let a;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?a=ArrayBuffer.isView(s):a=s&&s.buffer&&am(s.buffer),a}const n0=Wa("string"),ft=Wa("function"),om=Wa("number"),Ha=s=>s!==null&&typeof s=="object",s0=s=>s===!0||s===!1,Pa=s=>{if(Ua(s)!=="object")return!1;const a=ci(s);return(a===null||a===Object.prototype||Object.getPrototypeOf(a)===null)&&!(sm in s)&&!($a in s)},a0=Mt("Date"),o0=Mt("File"),l0=Mt("Blob"),i0=Mt("FileList"),c0=s=>Ha(s)&&ft(s.pipe),u0=s=>{let a;return s&&(typeof FormData=="function"&&s instanceof FormData||ft(s.append)&&((a=Ua(s))==="formdata"||a==="object"&&ft(s.toString)&&s.toString()==="[object FormData]"))},d0=Mt("URLSearchParams"),[m0,f0,h0,p0]=["ReadableStream","Request","Response","Headers"].map(Mt),x0=s=>s.trim?s.trim():s.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function xs(s,a,{allOwnKeys:o=!1}={}){if(s===null||typeof s>"u")return;let i,u;if(typeof s!="object"&&(s=[s]),wn(s))for(i=0,u=s.length;i<u;i++)a.call(null,s[i],i,s);else{const m=o?Object.getOwnPropertyNames(s):Object.keys(s),f=m.length;let p;for(i=0;i<f;i++)p=m[i],a.call(null,s[p],p,s)}}function lm(s,a){a=a.toLowerCase();const o=Object.keys(s);let i=o.length,u;for(;i-- >0;)if(u=o[i],a===u.toLowerCase())return u;return null}const Ir=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,im=s=>!fs(s)&&s!==Ir;function ei(){const{caseless:s}=im(this)&&this||{},a={},o=(i,u)=>{const m=s&&lm(a,u)||u;Pa(a[m])&&Pa(i)?a[m]=ei(a[m],i):Pa(i)?a[m]=ei({},i):wn(i)?a[m]=i.slice():a[m]=i};for(let i=0,u=arguments.length;i<u;i++)arguments[i]&&xs(arguments[i],o);return a}const g0=(s,a,o,{allOwnKeys:i}={})=>(xs(a,(u,m)=>{o&&ft(u)?s[m]=nm(u,o):s[m]=u},{allOwnKeys:i}),s),y0=s=>(s.charCodeAt(0)===65279&&(s=s.slice(1)),s),v0=(s,a,o,i)=>{s.prototype=Object.create(a.prototype,i),s.prototype.constructor=s,Object.defineProperty(s,"super",{value:a.prototype}),o&&Object.assign(s.prototype,o)},w0=(s,a,o,i)=>{let u,m,f;const p={};if(a=a||{},s==null)return a;do{for(u=Object.getOwnPropertyNames(s),m=u.length;m-- >0;)f=u[m],(!i||i(f,s,a))&&!p[f]&&(a[f]=s[f],p[f]=!0);s=o!==!1&&ci(s)}while(s&&(!o||o(s,a))&&s!==Object.prototype);return a},j0=(s,a,o)=>{s=String(s),(o===void 0||o>s.length)&&(o=s.length),o-=a.length;const i=s.indexOf(a,o);return i!==-1&&i===o},b0=s=>{if(!s)return null;if(wn(s))return s;let a=s.length;if(!om(a))return null;const o=new Array(a);for(;a-- >0;)o[a]=s[a];return o},N0=(s=>a=>s&&a instanceof s)(typeof Uint8Array<"u"&&ci(Uint8Array)),k0=(s,a)=>{const i=(s&&s[$a]).call(s);let u;for(;(u=i.next())&&!u.done;){const m=u.value;a.call(s,m[0],m[1])}},S0=(s,a)=>{let o;const i=[];for(;(o=s.exec(a))!==null;)i.push(o);return i},C0=Mt("HTMLFormElement"),E0=s=>s.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(o,i,u){return i.toUpperCase()+u}),vd=(({hasOwnProperty:s})=>(a,o)=>s.call(a,o))(Object.prototype),P0=Mt("RegExp"),cm=(s,a)=>{const o=Object.getOwnPropertyDescriptors(s),i={};xs(o,(u,m)=>{let f;(f=a(u,m,s))!==!1&&(i[m]=f||u)}),Object.defineProperties(s,i)},D0=s=>{cm(s,(a,o)=>{if(ft(s)&&["arguments","caller","callee"].indexOf(o)!==-1)return!1;const i=s[o];if(ft(i)){if(a.enumerable=!1,"writable"in a){a.writable=!1;return}a.set||(a.set=()=>{throw Error("Can not rewrite read-only method '"+o+"'")})}})},T0=(s,a)=>{const o={},i=u=>{u.forEach(m=>{o[m]=!0})};return wn(s)?i(s):i(String(s).split(a)),o},L0=()=>{},O0=(s,a)=>s!=null&&Number.isFinite(s=+s)?s:a;function M0(s){return!!(s&&ft(s.append)&&s[sm]==="FormData"&&s[$a])}const R0=s=>{const a=new Array(10),o=(i,u)=>{if(Ha(i)){if(a.indexOf(i)>=0)return;if(!("toJSON"in i)){a[u]=i;const m=wn(i)?[]:{};return xs(i,(f,p)=>{const y=o(f,u+1);!fs(y)&&(m[p]=y)}),a[u]=void 0,m}}return i};return o(s,0)},A0=Mt("AsyncFunction"),_0=s=>s&&(Ha(s)||ft(s))&&ft(s.then)&&ft(s.catch),um=((s,a)=>s?setImmediate:a?((o,i)=>(Ir.addEventListener("message",({source:u,data:m})=>{u===Ir&&m===o&&i.length&&i.shift()()},!1),u=>{i.push(u),Ir.postMessage(o,"*")}))(`axios@${Math.random()}`,[]):o=>setTimeout(o))(typeof setImmediate=="function",ft(Ir.postMessage)),F0=typeof queueMicrotask<"u"?queueMicrotask.bind(Ir):typeof process<"u"&&process.nextTick||um,z0=s=>s!=null&&ft(s[$a]),R={isArray:wn,isArrayBuffer:am,isBuffer:t0,isFormData:u0,isArrayBufferView:r0,isString:n0,isNumber:om,isBoolean:s0,isObject:Ha,isPlainObject:Pa,isReadableStream:m0,isRequest:f0,isResponse:h0,isHeaders:p0,isUndefined:fs,isDate:a0,isFile:o0,isBlob:l0,isRegExp:P0,isFunction:ft,isStream:c0,isURLSearchParams:d0,isTypedArray:N0,isFileList:i0,forEach:xs,merge:ei,extend:g0,trim:x0,stripBOM:y0,inherits:v0,toFlatObject:w0,kindOf:Ua,kindOfTest:Mt,endsWith:j0,toArray:b0,forEachEntry:k0,matchAll:S0,isHTMLForm:C0,hasOwnProperty:vd,hasOwnProp:vd,reduceDescriptors:cm,freezeMethods:D0,toObjectSet:T0,toCamelCase:E0,noop:L0,toFiniteNumber:O0,findKey:lm,global:Ir,isContextDefined:im,isSpecCompliantForm:M0,toJSONObject:R0,isAsyncFn:A0,isThenable:_0,setImmediate:um,asap:F0,isIterable:z0};function ye(s,a,o,i,u){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=s,this.name="AxiosError",a&&(this.code=a),o&&(this.config=o),i&&(this.request=i),u&&(this.response=u,this.status=u.status?u.status:null)}R.inherits(ye,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:R.toJSONObject(this.config),code:this.code,status:this.status}}});const dm=ye.prototype,mm={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(s=>{mm[s]={value:s}});Object.defineProperties(ye,mm);Object.defineProperty(dm,"isAxiosError",{value:!0});ye.from=(s,a,o,i,u,m)=>{const f=Object.create(dm);return R.toFlatObject(s,f,function(y){return y!==Error.prototype},p=>p!=="isAxiosError"),ye.call(f,s.message,a,o,i,u),f.cause=s,f.name=s.name,m&&Object.assign(f,m),f};const I0=null;function ti(s){return R.isPlainObject(s)||R.isArray(s)}function fm(s){return R.endsWith(s,"[]")?s.slice(0,-2):s}function wd(s,a,o){return s?s.concat(a).map(function(u,m){return u=fm(u),!o&&m?"["+u+"]":u}).join(o?".":""):a}function B0(s){return R.isArray(s)&&!s.some(ti)}const $0=R.toFlatObject(R,{},null,function(a){return/^is[A-Z]/.test(a)});function Va(s,a,o){if(!R.isObject(s))throw new TypeError("target must be an object");a=a||new FormData,o=R.toFlatObject(o,{metaTokens:!0,dots:!1,indexes:!1},!1,function(E,S){return!R.isUndefined(S[E])});const i=o.metaTokens,u=o.visitor||g,m=o.dots,f=o.indexes,y=(o.Blob||typeof Blob<"u"&&Blob)&&R.isSpecCompliantForm(a);if(!R.isFunction(u))throw new TypeError("visitor must be a function");function x(k){if(k===null)return"";if(R.isDate(k))return k.toISOString();if(!y&&R.isBlob(k))throw new ye("Blob is not supported. Use a Buffer instead.");return R.isArrayBuffer(k)||R.isTypedArray(k)?y&&typeof Blob=="function"?new Blob([k]):Buffer.from(k):k}function g(k,E,S){let _=k;if(k&&!S&&typeof k=="object"){if(R.endsWith(E,"{}"))E=i?E:E.slice(0,-2),k=JSON.stringify(k);else if(R.isArray(k)&&B0(k)||(R.isFileList(k)||R.endsWith(E,"[]"))&&(_=R.toArray(k)))return E=fm(E),_.forEach(function(H,F){!(R.isUndefined(H)||H===null)&&a.append(f===!0?wd([E],F,m):f===null?E:E+"[]",x(H))}),!1}return ti(k)?!0:(a.append(wd(S,E,m),x(k)),!1)}const v=[],j=Object.assign($0,{defaultVisitor:g,convertValue:x,isVisitable:ti});function P(k,E){if(!R.isUndefined(k)){if(v.indexOf(k)!==-1)throw Error("Circular reference detected in "+E.join("."));v.push(k),R.forEach(k,function(_,B){(!(R.isUndefined(_)||_===null)&&u.call(a,_,R.isString(B)?B.trim():B,E,j))===!0&&P(_,E?E.concat(B):[B])}),v.pop()}}if(!R.isObject(s))throw new TypeError("data must be an object");return P(s),a}function jd(s){const a={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(s).replace(/[!'()~]|%20|%00/g,function(i){return a[i]})}function ui(s,a){this._pairs=[],s&&Va(s,this,a)}const hm=ui.prototype;hm.append=function(a,o){this._pairs.push([a,o])};hm.toString=function(a){const o=a?function(i){return a.call(this,i,jd)}:jd;return this._pairs.map(function(u){return o(u[0])+"="+o(u[1])},"").join("&")};function U0(s){return encodeURIComponent(s).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function pm(s,a,o){if(!a)return s;const i=o&&o.encode||U0;R.isFunction(o)&&(o={serialize:o});const u=o&&o.serialize;let m;if(u?m=u(a,o):m=R.isURLSearchParams(a)?a.toString():new ui(a,o).toString(i),m){const f=s.indexOf("#");f!==-1&&(s=s.slice(0,f)),s+=(s.indexOf("?")===-1?"?":"&")+m}return s}class bd{constructor(){this.handlers=[]}use(a,o,i){return this.handlers.push({fulfilled:a,rejected:o,synchronous:i?i.synchronous:!1,runWhen:i?i.runWhen:null}),this.handlers.length-1}eject(a){this.handlers[a]&&(this.handlers[a]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(a){R.forEach(this.handlers,function(i){i!==null&&a(i)})}}const xm={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},W0=typeof URLSearchParams<"u"?URLSearchParams:ui,H0=typeof FormData<"u"?FormData:null,V0=typeof Blob<"u"?Blob:null,Q0={isBrowser:!0,classes:{URLSearchParams:W0,FormData:H0,Blob:V0},protocols:["http","https","file","blob","url","data"]},di=typeof window<"u"&&typeof document<"u",ri=typeof navigator=="object"&&navigator||void 0,Y0=di&&(!ri||["ReactNative","NativeScript","NS"].indexOf(ri.product)<0),J0=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",q0=di&&window.location.href||"http://localhost",K0=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:di,hasStandardBrowserEnv:Y0,hasStandardBrowserWebWorkerEnv:J0,navigator:ri,origin:q0},Symbol.toStringTag,{value:"Module"})),rt={...K0,...Q0};function X0(s,a){return Va(s,new rt.classes.URLSearchParams,Object.assign({visitor:function(o,i,u,m){return rt.isNode&&R.isBuffer(o)?(this.append(i,o.toString("base64")),!1):m.defaultVisitor.apply(this,arguments)}},a))}function G0(s){return R.matchAll(/\w+|\[(\w*)]/g,s).map(a=>a[0]==="[]"?"":a[1]||a[0])}function Z0(s){const a={},o=Object.keys(s);let i;const u=o.length;let m;for(i=0;i<u;i++)m=o[i],a[m]=s[m];return a}function gm(s){function a(o,i,u,m){let f=o[m++];if(f==="__proto__")return!0;const p=Number.isFinite(+f),y=m>=o.length;return f=!f&&R.isArray(u)?u.length:f,y?(R.hasOwnProp(u,f)?u[f]=[u[f],i]:u[f]=i,!p):((!u[f]||!R.isObject(u[f]))&&(u[f]=[]),a(o,i,u[f],m)&&R.isArray(u[f])&&(u[f]=Z0(u[f])),!p)}if(R.isFormData(s)&&R.isFunction(s.entries)){const o={};return R.forEachEntry(s,(i,u)=>{a(G0(i),u,o,0)}),o}return null}function ex(s,a,o){if(R.isString(s))try{return(a||JSON.parse)(s),R.trim(s)}catch(i){if(i.name!=="SyntaxError")throw i}return(o||JSON.stringify)(s)}const gs={transitional:xm,adapter:["xhr","http","fetch"],transformRequest:[function(a,o){const i=o.getContentType()||"",u=i.indexOf("application/json")>-1,m=R.isObject(a);if(m&&R.isHTMLForm(a)&&(a=new FormData(a)),R.isFormData(a))return u?JSON.stringify(gm(a)):a;if(R.isArrayBuffer(a)||R.isBuffer(a)||R.isStream(a)||R.isFile(a)||R.isBlob(a)||R.isReadableStream(a))return a;if(R.isArrayBufferView(a))return a.buffer;if(R.isURLSearchParams(a))return o.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),a.toString();let p;if(m){if(i.indexOf("application/x-www-form-urlencoded")>-1)return X0(a,this.formSerializer).toString();if((p=R.isFileList(a))||i.indexOf("multipart/form-data")>-1){const y=this.env&&this.env.FormData;return Va(p?{"files[]":a}:a,y&&new y,this.formSerializer)}}return m||u?(o.setContentType("application/json",!1),ex(a)):a}],transformResponse:[function(a){const o=this.transitional||gs.transitional,i=o&&o.forcedJSONParsing,u=this.responseType==="json";if(R.isResponse(a)||R.isReadableStream(a))return a;if(a&&R.isString(a)&&(i&&!this.responseType||u)){const f=!(o&&o.silentJSONParsing)&&u;try{return JSON.parse(a)}catch(p){if(f)throw p.name==="SyntaxError"?ye.from(p,ye.ERR_BAD_RESPONSE,this,null,this.response):p}}return a}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:rt.classes.FormData,Blob:rt.classes.Blob},validateStatus:function(a){return a>=200&&a<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};R.forEach(["delete","get","head","post","put","patch"],s=>{gs.headers[s]={}});const tx=R.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),rx=s=>{const a={};let o,i,u;return s&&s.split(`
`).forEach(function(f){u=f.indexOf(":"),o=f.substring(0,u).trim().toLowerCase(),i=f.substring(u+1).trim(),!(!o||a[o]&&tx[o])&&(o==="set-cookie"?a[o]?a[o].push(i):a[o]=[i]:a[o]=a[o]?a[o]+", "+i:i)}),a},Nd=Symbol("internals");function ls(s){return s&&String(s).trim().toLowerCase()}function Da(s){return s===!1||s==null?s:R.isArray(s)?s.map(Da):String(s)}function nx(s){const a=Object.create(null),o=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let i;for(;i=o.exec(s);)a[i[1]]=i[2];return a}const sx=s=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(s.trim());function Ql(s,a,o,i,u){if(R.isFunction(i))return i.call(this,a,o);if(u&&(a=o),!!R.isString(a)){if(R.isString(i))return a.indexOf(i)!==-1;if(R.isRegExp(i))return i.test(a)}}function ax(s){return s.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(a,o,i)=>o.toUpperCase()+i)}function ox(s,a){const o=R.toCamelCase(" "+a);["get","set","has"].forEach(i=>{Object.defineProperty(s,i+o,{value:function(u,m,f){return this[i].call(this,a,u,m,f)},configurable:!0})})}let ht=class{constructor(a){a&&this.set(a)}set(a,o,i){const u=this;function m(p,y,x){const g=ls(y);if(!g)throw new Error("header name must be a non-empty string");const v=R.findKey(u,g);(!v||u[v]===void 0||x===!0||x===void 0&&u[v]!==!1)&&(u[v||y]=Da(p))}const f=(p,y)=>R.forEach(p,(x,g)=>m(x,g,y));if(R.isPlainObject(a)||a instanceof this.constructor)f(a,o);else if(R.isString(a)&&(a=a.trim())&&!sx(a))f(rx(a),o);else if(R.isObject(a)&&R.isIterable(a)){let p={},y,x;for(const g of a){if(!R.isArray(g))throw TypeError("Object iterator must return a key-value pair");p[x=g[0]]=(y=p[x])?R.isArray(y)?[...y,g[1]]:[y,g[1]]:g[1]}f(p,o)}else a!=null&&m(o,a,i);return this}get(a,o){if(a=ls(a),a){const i=R.findKey(this,a);if(i){const u=this[i];if(!o)return u;if(o===!0)return nx(u);if(R.isFunction(o))return o.call(this,u,i);if(R.isRegExp(o))return o.exec(u);throw new TypeError("parser must be boolean|regexp|function")}}}has(a,o){if(a=ls(a),a){const i=R.findKey(this,a);return!!(i&&this[i]!==void 0&&(!o||Ql(this,this[i],i,o)))}return!1}delete(a,o){const i=this;let u=!1;function m(f){if(f=ls(f),f){const p=R.findKey(i,f);p&&(!o||Ql(i,i[p],p,o))&&(delete i[p],u=!0)}}return R.isArray(a)?a.forEach(m):m(a),u}clear(a){const o=Object.keys(this);let i=o.length,u=!1;for(;i--;){const m=o[i];(!a||Ql(this,this[m],m,a,!0))&&(delete this[m],u=!0)}return u}normalize(a){const o=this,i={};return R.forEach(this,(u,m)=>{const f=R.findKey(i,m);if(f){o[f]=Da(u),delete o[m];return}const p=a?ax(m):String(m).trim();p!==m&&delete o[m],o[p]=Da(u),i[p]=!0}),this}concat(...a){return this.constructor.concat(this,...a)}toJSON(a){const o=Object.create(null);return R.forEach(this,(i,u)=>{i!=null&&i!==!1&&(o[u]=a&&R.isArray(i)?i.join(", "):i)}),o}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([a,o])=>a+": "+o).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(a){return a instanceof this?a:new this(a)}static concat(a,...o){const i=new this(a);return o.forEach(u=>i.set(u)),i}static accessor(a){const i=(this[Nd]=this[Nd]={accessors:{}}).accessors,u=this.prototype;function m(f){const p=ls(f);i[p]||(ox(u,f),i[p]=!0)}return R.isArray(a)?a.forEach(m):m(a),this}};ht.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);R.reduceDescriptors(ht.prototype,({value:s},a)=>{let o=a[0].toUpperCase()+a.slice(1);return{get:()=>s,set(i){this[o]=i}}});R.freezeMethods(ht);function Yl(s,a){const o=this||gs,i=a||o,u=ht.from(i.headers);let m=i.data;return R.forEach(s,function(p){m=p.call(o,m,u.normalize(),a?a.status:void 0)}),u.normalize(),m}function ym(s){return!!(s&&s.__CANCEL__)}function jn(s,a,o){ye.call(this,s??"canceled",ye.ERR_CANCELED,a,o),this.name="CanceledError"}R.inherits(jn,ye,{__CANCEL__:!0});function vm(s,a,o){const i=o.config.validateStatus;!o.status||!i||i(o.status)?s(o):a(new ye("Request failed with status code "+o.status,[ye.ERR_BAD_REQUEST,ye.ERR_BAD_RESPONSE][Math.floor(o.status/100)-4],o.config,o.request,o))}function lx(s){const a=/^([-+\w]{1,25})(:?\/\/|:)/.exec(s);return a&&a[1]||""}function ix(s,a){s=s||10;const o=new Array(s),i=new Array(s);let u=0,m=0,f;return a=a!==void 0?a:1e3,function(y){const x=Date.now(),g=i[m];f||(f=x),o[u]=y,i[u]=x;let v=m,j=0;for(;v!==u;)j+=o[v++],v=v%s;if(u=(u+1)%s,u===m&&(m=(m+1)%s),x-f<a)return;const P=g&&x-g;return P?Math.round(j*1e3/P):void 0}}function cx(s,a){let o=0,i=1e3/a,u,m;const f=(x,g=Date.now())=>{o=g,u=null,m&&(clearTimeout(m),m=null),s.apply(null,x)};return[(...x)=>{const g=Date.now(),v=g-o;v>=i?f(x,g):(u=x,m||(m=setTimeout(()=>{m=null,f(u)},i-v)))},()=>u&&f(u)]}const Ma=(s,a,o=3)=>{let i=0;const u=ix(50,250);return cx(m=>{const f=m.loaded,p=m.lengthComputable?m.total:void 0,y=f-i,x=u(y),g=f<=p;i=f;const v={loaded:f,total:p,progress:p?f/p:void 0,bytes:y,rate:x||void 0,estimated:x&&p&&g?(p-f)/x:void 0,event:m,lengthComputable:p!=null,[a?"download":"upload"]:!0};s(v)},o)},kd=(s,a)=>{const o=s!=null;return[i=>a[0]({lengthComputable:o,total:s,loaded:i}),a[1]]},Sd=s=>(...a)=>R.asap(()=>s(...a)),ux=rt.hasStandardBrowserEnv?((s,a)=>o=>(o=new URL(o,rt.origin),s.protocol===o.protocol&&s.host===o.host&&(a||s.port===o.port)))(new URL(rt.origin),rt.navigator&&/(msie|trident)/i.test(rt.navigator.userAgent)):()=>!0,dx=rt.hasStandardBrowserEnv?{write(s,a,o,i,u,m){const f=[s+"="+encodeURIComponent(a)];R.isNumber(o)&&f.push("expires="+new Date(o).toGMTString()),R.isString(i)&&f.push("path="+i),R.isString(u)&&f.push("domain="+u),m===!0&&f.push("secure"),document.cookie=f.join("; ")},read(s){const a=document.cookie.match(new RegExp("(^|;\\s*)("+s+")=([^;]*)"));return a?decodeURIComponent(a[3]):null},remove(s){this.write(s,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function mx(s){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(s)}function fx(s,a){return a?s.replace(/\/?\/$/,"")+"/"+a.replace(/^\/+/,""):s}function wm(s,a,o){let i=!mx(a);return s&&(i||o==!1)?fx(s,a):a}const Cd=s=>s instanceof ht?{...s}:s;function $r(s,a){a=a||{};const o={};function i(x,g,v,j){return R.isPlainObject(x)&&R.isPlainObject(g)?R.merge.call({caseless:j},x,g):R.isPlainObject(g)?R.merge({},g):R.isArray(g)?g.slice():g}function u(x,g,v,j){if(R.isUndefined(g)){if(!R.isUndefined(x))return i(void 0,x,v,j)}else return i(x,g,v,j)}function m(x,g){if(!R.isUndefined(g))return i(void 0,g)}function f(x,g){if(R.isUndefined(g)){if(!R.isUndefined(x))return i(void 0,x)}else return i(void 0,g)}function p(x,g,v){if(v in a)return i(x,g);if(v in s)return i(void 0,x)}const y={url:m,method:m,data:m,baseURL:f,transformRequest:f,transformResponse:f,paramsSerializer:f,timeout:f,timeoutMessage:f,withCredentials:f,withXSRFToken:f,adapter:f,responseType:f,xsrfCookieName:f,xsrfHeaderName:f,onUploadProgress:f,onDownloadProgress:f,decompress:f,maxContentLength:f,maxBodyLength:f,beforeRedirect:f,transport:f,httpAgent:f,httpsAgent:f,cancelToken:f,socketPath:f,responseEncoding:f,validateStatus:p,headers:(x,g,v)=>u(Cd(x),Cd(g),v,!0)};return R.forEach(Object.keys(Object.assign({},s,a)),function(g){const v=y[g]||u,j=v(s[g],a[g],g);R.isUndefined(j)&&v!==p||(o[g]=j)}),o}const jm=s=>{const a=$r({},s);let{data:o,withXSRFToken:i,xsrfHeaderName:u,xsrfCookieName:m,headers:f,auth:p}=a;a.headers=f=ht.from(f),a.url=pm(wm(a.baseURL,a.url,a.allowAbsoluteUrls),s.params,s.paramsSerializer),p&&f.set("Authorization","Basic "+btoa((p.username||"")+":"+(p.password?unescape(encodeURIComponent(p.password)):"")));let y;if(R.isFormData(o)){if(rt.hasStandardBrowserEnv||rt.hasStandardBrowserWebWorkerEnv)f.setContentType(void 0);else if((y=f.getContentType())!==!1){const[x,...g]=y?y.split(";").map(v=>v.trim()).filter(Boolean):[];f.setContentType([x||"multipart/form-data",...g].join("; "))}}if(rt.hasStandardBrowserEnv&&(i&&R.isFunction(i)&&(i=i(a)),i||i!==!1&&ux(a.url))){const x=u&&m&&dx.read(m);x&&f.set(u,x)}return a},hx=typeof XMLHttpRequest<"u",px=hx&&function(s){return new Promise(function(o,i){const u=jm(s);let m=u.data;const f=ht.from(u.headers).normalize();let{responseType:p,onUploadProgress:y,onDownloadProgress:x}=u,g,v,j,P,k;function E(){P&&P(),k&&k(),u.cancelToken&&u.cancelToken.unsubscribe(g),u.signal&&u.signal.removeEventListener("abort",g)}let S=new XMLHttpRequest;S.open(u.method.toUpperCase(),u.url,!0),S.timeout=u.timeout;function _(){if(!S)return;const H=ht.from("getAllResponseHeaders"in S&&S.getAllResponseHeaders()),I={data:!p||p==="text"||p==="json"?S.responseText:S.response,status:S.status,statusText:S.statusText,headers:H,config:s,request:S};vm(function(U){o(U),E()},function(U){i(U),E()},I),S=null}"onloadend"in S?S.onloadend=_:S.onreadystatechange=function(){!S||S.readyState!==4||S.status===0&&!(S.responseURL&&S.responseURL.indexOf("file:")===0)||setTimeout(_)},S.onabort=function(){S&&(i(new ye("Request aborted",ye.ECONNABORTED,s,S)),S=null)},S.onerror=function(){i(new ye("Network Error",ye.ERR_NETWORK,s,S)),S=null},S.ontimeout=function(){let F=u.timeout?"timeout of "+u.timeout+"ms exceeded":"timeout exceeded";const I=u.transitional||xm;u.timeoutErrorMessage&&(F=u.timeoutErrorMessage),i(new ye(F,I.clarifyTimeoutError?ye.ETIMEDOUT:ye.ECONNABORTED,s,S)),S=null},m===void 0&&f.setContentType(null),"setRequestHeader"in S&&R.forEach(f.toJSON(),function(F,I){S.setRequestHeader(I,F)}),R.isUndefined(u.withCredentials)||(S.withCredentials=!!u.withCredentials),p&&p!=="json"&&(S.responseType=u.responseType),x&&([j,k]=Ma(x,!0),S.addEventListener("progress",j)),y&&S.upload&&([v,P]=Ma(y),S.upload.addEventListener("progress",v),S.upload.addEventListener("loadend",P)),(u.cancelToken||u.signal)&&(g=H=>{S&&(i(!H||H.type?new jn(null,s,S):H),S.abort(),S=null)},u.cancelToken&&u.cancelToken.subscribe(g),u.signal&&(u.signal.aborted?g():u.signal.addEventListener("abort",g)));const B=lx(u.url);if(B&&rt.protocols.indexOf(B)===-1){i(new ye("Unsupported protocol "+B+":",ye.ERR_BAD_REQUEST,s));return}S.send(m||null)})},xx=(s,a)=>{const{length:o}=s=s?s.filter(Boolean):[];if(a||o){let i=new AbortController,u;const m=function(x){if(!u){u=!0,p();const g=x instanceof Error?x:this.reason;i.abort(g instanceof ye?g:new jn(g instanceof Error?g.message:g))}};let f=a&&setTimeout(()=>{f=null,m(new ye(`timeout ${a} of ms exceeded`,ye.ETIMEDOUT))},a);const p=()=>{s&&(f&&clearTimeout(f),f=null,s.forEach(x=>{x.unsubscribe?x.unsubscribe(m):x.removeEventListener("abort",m)}),s=null)};s.forEach(x=>x.addEventListener("abort",m));const{signal:y}=i;return y.unsubscribe=()=>R.asap(p),y}},gx=function*(s,a){let o=s.byteLength;if(o<a){yield s;return}let i=0,u;for(;i<o;)u=i+a,yield s.slice(i,u),i=u},yx=async function*(s,a){for await(const o of vx(s))yield*gx(o,a)},vx=async function*(s){if(s[Symbol.asyncIterator]){yield*s;return}const a=s.getReader();try{for(;;){const{done:o,value:i}=await a.read();if(o)break;yield i}}finally{await a.cancel()}},Ed=(s,a,o,i)=>{const u=yx(s,a);let m=0,f,p=y=>{f||(f=!0,i&&i(y))};return new ReadableStream({async pull(y){try{const{done:x,value:g}=await u.next();if(x){p(),y.close();return}let v=g.byteLength;if(o){let j=m+=v;o(j)}y.enqueue(new Uint8Array(g))}catch(x){throw p(x),x}},cancel(y){return p(y),u.return()}},{highWaterMark:2})},Qa=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",bm=Qa&&typeof ReadableStream=="function",wx=Qa&&(typeof TextEncoder=="function"?(s=>a=>s.encode(a))(new TextEncoder):async s=>new Uint8Array(await new Response(s).arrayBuffer())),Nm=(s,...a)=>{try{return!!s(...a)}catch{return!1}},jx=bm&&Nm(()=>{let s=!1;const a=new Request(rt.origin,{body:new ReadableStream,method:"POST",get duplex(){return s=!0,"half"}}).headers.has("Content-Type");return s&&!a}),Pd=64*1024,ni=bm&&Nm(()=>R.isReadableStream(new Response("").body)),Ra={stream:ni&&(s=>s.body)};Qa&&(s=>{["text","arrayBuffer","blob","formData","stream"].forEach(a=>{!Ra[a]&&(Ra[a]=R.isFunction(s[a])?o=>o[a]():(o,i)=>{throw new ye(`Response type '${a}' is not supported`,ye.ERR_NOT_SUPPORT,i)})})})(new Response);const bx=async s=>{if(s==null)return 0;if(R.isBlob(s))return s.size;if(R.isSpecCompliantForm(s))return(await new Request(rt.origin,{method:"POST",body:s}).arrayBuffer()).byteLength;if(R.isArrayBufferView(s)||R.isArrayBuffer(s))return s.byteLength;if(R.isURLSearchParams(s)&&(s=s+""),R.isString(s))return(await wx(s)).byteLength},Nx=async(s,a)=>{const o=R.toFiniteNumber(s.getContentLength());return o??bx(a)},kx=Qa&&(async s=>{let{url:a,method:o,data:i,signal:u,cancelToken:m,timeout:f,onDownloadProgress:p,onUploadProgress:y,responseType:x,headers:g,withCredentials:v="same-origin",fetchOptions:j}=jm(s);x=x?(x+"").toLowerCase():"text";let P=xx([u,m&&m.toAbortSignal()],f),k;const E=P&&P.unsubscribe&&(()=>{P.unsubscribe()});let S;try{if(y&&jx&&o!=="get"&&o!=="head"&&(S=await Nx(g,i))!==0){let I=new Request(a,{method:"POST",body:i,duplex:"half"}),z;if(R.isFormData(i)&&(z=I.headers.get("content-type"))&&g.setContentType(z),I.body){const[U,oe]=kd(S,Ma(Sd(y)));i=Ed(I.body,Pd,U,oe)}}R.isString(v)||(v=v?"include":"omit");const _="credentials"in Request.prototype;k=new Request(a,{...j,signal:P,method:o.toUpperCase(),headers:g.normalize().toJSON(),body:i,duplex:"half",credentials:_?v:void 0});let B=await fetch(k);const H=ni&&(x==="stream"||x==="response");if(ni&&(p||H&&E)){const I={};["status","statusText","headers"].forEach(fe=>{I[fe]=B[fe]});const z=R.toFiniteNumber(B.headers.get("content-length")),[U,oe]=p&&kd(z,Ma(Sd(p),!0))||[];B=new Response(Ed(B.body,Pd,U,()=>{oe&&oe(),E&&E()}),I)}x=x||"text";let F=await Ra[R.findKey(Ra,x)||"text"](B,s);return!H&&E&&E(),await new Promise((I,z)=>{vm(I,z,{data:F,headers:ht.from(B.headers),status:B.status,statusText:B.statusText,config:s,request:k})})}catch(_){throw E&&E(),_&&_.name==="TypeError"&&/Load failed|fetch/i.test(_.message)?Object.assign(new ye("Network Error",ye.ERR_NETWORK,s,k),{cause:_.cause||_}):ye.from(_,_&&_.code,s,k)}}),si={http:I0,xhr:px,fetch:kx};R.forEach(si,(s,a)=>{if(s){try{Object.defineProperty(s,"name",{value:a})}catch{}Object.defineProperty(s,"adapterName",{value:a})}});const Dd=s=>`- ${s}`,Sx=s=>R.isFunction(s)||s===null||s===!1,km={getAdapter:s=>{s=R.isArray(s)?s:[s];const{length:a}=s;let o,i;const u={};for(let m=0;m<a;m++){o=s[m];let f;if(i=o,!Sx(o)&&(i=si[(f=String(o)).toLowerCase()],i===void 0))throw new ye(`Unknown adapter '${f}'`);if(i)break;u[f||"#"+m]=i}if(!i){const m=Object.entries(u).map(([p,y])=>`adapter ${p} `+(y===!1?"is not supported by the environment":"is not available in the build"));let f=a?m.length>1?`since :
`+m.map(Dd).join(`
`):" "+Dd(m[0]):"as no adapter specified";throw new ye("There is no suitable adapter to dispatch the request "+f,"ERR_NOT_SUPPORT")}return i},adapters:si};function Jl(s){if(s.cancelToken&&s.cancelToken.throwIfRequested(),s.signal&&s.signal.aborted)throw new jn(null,s)}function Td(s){return Jl(s),s.headers=ht.from(s.headers),s.data=Yl.call(s,s.transformRequest),["post","put","patch"].indexOf(s.method)!==-1&&s.headers.setContentType("application/x-www-form-urlencoded",!1),km.getAdapter(s.adapter||gs.adapter)(s).then(function(i){return Jl(s),i.data=Yl.call(s,s.transformResponse,i),i.headers=ht.from(i.headers),i},function(i){return ym(i)||(Jl(s),i&&i.response&&(i.response.data=Yl.call(s,s.transformResponse,i.response),i.response.headers=ht.from(i.response.headers))),Promise.reject(i)})}const Sm="1.9.0",Ya={};["object","boolean","number","function","string","symbol"].forEach((s,a)=>{Ya[s]=function(i){return typeof i===s||"a"+(a<1?"n ":" ")+s}});const Ld={};Ya.transitional=function(a,o,i){function u(m,f){return"[Axios v"+Sm+"] Transitional option '"+m+"'"+f+(i?". "+i:"")}return(m,f,p)=>{if(a===!1)throw new ye(u(f," has been removed"+(o?" in "+o:"")),ye.ERR_DEPRECATED);return o&&!Ld[f]&&(Ld[f]=!0,console.warn(u(f," has been deprecated since v"+o+" and will be removed in the near future"))),a?a(m,f,p):!0}};Ya.spelling=function(a){return(o,i)=>(console.warn(`${i} is likely a misspelling of ${a}`),!0)};function Cx(s,a,o){if(typeof s!="object")throw new ye("options must be an object",ye.ERR_BAD_OPTION_VALUE);const i=Object.keys(s);let u=i.length;for(;u-- >0;){const m=i[u],f=a[m];if(f){const p=s[m],y=p===void 0||f(p,m,s);if(y!==!0)throw new ye("option "+m+" must be "+y,ye.ERR_BAD_OPTION_VALUE);continue}if(o!==!0)throw new ye("Unknown option "+m,ye.ERR_BAD_OPTION)}}const Ta={assertOptions:Cx,validators:Ya},$t=Ta.validators;let Br=class{constructor(a){this.defaults=a||{},this.interceptors={request:new bd,response:new bd}}async request(a,o){try{return await this._request(a,o)}catch(i){if(i instanceof Error){let u={};Error.captureStackTrace?Error.captureStackTrace(u):u=new Error;const m=u.stack?u.stack.replace(/^.+\n/,""):"";try{i.stack?m&&!String(i.stack).endsWith(m.replace(/^.+\n.+\n/,""))&&(i.stack+=`
`+m):i.stack=m}catch{}}throw i}}_request(a,o){typeof a=="string"?(o=o||{},o.url=a):o=a||{},o=$r(this.defaults,o);const{transitional:i,paramsSerializer:u,headers:m}=o;i!==void 0&&Ta.assertOptions(i,{silentJSONParsing:$t.transitional($t.boolean),forcedJSONParsing:$t.transitional($t.boolean),clarifyTimeoutError:$t.transitional($t.boolean)},!1),u!=null&&(R.isFunction(u)?o.paramsSerializer={serialize:u}:Ta.assertOptions(u,{encode:$t.function,serialize:$t.function},!0)),o.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?o.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:o.allowAbsoluteUrls=!0),Ta.assertOptions(o,{baseUrl:$t.spelling("baseURL"),withXsrfToken:$t.spelling("withXSRFToken")},!0),o.method=(o.method||this.defaults.method||"get").toLowerCase();let f=m&&R.merge(m.common,m[o.method]);m&&R.forEach(["delete","get","head","post","put","patch","common"],k=>{delete m[k]}),o.headers=ht.concat(f,m);const p=[];let y=!0;this.interceptors.request.forEach(function(E){typeof E.runWhen=="function"&&E.runWhen(o)===!1||(y=y&&E.synchronous,p.unshift(E.fulfilled,E.rejected))});const x=[];this.interceptors.response.forEach(function(E){x.push(E.fulfilled,E.rejected)});let g,v=0,j;if(!y){const k=[Td.bind(this),void 0];for(k.unshift.apply(k,p),k.push.apply(k,x),j=k.length,g=Promise.resolve(o);v<j;)g=g.then(k[v++],k[v++]);return g}j=p.length;let P=o;for(v=0;v<j;){const k=p[v++],E=p[v++];try{P=k(P)}catch(S){E.call(this,S);break}}try{g=Td.call(this,P)}catch(k){return Promise.reject(k)}for(v=0,j=x.length;v<j;)g=g.then(x[v++],x[v++]);return g}getUri(a){a=$r(this.defaults,a);const o=wm(a.baseURL,a.url,a.allowAbsoluteUrls);return pm(o,a.params,a.paramsSerializer)}};R.forEach(["delete","get","head","options"],function(a){Br.prototype[a]=function(o,i){return this.request($r(i||{},{method:a,url:o,data:(i||{}).data}))}});R.forEach(["post","put","patch"],function(a){function o(i){return function(m,f,p){return this.request($r(p||{},{method:a,headers:i?{"Content-Type":"multipart/form-data"}:{},url:m,data:f}))}}Br.prototype[a]=o(),Br.prototype[a+"Form"]=o(!0)});let Ex=class Cm{constructor(a){if(typeof a!="function")throw new TypeError("executor must be a function.");let o;this.promise=new Promise(function(m){o=m});const i=this;this.promise.then(u=>{if(!i._listeners)return;let m=i._listeners.length;for(;m-- >0;)i._listeners[m](u);i._listeners=null}),this.promise.then=u=>{let m;const f=new Promise(p=>{i.subscribe(p),m=p}).then(u);return f.cancel=function(){i.unsubscribe(m)},f},a(function(m,f,p){i.reason||(i.reason=new jn(m,f,p),o(i.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(a){if(this.reason){a(this.reason);return}this._listeners?this._listeners.push(a):this._listeners=[a]}unsubscribe(a){if(!this._listeners)return;const o=this._listeners.indexOf(a);o!==-1&&this._listeners.splice(o,1)}toAbortSignal(){const a=new AbortController,o=i=>{a.abort(i)};return this.subscribe(o),a.signal.unsubscribe=()=>this.unsubscribe(o),a.signal}static source(){let a;return{token:new Cm(function(u){a=u}),cancel:a}}};function Px(s){return function(o){return s.apply(null,o)}}function Dx(s){return R.isObject(s)&&s.isAxiosError===!0}const ai={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(ai).forEach(([s,a])=>{ai[a]=s});function Em(s){const a=new Br(s),o=nm(Br.prototype.request,a);return R.extend(o,Br.prototype,a,{allOwnKeys:!0}),R.extend(o,a,null,{allOwnKeys:!0}),o.create=function(u){return Em($r(s,u))},o}const Be=Em(gs);Be.Axios=Br;Be.CanceledError=jn;Be.CancelToken=Ex;Be.isCancel=ym;Be.VERSION=Sm;Be.toFormData=Va;Be.AxiosError=ye;Be.Cancel=Be.CanceledError;Be.all=function(a){return Promise.all(a)};Be.spread=Px;Be.isAxiosError=Dx;Be.mergeConfig=$r;Be.AxiosHeaders=ht;Be.formToJSON=s=>gm(R.isHTMLForm(s)?new FormData(s):s);Be.getAdapter=km.getAdapter;Be.HttpStatusCode=ai;Be.default=Be;const{Axios:iv,AxiosError:cv,CanceledError:uv,isCancel:dv,CancelToken:mv,VERSION:fv,all:hv,Cancel:pv,isAxiosError:xv,spread:gv,toFormData:yv,AxiosHeaders:vv,HttpStatusCode:wv,formToJSON:jv,getAdapter:bv,mergeConfig:Nv}=Be,Ot=Be.create({baseURL:"http://localhost:8000/api/v1",headers:{"Content-Type":"application/json"}});Ot.interceptors.request.use(s=>{const a=localStorage.getItem("auth_token");return a&&(s.headers.Authorization=`Bearer ${a}`),s});function hs(s){const[a,o]=b.useState(null),[i,u]=b.useState(null),[m,f]=b.useState(!1);return{data:a,error:i,loading:m,fetchData:async(v={})=>{var j;f(!0),u(null);try{const P=await Ot.get(s,{params:v});return o(P.data),P.data}catch(P){return u(((j=P.response)==null?void 0:j.data)||{message:P.message}),null}finally{f(!1)}},createData:async v=>{var j;f(!0),u(null);try{return(await Ot.post(s,v)).data}catch(P){return u(((j=P.response)==null?void 0:j.data)||{message:P.message}),null}finally{f(!1)}},updateData:async(v,j)=>{var P;f(!0),u(null);try{return(await Ot.put(`${s}/${v}`,j)).data}catch(k){return u(((P=k.response)==null?void 0:P.data)||{message:k.message}),null}finally{f(!1)}},deleteData:async v=>{var j;f(!0),u(null);try{return(await Ot.delete(`${s}/${v}`)).data}catch(P){return u(((j=P.response)==null?void 0:j.data)||{message:P.message}),null}finally{f(!1)}}}}const Pm=b.createContext();function Tx({children:s}){const[a,o]=b.useState([]),[i,u]=b.useState(!0),[m,f]=b.useState(null),[p,y]=b.useState(null),[x,g]=b.useState(!1),[v,j]=b.useState(null),P=async()=>{try{u(!0),console.log("Fetching templates from API...");const I=await Ot.get("/forms/templates/");console.log("API response:",I.data);const z=I.data.results?I.data.results.map(U=>{var oe,fe,Z,re,de,ie,J,D;return{id:U.id,name:U.name,documentType:U.document_type,createdDate:U.created_at,status:U.status.charAt(0).toUpperCase()+U.status.slice(1),createdBy:((oe=U.created_by)==null?void 0:oe.name)||"Admin",updatedDate:U.updated_at,updatedBy:(fe=U.updated_by)==null?void 0:fe.name,questions:U.content&&U.content.questions?U.content.questions:[],mandatory:((re=(Z=U.content)==null?void 0:Z.settings)==null?void 0:re.mandatory)||!1,notify:((ie=(de=U.content)==null?void 0:de.settings)==null?void 0:ie.notify)||!1,expiration:((D=(J=U.content)==null?void 0:J.settings)==null?void 0:D.expiration)||null}}):[];o(z),u(!1)}catch(I){console.error("Error fetching templates:",I),console.error("Error details:",I.response?I.response.data:"No response data"),console.error("Error status:",I.response?I.response.status:"No status code"),f(`Failed to load templates: ${I.message}`),u(!1)}},k=b.useCallback(async I=>{var z,U,oe,fe,Z,re,de,ie,J;try{g(!0),j(null),console.log(`Fetching template details for ID: ${I}`);const D=await Ot.get(`/forms/templates/${I}/`);console.log("Template details response:",D.data);const q={id:D.data.id,name:D.data.name,documentType:D.data.document_type,status:D.data.status.charAt(0).toUpperCase()+D.data.status.slice(1),createdDate:D.data.created_at,createdBy:((z=D.data.created_by)==null?void 0:z.name)||"Admin",updatedDate:D.data.updated_at,updatedBy:(U=D.data.updated_by)==null?void 0:U.name,questions:((oe=D.data.content)==null?void 0:oe.questions)||[],history:D.data.history||[],settings:{mandatory:((Z=(fe=D.data.content)==null?void 0:fe.settings)==null?void 0:Z.mandatory)||!1,notify:((de=(re=D.data.content)==null?void 0:re.settings)==null?void 0:de.notify)||!1,expiration:((J=(ie=D.data.content)==null?void 0:ie.settings)==null?void 0:J.expiration)||null}};return y(q),g(!1),q}catch(D){throw console.error(`Error fetching template details for ID ${I}:`,D),console.error("Error details:",D.response?D.response.data:"No response data"),j(`Failed to load template details: ${D.message}`),g(!1),D}},[]),E=async I=>{try{console.log("Sending data to API:",JSON.stringify(I,null,2));const z=await Ot.post("/forms/templates/",I);return console.log("Template created successfully:",z.data),await P(),z.data}catch(z){if(console.error("Error creating template:",z),z.response&&z.response.status===400){console.error("Validation errors:",z.response.data);const U=Object.entries(z.response.data).map(([oe,fe])=>`${oe}: ${Array.isArray(fe)?fe.join(", "):fe}`).join(`
`);throw new Error(`Validation failed: ${U}`)}throw console.error("Error details:",z.response?z.response.data:"No response data"),console.error("Error status:",z.response?z.response.status:"No status code"),z}},S=async(I,z)=>{try{console.log("Updating template with data:",JSON.stringify(z,null,2)),await Ot.put(`/forms/templates/${I}/`,z),console.log("Template updated successfully"),await P()}catch(U){throw console.error("Error updating template:",U),console.error("Error details:",U.response?U.response.data:"No response data"),U}},_=async I=>{try{return console.log(`Deleting template with ID: ${I}`),await Ot.delete(`/forms/templates/${I}/`),console.log("Template deleted successfully"),o(a.filter(z=>z.id!==I)),!0}catch(z){throw console.error("Error deleting template:",z),console.error("Error details:",z.response?z.response.data:"No response data"),z}};b.useEffect(()=>{P()},[]);const B=async I=>{try{const z={name:I.name,document_type:I.documentType,status:I.status,business:1,content:{questions:I.questions||[],settings:{mandatory:I.mandatory||!1,notify:I.notify||!1,expiration:I.expiration||null}}};console.log("Creating new form with data:",z);const U=await E(z);return console.log("Form added successfully:",U),U}catch(z){throw console.error("Failed to add form:",z),z}},H=(I,z)=>{S(I,{name:z.name,document_type:z.documentType,status:z.status||"published",business:1,content:{questions:z.questions||[],settings:{mandatory:z.mandatory||!1,notify:z.notify||!1,expiration:z.expiration||null}}})},F=async I=>{try{return await _(I)}catch(z){throw console.error("Failed to delete form:",z),z}};return r.jsx(Pm.Provider,{value:{forms:a,loading:i,error:m,setForms:o,addForm:B,updateForm:H,deleteForm:F,fetchTemplates:P,templateDetails:p,detailsLoading:x,detailsError:v,fetchTemplateDetails:k},children:s})}function Ja(){return b.useContext(Pm)}function Od(){return r.jsxs("div",{className:"container py-10",children:[r.jsx("h1",{className:"text-3xl mb-6",children:"Dashboard"}),r.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:r.jsx("p",{className:"text-gray-600",children:"View your business analytics and key metrics here."})})]})}function Lx(){return r.jsxs("div",{className:"container py-10",children:[r.jsx("h1",{className:"text-3xl mb-6",children:"Calendar"}),r.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:r.jsx("p",{className:"text-gray-600",children:"Calendar view will be implemented here."})})]})}function Ox(){return r.jsxs("div",{className:"container py-10",children:[r.jsx("h1",{className:"text-3xl mb-6",children:"Checkout"}),r.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:r.jsx("p",{className:"text-gray-600",children:"Checkout functionality will be implemented here."})})]})}function Mx(){return r.jsxs("div",{className:"container py-10",children:[r.jsx("h1",{className:"text-3xl mb-6",children:"Checkout Settings"}),r.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:r.jsx("p",{className:"text-gray-600",children:"Configure your checkout settings here."})})]})}function Rx(){return r.jsxs("div",{className:"container py-10",children:[r.jsx("h1",{className:"text-3xl mb-6",children:"Refund Customer"}),r.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:r.jsx("p",{className:"text-gray-600",children:"Process customer refunds here."})})]})}function Ax(){return r.jsxs("div",{className:"container py-10",children:[r.jsx("h1",{className:"text-3xl mb-6",children:"Invoices"}),r.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:r.jsx("p",{className:"text-gray-600",children:"Manage your invoices here."})})]})}function _x(s=[]){const a=s,[o,i]=b.useState(""),[u,m]=b.useState("name"),[f,p]=b.useState("asc"),[y,x]=b.useState(1),g=10,v=z=>{u===z?p(f==="asc"?"desc":"asc"):(m(z),p("asc")),x(1)},j=b.useMemo(()=>{let z=a.filter(U=>{if(!o)return!0;const oe=o.toLowerCase();return U.name.toLowerCase().includes(oe)||U.email.toLowerCase().includes(oe)||U.phone.includes(o)});return z.sort((U,oe)=>{var re,de;let fe=U[u]??"",Z=oe[u]??"";if(u.includes("address.")){const ie=u.split(".")[1];fe=((re=U.address)==null?void 0:re[ie])??"",Z=((de=oe.address)==null?void 0:de[ie])??""}return fe=String(fe).toLowerCase(),Z=String(Z).toLowerCase(),f==="asc"?fe<Z?-1:fe>Z?1:0:fe>Z?-1:fe<Z?1:0}),z},[a,o,u,f]),P=b.useMemo(()=>{const z=(y-1)*g,U=z+g;return j.slice(z,U)},[j,y,g]),k=j.length,E=Math.ceil(k/g),S=k===0?0:(y-1)*g+1,_=Math.min(y*g,k);return{filteredCustomers:P,searchTerm:o,setSearchTerm:z=>{i(z),x(1)},sortBy:u,sortOrder:f,handleSort:v,currentPage:y,totalPages:E,totalItems:k,startItem:S,endItem:_,itemsPerPage:g,goToPage:z=>{x(Math.max(1,Math.min(z,E)))},goToNextPage:()=>{y<E&&x(y+1)},goToPrevPage:()=>{y>1&&x(y-1)}}}function Fx({searchTerm:s,setSearchTerm:a}){return r.jsx("div",{className:"mb-8",children:r.jsxs("div",{className:"relative max-w-md",children:[r.jsx("div",{className:"absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none",children:r.jsx("svg",{className:"h-5 w-5 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})}),r.jsx("input",{type:"text",placeholder:"Search by name, email, or phone number...",value:s,onChange:o=>a(o.target.value),className:"block w-full pl-12 pr-4 py-3 bg-white border border-gray-200 rounded-xl shadow-sm focus:ring-2 focus:ring-blue-500/20 focus:border-blue-500 transition-all duration-200 placeholder-gray-400 text-gray-900"}),s&&r.jsx("button",{onClick:()=>a(""),className:"absolute inset-y-0 right-0 pr-4 flex items-center text-gray-400 hover:text-gray-600 transition-colors",children:r.jsx("svg",{className:"h-4 w-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})})}function zx({customer:s}){const[a,o]=b.useState(!1),[i,u]=b.useState(!1),m=async(f,p)=>{try{await navigator.clipboard.writeText(f),p==="email"?(o(!0),setTimeout(()=>o(!1),2e3)):(u(!0),setTimeout(()=>u(!1),2e3))}catch(y){console.error("Failed to copy: ",y)}};return r.jsxs("tr",{className:"hover:bg-gray-50",children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsxs("div",{className:"flex items-center",children:[r.jsx("img",{src:s.avatar,alt:s.name,className:"w-10 h-10 rounded-full mr-3"}),r.jsx("div",{className:"flex items-center",children:r.jsxs(ot,{to:`/customers/${s.id}`,className:"text-sm font-medium text-gray-900 hover:text-blue-600 transition-colors flex items-center group",children:[s.name,r.jsxs("svg",{className:"w-3 h-3 sm:w-4 sm:h-4 ml-1 sm:ml-2 text-gray-400 group-hover:text-blue-600 transition-colors",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})]})})]})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsxs("div",{className:"flex items-center",children:[r.jsx("span",{className:"text-sm text-gray-900 mr-2",children:s.email}),r.jsx("button",{onClick:()=>m(s.email,"email"),className:"p-1 rounded hover:bg-gray-100 transition-colors",title:"Copy email",children:a?r.jsx("svg",{className:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}):r.jsx("svg",{className:"w-4 h-4 text-gray-400 hover:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})})})]})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:r.jsxs("div",{className:"flex items-center",children:[r.jsx("span",{className:"text-sm text-gray-900 mr-2",children:s.phone}),r.jsx("button",{onClick:()=>m(s.phone,"phone"),className:"p-1 rounded hover:bg-gray-100 transition-colors",title:"Copy phone number",children:i?r.jsx("svg",{className:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}):r.jsx("svg",{className:"w-4 h-4 text-gray-400 hover:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})})})]})})]})}function Ix({active:s,order:a}){return s?a==="asc"?r.jsx("svg",{className:"w-4 h-4 ml-1 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12"})}):r.jsx("svg",{className:"w-4 h-4 ml-1 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4"})}):r.jsx("svg",{className:"w-4 h-4 ml-1 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"})})}const Bx="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100";function $x({customers:s,onSort:a,sortBy:o,sortOrder:i}){return r.jsxs("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:[r.jsx("div",{className:"overflow-x-auto",children:r.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:r.jsx("tr",{children:[["name","Customer"],["email","Email"],["phone","Phone Number"]].map(([u,m])=>r.jsx("th",{className:Bx,onClick:()=>a(u),children:r.jsxs("div",{className:"flex items-center",children:[m,r.jsx(Ix,{active:o===u,order:i})]})},u))})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(u=>r.jsx(zx,{customer:u},u.id))})]})}),s.length===0&&r.jsxs("div",{className:"text-center py-12",children:[r.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"})}),r.jsx("p",{className:"mt-2 text-sm text-gray-500",children:"No customers found"})]})]})}function Ux({currentPage:s,totalPages:a,totalItems:o,startItem:i,endItem:u,itemsPerPage:m,onPageChange:f,onNextPage:p,onPrevPage:y}){const x=()=>{const v=[],j=Math.max(1,s-2),P=Math.min(a,s+2);j>1&&(v.push(1),j>2&&v.push("..."));for(let k=j;k<=P;k++)v.push(k);return P<a&&(P<a-1&&v.push("..."),v.push(a)),v};return a<=1?null:r.jsxs("div",{className:"bg-white px-4 py-3 flex items-center justify-between border-t border-gray-200 sm:px-6",children:[r.jsxs("div",{className:"flex-1 flex justify-between sm:hidden",children:[r.jsx("button",{onClick:y,disabled:s===1,className:"relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),r.jsx("button",{onClick:p,disabled:s===a,className:"ml-3 relative inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]}),r.jsxs("div",{className:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between",children:[r.jsx("div",{children:r.jsxs("p",{className:"text-sm text-gray-700",children:["Showing ",r.jsx("span",{className:"font-medium",children:i})," to"," ",r.jsx("span",{className:"font-medium",children:u})," of"," ",r.jsx("span",{className:"font-medium",children:o})," results"]})}),r.jsx("div",{children:r.jsxs("nav",{className:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination",children:[r.jsxs("button",{onClick:y,disabled:s===1,className:"relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[r.jsx("span",{className:"sr-only",children:"Previous"}),r.jsx("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:r.jsx("path",{fillRule:"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z",clipRule:"evenodd"})})]}),x().map((g,v)=>g==="..."?r.jsx("span",{className:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700",children:"..."},`ellipsis-${v}`):r.jsx("button",{onClick:()=>f(g),className:`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${g===s?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"}`,children:g},g)),r.jsxs("button",{onClick:p,disabled:s===a,className:"relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:[r.jsx("span",{className:"sr-only",children:"Next"}),r.jsx("svg",{className:"h-5 w-5",fill:"currentColor",viewBox:"0 0 20 20",children:r.jsx("path",{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"})})]})]})})]})]})}const Wx=s=>{const[a,o]=b.useState(!1),[i,u]=b.useState({name:"",email:"",phone:"",gender:"",birthday:"",address:{street:"",city:"",postalCode:"",country:""}}),[m,f]=b.useState({}),[p,y]=b.useState(!1),x=()=>{o(!0),v()},g=()=>{o(!1),v()},v=b.useCallback(()=>{u({name:"",email:"",phone:"",gender:"",birthday:"",address:{street:"",city:"",postalCode:"",country:""}}),f({}),y(!1)},[]),j=(E,S)=>{if(E.includes(".")){const[_,B]=E.split(".");u(H=>({...H,[_]:{...H[_],[B]:S}}))}else u(_=>({..._,[E]:S}));m[E]&&f(_=>({..._,[E]:""}))},P=()=>{const E={};return i.name.trim()||(E.name="Name is required"),i.email.trim()?/\S+@\S+\.\S+/.test(i.email)||(E.email="Please enter a valid email address"):E.email="Email is required",i.phone.trim()||(E.phone="Phone number is required"),f(E),Object.keys(E).length===0};return{showModal:a,formData:i,errors:m,isSubmitting:p,openModal:x,closeModal:g,resetForm:v,updateFormData:j,handleSubmit:async E=>{if(E.preventDefault(),!!P()){y(!0);try{await new Promise(_=>setTimeout(_,1e3));const S={id:Date.now(),...i,avatar:"https://via.placeholder.com/40",status:"active",joinDate:new Date().toISOString().split("T")[0],lastOrder:null,totalOrders:0};s(S),g()}catch(S){console.error("Error adding customer:",S),f({submit:"Failed to add customer. Please try again."})}finally{y(!1)}}}}};function Hx({isOpen:s,onClose:a,onAddCustomer:o}){const{formData:i,errors:u,isSubmitting:m,resetForm:f,updateFormData:p,handleSubmit:y}=Wx(o);return b.useEffect(()=>{s&&f()},[s]),s?r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:r.jsxs("div",{className:"bg-white rounded-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto shadow-2xl",children:[r.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[r.jsxs("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Add New Customer"}),r.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Create a new customer profile"})]}),r.jsx("button",{onClick:a,className:"text-gray-400 hover:text-gray-600 transition-colors p-1",disabled:m,children:r.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),r.jsxs("form",{onSubmit:y,className:"p-6",children:[u.submit&&r.jsx("div",{className:"mb-6 p-4 bg-red-50 border border-red-200 rounded-lg",children:r.jsxs("div",{className:"flex items-center",children:[r.jsx("svg",{className:"w-5 h-5 text-red-500 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),r.jsx("span",{className:"text-sm text-red-700",children:u.submit})]})}),r.jsxs("div",{className:"space-y-6",children:[r.jsxs("div",{children:[r.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-4",children:"Personal Information"}),r.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[r.jsxs("div",{className:"sm:col-span-2",children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Full Name ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{type:"text",value:i.name,onChange:x=>p("name",x.target.value),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${u.name?"border-red-300 bg-red-50":"border-gray-300"}`,placeholder:"Enter customer's full name",disabled:m}),u.name&&r.jsx("p",{className:"mt-1 text-sm text-red-600",children:u.name})]}),r.jsxs("div",{children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Email Address ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{type:"email",value:i.email,onChange:x=>p("email",x.target.value),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${u.email?"border-red-300 bg-red-50":"border-gray-300"}`,placeholder:"<EMAIL>",disabled:m}),u.email&&r.jsx("p",{className:"mt-1 text-sm text-red-600",children:u.email})]}),r.jsxs("div",{children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Phone Number ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{type:"tel",value:i.phone,onChange:x=>p("phone",x.target.value),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${u.phone?"border-red-300 bg-red-50":"border-gray-300"}`,placeholder:"+****************",disabled:m}),u.phone&&r.jsx("p",{className:"mt-1 text-sm text-red-600",children:u.phone})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Gender"}),r.jsxs("select",{value:i.gender,onChange:x=>p("gender",x.target.value),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${u.gender?"border-red-300 bg-red-50":"border-gray-300"}`,disabled:m,children:[r.jsx("option",{value:"",children:"Select gender"}),r.jsx("option",{value:"male",children:"Male"}),r.jsx("option",{value:"female",children:"Female"}),r.jsx("option",{value:"other",children:"Other"})]}),u.gender&&r.jsx("p",{className:"mt-1 text-sm text-red-600",children:u.gender})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Birthday"}),r.jsx("input",{type:"date",value:i.birthday,onChange:x=>p("birthday",x.target.value),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${u.birthday?"border-red-300 bg-red-50":"border-gray-300"}`,disabled:m}),u.birthday&&r.jsx("p",{className:"mt-1 text-sm text-red-600",children:u.birthday})]})]})]}),r.jsxs("div",{children:[r.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-4",children:"Address Information"}),r.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:[r.jsxs("div",{className:"sm:col-span-2",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Street Address"}),r.jsx("input",{type:"text",value:i.address.street,onChange:x=>p("address.street",x.target.value),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${u["address.street"]?"border-red-300 bg-red-50":"border-gray-300"}`,placeholder:"123 Main Street",disabled:m}),u["address.street"]&&r.jsx("p",{className:"mt-1 text-sm text-red-600",children:u["address.street"]})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"City"}),r.jsx("input",{type:"text",value:i.address.city,onChange:x=>p("address.city",x.target.value),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${u["address.city"]?"border-red-300 bg-red-50":"border-gray-300"}`,placeholder:"New York",disabled:m}),u["address.city"]&&r.jsx("p",{className:"mt-1 text-sm text-red-600",children:u["address.city"]})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Postal Code"}),r.jsx("input",{type:"text",value:i.address.postalCode,onChange:x=>p("address.postalCode",x.target.value),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${u["address.postalCode"]?"border-red-300 bg-red-50":"border-gray-300"}`,placeholder:"10001",disabled:m}),u["address.postalCode"]&&r.jsx("p",{className:"mt-1 text-sm text-red-600",children:u["address.postalCode"]})]}),r.jsxs("div",{className:"sm:col-span-2",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Country"}),r.jsx("input",{type:"text",value:i.address.country,onChange:x=>p("address.country",x.target.value),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors ${u["address.country"]?"border-red-300 bg-red-50":"border-gray-300"}`,placeholder:"United States",disabled:m}),u["address.country"]&&r.jsx("p",{className:"mt-1 text-sm text-red-600",children:u["address.country"]})]})]})]})]}),r.jsxs("div",{className:"flex flex-col sm:flex-row justify-end gap-3 mt-8 pt-6 border-t border-gray-200",children:[r.jsx("button",{type:"button",onClick:a,className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium",disabled:m,children:"Cancel"}),r.jsx("button",{type:"submit",className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium disabled:opacity-50 disabled:cursor-not-allowed flex items-center",disabled:m,children:m?r.jsxs(r.Fragment,{children:[r.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Adding Customer..."]}):"Add Customer"})]})]})]})}):null}const Vx="http://localhost:8000/api/v1";class Qx{constructor(){this.baseURL=Vx}getAuthToken(){return localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}async makeRequest(a,o={}){const i=`${this.baseURL}${a}`,m={headers:{"Content-Type":"application/json",Authorization:`Bearer ${this.getAuthToken()}`},...o};try{const f=await fetch(i,m);if(!f.ok){const p=await f.json().catch(()=>({}));throw new Error(p.message||p.error||`HTTP error! status: ${f.status}`)}return await f.json()}catch(f){throw console.error(`API request failed for ${a}:`,f),f}}async getCustomers(a={}){const o=new URLSearchParams;a.page&&o.append("page",a.page),a.limit&&o.append("limit",a.limit),a.search&&o.append("search",a.search),a.status&&o.append("status",a.status),a.sortBy&&o.append("sort_by",a.sortBy),a.sortOrder&&o.append("sort_order",a.sortOrder);const i=o.toString(),u=`/business-customers/me/${i?`?${i}`:""}`;return await this.makeRequest(u)}async getCustomer(a){return await this.makeRequest(`/business-customers/me/${a}/`)}async createCustomer(a){return await this.makeRequest("/business-customers/me/",{method:"POST",body:JSON.stringify(a)})}async updateCustomer(a,o){return await this.makeRequest(`/business-customers/me/${a}/`,{method:"PUT",body:JSON.stringify(o)})}async deleteCustomer(a){return await this.makeRequest(`/business-customers/me/${a}/`,{method:"DELETE"})}async getCustomerStats(){return await this.makeRequest("/business-customers/me/stats/")}}const mi=new Qx,{getCustomers:kv,getCustomer:Sv,createCustomer:Cv,updateCustomer:Ev,deleteCustomer:Pv,getCustomerStats:Dv}=mi;function Yx(){const[s,a]=b.useState(!1),[o,i]=b.useState([]),[u,m]=b.useState(!0),[f,p]=b.useState(null);b.useEffect(()=>{(async()=>{try{m(!0),p(null);const re=await mi.getCustomers();let de=[];Array.isArray(re)?de=re:re.customers&&Array.isArray(re.customers)?de=re.customers:re.results&&Array.isArray(re.results)?de=re.results:re.data&&Array.isArray(re.data)&&(de=re.data),console.log("📊 Fetched customers from backend:",de.length);const ie=de.map(J=>{var me;const D=J.user||((me=J.customer_profile)==null?void 0:me.user)||{};let q=J.name||D.full_name;if(!q){const Y=J.first_name||D.first_name||D.firstname||"",ee=J.last_name||D.last_name||D.lastname||"";q=`${Y} ${ee}`.trim()}return{...J,id:J.id||J.pk||J.uuid||D.id,name:q,email:J.email||J.email_address||J.primary_email||D.email||"",phone:J.phone||J.phone_number||J.mobile_phone||J.mobile||D.phone_number||"",avatar:J.avatar||J.profile_pic||J.profile_picture||"https://ui-avatars.com/api/?name="+encodeURIComponent(q),status:J.status||J.state||"active",joinDate:J.join_date||J.joinDate||J.created_at||J.createdAt||D.created_at,lastOrder:J.last_order||J.lastOrder,totalOrders:J.total_orders||J.totalOrders||0,address:J.address||{street:J.street||"",postalCode:J.postal_code||J.zip||"",city:J.city||"",country:J.country||""}}});i(ie)}catch(re){console.error("❌ Failed to fetch customers:",re),p(re.message),console.log("📝 Using fallback mock data"),i([{id:1,name:"John Doe",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/40",status:"active",gender:"male",joinDate:"2023-01-15",lastOrder:"2024-05-20",totalOrders:15,address:{street:"123 Main Street",postalCode:"10001",city:"New York",country:"USA"}},{id:2,name:"Jane Smith",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/40",status:"active",gender:"female",joinDate:"2023-03-22",lastOrder:"2024-06-01",totalOrders:8,address:{street:"456 Oak Avenue",postalCode:"90210",city:"Los Angeles",country:"USA"}},{id:3,name:"Bob Johnson",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/40",status:"inactive",gender:"male",joinDate:"2022-11-10",lastOrder:"2024-01-15",totalOrders:3,address:{street:"789 Pine Road",postalCode:"75001",city:"Dallas",country:"USA"}},{id:4,name:"Alice Brown",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/40",status:"active",gender:"female",joinDate:"2023-08-05",lastOrder:"2024-06-05",totalOrders:22,address:{street:"321 Beach Boulevard",postalCode:"33101",city:"Miami",country:"USA"}},{id:5,name:"Charlie Wilson",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/40",status:"pending",gender:"male",joinDate:"2024-01-20",lastOrder:null,totalOrders:0,address:{street:"654 Capitol Hill",postalCode:"98101",city:"Seattle",country:"USA"}},{id:6,name:"Diana Martinez",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/40",status:"active",gender:"female",joinDate:"2023-05-12",lastOrder:"2024-05-28",totalOrders:11,address:{street:"987 Sunset Strip",postalCode:"90028",city:"Hollywood",country:"USA"}},{id:7,name:"Michael Chen",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/40",status:"active",gender:"male",joinDate:"2023-02-14",lastOrder:"2024-06-10",totalOrders:18,address:{street:"147 Chinatown Street",postalCode:"94108",city:"San Francisco",country:"USA"}},{id:8,name:"Sarah Davis",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/40",status:"inactive",gender:"female",joinDate:"2022-09-30",lastOrder:"2024-02-15",totalOrders:5,address:{street:"258 Music Row",postalCode:"37203",city:"Nashville",country:"USA"}},{id:9,name:"James Rodriguez",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/40",status:"active",gender:"male",joinDate:"2023-07-18",lastOrder:"2024-06-08",totalOrders:13,address:{street:"369 River Walk",postalCode:"78205",city:"San Antonio",country:"USA"}},{id:10,name:"Emma Thompson",email:"<EMAIL>",phone:"+44 20 7946 0958",avatar:"https://via.placeholder.com/40",status:"active",gender:"female",joinDate:"2023-04-03",lastOrder:"2024-05-25",totalOrders:9,address:{street:"42 Baker Street",postalCode:"NW1 6XE",city:"London",country:"UK"}},{id:11,name:"Oliver Garcia",email:"<EMAIL>",phone:"+34 91 123 4567",avatar:"https://via.placeholder.com/40",status:"pending",gender:"male",joinDate:"2024-02-28",lastOrder:null,totalOrders:0,address:{street:"Calle Gran Via 15",postalCode:"28013",city:"Madrid",country:"Spain"}},{id:12,name:"Sophie Dubois",email:"<EMAIL>",phone:"+33 1 42 34 56 78",avatar:"https://via.placeholder.com/40",status:"active",gender:"female",joinDate:"2023-06-11",lastOrder:"2024-06-02",totalOrders:16,address:{street:"125 Champs-Élysées",postalCode:"75008",city:"Paris",country:"France"}},{id:13,name:"Lucas Mueller",email:"<EMAIL>",phone:"+49 30 12345678",avatar:"https://via.placeholder.com/40",status:"active",gender:"male",joinDate:"2023-01-28",lastOrder:"2024-05-30",totalOrders:12,address:{street:"Unter den Linden 77",postalCode:"10117",city:"Berlin",country:"Germany"}},{id:14,name:"Isabella Rossi",email:"<EMAIL>",phone:"+39 06 1234 5678",avatar:"https://via.placeholder.com/40",status:"inactive",gender:"female",joinDate:"2022-12-05",lastOrder:"2024-03-10",totalOrders:7,address:{street:"Via del Corso 234",postalCode:"00187",city:"Rome",country:"Italy"}},{id:15,name:"Hiroshi Tanaka",email:"<EMAIL>",phone:"+81 3 1234 5678",avatar:"https://via.placeholder.com/40",status:"active",gender:"male",joinDate:"2023-03-15",lastOrder:"2024-06-07",totalOrders:20,address:{street:"1-1-1 Shibuya",postalCode:"150-0002",city:"Tokyo",country:"Japan"}},{id:16,name:"Priya Sharma",email:"<EMAIL>",phone:"+91 11 2345 6789",avatar:"https://via.placeholder.com/40",status:"active",gender:"female",joinDate:"2023-05-20",lastOrder:"2024-06-03",totalOrders:14,address:{street:"Connaught Place 45",postalCode:"110001",city:"New Delhi",country:"India"}},{id:17,name:"Alex Kim",email:"<EMAIL>",phone:"+82 2 1234 5678",avatar:"https://via.placeholder.com/40",status:"pending",gender:"other",joinDate:"2024-03-10",lastOrder:null,totalOrders:0,address:{street:"Gangnam-gu 123",postalCode:"06028",city:"Seoul",country:"South Korea"}},{id:18,name:"Maria Silva",email:"<EMAIL>",phone:"+55 11 9876 5432",avatar:"https://via.placeholder.com/40",status:"active",gender:"female",joinDate:"2023-04-25",lastOrder:"2024-05-22",totalOrders:10,address:{street:"Avenida Paulista 1578",postalCode:"01310-200",city:"São Paulo",country:"Brazil"}},{id:19,name:"Ahmed Hassan",email:"<EMAIL>",phone:"+971 4 123 4567",avatar:"https://via.placeholder.com/40",status:"active",gender:"male",joinDate:"2023-02-08",lastOrder:"2024-06-09",totalOrders:17,address:{street:"Sheikh Zayed Road 789",postalCode:"12345",city:"Dubai",country:"UAE"}},{id:20,name:"Rachel Green",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/40",status:"inactive",gender:"female",joinDate:"2022-08-14",lastOrder:"2024-01-30",totalOrders:4,address:{street:"90 Bedford Street",postalCode:"10014",city:"New York",country:"USA"}},{id:21,name:"Connor O'Brien",email:"<EMAIL>",phone:"+353 1 234 5678",avatar:"https://via.placeholder.com/40",status:"active",gender:"male",joinDate:"2023-09-12",lastOrder:"2024-06-04",totalOrders:6,address:{street:"Temple Bar 12",postalCode:"D02 YD30",city:"Dublin",country:"Ireland"}},{id:22,name:"Zara Al-Rashid",email:"<EMAIL>",phone:"+966 11 234 5678",avatar:"https://via.placeholder.com/40",status:"active",gender:"female",joinDate:"2023-07-30",lastOrder:"2024-05-15",totalOrders:8,address:{street:"King Fahd Road 456",postalCode:"11564",city:"Riyadh",country:"Saudi Arabia"}},{id:23,name:"Nicolas Petrov",email:"<EMAIL>",phone:"****** 123 4567",avatar:"https://via.placeholder.com/40",status:"pending",gender:"male",joinDate:"2024-04-15",lastOrder:null,totalOrders:0,address:{street:"Red Square 1",postalCode:"109012",city:"Moscow",country:"Russia"}},{id:24,name:"Amelia Watson",email:"<EMAIL>",phone:"+61 2 9876 5432",avatar:"https://via.placeholder.com/40",status:"active",gender:"female",joinDate:"2023-10-22",lastOrder:"2024-06-06",totalOrders:19,address:{street:"88 Harbour Bridge Road",postalCode:"2000",city:"Sydney",country:"Australia"}},{id:25,name:"Erik Andersson",email:"<EMAIL>",phone:"+46 8 123 456 78",avatar:"https://via.placeholder.com/40",status:"active",gender:"male",joinDate:"2023-01-05",lastOrder:"2024-05-18",totalOrders:21,address:{street:"Gamla Stan 15",postalCode:"111 29",city:"Stockholm",country:"Sweden"}}])}finally{m(!1)}})()},[]);const{filteredCustomers:y,searchTerm:x,setSearchTerm:g,sortBy:v,sortOrder:j,handleSort:P,currentPage:k,totalPages:E,totalItems:S,startItem:_,endItem:B,itemsPerPage:H,goToPage:F,goToNextPage:I,goToPrevPage:z}=_x(o),U=Z=>{i(re=>[Z,...re]),a(!1)},oe=()=>{a(!0)},fe=()=>{a(!1)};return r.jsxs("div",{className:"container py-10",children:[r.jsxs("header",{className:"mb-8",children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Customer Management"}),r.jsx("p",{className:"text-gray-600 mt-2",children:"Manage and organize your customer database"}),u&&r.jsx("div",{className:"mt-4 p-4 bg-blue-50 border border-blue-200 rounded-lg",children:r.jsxs("div",{className:"flex items-center",children:[r.jsx("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2"}),r.jsx("span",{className:"text-blue-800",children:"Loading customers from backend..."})]})}),f&&r.jsx("div",{className:"mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:r.jsxs("div",{className:"flex items-center",children:[r.jsx("svg",{className:"w-4 h-4 text-yellow-600 mr-2",fill:"currentColor",viewBox:"0 0 20 20",children:r.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}),r.jsxs("span",{className:"text-yellow-800",children:["Backend connection failed: ",f,". Using fallback data."]})]})})]}),r.jsx("div",{className:"bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6",children:r.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4",children:[r.jsx("div",{className:"flex-1",children:r.jsx(Fx,{searchTerm:x,setSearchTerm:g})}),r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsxs("button",{className:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors",children:[r.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Export"]}),r.jsxs("button",{onClick:oe,className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg text-sm font-medium hover:bg-blue-700 transition-colors shadow-sm",children:[r.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),"Add Customer"]})]})]})}),r.jsx($x,{customers:y,onSort:P,sortBy:v,sortOrder:j}),r.jsx(Ux,{currentPage:k,totalPages:E,totalItems:S,startItem:_,endItem:B,itemsPerPage:H,onPageChange:F,onNextPage:I,onPrevPage:z}),r.jsx(Hx,{isOpen:s,onClose:fe,onAddCustomer:U})]})}function Jx(){const[s]=b.useState([{id:1,name:"John Doe",email:"<EMAIL>",phone:"************",avatar:"https://via.placeholder.com/150"},{id:2,name:"Jane Smith",email:"<EMAIL>",phone:"************",avatar:"https://via.placeholder.com/150"}]);return r.jsxs("div",{className:"container py-10",children:[r.jsx("h1",{className:"text-3xl mb-6",children:"Customer List"}),r.jsx("div",{className:"bg-white rounded-lg shadow-md overflow-hidden",children:r.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:r.jsxs("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Customer"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Email"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase",children:"Phone"}),r.jsx("th",{className:"px-6 py-3"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(a=>r.jsxs("tr",{children:[r.jsxs("td",{className:"px-6 py-4 whitespace-nowrap flex items-center",children:[r.jsx("img",{src:a.avatar,alt:a.name,className:"w-10 h-10 rounded-full mr-3"}),a.name]}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.email}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap",children:a.phone}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-primary-600",children:r.jsx(ot,{to:`/customers/${a.id}`,children:"View"})})]},a.id))})]})})]})}const qx=(s,a,o)=>{if(!s)return{isValid:!1,error:"No file selected"};if(s.size>o)return{isValid:!1,error:`File size exceeds ${Math.round(o/1048576)}MB limit`};const i="."+s.name.split(".").pop().toLowerCase();return a.includes(i)?{isValid:!0}:{isValid:!1,error:`Unsupported file format. Accepted formats: ${a.join(", ")}`}},Kx="http://localhost:8000/api/v1";class Xx{constructor(){this.baseURL=Kx}getAuthToken(){return localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}async makeFileRequest(a,o,i={}){const u=`${this.baseURL}${a}`,f={method:"POST",headers:{Authorization:`Bearer ${this.getAuthToken()}`},body:o,...i};try{const p=await fetch(u,f);if(!p.ok){const y=await p.json().catch(()=>({}));throw new Error(y.message||y.error||`HTTP error! status: ${p.status}`)}return await p.json()}catch(p){throw console.error(`File upload failed for ${a}:`,p),p}}async uploadImportFile(a,o={}){const i=new FormData;return i.append("file",a),i.append("file_type","customer_import"),o.description&&i.append("description",o.description),o.skipDuplicates!==void 0&&i.append("skip_duplicates",o.skipDuplicates),o.updateExisting!==void 0&&i.append("update_existing",o.updateExisting),await this.makeFileRequest("/files/upload/",i)}async getFileStatus(a){const o=`${this.baseURL}/files/${a}/status/`,i=this.getAuthToken(),u=await fetch(o,{headers:{Authorization:`Bearer ${i}`}});if(!u.ok)throw new Error(`Failed to get file status: ${u.status}`);return await u.json()}async startFileProcessing(a,o={}){const i=`${this.baseURL}/files/${a}/process/`,u=this.getAuthToken(),m=await fetch(i,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${u}`},body:JSON.stringify(o)});if(!m.ok)throw new Error(`Failed to start processing: ${m.status}`);return await m.json()}async getProcessingResults(a){const o=`${this.baseURL}/files/${a}/results/`,i=this.getAuthToken(),u=await fetch(o,{headers:{Authorization:`Bearer ${i}`}});if(!u.ok)throw new Error(`Failed to get results: ${u.status}`);return await u.json()}async deleteFile(a){const o=`${this.baseURL}/files/${a}/`,i=this.getAuthToken(),u=await fetch(o,{method:"DELETE",headers:{Authorization:`Bearer ${i}`}});if(!u.ok)throw new Error(`Failed to delete file: ${u.status}`);return u.status===204}}const us=new Xx,{uploadImportFile:Tv,getFileStatus:Lv,startFileProcessing:Ov,getProcessingResults:Mv,deleteFile:Rv}=us;function Gx({onFileUpload:s,acceptedFormats:a=[".xlsx",".xls",".csv"],maxSize:o=10*1024*1024}){const[i,u]=b.useState(!1),[m,f]=b.useState(!1),[p,y]=b.useState(0),[x,g]=b.useState(null),v=b.useRef(null),j=b.useCallback(async F=>{if(!F||F.length===0)return;const I=F[0];g(null),f(!0),y(0);try{const z=qx(I,a,o);if(!z.isValid)throw new Error(z.error);console.log("📤 Uploading file to backend:",I.name);const U=await us.uploadImportFile(I,{description:`Customer import file: ${I.name}`,skipDuplicates:!0,updateExisting:!1});console.log("✅ File uploaded successfully:",U),s(I,{fileId:U.file_id||U.id,fileName:U.file_name||I.name,filePath:U.file_path,fileSize:U.file_size||I.size,uploadedAt:U.uploaded_at||new Date().toISOString(),status:U.status||"uploaded",isUploaded:!0})}catch(z){console.error("❌ File upload failed:",z),g(z.message)}finally{f(!1),y(0)}},[s,a,o]),P=b.useCallback(F=>{F.preventDefault(),u(!1);const I=Array.from(F.dataTransfer.files);j(I)},[j]),k=b.useCallback(F=>{F.preventDefault()},[]),E=b.useCallback(F=>{F.preventDefault(),u(!0)},[]),S=b.useCallback(F=>{F.preventDefault(),!F.currentTarget.contains(F.relatedTarget)&&u(!1)},[]),_=b.useCallback(F=>{const I=Array.from(F.target.files);j(I),F.target.value=""},[j]),B=()=>{var F;(F=v.current)==null||F.click()},H=F=>{if(F===0)return"0 Bytes";const I=1024,z=["Bytes","KB","MB","GB"],U=Math.floor(Math.log(F)/Math.log(I));return parseFloat((F/Math.pow(I,U)).toFixed(2))+" "+z[U]};return r.jsxs("div",{className:"space-y-6",children:[r.jsxs("div",{onDrop:P,onDragOver:k,onDragEnter:E,onDragLeave:S,className:`relative border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 ${i?"border-blue-500 bg-blue-50 scale-[1.02]":x?"border-red-300 bg-red-50":"border-gray-300 bg-gray-50 hover:border-gray-400 hover:bg-gray-100"} ${m?"pointer-events-none opacity-75":"cursor-pointer"}`,onClick:B,children:[m?r.jsxs("div",{className:"flex flex-col items-center",children:[r.jsx("div",{className:"w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mb-4"}),r.jsx("p",{className:"text-lg font-medium text-gray-900 mb-2",children:"Uploading File..."}),r.jsx("p",{className:"text-sm text-gray-600",children:"Sending file to server for processing"}),p>0&&r.jsxs("div",{className:"w-full max-w-xs mt-3",children:[r.jsx("div",{className:"bg-gray-200 rounded-full h-2",children:r.jsx("div",{className:"bg-blue-600 h-2 rounded-full transition-all duration-300",style:{width:`${p}%`}})}),r.jsxs("p",{className:"text-xs text-gray-500 mt-1",children:[p,"% uploaded"]})]})]}):r.jsxs("div",{className:"flex flex-col items-center",children:[r.jsx("div",{className:`w-16 h-16 rounded-full flex items-center justify-center mb-4 ${i?"bg-blue-100":x?"bg-red-100":"bg-gray-100"}`,children:i?r.jsx("svg",{className:"w-8 h-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10"})}):x?r.jsx("svg",{className:"w-8 h-8 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}):r.jsx("svg",{className:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})})}),r.jsx("h3",{className:`text-lg font-medium mb-2 ${i?"text-blue-900":x?"text-red-900":"text-gray-900"}`,children:i?"Drop your file here":x?"Upload Failed":"Upload Customer Data"}),r.jsx("p",{className:`text-sm mb-4 ${i?"text-blue-700":x?"text-red-700":"text-gray-600"}`,children:i?"Release to upload your file":x||"Drag and drop your file here, or click to browse"}),!x&&r.jsxs("button",{type:"button",className:"inline-flex items-center px-6 py-3 border border-gray-300 shadow-sm text-sm font-medium rounded-lg text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 transition-colors",children:[r.jsx("svg",{className:"w-5 h-5 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12"})}),"Choose File"]})]}),r.jsx("input",{ref:v,type:"file",accept:a.join(","),onChange:_,className:"hidden"})]}),r.jsx("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:r.jsxs("div",{className:"flex items-start",children:[r.jsx("svg",{className:"w-5 h-5 text-blue-600 mt-0.5 mr-3 flex-shrink-0",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),r.jsxs("div",{className:"flex-1",children:[r.jsx("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"Supported File Formats"}),r.jsxs("div",{className:"text-sm text-blue-800 space-y-1",children:[r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx("span",{children:"Excel Files (.xlsx, .xls)"}),r.jsx("span",{className:"text-xs bg-blue-100 px-2 py-1 rounded",children:"Recommended"})]}),r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx("span",{children:"CSV Files (.csv)"}),r.jsx("span",{className:"text-xs bg-blue-100 px-2 py-1 rounded",children:"Supported"})]}),r.jsxs("div",{className:"text-xs text-blue-600 mt-2",children:["Maximum file size: ",H(o)]})]})]})]})}),r.jsxs("div",{className:"bg-gray-50 border border-gray-200 rounded-lg p-4",children:[r.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"File Processing"}),r.jsxs("div",{className:"text-sm text-gray-600 space-y-2",children:[r.jsx("p",{children:"Files are uploaded to the server and processed there. Your file should contain customer information with columns like:"}),r.jsx("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2 mt-3",children:["Name","Email","Phone","Address","Birthday","Gender","City","Postal Code"].map(F=>r.jsx("div",{className:"bg-white px-3 py-1 rounded border text-xs font-medium text-center",children:F},F))}),r.jsxs("p",{className:"text-xs text-gray-500 mt-3",children:["✓ Files are securely stored on the server",r.jsx("br",{}),"✓ Processing happens server-side for better performance",r.jsx("br",{}),"✓ Large files are supported"]})]})]})]})}function Zx({progress:s,status:a,recordCount:o,errors:i=[]}){const[u,m]=b.useState(0),[f,p]=b.useState(0),[y]=b.useState(Date.now());b.useEffect(()=>{if(a==="processing"){const k=setInterval(()=>{p(Math.floor((Date.now()-y)/1e3))},1e3);return()=>clearInterval(k)}},[a,y]),b.useEffect(()=>{m(Math.floor(s/100*o))},[s,o]);const x=k=>{const E=Math.floor(k/60),S=k%60;return E>0?`${E}m ${S}s`:`${S}s`},g=()=>{if(s===0||f===0)return null;const k=f/s*100;return Math.max(0,Math.floor(k-f))},v=()=>{switch(a){case"processing":return r.jsx("div",{className:"w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"});case"success":return r.jsx("div",{className:"w-8 h-8 bg-green-500 rounded-full flex items-center justify-center",children:r.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})});case"error":return r.jsx("div",{className:"w-8 h-8 bg-red-500 rounded-full flex items-center justify-center",children:r.jsx("svg",{className:"w-5 h-5 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})});default:return r.jsx("div",{className:"w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center",children:r.jsx("svg",{className:"w-5 h-5 text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})})}},j=()=>{switch(a){case"processing":return`Processing record ${u} of ${o}...`;case"success":return`Successfully imported ${o} customers`;case"error":return"Import failed due to errors";default:return"Preparing to import..."}},P=()=>{switch(a){case"processing":return"text-blue-600";case"success":return"text-green-600";case"error":return"text-red-600";default:return"text-gray-600"}};return r.jsxs("div",{className:"space-y-6",children:[r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"flex items-center justify-center mb-4",children:v()}),r.jsx("h3",{className:`text-lg font-medium mb-2 ${P()}`,children:j()}),r.jsxs("div",{className:"max-w-md mx-auto",children:[r.jsx("div",{className:"bg-gray-200 rounded-full h-3 mb-4",children:r.jsx("div",{className:`h-3 rounded-full transition-all duration-300 ${a==="success"?"bg-green-500":a==="error"?"bg-red-500":"bg-blue-500"}`,style:{width:`${s}%`}})}),r.jsxs("div",{className:"flex justify-between text-sm text-gray-600 mb-2",children:[r.jsxs("span",{children:[s,"% Complete"]}),r.jsxs("span",{children:[u," / ",o," records"]})]})]})]}),r.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[r.jsxs("div",{className:"bg-gray-50 rounded-lg p-4 text-center",children:[r.jsx("div",{className:"text-lg font-bold text-gray-900",children:o}),r.jsx("div",{className:"text-sm text-gray-600",children:"Total Records"})]}),r.jsxs("div",{className:"bg-blue-50 rounded-lg p-4 text-center",children:[r.jsx("div",{className:"text-lg font-bold text-blue-600",children:u}),r.jsx("div",{className:"text-sm text-blue-800",children:"Processed"})]}),r.jsxs("div",{className:"bg-green-50 rounded-lg p-4 text-center",children:[r.jsx("div",{className:"text-lg font-bold text-green-600",children:a==="success"?o:Math.max(0,u-i.length)}),r.jsx("div",{className:"text-sm text-green-800",children:"Successful"})]}),r.jsxs("div",{className:"bg-red-50 rounded-lg p-4 text-center",children:[r.jsx("div",{className:"text-lg font-bold text-red-600",children:i.length}),r.jsx("div",{className:"text-sm text-red-800",children:"Errors"})]})]}),r.jsx("div",{className:"bg-gray-50 rounded-lg p-4",children:r.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-4 text-sm",children:[r.jsxs("div",{children:[r.jsx("span",{className:"text-gray-500",children:"Time Elapsed:"}),r.jsx("div",{className:"font-medium",children:x(f)})]}),a==="processing"&&g()!==null&&r.jsxs("div",{children:[r.jsx("span",{className:"text-gray-500",children:"Estimated Remaining:"}),r.jsx("div",{className:"font-medium",children:x(g())})]}),r.jsxs("div",{children:[r.jsx("span",{className:"text-gray-500",children:"Status:"}),r.jsx("div",{className:`font-medium capitalize ${P()}`,children:a})]})]})}),r.jsxs("div",{className:"space-y-3",children:[r.jsx("h4",{className:"text-sm font-medium text-gray-900",children:"Import Steps"}),r.jsxs("div",{className:"space-y-2",children:[r.jsxs("div",{className:`flex items-center p-3 rounded-lg ${s>=10?"bg-green-50 border border-green-200":"bg-gray-50 border border-gray-200"}`,children:[r.jsx("div",{className:`w-5 h-5 rounded-full mr-3 flex items-center justify-center ${s>=10?"bg-green-500":"bg-gray-300"}`,children:s>=10&&r.jsx("svg",{className:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M5 13l4 4L19 7"})})}),r.jsx("span",{className:`text-sm ${s>=10?"text-green-800":"text-gray-600"}`,children:"Validating data format and structure"})]}),r.jsxs("div",{className:`flex items-center p-3 rounded-lg ${s>=30?"bg-green-50 border border-green-200":"bg-gray-50 border border-gray-200"}`,children:[r.jsx("div",{className:`w-5 h-5 rounded-full mr-3 flex items-center justify-center ${s>=30?"bg-green-500":"bg-gray-300"}`,children:s>=30&&r.jsx("svg",{className:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M5 13l4 4L19 7"})})}),r.jsx("span",{className:`text-sm ${s>=30?"text-green-800":"text-gray-600"}`,children:"Checking for duplicates and conflicts"})]}),r.jsxs("div",{className:`flex items-center p-3 rounded-lg ${s>=60?"bg-green-50 border border-green-200":"bg-gray-50 border border-gray-200"}`,children:[r.jsx("div",{className:`w-5 h-5 rounded-full mr-3 flex items-center justify-center ${s>=60?"bg-green-500":"bg-gray-300"}`,children:s>=60&&r.jsx("svg",{className:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M5 13l4 4L19 7"})})}),r.jsx("span",{className:`text-sm ${s>=60?"text-green-800":"text-gray-600"}`,children:"Creating customer records"})]}),r.jsxs("div",{className:`flex items-center p-3 rounded-lg ${s>=90?"bg-green-50 border border-green-200":"bg-gray-50 border border-gray-200"}`,children:[r.jsx("div",{className:`w-5 h-5 rounded-full mr-3 flex items-center justify-center ${s>=90?"bg-green-500":"bg-gray-300"}`,children:s>=90&&r.jsx("svg",{className:"w-3 h-3 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:3,d:"M5 13l4 4L19 7"})})}),r.jsx("span",{className:`text-sm ${s>=90?"text-green-800":"text-gray-600"}`,children:"Finalizing import and updating indexes"})]})]})]}),i.length>0&&r.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[r.jsxs("h4",{className:"text-sm font-medium text-red-900 mb-3",children:["Import Errors (",i.length,")"]}),r.jsxs("div",{className:"space-y-2 max-h-40 overflow-y-auto",children:[i.slice(0,5).map((k,E)=>r.jsxs("div",{className:"text-sm text-red-700",children:["• ",k]},E)),i.length>5&&r.jsxs("div",{className:"text-sm text-red-600 font-medium",children:["... and ",i.length-5," more errors"]})]})]}),a==="processing"&&r.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[r.jsx("h4",{className:"text-sm font-medium text-blue-900 mb-3",children:"Current Activity"}),r.jsxs("div",{className:"space-y-1 text-sm text-blue-800",children:[s<10&&r.jsx("div",{className:"animate-pulse",children:"• Validating file structure..."}),s>=10&&s<30&&r.jsx("div",{className:"animate-pulse",children:"• Checking for duplicate records..."}),s>=30&&s<60&&r.jsx("div",{className:"animate-pulse",children:"• Processing customer data..."}),s>=60&&s<90&&r.jsx("div",{className:"animate-pulse",children:"• Creating database records..."}),s>=90&&r.jsx("div",{className:"animate-pulse",children:"• Finalizing import process..."})]})]})]})}function eg(){const[s,a]=b.useState("upload"),[o,i]=b.useState(null),[u,m]=b.useState(0),[f,p]=b.useState("idle"),[y,x]=b.useState([]),[g,v]=b.useState([]),[j,P]=b.useState(null),[k,E]=b.useState(null),[S,_]=b.useState(null),B=()=>{a("upload"),i(null),_(null),m(0),p("idle"),x([]),v([]),P(null),E(null)},H=(Z,re)=>{i(Z),_(re),a("processing"),F(re.fileId)},F=async Z=>{p("processing"),m(0),x([]);try{console.log("🚀 Starting file processing for fileId:",Z);const re=await us.startFileProcessing(Z,{skipDuplicates:!0,updateExisting:!1,validateEmails:!0});console.log("✅ Processing started:",re);const de=re.job_id||re.id;P(de),I(Z,de)}catch(re){console.error("❌ Failed to start processing:",re),p("error"),x([re.message||"Failed to start file processing"])}},I=async(Z,re)=>{const de=setInterval(async()=>{try{const ie=await us.getFileStatus(Z);if(m(ie.progress||0),ie.status==="completed"||ie.status==="processed"){clearInterval(de),p("success");const J=await us.getProcessingResults(Z);console.log("🔍 Processing results:",J),E(J);let D=0,q=[];J.successful?(q=Array.isArray(J.successful)?J.successful:[],D=q.length):J.imported?(q=Array.isArray(J.imported)?J.imported:[],D=q.length):J.success_count||J.successCount?(D=J.success_count||J.successCount,q=Array(D).fill({})):J.total_imported||J.totalImported?(D=J.total_imported||J.totalImported,q=Array(D).fill({})):J.imported_count||J.importedCount?(D=J.imported_count||J.importedCount,q=Array(D).fill({})):typeof J.count=="number"&&(D=J.count,q=Array(D).fill({})),console.log("✅ Extracted import count:",D),console.log("✅ Successful records:",q),v(q),x(J.errors||J.failed||[]),a("complete")}else ie.status==="failed"||ie.status==="error"?(clearInterval(de),p("error"),x(ie.errors||["File processing failed"])):ie.status==="cancelled"&&(clearInterval(de),p("error"),x(["File processing was cancelled"]));ie.processed_rows&&ie.total_rows&&m(Math.round(ie.processed_rows/ie.total_rows*100))}catch(ie){console.error("Failed to get file status:",ie),clearInterval(de),p("error"),x(["Failed to get processing status"])}},2e3);setTimeout(()=>{clearInterval(de),f==="processing"&&(p("error"),x(["Processing timeout - please check your backend"]))},6e5)},z=(Z,re,de)=>de?r.jsx("svg",{className:"w-5 h-5 text-green-600",fill:"currentColor",viewBox:"0 0 20 20",children:r.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}):re?r.jsx("div",{className:"w-5 h-5 bg-blue-600 rounded-full flex items-center justify-center",children:r.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})}):r.jsx("div",{className:"w-5 h-5 bg-gray-300 rounded-full"}),U=[{id:"upload",name:"Upload File",description:"Select or drag file"},{id:"processing",name:"Processing",description:"Backend processing"},{id:"complete",name:"Complete",description:"Review results"}],oe=Z=>U.findIndex(re=>re.id===Z),fe=oe(s);return r.jsxs("div",{className:"container py-10",children:[r.jsxs("div",{className:"flex items-center justify-between mb-8",children:[r.jsxs("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"Import Management"}),r.jsx("p",{className:"text-gray-600",children:"Import customer data from Excel files and other formats"})]}),s!=="upload"&&r.jsxs("button",{onClick:B,className:"inline-flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium",children:[r.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 4v16m8-8H4"})}),"New Import"]})]}),r.jsx("div",{className:"bg-white rounded-xl shadow-sm p-6 mb-8",children:r.jsx("div",{className:"flex items-center justify-between",children:U.map((Z,re)=>{const de=Z.id===s,ie=oe(Z.id)<fe,J=re===U.length-1;return r.jsxs("div",{className:"flex items-center flex-1",children:[r.jsxs("div",{className:"flex items-center",children:[r.jsx("div",{className:`flex items-center justify-center w-10 h-10 rounded-full border-2 ${ie?"border-green-500 bg-green-50":de?"border-blue-500 bg-blue-50":"border-gray-300 bg-white"}`,children:z(Z.id,de,ie)}),r.jsxs("div",{className:"ml-3 flex-1 min-w-0",children:[r.jsx("div",{className:`text-sm font-medium ${ie||de?"text-gray-900":"text-gray-500"}`,children:Z.name}),r.jsx("div",{className:"text-xs text-gray-500",children:Z.description})]})]}),!J&&r.jsx("div",{className:`flex-1 h-0.5 mx-4 ${ie?"bg-green-500":"bg-gray-300"}`})]},Z.id)})})}),r.jsxs("div",{className:"space-y-8",children:[s==="upload"&&r.jsxs("div",{className:"bg-white rounded-xl shadow-sm",children:[r.jsxs("div",{className:"p-6 border-b border-gray-200",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Upload Customer Data"}),r.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Upload Excel files (.xlsx, .xls) or CSV files containing customer information"})]}),r.jsx("div",{className:"p-6",children:r.jsx(Gx,{onFileUpload:H})})]}),s==="complete"&&r.jsxs("div",{className:"bg-white rounded-xl shadow-sm",children:[r.jsxs("div",{className:"p-6 border-b border-gray-200",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Import Complete"}),r.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Customer data has been successfully imported"})]}),r.jsx("div",{className:"p-6",children:r.jsxs("div",{className:"text-center py-8",children:[r.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),r.jsxs("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:["Successfully imported ",g.length," customers"]}),r.jsx("p",{className:"text-gray-600 mb-6",children:g.length>0?"All customer records have been processed and added to your database.":"Processing completed. Check the backend logs if no customers were imported."}),k&&r.jsxs("div",{className:"bg-gray-50 rounded-lg p-4 mb-6 text-left",children:[r.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-2",children:"Import Details:"}),r.jsx("div",{className:"text-sm text-gray-600 space-y-1",children:Object.entries(k).map(([Z,re])=>r.jsxs("div",{children:[r.jsxs("span",{className:"font-medium",children:[Z,":"]})," ",JSON.stringify(re)]},Z))})]}),y&&y.length>0&&r.jsxs("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 mb-6 text-left",children:[r.jsx("h4",{className:"text-sm font-medium text-red-900 mb-2",children:"Import Errors:"}),r.jsx("ul",{className:"text-sm text-red-800 list-disc list-inside space-y-1",children:y.map((Z,re)=>r.jsx("li",{children:typeof Z=="string"?Z:JSON.stringify(Z)},re))})]}),r.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 justify-center",children:[r.jsx("button",{onClick:B,className:"px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium",children:"Import More Data"}),r.jsx("button",{onClick:()=>window.location.href="/customers",className:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium",children:"View Customers"})]})]})})]}),s==="processing"&&S&&r.jsxs("div",{className:"bg-white rounded-xl shadow-sm",children:[r.jsx("div",{className:"p-6 border-b border-gray-200",children:r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("div",{children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Processing File"}),r.jsx("p",{className:"text-sm text-gray-600 mt-1",children:"Server is processing your uploaded file..."})]}),r.jsxs("div",{className:"text-sm text-gray-500",children:["File: ",r.jsx("span",{className:"font-medium",children:S.fileName})]})]})}),r.jsx("div",{className:"p-6",children:r.jsxs("div",{className:"space-y-4",children:[r.jsxs("div",{className:"bg-blue-50 border border-blue-200 rounded-lg p-4",children:[r.jsx("h4",{className:"text-sm font-medium text-blue-900 mb-2",children:"File Upload Complete"}),r.jsxs("div",{className:"text-sm text-blue-800 space-y-1",children:[r.jsxs("div",{children:["✅ File uploaded to: ",r.jsx("code",{className:"bg-blue-100 px-1 rounded text-xs",children:S.filePath})]}),r.jsxs("div",{children:["✅ File size: ",(S.fileSize/1024).toFixed(1)," KB"]}),r.jsxs("div",{children:["✅ Upload time: ",new Date(S.uploadedAt).toLocaleString()]})]})]}),r.jsx(Zx,{progress:u,status:f,recordCount:g.length,errors:y})]})})]})]})]})}function tg({customer:s}){const[a,o]=b.useState(""),[i,u]=b.useState(!1),[m,f]=b.useState(!1),p=()=>{u(!0)},y=()=>{a.trim()&&(f(!0),setTimeout(()=>{f(!1),u(!1)},2e3))},x=j=>{j.key==="Enter"&&y()},g=()=>{u(!1)},v=()=>{o(""),u(!1)};return r.jsx("div",{className:"bg-white rounded-xl shadow-sm p-8 mb-6",children:r.jsxs("div",{className:"flex flex-col sm:flex-row items-center sm:items-start gap-6",children:[r.jsxs("div",{className:"relative",children:[r.jsx("img",{src:s.avatar,alt:s.name,className:"w-24 h-24 rounded-full object-cover border-4 border-gray-100"}),r.jsx("div",{className:"absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white"})]}),r.jsxs("div",{className:"flex-1 text-center sm:text-left",children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:s.name}),r.jsx("div",{className:"mb-4",children:!i&&!a?r.jsxs("button",{onClick:p,className:"flex items-center text-xs text-gray-500 hover:text-gray-700 transition-colors group",children:[r.jsx("svg",{className:"w-3 h-3 mr-1 group-hover:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),"Add pronunciation"]}):!i&&a?r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsxs("div",{className:"flex items-center text-xs text-gray-600",children:[r.jsx("svg",{className:"w-3 h-3 mr-1 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"})}),r.jsx("span",{className:"font-medium",children:a})]}),r.jsx("button",{onClick:v,className:"text-xs text-gray-400 hover:text-red-500 transition-colors",title:"Clear pronunciation",children:"×"})]}):r.jsxs("div",{className:`transition-all duration-200 ${m?"ring-2 ring-green-500/20 border-green-500":"ring-2 ring-blue-500/20 border-blue-500"} border rounded-lg bg-white`,children:[r.jsxs("div",{className:"flex items-center px-3 py-2",children:[r.jsx("svg",{className:`w-4 h-4 mr-2 transition-colors ${m?"text-green-500":"text-blue-500"}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3"})}),r.jsx("input",{type:"text",value:a,onChange:j=>o(j.target.value),onKeyPress:x,placeholder:"e.g., JOHN DOH, Jane SMITH...",className:"flex-1 bg-transparent border-none outline-none text-sm text-gray-900 placeholder-gray-400",autoFocus:!0}),r.jsxs("div",{className:"flex items-center space-x-2 ml-2",children:[a&&r.jsx("button",{onClick:y,className:"text-xs text-gray-500 hover:text-gray-700 transition-colors",children:"save"}),r.jsx("button",{onClick:g,className:"text-xs text-gray-400 hover:text-gray-600 transition-colors",title:"Cancel",children:"×"})]})]}),r.jsx("div",{className:"px-3 pb-2",children:r.jsx("span",{className:"text-xs text-gray-500",children:m?"Pronunciation saved!":"Press Enter or click save • For employee reference"})})]})}),r.jsxs("div",{className:"space-y-1",children:[r.jsxs("p",{className:"text-gray-600 flex items-center justify-center sm:justify-start",children:[r.jsx("svg",{className:"w-4 h-4 mr-2 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),s.email]}),r.jsxs("p",{className:"text-gray-600 flex items-center justify-center sm:justify-start",children:[r.jsx("svg",{className:"w-4 h-4 mr-2 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})}),s.phone]})]})]})]})})}function rg({customer:s}){const[a,o]=b.useState(!1),[i,u]=b.useState(!1),[m,f]=b.useState(!1),p=async(k,E)=>{try{await navigator.clipboard.writeText(k),E==="email"?(o(!0),setTimeout(()=>o(!1),2e3)):(u(!0),setTimeout(()=>u(!1),2e3))}catch(S){console.error("Failed to copy: ",S)}},y=k=>{const E=k.replace(/\D/g,"");return E.length===10?`(${E.slice(0,3)}) ${E.slice(3,6)}-${E.slice(6)}`:E.length===11&&E[0]==="1"?`+1 (${E.slice(1,4)}) ${E.slice(4,7)}-${E.slice(7)}`:k},x=k=>k?k.charAt(0).toUpperCase()+k.slice(1):"Not specified",g=k=>k?`${k.street}, ${k.city}, ${k.postalCode}, ${k.country}`:"No address provided",v=k=>{const E=k.trim().split(" "),S=E[0]||"",_=E.slice(1).join(" ")||"";return{firstName:S,lastName:_}},{firstName:j,lastName:P}=v(s.name);return r.jsxs("div",{className:"bg-white rounded-xl shadow-sm p-8 mb-6",children:[r.jsxs("div",{className:"flex items-center justify-between mb-6",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Customer Information"}),r.jsx("span",{className:"text-sm text-gray-500",children:"Business View"})]}),r.jsxs("div",{className:"mb-8",children:[r.jsx("h3",{className:"text-sm font-medium text-gray-500 uppercase tracking-wide border-b border-gray-200 pb-2 mb-6",children:"Required Information"}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"First Name"}),r.jsxs("div",{className:"flex items-center p-3 border border-gray-200 rounded-lg bg-gray-50",children:[r.jsx("svg",{className:"w-5 h-5 text-gray-400 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),r.jsx("span",{className:"text-gray-900 flex-1",children:j})]})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Last Name"}),r.jsxs("div",{className:"flex items-center p-3 border border-gray-200 rounded-lg bg-gray-50",children:[r.jsx("svg",{className:"w-5 h-5 text-gray-400 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),r.jsx("span",{className:"text-gray-900 flex-1",children:P||"Not provided"})]})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email Address"}),r.jsxs("div",{className:"flex items-center p-3 border border-gray-200 rounded-lg bg-gray-50",children:[r.jsx("svg",{className:"w-5 h-5 text-gray-400 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"})}),r.jsx("span",{className:"text-gray-900 flex-1",children:s.email}),r.jsx("button",{onClick:()=>p(s.email,"email"),className:"ml-2 p-1 rounded hover:bg-gray-200 transition-colors",title:"Copy email",children:a?r.jsx("svg",{className:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}):r.jsx("svg",{className:"w-4 h-4 text-gray-400 hover:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})})})]})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Phone Number"}),r.jsxs("div",{className:"flex items-center p-3 border border-gray-200 rounded-lg bg-gray-50",children:[r.jsx("svg",{className:"w-5 h-5 text-gray-400 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"})}),r.jsx("span",{className:"text-gray-900 flex-1",children:y(s.phone)}),r.jsx("button",{onClick:()=>p(s.phone,"phone"),className:"ml-2 p-1 rounded hover:bg-gray-200 transition-colors",title:"Copy phone number",children:i?r.jsx("svg",{className:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}):r.jsx("svg",{className:"w-4 h-4 text-gray-400 hover:text-gray-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"})})})]})]})]})]}),r.jsxs("div",{children:[r.jsxs("button",{onClick:()=>f(!m),className:"flex items-center justify-between w-full text-sm font-medium text-gray-500 uppercase tracking-wide border-b border-gray-200 pb-2 mb-4 hover:text-gray-700 transition-colors",children:[r.jsx("span",{children:"Additional Information"}),r.jsx("svg",{className:`w-4 h-4 transform transition-transform ${m?"rotate-180":""}`,fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})]}),m&&r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6 animate-fadeIn",children:[r.jsxs("div",{children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Gender",r.jsx("span",{className:"text-xs text-gray-500 ml-1",children:"(Optional)"})]}),r.jsxs("div",{className:"flex items-center p-3 border border-gray-200 rounded-lg bg-gray-50",children:[r.jsx("svg",{className:"w-5 h-5 text-gray-400 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})}),r.jsx("span",{className:"text-gray-600",children:x(s.gender)})]})]}),r.jsxs("div",{children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Birthday",r.jsx("span",{className:"text-xs text-gray-500 ml-1",children:"(Optional)"})]}),r.jsxs("div",{className:"flex items-center p-3 border border-gray-200 rounded-lg bg-gray-50",children:[r.jsx("svg",{className:"w-5 h-5 text-gray-400 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v13m0-13V6a2 2 0 112 2h-2zm0 0V5.5A2.5 2.5 0 109.5 8H12zm-7 4h14M5 12a2 2 0 110-4h14a2 2 0 110 4M5 12v7a2 2 0 002 2h10a2 2 0 002-2v-7"})}),r.jsx("span",{className:"text-gray-600",children:s.birthday?new Date(s.birthday).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"Not provided"})]})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:s.customerSince?"Customer Since":"Member Since"}),r.jsxs("div",{className:"flex items-center p-3 border border-gray-200 rounded-lg bg-gray-50",children:[r.jsx("svg",{className:"w-5 h-5 text-gray-400 mr-3",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"})}),r.jsx("span",{className:"text-gray-600",children:s.customerSince?s.customerSince:s.joinDate?new Date(s.joinDate).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"Not available"})]})]}),r.jsxs("div",{className:"md:col-span-2",children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:["Address",r.jsx("span",{className:"text-xs text-gray-500 ml-1",children:"(Optional)"})]}),r.jsxs("div",{className:"flex items-start p-3 border border-gray-200 rounded-lg bg-gray-50",children:[r.jsxs("svg",{className:"w-5 h-5 text-gray-400 mr-3 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"}),r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 11a3 3 0 11-6 0 3 3 0 016 0z"})]}),r.jsx("span",{className:"text-gray-600 leading-relaxed",children:g(s.address)})]})]})]})]})]})}function ng({customer:s}){const a=u=>{if(!u||u.trim()==="")return null;const m=u.toLowerCase();let f="",p="Unknown";const y=u.match(/\d{4}/);return y&&(f=y[0]),m.includes("visa")?p="Visa":m.includes("master")||m.includes("mc")?p="MasterCard":m.includes("amex")||m.includes("american express")?p="American Express":m.includes("discover")&&(p="Discover"),{id:1,type:p,lastFour:f,expiryDate:"Hidden",isDefault:!0,cardHolder:s.name,originalInfo:u}},o=[];if(s.creditCard){const u=a(s.creditCard);u&&o.push(u)}const i=u=>{switch(u){case"Visa":return r.jsx("div",{className:"w-8 h-8 bg-blue-600 rounded flex items-center justify-center",children:r.jsx("span",{className:"text-white text-xs font-bold",children:"V"})});case"MasterCard":return r.jsx("div",{className:"w-8 h-8 bg-red-600 rounded flex items-center justify-center",children:r.jsx("span",{className:"text-white text-xs font-bold",children:"MC"})});case"American Express":return r.jsx("div",{className:"w-8 h-8 bg-green-600 rounded flex items-center justify-center",children:r.jsx("span",{className:"text-white text-xs font-bold",children:"AE"})});case"Discover":return r.jsx("div",{className:"w-8 h-8 bg-orange-600 rounded flex items-center justify-center",children:r.jsx("span",{className:"text-white text-xs font-bold",children:"D"})});case"Unknown":return r.jsx("div",{className:"w-8 h-8 bg-purple-600 rounded flex items-center justify-center",children:r.jsx("span",{className:"text-white text-xs font-bold",children:"?"})});default:return r.jsx("div",{className:"w-8 h-8 bg-gray-400 rounded flex items-center justify-center",children:r.jsx("svg",{className:"w-4 h-4 text-white",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})})})}};return r.jsxs("div",{className:"bg-white rounded-xl shadow-sm p-8",children:[r.jsxs("div",{className:"flex items-center justify-between mb-6",children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Payment Methods"}),r.jsx("span",{className:"text-sm text-gray-500",children:"View Only"})]}),o.length===0?r.jsxs("div",{className:"text-center py-12",children:[r.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx("svg",{className:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"})})}),r.jsx("p",{className:"text-gray-500 text-sm",children:"No payment methods on file"})]}):r.jsx("div",{className:"space-y-4",children:o.map(u=>r.jsxs("div",{className:`relative p-4 border rounded-xl ${u.isDefault?"border-blue-200 bg-blue-50":"border-gray-200 bg-gray-50"}`,children:[u.isDefault&&r.jsx("div",{className:"absolute -top-2 -right-2",children:r.jsx("span",{className:"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-600 text-white",children:"Default"})}),r.jsxs("div",{className:"flex items-center space-x-4",children:[i(u.type),r.jsxs("div",{className:"flex-1",children:[r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{className:"text-sm font-medium text-gray-900",children:u.type}),r.jsx("span",{className:"text-sm text-gray-600",children:u.lastFour?`•••• •••• •••• ${u.lastFour}`:u.originalInfo})]}),r.jsxs("div",{className:"flex items-center space-x-4 mt-1",children:[r.jsx("span",{className:"text-xs text-gray-500",children:u.originalInfo}),r.jsx("span",{className:"text-xs text-gray-500",children:u.cardHolder})]})]})]})]},u.id))}),r.jsx("div",{className:"mt-6 p-4 bg-gray-50 rounded-lg",children:r.jsxs("div",{className:"flex items-start space-x-3",children:[r.jsx("svg",{className:"w-5 h-5 text-gray-400 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})}),r.jsxs("div",{children:[r.jsx("p",{className:"text-sm font-medium text-gray-900",children:"Customer Payment Information"}),r.jsx("p",{className:"text-xs text-gray-600 mt-1",children:"Payment information is encrypted and securely stored. Only masked card details are visible for privacy protection."})]})]})})]})}const sg=()=>[{id:1,appointmentDate:"2024-06-15",checkoutDate:"2024-06-15",status:"completed",serviceProvider:"Sarah Johnson",service:"Hair Cut & Styling",lastVisit:"2024-06-15",transaction:{products:[{name:"Hair Cut & Styling",price:85},{name:"Hair Treatment",price:45},{name:"Hair Product (Shampoo)",price:25}],subtotal:155,discountAmount:15.5,discountPercentage:10,tipAmount:20,tipPercentage:12.9,tax:12.4,total:171.9,paid:!0,status:"completed",paymentMethod:"Credit Card"},modificationHistory:[{id:1,timestamp:"2024-06-10T09:00:00Z",action:"Appointment Created",user:"Sarah Johnson",details:"Initial appointment scheduled for Hair Cut & Styling",type:"creation"},{id:2,timestamp:"2024-06-12T14:30:00Z",action:"Service Added",user:"Sarah Johnson",details:"Added Hair Treatment service",type:"modification"},{id:3,timestamp:"2024-06-15T10:15:00Z",action:"Customer Checked In",user:"Reception Staff",details:"Customer arrived and checked in",type:"status"},{id:4,timestamp:"2024-06-15T11:45:00Z",action:"Service Completed",user:"Sarah Johnson",details:"All services completed successfully",type:"completion"},{id:5,timestamp:"2024-06-15T12:00:00Z",action:"Payment Processed",user:"Reception Staff",details:"Payment of $171.90 processed via card",type:"payment"}]},{id:2,appointmentDate:"2024-05-20",checkoutDate:"2024-05-20",status:"completed",serviceProvider:"Mike Davis",service:"Massage Therapy",lastVisit:"2024-05-20",transaction:{products:[{name:"Deep Tissue Massage (60min)",price:120},{name:"Aromatherapy Add-on",price:25}],subtotal:145,discountAmount:0,discountPercentage:0,tipAmount:25,tipPercentage:17.2,tax:11.6,total:181.6,paid:!0,status:"refunded",paymentMethod:"Credit Card",refundAmount:181.6,refundDate:"2024-05-22T10:30:00Z",refundReason:"Customer dissatisfied with service quality"},modificationHistory:[{id:1,timestamp:"2024-05-18T10:00:00Z",action:"Appointment Created",user:"Mike Davis",details:"Initial appointment scheduled for Massage Therapy",type:"creation"},{id:2,timestamp:"2024-05-20T09:30:00Z",action:"Customer Checked In",user:"Reception Staff",details:"Customer arrived for massage appointment",type:"status"},{id:3,timestamp:"2024-05-20T11:00:00Z",action:"Service Completed",user:"Mike Davis",details:"Deep tissue massage completed",type:"completion"},{id:4,timestamp:"2024-05-20T11:15:00Z",action:"Payment Processed",user:"Reception Staff",details:"Payment of $181.60 processed via cash",type:"payment"}]},{id:3,appointmentDate:"2024-04-10",checkoutDate:"2024-04-10",status:"completed",serviceProvider:"Lisa Chen",service:"Facial Treatment",lastVisit:"2024-04-10",transaction:{products:[{name:"Anti-Aging Facial",price:95},{name:"Eye Treatment",price:35},{name:"Skincare Kit",price:65}],subtotal:195,discountAmount:19.5,discountPercentage:10,tipAmount:30,tipPercentage:17.1,tax:15.6,total:221.1,paid:!0,status:"completed",paymentMethod:"Credit Card"},modificationHistory:[{id:1,timestamp:"2024-04-08T11:00:00Z",action:"Appointment Created",user:"Lisa Chen",details:"Initial appointment scheduled for Facial Treatment",type:"creation"},{id:2,timestamp:"2024-04-09T16:20:00Z",action:"Service Modified",user:"Lisa Chen",details:"Added Eye Treatment to appointment",type:"modification"},{id:3,timestamp:"2024-04-10T13:00:00Z",action:"Customer Checked In",user:"Reception Staff",details:"Customer arrived for facial appointment",type:"status"},{id:4,timestamp:"2024-04-10T15:30:00Z",action:"Service Completed",user:"Lisa Chen",details:"Facial treatment and eye treatment completed",type:"completion"},{id:5,timestamp:"2024-04-10T15:45:00Z",action:"Payment Processed",user:"Reception Staff",details:"Payment of $221.10 processed via card",type:"payment"}]},{id:4,appointmentDate:"2024-03-25",checkoutDate:null,status:"cancelled",serviceProvider:"Sarah Johnson",service:"Hair Color",lastVisit:null,transaction:{products:[{name:"Hair Color Treatment",price:150},{name:"Color Protection Product",price:35}],subtotal:185,discountAmount:0,discountPercentage:0,tipAmount:0,tipPercentage:0,tax:14.8,total:199.8,paid:!1,status:"failed",paymentMethod:"Credit Card",failureReason:"Insufficient funds"},modificationHistory:[{id:1,timestamp:"2024-03-20T14:00:00Z",action:"Appointment Created",user:"Sarah Johnson",details:"Initial appointment scheduled for Hair Color",type:"creation"},{id:2,timestamp:"2024-03-25T08:30:00Z",action:"Appointment Cancelled",user:"Customer",details:"Customer cancelled appointment due to scheduling conflict",type:"cancellation"}]},{id:5,appointmentDate:"2024-07-02",checkoutDate:null,status:"scheduled",serviceProvider:"Mike Davis",service:"Sports Massage",lastVisit:null,transaction:{products:[{name:"Sports Massage (90min)",price:140},{name:"Recovery Kit",price:45}],subtotal:185,discountAmount:18.5,discountPercentage:10,tipAmount:0,tipPercentage:0,tax:13.32,total:179.82,paid:!1,status:"pending",paymentMethod:"Cash",pendingReason:"Appointment scheduled - payment due at service"},modificationHistory:[{id:1,timestamp:"2024-06-25T16:00:00Z",action:"Appointment Created",user:"Mike Davis",details:"Initial appointment scheduled for Sports Massage",type:"creation"}]}],ag=s=>{var F;const[a,o]=b.useState(1),[i]=b.useState(5),[u,m]=b.useState("desc"),[f,p]=b.useState("all"),y=sg(),x=b.useMemo(()=>y.filter(I=>f==="all"||(()=>{const U=new Date(I.appointmentDate),oe=new Date;switch(f){case"thisMonth":return U.getMonth()===oe.getMonth()&&U.getFullYear()===oe.getFullYear();case"lastMonth":const fe=new Date(oe);return fe.setMonth(oe.getMonth()-1),U.getMonth()===fe.getMonth()&&U.getFullYear()===fe.getFullYear();case"last3Months":const Z=new Date(oe);return Z.setMonth(oe.getMonth()-3),U>=Z;case"thisYear":return U.getFullYear()===oe.getFullYear();default:return!0}})()),[y,f]),g=b.useMemo(()=>[...x].sort((I,z)=>{const U=new Date(I.appointmentDate),oe=new Date(z.appointmentDate);return u==="desc"?oe-U:U-oe}),[x,u]),v=Math.ceil(g.length/i),j=(a-1)*i,P=j+i,k=g.slice(j,P),E=(F=y.filter(I=>I.status==="completed"&&I.lastVisit).sort((I,z)=>new Date(z.lastVisit)-new Date(I.lastVisit))[0])==null?void 0:F.lastVisit;return{currentAppointments:k,sortedAppointments:g,lastVisitDate:E,currentPage:a,totalPages:v,startIndex:j,endIndex:P,itemsPerPage:i,sortOrder:u,dateRangeFilter:f,toggleSort:()=>{m(u==="desc"?"asc":"desc")},handlePageChange:I=>{o(I)},handleFilterChange:()=>{o(1)},clearFilters:()=>{p("all"),o(1)},setDateRangeFilter:p}},og=()=>{const[s,a]=b.useState(new Set),[o,i]=b.useState(!1),[u,m]=b.useState(null);return{expandedTransactions:s,showHistoryModal:o,selectedAppointmentHistory:u,toggleTransaction:P=>{a(k=>{const E=new Set(k);return E.has(P)?E.delete(P):E.add(P),E})},openHistoryModal:P=>{m(P),i(!0)},closeHistoryModal:()=>{i(!1),m(null)},closeTransactionOnPageChange:()=>{a(new Set)},isTransactionExpanded:P=>s.has(P),hasExpandedTransactions:()=>s.size>0,collapseAllTransactions:()=>{a(new Set)}}},Dm=s=>{const a={completed:"bg-green-100 text-green-800",scheduled:"bg-blue-100 text-blue-800",cancelled:"bg-red-100 text-red-800","no-show":"bg-gray-100 text-gray-800"};return a[s]||a["no-show"]},lg=(s,a)=>{const o={appointmentId:s.id,customerName:a.name,service:s.service,serviceProvider:s.serviceProvider,appointmentDate:s.appointmentDate,modificationHistory:s.modificationHistory},i=new Blob([JSON.stringify(o,null,2)],{type:"application/json"}),u=URL.createObjectURL(i),m=document.createElement("a");m.href=u,m.download=`appointment-${s.id}-history.json`,document.body.appendChild(m),m.click(),document.body.removeChild(m),URL.revokeObjectURL(u)},ig=s=>({creation:r.jsx("svg",{className:"w-4 h-4 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6v6m0 0v6m0-6h6m-6 0H6"})}),modification:r.jsx("svg",{className:"w-4 h-4 text-yellow-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),status:r.jsx("svg",{className:"w-4 h-4 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),completion:r.jsx("svg",{className:"w-4 h-4 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),payment:r.jsx("svg",{className:"w-4 h-4 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"})}),cancellation:r.jsx("svg",{className:"w-4 h-4 text-red-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})[s]||r.jsx("svg",{className:"w-4 h-4 text-gray-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),cg=(s,a)=>s==="completed"?"text-green-600 font-medium":s==="refunded"?"text-purple-600 font-medium":s==="failed"?"text-red-600 font-medium":s==="pending"?"text-yellow-600 font-medium":a?"text-green-600":"text-red-600",ug=(s,a)=>s?s.charAt(0).toUpperCase()+s.slice(1):a?"Paid":"Unpaid";function dg({dateRangeFilter:s,setDateRangeFilter:a,handleFilterChange:o,clearFilters:i,sortOrder:u,toggleSort:m,onCollapseAll:f,hasExpandedTransactions:p}){return r.jsxs("div",{className:"mb-6 flex items-end justify-between",children:[r.jsxs("div",{className:"max-w-xs",children:[r.jsx("label",{className:"block text-xs font-medium text-gray-700 mb-1",children:"Date Range"}),r.jsxs("select",{value:s,onChange:y=>{a(y.target.value),o()},className:"block w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500 text-sm",children:[r.jsx("option",{value:"all",children:"All Time"}),r.jsx("option",{value:"thisMonth",children:"This Month"}),r.jsx("option",{value:"lastMonth",children:"Last Month"}),r.jsx("option",{value:"last3Months",children:"Last 3 Months"}),r.jsx("option",{value:"thisYear",children:"This Year"})]})]}),r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsxs("button",{onClick:m,className:"inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors",children:[r.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:u==="desc"?"M3 4h13M3 8h9m-9 4h6m4 0l4-4m0 0l4 4m-4-4v12":"M3 4h13M3 8h9m-9 4h9m5-4v12m0 0l-4-4m4 4l4-4"})}),u==="desc"?"Newest First":"Oldest First"]}),p&&r.jsxs("button",{onClick:f,className:"inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors",children:[r.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 15l7-7 7 7"})}),"Collapse All"]}),s!=="all"&&r.jsxs("button",{onClick:i,className:"inline-flex items-center px-3 py-1 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors",children:[r.jsx("svg",{className:"w-4 h-4 mr-1",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})}),"Clear Filters"]})]})]})}function mg({appointment:s,onHistoryClick:a,onTransactionToggle:o,isTransactionExpanded:i}){const u=m=>{console.log("View on calendar for appointment:",m)};return r.jsxs("div",{className:"flex space-x-2",children:[r.jsxs("button",{onClick:()=>a(s),className:"p-1 text-blue-600 hover:text-blue-900 hover:bg-blue-50 rounded transition-colors group relative",title:"Modification History",children:[r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),r.jsx("span",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap",children:"Modification History"})]}),r.jsxs("button",{onClick:()=>u(s.id),className:"p-1 text-purple-600 hover:text-purple-900 hover:bg-purple-50 rounded transition-colors group relative",title:"View On Calendar",children:[r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"})}),r.jsx("span",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap",children:"View On Calendar"})]}),r.jsxs("button",{onClick:()=>o(s.id),className:"p-1 text-green-600 hover:text-green-900 hover:bg-green-50 rounded transition-colors group relative",title:"Transaction Details",children:[i?r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 15l7-7 7 7"})}):r.jsx("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})}),r.jsx("span",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-2 py-1 text-xs text-white bg-gray-900 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap",children:"Transaction Details"})]})]})}function fg({appointment:s,isExpanded:a}){const o=i=>{console.log("Process refund for appointment:",i)};return a?r.jsx("tr",{className:"bg-gray-50",children:r.jsx("td",{colSpan:"6",className:"px-6 py-4",children:r.jsxs("div",{className:"bg-white rounded-lg p-4 border border-gray-200",children:[r.jsx("h4",{className:"text-sm font-medium text-gray-900 mb-3",children:"Transaction Details"}),s.transaction?r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[r.jsxs("div",{children:[r.jsx("h5",{className:"text-xs font-medium text-gray-500 uppercase mb-2",children:"Products/Services"}),r.jsx("div",{className:"space-y-2",children:s.transaction.products.map((i,u)=>r.jsxs("div",{className:"flex justify-between text-sm",children:[r.jsx("span",{className:"text-gray-900",children:i.name}),r.jsxs("span",{className:"text-gray-600",children:["$",i.price.toFixed(2)]})]},u))})]}),r.jsxs("div",{children:[r.jsx("h5",{className:"text-xs font-medium text-gray-500 uppercase mb-2",children:"Payment Summary"}),r.jsxs("div",{className:"space-y-1 text-sm",children:[r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Subtotal:"}),r.jsxs("span",{children:["$",s.transaction.subtotal.toFixed(2)]})]}),s.transaction.discountAmount>0&&r.jsxs("div",{className:"flex justify-between text-green-600",children:[r.jsxs("span",{children:["Discount (",s.transaction.discountPercentage,"%):"]}),r.jsxs("span",{children:["-$",s.transaction.discountAmount.toFixed(2)]})]}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Tax:"}),r.jsxs("span",{children:["$",s.transaction.tax.toFixed(2)]})]}),s.transaction.tipAmount>0&&r.jsxs("div",{className:"flex justify-between",children:[r.jsxs("span",{children:["Tip (",s.transaction.tipPercentage,"%):"]}),r.jsxs("span",{children:["$",s.transaction.tipAmount.toFixed(2)]})]}),r.jsxs("div",{className:"flex justify-between font-medium border-t pt-1",children:[r.jsx("span",{children:"Total:"}),r.jsxs("span",{children:["$",s.transaction.total.toFixed(2)]})]}),s.transaction.paymentMethod&&r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Payment Method:"}),r.jsx("span",{className:"text-gray-600",children:s.transaction.paymentMethod})]}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Status:"}),r.jsx("span",{className:cg(s.transaction.status,s.transaction.paid),children:ug(s.transaction.status,s.transaction.paid)})]}),s.transaction.status==="refunded"&&r.jsxs("div",{className:"mt-3 pt-3 border-t border-gray-200 bg-purple-50 rounded-lg p-3",children:[r.jsx("h6",{className:"text-xs font-medium text-purple-700 uppercase mb-2",children:"Refund Details"}),r.jsxs("div",{className:"space-y-1 text-sm",children:[r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Refund Amount:"}),r.jsxs("span",{className:"font-medium text-purple-600",children:["$",s.transaction.refundAmount.toFixed(2)]})]}),r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Refund Date:"}),r.jsx("span",{className:"text-gray-600",children:new Date(s.transaction.refundDate).toLocaleDateString()})]}),r.jsxs("div",{className:"mt-2",children:[r.jsx("span",{className:"text-xs font-medium text-gray-700",children:"Reason:"}),r.jsx("p",{className:"text-xs text-gray-600 mt-1",children:s.transaction.refundReason})]})]})]}),s.transaction.status==="failed"&&r.jsxs("div",{className:"mt-3 pt-3 border-t border-gray-200 bg-red-50 rounded-lg p-3",children:[r.jsx("h6",{className:"text-xs font-medium text-red-700 uppercase mb-2",children:"Payment Failed"}),r.jsxs("div",{className:"text-sm",children:[r.jsxs("div",{className:"flex justify-between",children:[r.jsx("span",{children:"Failure Reason:"}),r.jsx("span",{className:"text-red-600 font-medium",children:s.transaction.failureReason})]}),r.jsx("p",{className:"text-xs text-red-600 mt-2",children:"Payment attempt was unsuccessful. Customer may need to retry with different payment method."})]})]}),s.transaction.status==="pending"&&r.jsxs("div",{className:"mt-3 pt-3 border-t border-gray-200 bg-yellow-50 rounded-lg p-3",children:[r.jsx("h6",{className:"text-xs font-medium text-yellow-700 uppercase mb-2",children:"Payment Pending"}),r.jsx("div",{className:"text-sm",children:r.jsx("p",{className:"text-xs text-yellow-600",children:s.transaction.pendingReason})})]})]}),s.transaction.paid&&s.status==="completed"&&s.transaction.status==="completed"&&r.jsx("div",{className:"mt-4 pt-3 border-t border-gray-200",children:r.jsxs("button",{onClick:()=>o(s.id),className:"inline-flex items-center px-3 py-2 border border-red-300 rounded-lg text-sm font-medium text-red-700 bg-white hover:bg-red-50 transition-colors",children:[r.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 15v-1a4 4 0 00-4-4H8m0 0l3 3m-3-3l3-3m9 14V5a2 2 0 00-2-2H6a2 2 0 00-2 2v16l4-2 4 2 4-2 4 2z"})}),"Process Refund"]})})]})]}):r.jsxs("div",{className:"text-center py-8",children:[r.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx("svg",{className:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})})}),r.jsx("h5",{className:"text-sm font-medium text-gray-900 mb-1",children:"No Transaction Available"}),r.jsxs("p",{className:"text-sm text-gray-500",children:[s.status==="scheduled"&&"This appointment is scheduled but not yet completed.",s.status==="cancelled"&&"This appointment was cancelled before completion.",s.status==="completed"&&"Transaction details are not available for this appointment."]})]})]})})}):null}function hg({currentPage:s,totalPages:a,startIndex:o,endIndex:i,totalItems:u,onPageChange:m}){return a<=1?null:r.jsxs("div",{className:"flex items-center justify-between mt-6 px-4 py-3 bg-gray-50 rounded-lg",children:[r.jsx("div",{className:"flex items-center",children:r.jsxs("p",{className:"text-sm text-gray-700",children:["Showing ",o+1," to ",Math.min(i,u)," of ",u," appointments"]})}),r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx("button",{onClick:()=>m(s-1),disabled:s===1,className:"px-3 py-1 border border-gray-300 rounded text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),r.jsx("div",{className:"flex space-x-1",children:[...Array(a)].map((f,p)=>{const y=p+1,x=y===s,g=y===1||y===a||y>=s-1&&y<=s+1;return!g&&y===s-2?r.jsx("span",{className:"px-2 text-gray-400",children:"..."},y):!g&&y===s+2?r.jsx("span",{className:"px-2 text-gray-400",children:"..."},y):g?r.jsx("button",{onClick:()=>m(y),className:`px-3 py-1 border rounded text-sm font-medium ${x?"border-blue-500 bg-blue-50 text-blue-600":"border-gray-300 bg-white text-gray-700 hover:bg-gray-50"}`,children:y},y):null})}),r.jsx("button",{onClick:()=>m(s+1),disabled:s===a,className:"px-3 py-1 border border-gray-300 rounded text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})}function pg({isOpen:s,appointment:a,customer:o,onClose:i}){if(!s||!a)return null;const u=()=>{lg(a,o)},m=f=>{const p=Dm(f);return r.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${p}`,children:f.charAt(0).toUpperCase()+f.slice(1)})};return r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:r.jsxs("div",{className:"bg-white rounded-xl max-w-4xl w-full max-h-[90vh] overflow-hidden",children:[r.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[r.jsxs("div",{children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"Modification History"}),r.jsxs("p",{className:"text-sm text-gray-600 mt-1",children:["Appointment #",a.id," - ",a.service]})]}),r.jsxs("div",{className:"flex items-center space-x-3",children:[r.jsxs("button",{onClick:u,className:"inline-flex items-center px-3 py-2 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 transition-colors",children:[r.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),"Download"]}),r.jsx("button",{onClick:i,className:"text-gray-400 hover:text-gray-600 transition-colors",children:r.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]})]}),r.jsxs("div",{className:"p-6 overflow-y-auto max-h-[calc(90vh-120px)]",children:[r.jsx("div",{className:"bg-gray-50 rounded-lg p-4 mb-6",children:r.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 text-sm",children:[r.jsxs("div",{children:[r.jsx("span",{className:"font-medium text-gray-700",children:"Date:"}),r.jsx("p",{className:"text-gray-900",children:new Date(a.appointmentDate).toLocaleDateString()})]}),r.jsxs("div",{children:[r.jsx("span",{className:"font-medium text-gray-700",children:"Service Provider:"}),r.jsx("p",{className:"text-gray-900",children:a.serviceProvider})]}),r.jsxs("div",{children:[r.jsx("span",{className:"font-medium text-gray-700",children:"Status:"}),r.jsx("div",{className:"mt-1",children:m(a.status)})]}),r.jsxs("div",{children:[r.jsx("span",{className:"font-medium text-gray-700",children:"Customer:"}),r.jsx("p",{className:"text-gray-900",children:o.name})]})]})}),r.jsxs("div",{className:"space-y-4",children:[r.jsx("h4",{className:"text-sm font-medium text-gray-700 uppercase tracking-wide",children:"Activity Timeline"}),r.jsx("div",{className:"flow-root",children:r.jsx("ul",{className:"-mb-8",children:a.modificationHistory.map((f,p)=>r.jsx("li",{children:r.jsxs("div",{className:"relative pb-8",children:[p!==a.modificationHistory.length-1&&r.jsx("span",{className:"absolute top-5 left-5 -ml-px h-full w-0.5 bg-gray-200","aria-hidden":"true"}),r.jsxs("div",{className:"relative flex items-start space-x-3",children:[r.jsx("div",{className:"relative",children:r.jsx("div",{className:"h-10 w-10 rounded-full bg-gray-100 flex items-center justify-center ring-8 ring-white",children:ig(f.type)})}),r.jsx("div",{className:"min-w-0 flex-1",children:r.jsxs("div",{className:"text-sm",children:[r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"font-medium text-gray-900",children:f.action}),r.jsx("time",{className:"text-xs text-gray-500",children:new Date(f.timestamp).toLocaleString()})]}),r.jsx("p",{className:"mt-1 text-gray-600",children:f.details}),r.jsxs("p",{className:"mt-1 text-xs text-gray-500",children:["by ",f.user]})]})})]})]})},f.id))})})]})]})]})})}function xg({customer:s}){const a=ag(),o=og(),i=m=>{const f=Dm(m);return r.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${f}`,children:m.charAt(0).toUpperCase()+m.slice(1)})},u=m=>{a.handlePageChange(m),o.closeTransactionOnPageChange()};return r.jsxs("div",{className:"bg-white rounded-xl shadow-sm p-8",children:[r.jsx("div",{className:"flex items-center justify-between mb-6",children:r.jsxs("div",{children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Appointment History"}),a.lastVisitDate&&r.jsxs("p",{className:"text-sm text-gray-600 mt-1",children:["Last Visit: ",new Date(a.lastVisitDate).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})]}),r.jsxs("p",{className:"text-sm text-gray-500 mt-1",children:["Showing ",a.currentAppointments.length," of ",a.sortedAppointments.length," appointments"]})]})}),r.jsx(dg,{dateRangeFilter:a.dateRangeFilter,setDateRangeFilter:a.setDateRangeFilter,handleFilterChange:a.handleFilterChange,clearFilters:a.clearFilters,sortOrder:a.sortOrder,toggleSort:a.toggleSort,onCollapseAll:o.collapseAllTransactions,hasExpandedTransactions:o.hasExpandedTransactions()}),r.jsx("div",{className:"w-full",children:r.jsxs("table",{className:"w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:r.jsxs("tr",{children:[r.jsx("th",{className:"px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24",children:"Actions"}),r.jsx("th",{className:"px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28",children:"Appointment Date"}),r.jsx("th",{className:"px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-28",children:"Checkout Date"}),r.jsx("th",{className:"px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-24",children:"Status"}),r.jsx("th",{className:"px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Service Provider"}),r.jsx("th",{className:"px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Service"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:a.currentAppointments.map(m=>r.jsxs(r.Fragment,{children:[r.jsxs("tr",{className:"hover:bg-gray-50",children:[r.jsx("td",{className:"px-3 py-4 text-sm font-medium w-24",children:r.jsx(mg,{appointment:m,onHistoryClick:o.openHistoryModal,onTransactionToggle:o.toggleTransaction,isTransactionExpanded:o.isTransactionExpanded(m.id)})}),r.jsx("td",{className:"px-3 py-4 text-sm text-gray-900 w-28",children:r.jsx("div",{className:"truncate",children:new Date(m.appointmentDate).toLocaleDateString()})}),r.jsx("td",{className:"px-3 py-4 text-sm text-gray-900 w-28",children:r.jsx("div",{className:"truncate",children:m.checkoutDate?new Date(m.checkoutDate).toLocaleDateString():"-"})}),r.jsx("td",{className:"px-3 py-4 w-24",children:i(m.status)}),r.jsx("td",{className:"px-3 py-4 text-sm text-gray-900",children:r.jsx("div",{className:"truncate max-w-32",title:m.serviceProvider,children:m.serviceProvider})}),r.jsx("td",{className:"px-3 py-4 text-sm text-gray-900",children:r.jsx("div",{className:"truncate max-w-40",title:m.service,children:m.service})})]},m.id),r.jsx(fg,{appointment:m,isExpanded:o.isTransactionExpanded(m.id)})]}))})]})}),r.jsx(hg,{currentPage:a.currentPage,totalPages:a.totalPages,startIndex:a.startIndex,endIndex:a.endIndex,totalItems:a.sortedAppointments.length,onPageChange:u}),a.sortedAppointments.length===0&&r.jsxs("div",{className:"text-center py-12",children:[r.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx("svg",{className:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v14a2 2 0 002 2z"})})}),r.jsx("p",{className:"text-gray-500 text-sm",children:"No appointments match your filters"}),a.dateRangeFilter!=="all"&&r.jsx("button",{onClick:a.clearFilters,className:"mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium",children:"Clear filters to see all appointments"})]}),r.jsx(pg,{isOpen:o.showHistoryModal,appointment:o.selectedAppointmentHistory,customer:s,onClose:o.closeHistoryModal})]})}const gg="modulepreload",yg=function(s){return"/"+s},Md={},Aa=function(a,o,i){let u=Promise.resolve();if(o&&o.length>0){let f=function(x){return Promise.all(x.map(g=>Promise.resolve(g).then(v=>({status:"fulfilled",value:v}),v=>({status:"rejected",reason:v}))))};document.getElementsByTagName("link");const p=document.querySelector("meta[property=csp-nonce]"),y=(p==null?void 0:p.nonce)||(p==null?void 0:p.getAttribute("nonce"));u=f(o.map(x=>{if(x=yg(x),x in Md)return;Md[x]=!0;const g=x.endsWith(".css"),v=g?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${x}"]${v}`))return;const j=document.createElement("link");if(j.rel=g?"stylesheet":gg,g||(j.as="script"),j.crossOrigin="",j.href=x,y&&j.setAttribute("nonce",y),document.head.appendChild(j),g)return new Promise((P,k)=>{j.addEventListener("load",P),j.addEventListener("error",()=>k(new Error(`Unable to preload CSS for ${x}`)))})}))}function m(f){const p=new Event("vite:preloadError",{cancelable:!0});if(p.payload=f,window.dispatchEvent(p),!p.defaultPrevented)throw f}return u.then(f=>{for(const p of f||[])p.status==="rejected"&&m(p.reason);return a().catch(m)})},vg="http://localhost:8000/api/v1";class wg{constructor(){this.baseURL=vg}getAuthToken(){const a=localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token");return console.log(`Auth token available: ${!!a}, length: ${(a==null?void 0:a.length)||0}`),a}async makeFileRequest(a,o,i={}){const u=`${this.baseURL}${a}`,m=this.getAuthToken(),f={};m?(f.Authorization=`Bearer ${m}`,console.log("Using Authorization header with Bearer token")):console.warn("No auth token available for request");const p={method:"POST",headers:f,body:o,mode:"cors",credentials:"include",...i};console.log(`Making request to ${u} with headers:`,f);const y=async()=>{console.log("Executing fetch request...");const x=await fetch(u,p);if(console.log(`Response status: ${x.status}, ok: ${x.ok}`),console.log("Response headers:"),x.headers.forEach((v,j)=>{console.log(`${j}: ${v}`)}),x.ok)return x.json();if(x.status===401){console.log("Received 401 Unauthorized, attempting token refresh...");const v=await this.refreshAccessToken();if(v){console.log("Token refreshed successfully, retrying request"),p.headers.Authorization=`Bearer ${v}`;const j=await fetch(u,p);if(console.log(`Retry response status: ${j.status}, ok: ${j.ok}`),j.ok)return j.json()}else console.error("Token refresh failed")}if(x.status===404&&a.startsWith("/v1/")){console.log("Endpoint not found, trying alternative path without /v1/");const v=a.replace("/v1/","/"),j=`${this.baseURL}${v}`;console.log(`Trying alternative URL: ${j}`);const P=await fetch(j,p);if(console.log(`Alternative response status: ${P.status}, ok: ${P.ok}`),P.ok)return P.json()}let g=`HTTP error! status: ${x.status}`;try{const v=await x.json();console.error("Error response data:",v),g=v.message||v.error||g}catch{console.error("Could not parse error response as JSON");try{const j=await x.text();console.log("Response text content:",j)}catch(j){console.error("Could not get response text:",j)}}throw new Error(g)};try{return console.log(`Making request to ${u}`),await y()}catch(x){throw console.error(`PDF upload failed for ${a}:`,x),x}}async refreshAccessToken(){const a=localStorage.getItem("refresh_token")||sessionStorage.getItem("refresh_token");if(console.log(`Refresh token available: ${!!a}, length: ${(a==null?void 0:a.length)||0}`),!a)return console.warn("No refresh token available"),null;let o;try{const i=new URL(this.baseURL);let u=i.pathname.replace(/\/$/,"");u.endsWith("/v1")&&(u=u.slice(0,-3)),o=`${i.origin}${u}/auth/refresh/`,console.log(`Constructed refresh URL: ${o}`)}catch{o="/api/auth/refresh/",console.log(`Using fallback refresh URL: ${o}`)}try{console.log(`Sending refresh token request to ${o}`);const i=await fetch(o,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({refresh:a})});if(console.log(`Refresh response status: ${i.status}, ok: ${i.ok}`),!i.ok)return console.warn("Token refresh failed"),null;const u=await i.json();return console.log("Refresh response data:",u),u.access?(localStorage.setItem("auth_token",u.access),console.log("New access token saved to localStorage"),u.access):null}catch(i){return console.error("Token refresh error:",i),null}}async uploadPdf(a,o,i={}){console.log(`Preparing to upload PDF: ${o}`);try{await this.checkApiConnection()}catch(f){console.error("API connection check failed:",f)}const u=new File([a],o,{type:"application/pdf"});console.log("Created File object:",u.name,u.size,u.type);const m=new FormData;m.append("file",u),m.append("file_type","other"),i.customerName&&m.append("description",`Customer signature for ${i.customerName}`),console.log("FormData prepared with fields:");for(const f of m.entries())console.log(`- ${f[0]}: ${f[1]instanceof File?`File: ${f[1].name}, ${f[1].size} bytes`:f[1]}`);console.log("Sending to backend...");try{const f=await this.uploadWithXhr("/files/upload/",m);return console.log("Upload successful with XHR:",f),f}catch(f){console.error("XHR upload failed, falling back to fetch:",f);const p=await this.makeFileRequest("/files/upload/",m);return console.log("Upload result with fetch:",p),p}}async checkApiConnection(){try{console.log("Checking API connection...");const a=`${this.baseURL}/health/`,o=await fetch(a,{method:"GET",headers:{Accept:"application/json"}});if(console.log(`API health check status: ${o.status}`),o.ok){const i=await o.json();return console.log("API health check response:",i),!0}else return console.error("API health check failed:",o.status),!1}catch(a){throw console.error("API connection check error:",a),a}}uploadWithXhr(a,o){return new Promise((i,u)=>{const m=`${this.baseURL}${a}`,f=new XMLHttpRequest;f.open("POST",m,!0),f.withCredentials=!0;const p=this.getAuthToken();p&&f.setRequestHeader("Authorization",`Bearer ${p}`);const x=(()=>{const g="csrftoken=",j=decodeURIComponent(document.cookie).split(";");for(let P=0;P<j.length;P++){let k=j[P].trim();if(k.indexOf(g)===0)return k.substring(g.length,k.length)}return null})();x?(f.setRequestHeader("X-CSRFToken",x),console.log("Added CSRF token to request")):console.warn("No CSRF token found in cookies"),console.log("Request headers:"),p&&console.log("- Authorization: Bearer [token]"),x&&console.log("- X-CSRFToken:",x),f.onload=function(){if(console.log(`XHR complete - status: ${f.status}`),f.status>=200&&f.status<300)try{const g=JSON.parse(f.responseText);i(g)}catch(g){console.error("Error parsing response:",g),i({success:!0,message:"Upload successful but could not parse response"})}else{console.error("XHR error response:",f.responseText);try{const g=JSON.parse(f.responseText);if(console.log("Parsed error data:",g),g.details&&typeof g.details=="object"){console.log("Validation errors:",g.details);const v=[];Object.keys(g.details).forEach(j=>{const P=g.details[j];Array.isArray(P)?P.forEach(k=>v.push(`${j}: ${k}`)):v.push(`${j}: ${P}`)}),u(new Error(`Validation failed: ${v.join(", ")}`))}else u(new Error(g.message||g.error||`HTTP error! status: ${f.status}`))}catch{u(new Error(`HTTP error! status: ${f.status}, response: ${f.responseText}`))}}},f.onerror=function(){console.error("XHR network error"),u(new Error("Network error occurred during upload"))},f.upload.onprogress=function(g){if(g.lengthComputable){const v=g.loaded/g.total*100;console.log(`Upload progress: ${v.toFixed(2)}%`)}},console.log(`Sending XHR request to ${m}`),f.send(o)})}async getPdfStatus(a){const o=`${this.baseURL}/files/${a}/status/`,i=this.getAuthToken(),u=await fetch(o,{headers:{Authorization:`Bearer ${i}`}});if(!u.ok)throw new Error(`Failed to get PDF status: ${u.status}`);return await u.json()}async getPdfDownloadUrl(a){const o=`${this.baseURL}/files/${a}/download/`,i=this.getAuthToken(),u=await fetch(o,{headers:{Authorization:`Bearer ${i}`}});if(!u.ok)throw new Error(`Failed to get PDF download URL: ${u.status}`);return await u.json()}}const jg=new wg,Tm=(...s)=>jg.uploadPdf(...s),bg=async(s,a="signature.pdf",o={})=>{try{console.log("Starting PDF generation process...");const[i,u]=await Promise.all([Aa(()=>import("./jspdf.es.min-DFZ4OdAQ.js").then(j=>j.j),[]).then(j=>j.default),Aa(()=>import("./html2canvas.esm-CBrSDip1.js"),[]).then(j=>j.default)]);console.log("Libraries loaded successfully");const m=document.createElement("div");m.style.position="absolute",m.style.left="-9999px",m.style.top="-9999px",m.style.padding="20px",m.style.width="800px",m.innerHTML=`
      <div style="font-family: Arial, sans-serif;">
        <h2 style="text-align: center; margin-bottom: 20px;">${o.title||"Signature Document"}</h2>
        ${o.customerName?`<p><strong>Customer:</strong> ${o.customerName}</p>`:""}
        ${o.documentType?`<p><strong>Document Type:</strong> ${o.documentType}</p>`:""}
        <p><strong>Date:</strong> ${new Date().toLocaleDateString()}</p>
        <div style="margin: 30px 0; border-bottom: 1px solid #ccc; padding-bottom: 10px;">
          <p><strong>Customer Signature:</strong></p>
          <img src="${s}" style="max-width: 100%; max-height: 250px;" />
        </div>
      </div>
    `,document.body.appendChild(m),console.log("Signature container created");const f=await u(m,{scale:2,useCORS:!0,allowTaint:!0,logging:!1});console.log("Canvas rendered successfully");const p=f.toDataURL("image/png"),y=new i({orientation:"portrait",unit:"mm",format:"a4"}),x=210,g=f.height*x/f.width;if(y.addImage(p,"PNG",0,0,x,g),y.save(a),console.log("PDF saved locally"),document.body.removeChild(m),o.skipUpload)return console.log("Skipping upload to AWS S3 (debug mode)"),{success:!0,message:"PDF saved locally only (upload skipped)",debug:!0};console.log("Preparing to upload PDF to AWS S3...");const v=y.output("blob");try{console.log("Calling uploadPdf with blob size:",v.size);const j=await Tm(v,a,{customerName:o.customerName,documentType:o.documentType,category:"signatures"});return console.log("Upload successful:",j),j}catch(j){throw console.error("Upload failed:",j),console.error("Error details:",j.stack),new Error(`PDF saved locally but upload failed: ${j.message}`)}}catch(i){throw console.error("Error in PDF process:",i),i}},Ng=async(s="Form",a=[],o="form.pdf",i={})=>{try{const[u,m]=await Promise.all([Aa(()=>import("./jspdf.es.min-DFZ4OdAQ.js").then(S=>S.j),[]).then(S=>S.default),Aa(()=>import("./html2canvas.esm-CBrSDip1.js"),[]).then(S=>S.default)]),f=document.createElement("div");Object.assign(f.style,{position:"absolute",left:"-9999px",top:"-9999px",padding:"20px",width:"800px",fontFamily:"Arial, sans-serif",background:"#ffffff",color:"#000000"});const p=(S,_)=>{var B;if(!S)return"";switch(S.type){case"text":{const H=S.textStyle==="Heading"?"h2":S.textStyle==="Subheading"?"h3":"p";return`<${H} style="margin:12px 0;">${S.textContent||""}</${H}>`}case"short":case"long":{const H=S.type==="short"?30:60;return`
            <div style="margin:14px 0;">
              <p style="margin:0 0 6px;"><strong>Q${_+1}: ${S.label||""}</strong></p>
              <div style="height:${H}px;border-bottom:1px solid #bbb;"></div>
            </div>
          `}case"multiple":{const H=(S.options||[]).map(F=>`<li>${F}</li>`).join("");return`
            <div style="margin:14px 0;">
              <p style="margin:0 0 6px;"><strong>Q${_+1}: ${S.label||""}</strong></p>
              <ul style="margin:0 0 0 18px;padding:0;list-style-type:disc;">${H}</ul>
            </div>
          `}case"signature":return`
            <div style="margin:18px 0;">
              <p style="margin:0 0 6px;"><strong>Customer Signature</strong></p>
              ${(B=S.signatureData)!=null&&B.customer?`<img src="${S.signatureData.customer}" style="max-width:100%;max-height:200px;" />`:'<div style="height:100px;border:1px dashed #bbb;"></div>'}
            </div>
          `;default:return""}},y=a.map((S,_)=>p(S,_)).join("");f.innerHTML=`
      <div>
        <h1 style="text-align:center;margin-bottom:24px;">${s}</h1>
        ${y}
        <p style="margin-top:24px;text-align:right;">Date: ${new Date().toLocaleDateString()}</p>
      </div>
    `,document.body.appendChild(f);const x=await m(f,{scale:2,useCORS:!0,allowTaint:!0,logging:!1}),g=x.toDataURL("image/png"),v=new u({orientation:"portrait",unit:"mm",format:"a4"}),j=210,P=x.height*j/x.width;if(v.addImage(g,"PNG",0,0,j,P),v.save(o),document.body.removeChild(f),i.skipUpload)return{success:!0,message:"PDF saved locally (upload skipped)",debug:!0};const k=v.output("blob");return await Tm(k,o,{documentType:i.documentType||"Form",description:`Form PDF for ${s}`,category:"forms"})}catch(u){throw console.error("Error in saveFormToPdf:",u),u}},Lm=({onSave:s,initialData:a={},customerName:o="",documentType:i=""})=>{const u=b.useRef(null),[m,f]=b.useState(!1),[p,y]=b.useState({x:0,y:0}),[x,g]=b.useState(a.agreed||!1),[v,j]=b.useState(a.customer||null),[P,k]=b.useState(!a.customer),[E,S]=b.useState(!1),[_,B]=b.useState(""),H=D=>{if(!D)return;const q=D.getContext("2d"),me=D.getBoundingClientRect();D.width=me.width,D.height=me.height,q.strokeStyle="#444",q.lineWidth=2,q.lineCap="round",q.lineJoin="round"},F=(D,q)=>{const me=q.getBoundingClientRect(),Y=q.width/me.width,ee=q.height/me.height;return{x:(D.clientX-me.left)*Y,y:(D.clientY-me.top)*ee}};b.useEffect(()=>{const D=u.current;H(D);const q=()=>{if(!D)return;const me=D.toDataURL("image/png");if(H(D),me){const Y=new Image;Y.onload=()=>{D.getContext("2d").drawImage(Y,0,0,D.width,D.height)},Y.src=me}};return window.addEventListener("resize",q),()=>window.removeEventListener("resize",q)},[]),b.useEffect(()=>{if(a.customer&&u.current){const D=new Image;D.onload=()=>{const q=u.current,me=q.getContext("2d");me.clearRect(0,0,q.width,q.height),me.drawImage(D,0,0,q.width,q.height)},D.src=a.customer,j(a.customer),k(!1)}},[]),b.useEffect(()=>{s==null||s({customer:v,agreed:x})},[v,x,s]);const I=D=>{if(!P)return;const q=u.current;if(!q)return;const{x:me,y:Y}=F(D,q);f(!0),y({x:me,y:Y});const ee=q.getContext("2d");ee.beginPath(),ee.moveTo(me,Y)},z=D=>{if(!m||!P)return;const q=u.current;if(!q)return;const{x:me,y:Y}=F(D,q),ee=q.getContext("2d");ee.lineTo(me,Y),ee.stroke(),y({x:me,y:Y})},U=()=>{f(!1)},oe=()=>{const D=u.current;if(!D)return;const q=D.toDataURL("image/png");j(q),k(!1)},fe=()=>{const D=u.current;if(!D)return;D.getContext("2d").clearRect(0,0,D.width,D.height),j(null),k(!0)},Z=()=>{k(!0)},re=async()=>{if(!v){alert("Please add a customer signature before saving as PDF");return}S(!0),B("Generating PDF...");try{let D=o;if(!D)try{const X=await fetch("http://localhost:8000/api/v1/users/profile/",{headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")||""}`}});if(X.ok){const T=await X.json(),$=T.first_name||"",xe=T.last_name||"",pe=`${$} ${xe}`.trim();pe&&(D=pe)}else console.warn("Could not fetch user profile – using generic filename")}catch(ee){console.error("Error fetching user profile:",ee)}const q=D?`${D.replace(/\s+/g,"_")}_customer_signature.pdf`:"customer_signature.pdf";B("Uploading to cloud storage...");const Y=await bg(v,q,{title:"Customer Signature Document",customerName:D,documentType:i,skipUpload:!1});Y&&Y.id?B(`PDF saved and uploaded successfully! File ID: ${Y.id}`):Y&&Y.debug?B("PDF saved locally (upload skipped in debug mode)"):B("PDF saved locally and upload initiated. Check console for details."),setTimeout(()=>{_.includes("Error")||B("")},5e3)}catch(D){console.error("Error in PDF save process:",D),D.message.includes("auth")?B("Error: Authentication failed. Please log in again."):D.message.includes("network")?B("Error: Network issue. Check your connection and try again."):B(`Error: ${D.message}`)}finally{S(!1)}},de=D=>{const q=D.touches[0];I(new MouseEvent("mousedown",{clientX:q.clientX,clientY:q.clientY}))},ie=D=>{if(!m)return;const q=D.touches[0];z(new MouseEvent("mousemove",{clientX:q.clientX,clientY:q.clientY}))},J=()=>{U()};return r.jsxs("div",{className:"bg-gray-50 rounded-lg p-6 border border-gray-200 w-full max-w-3xl mx-auto",children:[r.jsx("h2",{className:"font-semibold text-lg mb-3",children:"Signature"}),r.jsxs("label",{className:"flex items-center mb-4 text-sm select-none cursor-pointer",children:[r.jsx("input",{type:"checkbox",checked:x,onChange:D=>g(D.target.checked),className:"mr-2"}),r.jsx("span",{className:"text-gray-500",children:"I agree to use electronic records and signatures."})]}),r.jsxs("div",{className:"relative bg-white border rounded mb-4",style:{height:200},children:[r.jsx("canvas",{ref:u,className:`w-full h-full block ${P?"cursor-crosshair":"cursor-default"}`,style:{touchAction:"none"},onMouseDown:I,onMouseMove:z,onMouseUp:U,onMouseOut:U,onTouchStart:D=>{D.preventDefault(),de(D)},onTouchMove:D=>{D.preventDefault(),ie(D)},onTouchEnd:D=>{D.preventDefault(),J()}}),r.jsxs("div",{className:"absolute left-3 bottom-2 flex items-center text-gray-400 text-sm pointer-events-none",children:[r.jsx("span",{className:"mr-2",children:"✖"}),r.jsxs("span",{children:["Customer Signature ",v?"(Signed)":""]})]}),r.jsx("div",{className:"absolute left-0 right-0 bottom-0 h-px bg-gray-400"})]}),r.jsxs("div",{className:"flex items-center gap-2 text-sm mb-6",children:[r.jsx("span",{className:`w-3 h-3 rounded-full ${v?"bg-green-500":"bg-gray-300"}`}),r.jsxs("span",{children:["Customer Signature: ",v?"Completed":P?"Drawing":"Not signed"]})]}),_&&r.jsxs("div",{className:`mb-4 p-2 text-sm rounded ${_.includes("Error")?"bg-red-50 text-red-700 border border-red-200":_.includes("success")?"bg-green-50 text-green-700 border border-green-200":"bg-blue-50 text-blue-700 border border-blue-200"}`,children:[E&&r.jsx("div",{className:"inline-block mr-2 w-4 h-4 border-2 border-t-transparent border-blue-600 rounded-full animate-spin"}),_]}),r.jsxs("div",{className:"flex flex-wrap gap-3",children:[r.jsx("button",{type:"button",onClick:fe,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",disabled:E,children:"Clear Signature"}),P?r.jsx("button",{type:"button",onClick:oe,className:"px-4 py-2 text-sm font-medium text-white bg-green-600 border border-green-700 rounded-md hover:bg-green-700",disabled:E,children:"Done Signing"}):r.jsx("button",{type:"button",onClick:Z,className:"px-4 py-2 text-sm font-medium text-blue-600 bg-white border border-blue-600 rounded-md hover:bg-blue-50",disabled:E,children:"Edit Signature"}),r.jsxs("button",{type:"button",onClick:re,className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-700 rounded-md hover:bg-blue-700 disabled:opacity-40 flex items-center",disabled:!v||E,children:[E&&r.jsx("div",{className:"mr-2 w-4 h-4 border-2 border-t-transparent border-white rounded-full animate-spin"}),"Save as PDF"]})]})]})};function kg({customer:s}){const[a,o]=b.useState(null),[i,u]=b.useState(!1),[m,f]=b.useState(null),[p,y]=b.useState({customer:null,employee:null,agreed:!1});b.useEffect(()=>{},[i]);const x=[{id:1,name:"Service Agreement 2024",type:"agreement",uploadDate:"2024-01-15",fileSize:"245 KB",status:"signed",signedDate:"2024-01-15",documentUrl:"#",description:"Standard service agreement for 2024 services"},{id:2,name:"Privacy Policy Consent",type:"consent",uploadDate:"2024-01-15",fileSize:"156 KB",status:"signed",signedDate:"2024-01-15",documentUrl:"#",description:"Customer privacy policy acknowledgment and consent"},{id:3,name:"Health & Safety Waiver",type:"waiver",uploadDate:"2024-03-10",fileSize:"198 KB",status:"signed",signedDate:"2024-03-10",documentUrl:"#",description:"Health and safety waiver for massage and spa services"},{id:4,name:"Photo Release Form",type:"release",uploadDate:"2024-05-20",fileSize:"134 KB",status:"pending",signedDate:null,documentUrl:"#",description:"Permission to use photos for marketing purposes"},{id:5,name:"Membership Agreement 2024",type:"membership",uploadDate:"2024-06-01",fileSize:"312 KB",status:"signed",signedDate:"2024-06-01",documentUrl:"#",description:"Premium membership terms and conditions"}],g=_=>{const B={signed:"bg-green-100 text-green-800",pending:"bg-yellow-100 text-yellow-800",expired:"bg-red-100 text-red-800"};return r.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${B[_]||B.pending}`,children:_.charAt(0).toUpperCase()+_.slice(1)})},v=_=>{const B={agreement:r.jsx("svg",{className:"w-5 h-5 text-blue-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})}),consent:r.jsx("svg",{className:"w-5 h-5 text-green-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"})}),waiver:r.jsx("svg",{className:"w-5 h-5 text-yellow-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.664-.833-2.464 0L4.35 16.5c-.77.833.192 2.5 1.732 2.5z"})}),release:r.jsx("svg",{className:"w-5 h-5 text-purple-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M3 9a2 2 0 012-2h.93a2 2 0 001.664-.89l.812-1.22A2 2 0 0110.07 4h3.86a2 2 0 011.664.89l.812 1.22A2 2 0 0018.07 7H19a2 2 0 012 2v9a2 2 0 01-2 2H5a2 2 0 01-2-2V9z"})}),membership:r.jsx("svg",{className:"w-5 h-5 text-indigo-500",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"})})};return B[_]||B.agreement},j=_=>{o(_)},P=()=>{o(null)},k=_=>{f(_),u(!0)},E=()=>{f(null),u(!1)},S=_=>{console.log("Signature saved:",_),y(_)};return r.jsxs("div",{className:"bg-white rounded-xl shadow-sm p-8",children:[r.jsxs("div",{className:"flex items-center justify-between mb-6",children:[r.jsxs("div",{children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-900",children:"Customer Documents"}),r.jsxs("p",{className:"text-sm text-gray-600 mt-1",children:["Signed agreements and forms (",x.filter(_=>_.status==="signed").length," of ",x.length," completed)"]})]}),r.jsxs("div",{className:"flex items-center space-x-2",children:[r.jsx("span",{className:"text-sm text-gray-500",children:"Total Files:"}),r.jsx("span",{className:"text-sm font-medium text-gray-900",children:x.length})]})]}),r.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 gap-6",children:x.map(_=>r.jsxs("div",{className:"border border-gray-200 rounded-lg p-5 hover:shadow-lg hover:border-gray-300 transition-all duration-200 cursor-pointer group",onClick:()=>j(_),children:[r.jsxs("div",{className:"flex items-start justify-between mb-4",children:[r.jsxs("div",{className:"flex items-center space-x-3 flex-1 min-w-0",children:[r.jsx("div",{className:"flex-shrink-0",children:v(_.type)}),r.jsxs("div",{className:"flex-1 min-w-0",children:[r.jsxs("div",{className:"group relative",children:[r.jsx("h3",{className:"text-sm font-medium text-gray-900 leading-5 line-clamp-2 group-hover:text-blue-600 transition-colors",children:_.name}),_.name.length>25&&r.jsxs("div",{className:"absolute bottom-full left-0 mb-2 px-3 py-2 bg-gray-900 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none z-10 whitespace-nowrap",children:[_.name,r.jsx("div",{className:"absolute top-full left-4 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900"})]})]}),r.jsx("p",{className:"text-xs text-gray-500 mt-1",children:_.fileSize})]})]}),r.jsx("div",{className:"flex-shrink-0 ml-3",children:g(_.status)})]}),r.jsx("p",{className:"text-xs text-gray-600 mb-4 line-clamp-2 leading-relaxed",children:_.description}),r.jsxs("div",{className:"flex flex-col space-y-1 text-xs text-gray-500",children:[r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"font-medium",children:"Uploaded:"}),r.jsx("span",{children:new Date(_.uploadDate).toLocaleDateString()})]}),_.signedDate&&r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsx("span",{className:"font-medium",children:"Signed:"}),r.jsx("span",{className:"text-green-600",children:new Date(_.signedDate).toLocaleDateString()})]})]})]},_.id))}),x.length===0&&r.jsxs("div",{className:"text-center py-12",children:[r.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx("svg",{className:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),r.jsx("p",{className:"text-gray-500 text-sm",children:"No documents found"})]}),a&&r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:r.jsxs("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-3xl",children:[r.jsxs("div",{className:"flex items-center justify-between border-b px-6 py-4",children:[r.jsx("h3",{className:"text-lg font-medium text-gray-900",children:a.name}),r.jsx("button",{onClick:P,className:"text-gray-400 hover:text-gray-500",children:r.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),r.jsxs("div",{className:"p-6",children:[r.jsxs("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-6 mb-6",children:[r.jsxs("div",{children:[r.jsx("label",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:"Document Type"}),r.jsx("p",{className:"text-sm text-gray-900 capitalize mt-1",children:a.type})]}),r.jsxs("div",{children:[r.jsx("label",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:"File Size"}),r.jsx("p",{className:"text-sm text-gray-900 mt-1",children:a.fileSize})]}),r.jsxs("div",{children:[r.jsx("label",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:"Upload Date"}),r.jsx("p",{className:"text-sm text-gray-900 mt-1",children:new Date(a.uploadDate).toLocaleDateString()})]}),r.jsxs("div",{children:[r.jsx("label",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:"Status"}),r.jsx("div",{className:"mt-1",children:g(a.status)})]}),a.signedDate&&r.jsxs("div",{className:"sm:col-span-2",children:[r.jsx("label",{className:"text-xs font-medium text-gray-500 uppercase tracking-wide",children:"Signed Date"}),r.jsx("p",{className:"text-sm text-gray-900 mt-1",children:new Date(a.signedDate).toLocaleDateString()})]})]}),r.jsxs("div",{className:"flex justify-end space-x-3 mt-6",children:[r.jsx("button",{onClick:P,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50",children:"Close"}),a.status==="signed"&&r.jsx("button",{onClick:()=>k(a),className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-blue-600 rounded-md hover:bg-blue-700",children:"View & Save Signature"})]})]})]})}),i&&m&&r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:r.jsxs("div",{className:"bg-white rounded-lg shadow-xl w-full max-w-4xl",children:[r.jsxs("div",{className:"flex items-center justify-between border-b px-6 py-4",children:[r.jsxs("h3",{className:"text-lg font-medium text-gray-900",children:["Signature for ",m.name]}),r.jsx("button",{onClick:E,className:"text-gray-400 hover:text-gray-500",children:r.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})})]}),r.jsx("div",{className:"p-6",children:r.jsx(Lm,{initialData:{customer:p.customer||"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAASwAAACWCAYAAABkW7XSAAAABmJLR0QA/wD/AP+gvaeTAAAF+UlEQVR4nO3dS2hcZRzG4d+ZJE3SphaNtYKIWxERXIgbQfGCKLjThVJ05U5QEBeKCxEXult0IYKCoBsXghcQvIALQVwUFRRRRBQvWGm9tE2aZDJzXDRpJ5PMJHPOnO+c8z4PZJfJ+eY/+eZ8c+ZMIgEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADQAcPWA2C2rKg2JR1JOpS03XgcAF1VVJuT9icdTzqWdDjpUNKBpP1Jg6QqaZx0IWlr0pakzUkbk9YnrUtamzSStCZpddLKpBVJy5KWJS1JWpy0KGlh0oKk+UnDpHlJVdKcpKqPJ4DuGSatSFqZdFnS5Uk3Jd2adEfSvUkPJD2c9FjSk0nPJr2Y9ErSm0nvJX2S9GXSN0k/Jv2c9FvSyaSzSX8mXUgaJ42TxkkXk7YmbU3anLQpaWPShqR1SWuT1iStTlqVtDJpRdLypGVJSzP/rBZl/jktyvxzWpj557Qg889pftKcpGpO5p/T3Mw/p3mZf07zM/+cFmT+OS3M/HNalPnntDjzz2lJ5p/T0sw/p2WZf07LM/+cVmT+Oa3M/HNalfnntDrzz2lN5p/T2sw/p3WZf077M/+cDmT+OR3M/HM6lPnndDjzz+lI5p/T0cw/p2OZf07HM/+cTmT+OZ3M/HM6lfnndDrzz+lM5p/T2cw/p3OZf07nM/+cLmT+OV3M/HO6lPnndDnzz+lK5p/T1cw/p2uZf07XM/+cbmT+Od3M/HO6lfnndDvzz+lO5p/T3cw/p3uZf073M/+cHmT+OT3M/HN6lPnn9Djzz+lJ5p/T08w/p2eZf07PM/+cXmT+Ob3M/HN6mfnn9Crzz+l55p/Ti8w/p5eZf06vMv+cXmf+Ob3J/HN6m/nn9C7zz+l95p/Tx8w/p0+Zf06fM/+cvmT+OX3N/HP6lvnn9CPzz+ln5p/Tr8w/p9+Zf05nMv+czmX+OZ3P/HO6kPnndDHzz+lS5p/T5cw/pyuZf05XM/+crmX+OV3P/HO6kfnndDPzz+lW5p/T7cw/pzuZf053M/+c7mX+Od3P/HN6kPnn9DDzz+lR5p/T48w/pyeZf05PM/+cnmX+OT3P/HN6kfnn9DLzz+lV5p/T68w/pzeZf05vM/+c3mX+Ob3P/HP6mPnn9Cnzz+lz5p/Tl8w/p6+Zf07fMv+cfmT+Of3M/HP6lfnn9Dvzz+lM5p/Tucw/p/OZf04XMv+cLmb+OV3K/HO6nPnndCXzz+lq5p/Ttcw/p+uZf043Mv+cbmb+Od3K/HO6nfnndCfzz+lu5p/Tvcw/p/uZf04PMv+cHmb+OT3K/HN6nPnn9CTzz+lp5p/Ts8w/p+eZf04vMv+cXmb+Ob3K/HN6nfnn9Cbzz+l15p/Tm8w/p7eZf07vMv+c3mf+OX3M/HP6lPnn9Dnzz+lL5p/T18w/p2+Zf04/Mv+cfmb+Of3K/HP6nfnn9B8rxXz7uT/GGQAAAABJRU5ErkJggg==",employee:p.employee||null,agreed:p.agreed||!0},onSave:S,customerName:(s==null?void 0:s.name)||"Customer",documentType:m.type})})]})})]})}function Sg(){const{id:s}=Ia(),[a,o]=b.useState("profile"),[i,u]=b.useState(null),[m,f]=b.useState(!0),[p,y]=b.useState(null);return b.useEffect(()=>{(async()=>{try{f(!0),y(null);const g=await mi.getCustomer(s);console.log("📊 Fetched customer from backend:",g);const v={id:g.id,name:g.name||"Unknown Customer",email:g.email||"",phone:g.phone||"",avatar:g.avatar||"https://ui-avatars.com/api/?name="+encodeURIComponent(g.name||"Unknown"),status:g.status||"active",gender:g.gender||"unknown",joinDate:g.joinDate,lastOrder:g.lastOrder,totalOrders:g.totalOrders||0,address:g.address||{street:"",postalCode:"",city:"",state:"",country:""},customerSince:g.customerSince,lastVisited:g.lastVisited,membership:g.membership,birthdate:g.birthdate,dayPhone:g.dayPhone,nightPhone:g.nightPhone,referredBy:g.referredBy,onlineBooking:g.onlineBooking,creditCard:g.creditCard,bank:g.bank,appointmentsBooked:g.appointmentsBooked||0,classesBooked:g.classesBooked||0,checkIns:g.checkIns||0,pointsEarned:g.pointsEarned||0,amountPaid:g.amountPaid||0,noShowsCancellations:g.noShowsCancellations||0,employeeSeen:g.employeeSeen,...g};u(v)}catch(g){console.error("❌ Failed to fetch customer:",g),y(g.message),console.log("📝 Using fallback mock data for customer ID:",s);const j=[{id:1,name:"John Doe",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/80",status:"active",gender:"male",joinDate:"2023-01-15",lastOrder:"2024-05-20",totalOrders:15,address:{street:"123 Main Street",postalCode:"10001",city:"New York",country:"USA"}},{id:2,name:"Jane Smith",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/80",status:"active",gender:"female",joinDate:"2023-03-22",lastOrder:"2024-06-01",totalOrders:8,address:{street:"456 Oak Avenue",postalCode:"90210",city:"Los Angeles",country:"USA"}},{id:3,name:"Bob Johnson",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/80",status:"inactive",gender:"male",joinDate:"2022-11-10",lastOrder:"2024-01-15",totalOrders:3,address:{street:"789 Pine Road",postalCode:"75001",city:"Dallas",country:"USA"}},{id:4,name:"Alice Brown",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/80",status:"active",gender:"female",joinDate:"2023-08-05",lastOrder:"2024-06-05",totalOrders:22,address:{street:"321 Beach Boulevard",postalCode:"33101",city:"Miami",country:"USA"}},{id:5,name:"Charlie Wilson",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/80",status:"pending",gender:"male",joinDate:"2024-01-20",lastOrder:null,totalOrders:0,address:{street:"654 Capitol Hill",postalCode:"98101",city:"Seattle",country:"USA"}},{id:6,name:"Diana Martinez",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/80",status:"active",gender:"female",joinDate:"2023-05-12",lastOrder:"2024-05-28",totalOrders:11,address:{street:"987 Sunset Strip",postalCode:"90028",city:"Hollywood",country:"USA"}},{id:7,name:"Michael Chen",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/80",status:"active",gender:"male",joinDate:"2023-02-14",lastOrder:"2024-06-10",totalOrders:18,address:{street:"147 Chinatown Street",postalCode:"94108",city:"San Francisco",country:"USA"}},{id:8,name:"Sarah Davis",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/80",status:"inactive",gender:"female",joinDate:"2022-09-30",lastOrder:"2024-02-15",totalOrders:5,address:{street:"258 Music Row",postalCode:"37203",city:"Nashville",country:"USA"}},{id:9,name:"James Rodriguez",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/80",status:"active",gender:"male",joinDate:"2023-07-18",lastOrder:"2024-06-08",totalOrders:13,address:{street:"369 River Walk",postalCode:"78205",city:"San Antonio",country:"USA"}},{id:10,name:"Emma Thompson",email:"<EMAIL>",phone:"+44 20 7946 0958",avatar:"https://via.placeholder.com/80",status:"active",gender:"female",joinDate:"2023-04-03",lastOrder:"2024-05-25",totalOrders:9,address:{street:"42 Baker Street",postalCode:"NW1 6XE",city:"London",country:"UK"}},{id:11,name:"Oliver Garcia",email:"<EMAIL>",phone:"+34 91 123 4567",avatar:"https://via.placeholder.com/80",status:"pending",gender:"male",joinDate:"2024-02-28",lastOrder:null,totalOrders:0,address:{street:"Calle Gran Via 15",postalCode:"28013",city:"Madrid",country:"Spain"}},{id:12,name:"Sophie Dubois",email:"<EMAIL>",phone:"+33 1 42 34 56 78",avatar:"https://via.placeholder.com/80",status:"active",gender:"female",joinDate:"2023-06-11",lastOrder:"2024-06-02",totalOrders:16,address:{street:"125 Champs-Élysées",postalCode:"75008",city:"Paris",country:"France"}},{id:13,name:"Lucas Mueller",email:"<EMAIL>",phone:"+49 30 12345678",avatar:"https://via.placeholder.com/80",status:"active",gender:"male",joinDate:"2023-01-28",lastOrder:"2024-05-30",totalOrders:12,address:{street:"Unter den Linden 77",postalCode:"10117",city:"Berlin",country:"Germany"}},{id:14,name:"Isabella Rossi",email:"<EMAIL>",phone:"+39 06 1234 5678",avatar:"https://via.placeholder.com/80",status:"inactive",gender:"female",joinDate:"2022-12-05",lastOrder:"2024-03-10",totalOrders:7,address:{street:"Via del Corso 234",postalCode:"00187",city:"Rome",country:"Italy"}},{id:15,name:"Hiroshi Tanaka",email:"<EMAIL>",phone:"+81 3 1234 5678",avatar:"https://via.placeholder.com/80",status:"active",gender:"male",joinDate:"2023-03-15",lastOrder:"2024-06-07",totalOrders:20,address:{street:"1-1-1 Shibuya",postalCode:"150-0002",city:"Tokyo",country:"Japan"}},{id:16,name:"Priya Sharma",email:"<EMAIL>",phone:"+91 11 2345 6789",avatar:"https://via.placeholder.com/80",status:"active",gender:"female",joinDate:"2023-05-20",lastOrder:"2024-06-03",totalOrders:14,address:{street:"Connaught Place 45",postalCode:"110001",city:"New Delhi",country:"India"}},{id:17,name:"Alex Kim",email:"<EMAIL>",phone:"+82 2 1234 5678",avatar:"https://via.placeholder.com/80",status:"pending",gender:"other",joinDate:"2024-03-10",lastOrder:null,totalOrders:0,address:{street:"Gangnam-gu 123",postalCode:"06028",city:"Seoul",country:"South Korea"}},{id:18,name:"Maria Silva",email:"<EMAIL>",phone:"+55 11 9876 5432",avatar:"https://via.placeholder.com/80",status:"active",gender:"female",joinDate:"2023-04-25",lastOrder:"2024-05-22",totalOrders:10,address:{street:"Avenida Paulista 1578",postalCode:"01310-200",city:"São Paulo",country:"Brazil"}},{id:19,name:"Ahmed Hassan",email:"<EMAIL>",phone:"+971 4 123 4567",avatar:"https://via.placeholder.com/80",status:"active",gender:"male",joinDate:"2023-02-08",lastOrder:"2024-06-09",totalOrders:17,address:{street:"Sheikh Zayed Road 789",postalCode:"12345",city:"Dubai",country:"UAE"}},{id:20,name:"Rachel Green",email:"<EMAIL>",phone:"+****************",avatar:"https://via.placeholder.com/80",status:"inactive",gender:"female",joinDate:"2022-08-14",lastOrder:"2024-01-30",totalOrders:4,address:{street:"90 Bedford Street",postalCode:"10014",city:"New York",country:"USA"}},{id:21,name:"Connor O'Brien",email:"<EMAIL>",phone:"+353 1 234 5678",avatar:"https://via.placeholder.com/80",status:"active",gender:"male",joinDate:"2023-09-12",lastOrder:"2024-06-04",totalOrders:6,address:{street:"Temple Bar 12",postalCode:"D02 YD30",city:"Dublin",country:"Ireland"}},{id:22,name:"Zara Al-Rashid",email:"<EMAIL>",phone:"+966 11 234 5678",avatar:"https://via.placeholder.com/80",status:"active",gender:"female",joinDate:"2023-07-30",lastOrder:"2024-05-15",totalOrders:8,address:{street:"King Fahd Road 456",postalCode:"11564",city:"Riyadh",country:"Saudi Arabia"}},{id:23,name:"Nicolas Petrov",email:"<EMAIL>",phone:"****** 123 4567",avatar:"https://via.placeholder.com/80",status:"pending",gender:"male",joinDate:"2024-04-15",lastOrder:null,totalOrders:0,address:{street:"Red Square 1",postalCode:"109012",city:"Moscow",country:"Russia"}},{id:24,name:"Amelia Watson",email:"<EMAIL>",phone:"+61 2 9876 5432",avatar:"https://via.placeholder.com/80",status:"active",gender:"female",joinDate:"2023-10-22",lastOrder:"2024-06-06",totalOrders:19,address:{street:"88 Harbour Bridge Road",postalCode:"2000",city:"Sydney",country:"Australia"}},{id:25,name:"Erik Andersson",email:"<EMAIL>",phone:"+46 8 123 456 78",avatar:"https://via.placeholder.com/80",status:"active",gender:"male",joinDate:"2023-01-05",lastOrder:"2024-05-18",totalOrders:21,address:{street:"Gamla Stan 15",postalCode:"111 29",city:"Stockholm",country:"Sweden"}}].find(P=>P.id===Number(s));u(j)}finally{f(!1)}})()},[s]),m?r.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:r.jsxs("div",{className:"max-w-md w-full bg-white rounded-xl shadow-sm p-8 text-center",children:[r.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"}),r.jsx("h1",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Loading customer..."}),r.jsx("p",{className:"text-gray-600",children:"Fetching customer profile from backend"})]})}):i?r.jsx("div",{className:"min-h-screen bg-gray-50",children:r.jsxs("div",{className:"max-w-4xl mx-auto px-4 py-8",children:[r.jsx("nav",{className:"mb-6",children:r.jsxs(ot,{to:"/customers/management",className:"inline-flex items-center text-gray-600 hover:text-gray-900 transition-colors",children:[r.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Customer Management"]})}),r.jsx(tg,{customer:i}),r.jsx("div",{className:"bg-white rounded-xl shadow-sm mb-6",children:r.jsx("div",{className:"border-b border-gray-200",children:r.jsxs("nav",{className:"flex space-x-8 px-8",children:[r.jsx("button",{onClick:()=>o("profile"),className:`py-4 px-1 border-b-2 font-medium text-sm ${a==="profile"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Profile"}),r.jsx("button",{onClick:()=>o("appointments"),className:`py-4 px-1 border-b-2 font-medium text-sm ${a==="appointments"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Appointments"}),r.jsx("button",{onClick:()=>o("forms"),className:`py-4 px-1 border-b-2 font-medium text-sm ${a==="forms"?"border-blue-500 text-blue-600":"border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"}`,children:"Forms"})]})})}),a==="profile"&&r.jsxs("div",{className:"space-y-6",children:[r.jsx(rg,{customer:i}),r.jsx(ng,{customer:i})]}),a==="appointments"&&r.jsx(xg,{customer:i}),a==="forms"&&r.jsx(kg,{customer:i})]})}):r.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:r.jsxs("div",{className:"max-w-md w-full bg-white rounded-xl shadow-sm p-8 text-center",children:[r.jsx("div",{className:"w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4",children:r.jsx("svg",{className:"w-8 h-8 text-gray-400",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"})})}),r.jsx("h1",{className:"text-xl font-semibold text-gray-900 mb-2",children:"Customer not found"}),r.jsx("p",{className:"text-gray-600 mb-2",children:"The customer profile you're looking for doesn't exist or has been removed."}),p&&r.jsxs("p",{className:"text-sm text-red-600 mb-4",children:["Error: ",p]}),r.jsxs(ot,{to:"/customers/management",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:[r.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M10 19l-7-7m0 0l7-7m-7 7h18"})}),"Back to Customer Management"]})]})})}function Cg(){return r.jsxs("div",{className:"container py-10",children:[r.jsx("h1",{className:"text-3xl mb-6",children:"Marketing"}),r.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:r.jsx("p",{className:"text-gray-600",children:"Marketing tools and campaigns will be managed here."})})]})}function Om({open:s,status:a,onClose:o}){if(!s)return null;b.useEffect(()=>(document.body.style.overflow="hidden",()=>{document.body.style.overflow="auto"}),[]);const i=a==="Published",u=a==="Deleted";let m,f,p;return u?(m="Form Deleted!",f="The form has been successfully deleted.",p={bg:"bg-red-100",icon:"text-red-600",heading:"text-red-700"}):i?(m="Form Published!",f="Your form has been successfully published and is now live.",p={bg:"bg-green-100",icon:"text-green-600",heading:"text-green-700"}):(m="Draft Saved!",f="Your form draft has been saved. You can continue editing it anytime.",p={bg:"bg-green-100",icon:"text-green-600",heading:"text-green-700"}),r.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 backdrop-blur-sm",children:r.jsxs("div",{className:"bg-white rounded-xl shadow-2xl w-11/12 max-w-md p-8 transform transition-all duration-300",children:[r.jsx("div",{className:`flex items-center justify-center w-16 h-16 mx-auto mb-4 ${p.bg} rounded-full`,children:r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:`h-8 w-8 ${p.icon}`,fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M5 13l4 4L19 7"})})}),r.jsx("h3",{className:`text-2xl font-semibold text-center ${p.heading} mb-2`,children:m}),r.jsx("p",{className:"text-gray-600 text-center mb-6",children:f}),r.jsx("div",{className:"flex justify-center gap-3",children:r.jsx("button",{onClick:o,className:"px-6 py-2 bg-blue-600 text-white rounded shadow hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-400",children:"OK"})})]})})}function Eg(s){const a={Published:"bg-blue-100 text-blue-800",Draft:"bg-gray-100 text-gray-800"};return r.jsx("span",{className:`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${a[s]||a.Draft}`,children:s})}function Pg(){const{forms:s,loading:a,error:o,fetchTemplates:i,addForm:u,updateForm:m,deleteForm:f}=Ja(),[p,y]=b.useState(null),[x,g]=b.useState(!1),[v,j]=b.useState("All"),[P,k]=b.useState(""),[E,S]=b.useState(!1),[_,B]=b.useState({show:!1,id:null}),H=Zt(),F=D=>{y(p===D?null:D)},I=async D=>{B({show:!0,id:D})},z=async()=>{try{await f(_.id),B({show:!1,id:null}),S(!0)}catch(D){alert("Failed to delete form: "+D.message),B({show:!1,id:null})}},U=D=>{H(`/forms/edit/${D}`)},oe=D=>{const q={...D,name:D.name+" (Copy)",status:"Draft"};u(q)},fe=()=>{i()},Z=async()=>{try{k("Testing connection...");try{const D=await Be.get("http://localhost:8000/api/v1/forms/templates/",{timeout:5e3,headers:{Accept:"application/json","Content-Type":"application/json"}});k(q=>q+`
Django API (8000): `+JSON.stringify(D.data).substring(0,100)+"...")}catch(D){k(q=>q+`
Django API (8000) error: `+D.message+(D.response?` (Status: ${D.response.status})`:""))}try{const D=await Ot.get("/forms/templates/",{timeout:5e3,headers:{Accept:"application/json","Content-Type":"application/json"}});k(q=>q+`
Proxy API: `+JSON.stringify(D.data).substring(0,100)+"...")}catch(D){k(q=>q+`
Proxy API error: `+D.message+(D.response?` (Status: ${D.response.status})`:""))}try{const D=await Be.get("http://localhost:8000/admin/",{timeout:5e3});k(q=>q+`
Django Admin accessible: Yes`)}catch(D){k(q=>q+`
Django Admin error: `+D.message)}}catch(D){k("Debug error: "+D.message)}},re=[{id:"blank",name:"Blank Form",icon:r.jsxs("svg",{className:"w-8 h-8 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[r.jsx("rect",{x:"5",y:"4",width:"14",height:"16",rx:"2",strokeWidth:"2"}),r.jsx("path",{d:"M9 2v4h6V2",strokeWidth:"2"})]})},...s.map(D=>({id:D.id,name:D.name,icon:r.jsx("svg",{className:"w-8 h-8 mx-auto mb-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("rect",{x:"5",y:"4",width:"14",height:"16",rx:"2",strokeWidth:"2"})})}))],de=D=>{g(!1),D.id==="blank"?H("/forms/edit/new",{state:{template:"blank"}}):H("/forms/edit/new",{state:{templateId:D.id}})},ie=D=>{H(`/forms/details/${D.id}`)},J=v==="All"?s:s.filter(D=>D.status===v);return a?r.jsx("div",{className:"bg-white rounded-lg shadow-md p-6 flex justify-center items-center h-64",children:r.jsxs("div",{className:"text-center",children:[r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mx-auto mb-4"}),r.jsx("p",{className:"text-gray-600",children:"Loading forms..."})]})}):o?r.jsx("div",{className:"bg-white rounded-lg shadow-md p-6",children:r.jsxs("div",{className:"text-center text-red-600 p-4",children:[r.jsx("p",{children:o}),r.jsxs("div",{className:"flex justify-center gap-4 mt-4",children:[r.jsx("button",{onClick:fe,className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Try Again"}),r.jsx("button",{onClick:Z,className:"px-4 py-2 bg-gray-600 text-white rounded hover:bg-gray-700",children:"Debug Connection"})]}),P&&r.jsx("div",{className:"mt-4 p-4 bg-gray-100 text-left text-xs font-mono whitespace-pre-wrap rounded",children:P})]})}):r.jsxs("div",{className:"bg-white rounded-lg shadow-md p-6",children:[r.jsxs("div",{className:"flex items-center justify-between mb-4",children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-900",children:"Forms"}),r.jsxs("div",{className:"flex items-center gap-4",children:[r.jsxs("select",{className:"border border-gray-300 rounded px-3 py-2 text-sm",value:v,onChange:D=>j(D.target.value),children:[r.jsx("option",{value:"All",children:"All"}),r.jsx("option",{value:"Draft",children:"Draft"}),r.jsx("option",{value:"Published",children:"Published"})]}),r.jsx("button",{className:"bg-blue-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-blue-700 transition-colors shadow-sm",type:"button",onClick:()=>g(!0),children:"+ Add New Form"})]})]}),P&&r.jsx("div",{className:"mb-4 p-4 bg-gray-100 text-left text-xs font-mono whitespace-pre-wrap rounded",children:P}),s.length===0?r.jsx("div",{className:"text-center py-8 text-gray-500",children:r.jsx("p",{children:'No forms found. Click "Add New Form" to create one.'})}):r.jsxs("table",{className:"min-w-full divide-y divide-gray-200",children:[r.jsx("thead",{className:"bg-gray-50",children:r.jsxs("tr",{children:[r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Form Name"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Document Type"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created Date"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),r.jsx("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),r.jsx("tbody",{className:"bg-white divide-y divide-gray-200",children:J.map(D=>r.jsxs("tr",{className:"align-middle hover:bg-gray-50 transition-colors",children:[r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-left align-middle",children:r.jsx("a",{href:"#",className:"text-blue-600 hover:underline",onClick:q=>{q.preventDefault(),ie(D)},children:D.name})}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-left align-middle",children:D.documentType}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900 text-left align-middle",children:D.createdDate?new Date(D.createdDate).toLocaleDateString():""}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-left align-middle",children:Eg(D.status)}),r.jsx("td",{className:"px-6 py-4 whitespace-nowrap text-sm align-middle",children:r.jsxs("div",{className:"flex items-center space-x-2 relative",children:[r.jsx("button",{className:"text-blue-600 hover:underline font-medium px-2 py-1 rounded",title:"Edit",onClick:()=>U(D.id),children:"Edit"}),r.jsx("button",{className:"text-red-600 hover:underline font-medium px-2 py-1 rounded",title:"Delete",onClick:()=>I(D.id),children:"Delete"}),r.jsx("button",{className:"text-gray-700 hover:underline font-medium px-2 py-1 rounded",title:"Duplicate",onClick:()=>oe(D),children:"Duplicate"}),r.jsxs("div",{className:"relative",children:[r.jsx("button",{onClick:()=>F(D.id),className:"p-1 text-gray-400 hover:text-gray-700 hover:bg-gray-100 rounded",title:"More",type:"button",children:r.jsxs("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[r.jsx("circle",{cx:"12",cy:"12",r:"1.5"}),r.jsx("circle",{cx:"19",cy:"12",r:"1.5"}),r.jsx("circle",{cx:"5",cy:"12",r:"1.5"})]})}),p===D.id&&r.jsx("div",{className:"absolute right-0 mt-2 w-48 bg-white border border-gray-200 rounded-lg shadow-lg z-20",children:r.jsx("button",{className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",type:"button",onClick:()=>ie(D),children:"Template Details"})})]})]})})]},D.id))})]}),x&&r.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",children:r.jsxs("div",{className:"bg-white rounded-lg shadow-xl p-6 w-full max-w-2xl",children:[r.jsxs("div",{className:"flex justify-between items-center mb-4",children:[r.jsx("h3",{className:"text-lg font-medium",children:"Create New Form"}),r.jsx("button",{onClick:()=>g(!1),className:"text-gray-400 hover:text-gray-600",children:r.jsx("svg",{className:"w-6 h-6",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"M6 18L18 6M6 6l12 12"})})})]}),r.jsxs("div",{className:"mb-4",children:[r.jsx("p",{className:"text-gray-600 mb-4",children:"Choose a template to get started or create a blank form."}),r.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4",children:re.map(D=>r.jsxs("button",{className:"bg-white border border-gray-200 rounded-lg p-4 hover:border-blue-500 hover:shadow-md transition-all text-center",onClick:()=>de(D),children:[D.icon,r.jsx("div",{className:"text-sm font-medium",children:D.name})]},D.id))})]})]})}),E&&r.jsx(Om,{open:E,status:"Deleted",onClose:()=>S(!1)}),_.show&&r.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-40 backdrop-blur-sm",children:r.jsxs("div",{className:"bg-white rounded-xl shadow-2xl w-11/12 max-w-md p-8 transform transition-all duration-300",children:[r.jsx("div",{className:"flex items-center justify-center w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full",children:r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"})})}),r.jsx("h3",{className:"text-2xl font-semibold text-center text-red-700 mb-2",children:"Delete Form"}),r.jsx("p",{className:"text-gray-600 text-center mb-6",children:"Are you sure you want to delete this form?"}),r.jsxs("div",{className:"flex justify-center gap-3",children:[r.jsx("button",{onClick:()=>B({show:!1,id:null}),className:"px-6 py-2 bg-gray-300 text-gray-700 rounded shadow hover:bg-gray-400 focus:outline-none focus:ring-2 focus:ring-gray-400",children:"Cancel"}),r.jsx("button",{onClick:z,className:"px-6 py-2 bg-red-600 text-white rounded shadow hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-red-400",children:"Delete"})]})]})})]})}const Dg=[{id:"short",label:"Short Answer",icon:"📝"},{id:"long",label:"Long Answer",icon:"📄"},{id:"multiple",label:"Multiple Choice",icon:"☑️"},{id:"single",label:"Single Choice",icon:"⭕"},{id:"date",label:"Date",icon:"📅"},{id:"file",label:"File Upload",icon:"📎"}];function Tg({question:s,index:a,onUpdate:o,onDelete:i}){var u;return r.jsx("div",{className:"bg-white p-4 rounded-lg shadow-sm border border-gray-200 mb-4",children:r.jsxs("div",{className:"flex items-center gap-4",children:[r.jsxs("div",{className:"flex-1",children:[r.jsx("input",{type:"text",value:s.label,onChange:m=>o(a,{...s,label:m.target.value}),className:"w-full p-2 border rounded",placeholder:"Question label"}),s.type==="multiple"||s.type==="single"?r.jsxs("div",{className:"mt-2",children:[(u=s.options)==null?void 0:u.map((m,f)=>r.jsxs("div",{className:"flex items-center gap-2 mt-1",children:[r.jsx("input",{type:"text",value:m,onChange:p=>{const y=[...s.options];y[f]=p.target.value,o(a,{...s,options:y})},className:"flex-1 p-2 border rounded",placeholder:`Option ${f+1}`}),r.jsx("button",{onClick:()=>{const p=s.options.filter((y,x)=>x!==f);o(a,{...s,options:p})},className:"text-red-500 hover:text-red-700",children:"×"})]},f)),r.jsx("button",{onClick:()=>{const m=[...s.options||[],""];o(a,{...s,options:m})},className:"mt-2 text-blue-500 hover:text-blue-700",children:"+ Add Option"})]}):null]}),r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsxs("label",{className:"flex items-center gap-2",children:[r.jsx("input",{type:"checkbox",checked:s.required,onChange:m=>o(a,{...s,required:m.target.checked}),className:"rounded"}),"Required"]}),r.jsx("button",{onClick:()=>i(a),className:"text-red-500 hover:text-red-700 p-2",title:"Delete question",children:"×"})]})]})})}function Lg({type:s,onClick:a}){return r.jsx("button",{onClick:()=>a(s),className:"w-full bg-white p-4 rounded-lg shadow-sm border border-gray-200 hover:bg-gray-50 transition-all duration-200 text-left",children:r.jsxs("div",{className:"flex items-center gap-3",children:[r.jsx("span",{className:"text-2xl",children:s.icon}),r.jsx("span",{className:"font-medium",children:s.label})]})})}function Og({formId:s}){const{forms:a,updateForm:o}=Ja(),i=a.find(x=>x.id===s),[u,m]=b.useState((i==null?void 0:i.questions)||[]),f=x=>{const g={id:Date.now(),type:x.id,label:`New ${x.label}`,required:!1,options:x.id==="multiple"||x.id==="single"?[""]:void 0},v=[...u,g];m(v),o(s,{questions:v})},p=(x,g)=>{const v=[...u];v[x]=g,m(v),o(s,{questions:v})},y=x=>{const g=u.filter((v,j)=>j!==x);m(g),o(s,{questions:g,name:i==null?void 0:i.name,documentType:i==null?void 0:i.documentType,status:(i==null?void 0:i.status)||"Draft"})};return r.jsx("div",{className:"container mx-auto p-6",children:r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[r.jsxs("div",{className:"md:col-span-1",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Question Types"}),r.jsx("div",{className:"space-y-4",children:Dg.map(x=>r.jsx(Lg,{type:x,onClick:f},x.id))})]}),r.jsxs("div",{className:"md:col-span-3",children:[r.jsx("h2",{className:"text-xl font-semibold mb-4",children:"Form Questions"}),r.jsxs("div",{className:"space-y-4",children:[u.map((x,g)=>r.jsx(Tg,{question:x,index:g,onUpdate:p,onDelete:y},x.id)),u.length===0&&r.jsx("div",{className:"text-center py-8 text-gray-500 border-2 border-dashed border-gray-300 rounded-lg",children:"Click on a question type to add it to your form"})]})]})]})})}function Mg(){const{id:s}=Ia();return r.jsxs("div",{className:"container py-10",children:[r.jsx("h1",{className:"text-3xl mb-6",children:"Forms"}),s?r.jsx(Og,{formId:parseInt(s)}):r.jsx(Pg,{})]})}const Rg=({children:s,initialMode:a="mobile"})=>{const[o,i]=b.useState(a);b.useEffect(()=>{i(a)},[a]);const u=()=>r.jsx("div",{className:"mx-auto w-[430px]",children:r.jsxs("div",{className:"relative rounded-[40px] bg-white border border-gray-200 shadow-xl overflow-hidden",style:{boxShadow:"0 10px 25px -5px rgba(0, 0, 0, 0.15), 0 5px 12px -3px rgba(0, 0, 0, 0.1)"},children:[r.jsx("div",{className:"absolute top-0 left-1/2 -translate-x-1/2 w-[40%] h-7 bg-black rounded-b-2xl z-10"}),r.jsx("div",{className:"w-full h-10 bg-black"}),r.jsx("div",{className:"h-[700px] overflow-y-auto text-[15px] leading-6",style:{paddingTop:"1.5rem",paddingLeft:"1.75rem",paddingRight:"1.75rem",paddingBottom:"2rem"},children:s}),r.jsx("div",{className:"w-full h-10 bg-black flex items-center justify-center",children:r.jsx("div",{className:"w-[30%] h-1 bg-gray-400 rounded-full"})})]})}),m=()=>r.jsx("div",{className:"w-full",children:s});return r.jsx("section",{children:r.jsx("div",{children:o==="mobile"?u():m()})})},Rd=[{type:"short",label:"Short Answer",icon:r.jsxs("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[r.jsx("line",{x1:"4",y1:"8",x2:"20",y2:"8"}),r.jsx("line",{x1:"4",y1:"16",x2:"14",y2:"16"})]})},{type:"long",label:"Long Answer",icon:r.jsxs("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[r.jsx("line",{x1:"4",y1:"6",x2:"20",y2:"6"}),r.jsx("line",{x1:"4",y1:"12",x2:"20",y2:"12"}),r.jsx("line",{x1:"4",y1:"18",x2:"14",y2:"18"})]})},{type:"multiple",label:"Multiple Choice",icon:r.jsxs("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[r.jsx("rect",{x:"4",y:"4",width:"16",height:"16",rx:"2"}),r.jsx("polyline",{points:"8,12 11,15 16,10"})]})},{type:"text",label:"Text",icon:r.jsxs("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",children:[r.jsx("line",{x1:"4",y1:"6",x2:"20",y2:"6"}),r.jsx("line",{x1:"4",y1:"12",x2:"20",y2:"12"}),r.jsx("line",{x1:"4",y1:"18",x2:"20",y2:"18"})]})},{type:"signature",label:"Signature",icon:r.jsxs("svg",{width:"24",height:"24",fill:"none",stroke:"currentColor",strokeWidth:"2",viewBox:"0 0 24 24",children:[r.jsx("path",{d:"M3 17c2 0 3-3 5-3s3 3 5 3 3-3 5-3 3 3 3 3"}),r.jsx("path",{d:"M9 13c.5-1.5 2.5-1.5 3 0"}),r.jsx("path",{d:"M15 13c.5-1.5 2.5-1.5 3 0"})]})}];function Ag({qt:s}){const a=o=>{o.dataTransfer.setData("questionType",JSON.stringify(s)),o.dataTransfer.effectAllowed="copy"};return r.jsxs("div",{draggable:!0,onDragStart:a,className:"flex flex-col items-center justify-center p-4 bg-white border border-gray-200 rounded-lg shadow-sm hover:bg-blue-50 transition-all cursor-move select-none",style:{minHeight:"90px"},children:[r.jsx("div",{className:"mb-2 text-2xl text-gray-700",children:s.icon}),r.jsx("div",{className:"text-sm font-medium text-gray-900 text-center",children:s.label})]})}function _g(){const{id:s}=Ia(),a=Gt(),o=Zt(),{forms:i,addForm:u,updateForm:m}=Ja();let f="Untitled Form",p=[],y="",x="Super Admin";new Date().toISOString().slice(0,10);let g=!1,v=!1,j="";if(s&&s!=="new"){const A=i.find(le=>le.id===parseInt(s,10));A&&(f=A.name,p=A.questions||[],y=A.documentType,x=A.createdBy,A.createdDate,g=A.mandatory||!1,v=A.notify||!1,j=A.expiration||"")}else if(a.state&&a.state.templateId){const A=i.find(le=>le.id===a.state.templateId);A&&(f=A.name,p=(A.questions||[]).map((le,we)=>({id:we+1,type:le.type,label:"",options:le.type==="multiple"?["Option 1"]:void 0,required:!1,...le.type==="text"?{textStyle:"Heading",textContent:""}:{}})),y=A.documentType,x=A.createdBy,new Date().toISOString().slice(0,10),g=A.mandatory||!1,v=A.notify||!1,j=A.expiration||"")}const[P,k]=b.useState(p),[E,S]=b.useState(f),[_,B]=b.useState(g),[H,F]=b.useState(v),[I,z]=b.useState(j),[U,oe]=b.useState("design"),[fe,Z]=b.useState(null),[re,de]=b.useState(null),[ie,J]=b.useState(null),[D,q]=b.useState(!1),[me,Y]=b.useState(null),[ee,X]=b.useState({visible:!1,status:""}),T=b.useRef(null);b.useEffect(()=>{if(s&&s!=="new"){const A=i.find(le=>le.id===parseInt(s,10));A&&A.questions&&(k(A.questions),S(A.name),B(A.mandatory||!1),F(A.notify||!1),z(A.expiration||""))}},[i,s]),b.useEffect(()=>{function A(le){T.current&&!T.current.contains(le.target)&&J(null)}return ie!==null?document.addEventListener("mousedown",A):document.removeEventListener("mousedown",A),()=>{document.removeEventListener("mousedown",A)}},[ie]);const $=A=>{A.preventDefault(),A.dataTransfer.dropEffect="copy"},xe=A=>{A.preventDefault();const le=A.dataTransfer.getData("questionType");if(le){const we=JSON.parse(le),ge=P.length?Math.max(...P.map(Le=>Le.id))+1:1;k([...P,{id:ge,type:we.type,label:"",options:we.type==="multiple"?["Option 1"]:void 0,required:!1,...we.type==="text"?{textStyle:"Heading",textContent:""}:{}}]);return}if(fe!==null&&re!==null&&fe!==re){const we=P.findIndex(Le=>Le.id===fe),ge=P.findIndex(Le=>Le.id===re);if(we!==-1&&ge!==-1){const Le=[...P],[Me]=Le.splice(we,1);Le.splice(ge,0,Me),k(Le)}}Z(null),de(null)},pe=(A,le,we)=>{k(P.map(ge=>ge.id===A?{...ge,[le]:we}:ge))},je=(A,le,we)=>{k(P.map(ge=>ge.id===A?{...ge,options:ge.options.map((Le,Me)=>Me===le?we:Le)}:ge))},be=A=>{k(P.map(le=>le.id===A?{...le,options:[...le.options,`Option ${le.options.length+1}`]}:le))},De=A=>{const le=P.filter(we=>we.id!==A);k(le),J(null),s&&s!=="new"&&m(parseInt(s,10),{name:E,documentType:y||"Form",questions:le,status:"Draft",mandatory:_,notify:H,expiration:I})},Se=A=>{const le=P.findIndex(Me=>Me.id===A);if(le===-1)return;const we=Math.max(...P.map(Me=>Me.id))+1,ge={...P[le],id:we},Le=[...P.slice(0,le+1),ge,...P.slice(le+1)];k(Le),s&&s!=="new"&&m(parseInt(s,10),{questions:Le})},Te=async A=>{try{q(!0),Y(null);const le=A==="Draft"?"Draft":"Published";if(console.log("Saving form with questions:",P),s&&s!=="new")console.log(`Updating form ${s} with status ${le}`),await m(parseInt(s,10),{name:E,documentType:y||"Form",questions:P,status:le,mandatory:_,notify:H,expiration:I});else{const we=i.length?Math.max(...i.map(ge=>ge.id))+1:1;console.log(`Creating new form with ID ${we} and status ${le}`),await u({id:we,name:E,documentType:y||"Form",createdDate:new Date().toISOString().slice(0,10),status:le,createdBy:x,questions:P,mandatory:_,notify:H,expiration:I})}if(le==="Published")try{const ge=`${E.replace(/[^a-z0-9]/gi,"_").toLowerCase()||"form"}_${Date.now()}.pdf`;await Ng(E,P,ge,{documentType:y||"Form"}),console.log("Form PDF generated and upload initiated")}catch(we){console.error("Failed to generate/upload form PDF:",we)}q(!1),X({visible:!0,status:A})}catch(le){console.error("Error saving form:",le),Y(`Failed to save form: ${le.message}`),q(!1)}},nt=A=>le=>{le.stopPropagation(),Z(A),le.dataTransfer.effectAllowed="move",le.dataTransfer.setData("text/plain",String(A))},Wr=A=>le=>{le.preventDefault(),le.stopPropagation(),de(A),le.dataTransfer.dropEffect="move"},ys=A=>le=>{le.preventDefault(),le.stopPropagation();const we=Number(le.dataTransfer.getData("text/plain"));if(we&&we!==A){const ge=P.findIndex(Me=>Me.id===we),Le=P.findIndex(Me=>Me.id===A);if(ge!==-1&&Le!==-1){const Me=[...P],[At]=Me.splice(ge,1);Me.splice(Le,0,At),k(Me),s&&s!=="new"&&m(parseInt(s,10),{questions:Me})}}Z(null),de(null)},Hr=A=>{A.preventDefault(),A.stopPropagation(),Z(null),de(null)};return r.jsxs(r.Fragment,{children:[r.jsx(Om,{open:ee.visible,status:ee.status,onClose:()=>{X({visible:!1,status:""}),o("/forms")}}),r.jsxs("div",{className:"flex max-w-5xl mx-auto py-10",children:[r.jsxs("div",{className:"w-56 min-w-[14rem] max-w-[14rem] pr-6 border-r flex-shrink-0 sticky top-20 h-[calc(100vh-5rem)] overflow-y-auto",children:[r.jsx("div",{className:"font-semibold mb-4",children:"Add Question"}),r.jsx("div",{className:"grid grid-cols-2 gap-3",children:Rd.map(A=>r.jsx(Ag,{qt:A},A.type))})]}),r.jsxs("div",{className:"flex-1 px-8 min-w-0",onDragOver:$,onDrop:xe,children:[r.jsxs("div",{className:"mb-8 flex items-center space-x-3",children:[r.jsx("input",{className:"text-2xl font-bold border-b border-gray-300 focus:border-blue-500 outline-none flex-1",value:E,onChange:A=>S(A.target.value)}),r.jsxs("div",{className:"flex items-center gap-2 ml-4",children:[r.jsx("button",{type:"button",onClick:()=>oe("design"),className:`px-3 py-2 rounded text-sm font-medium transition-colors ${U==="design"?"bg-blue-600 text-white":"bg-gray-100 hover:bg-blue-100"}`,children:"Design"}),r.jsx("button",{type:"button",onClick:()=>oe("preview-desktop"),className:`px-3 py-2 rounded text-sm font-medium transition-colors ${U==="preview-desktop"?"bg-blue-600 text-white":"bg-gray-100 hover:bg-blue-100"}`,children:"🖥️ Desktop Preview"}),r.jsx("button",{type:"button",onClick:()=>oe("preview-mobile"),className:`px-3 py-2 rounded text-sm font-medium transition-colors ${U==="preview-mobile"?"bg-blue-600 text-white":"bg-gray-100 hover:bg-blue-100"}`,children:"📱 Mobile Preview"})]})]}),U==="design"?r.jsxs("div",{className:"space-y-6",children:[P.map((A,le)=>{var we;return r.jsxs("div",{className:`bg-white border rounded p-0 shadow-sm relative ${re===A.id&&fe!==null?"ring-2 ring-blue-400":""}`,onDragOver:Wr(A.id),onDrop:ys(A.id),onDragEnd:Hr,children:[r.jsxs("div",{className:"p-4",children:[r.jsxs("div",{className:"flex items-center mb-2",children:[r.jsxs("span",{className:"font-semibold mr-2",children:["Q",le+1,"."]}),r.jsx("span",{className:"px-2 py-1 bg-gray-100 rounded text-sm mr-2",children:((we=Rd.find(ge=>ge.type===A.type))==null?void 0:we.label)||A.type}),A.type!=="text"&&A.type!=="signature"&&r.jsx("input",{className:"flex-1 border-b border-gray-200 focus:border-blue-400 outline-none text-sm",placeholder:"Ask your question",value:A.label,onChange:ge=>pe(A.id,"label",ge.target.value)})]}),A.type==="text"&&r.jsxs("div",{className:"space-y-2 mt-2",children:[r.jsxs("select",{className:"w-full border rounded px-2 py-2 text-sm",value:A.textStyle||"Heading",onChange:ge=>pe(A.id,"textStyle",ge.target.value),children:[r.jsx("option",{value:"Heading",children:"Heading"}),r.jsx("option",{value:"Paragraph",children:"Paragraph"}),r.jsx("option",{value:"Subheading",children:"Subheading"})]}),r.jsx("input",{className:"w-full border rounded px-2 py-2 text-sm",placeholder:"Enter your text here",value:A.textContent||"",onChange:ge=>pe(A.id,"textContent",ge.target.value)})]}),A.type==="short"&&r.jsx("input",{className:"w-full border rounded px-2 py-2 text-sm mt-2",placeholder:"Answer",disabled:!0}),A.type==="long"&&r.jsx("textarea",{className:"w-full border rounded px-2 py-2 text-sm mt-2",placeholder:"Answer",rows:3,disabled:!0}),A.type==="multiple"&&r.jsxs("div",{className:"space-y-2 mt-2",children:[A.options.map((ge,Le)=>r.jsx("input",{className:"w-full border rounded px-2 py-2 text-sm",value:ge,placeholder:`Option ${Le+1}`,onChange:Me=>je(A.id,Le,Me.target.value)},Le)),r.jsx("button",{className:"text-blue-500 hover:underline text-xs mt-1",onClick:()=>be(A.id),type:"button",children:"+ Add an option"})]}),A.type==="signature"&&r.jsx("div",{className:"mt-2",children:r.jsx(Lm,{initialData:A.signatureData||{},onSave:ge=>{pe(A.id,"signatureData",ge)}})})]}),r.jsxs("div",{className:"flex items-center justify-between border-t px-4 py-2 bg-gray-50 rounded-b",children:[r.jsx("div",{className:"cursor-move text-2xl text-gray-400 hover:text-blue-500 select-none",title:"Drag to reorder",draggable:!0,onDragStart:nt(A.id),style:{width:"32px",display:"flex",alignItems:"center",justifyContent:"center"},children:r.jsx("span",{style:{fontFamily:"monospace",fontWeight:"bold",fontSize:"20px",lineHeight:"16px"},children:"≡"})}),r.jsxs("div",{className:"flex items-center gap-4",children:[r.jsxs("label",{className:"flex items-center text-xs font-medium cursor-pointer",children:[r.jsx("input",{type:"checkbox",checked:A.required,onChange:ge=>pe(A.id,"required",ge.target.checked),className:"mr-1"}),"Required"]}),r.jsxs("div",{className:"relative",ref:ie===A.id?T:null,children:[r.jsx("button",{className:"text-gray-400 hover:text-gray-700 text-lg px-2",type:"button",onClick:()=>J(ie===A.id?null:A.id),children:"⋮"}),ie===A.id&&r.jsxs("div",{className:"absolute right-0 mt-2 w-32 bg-white border border-gray-200 rounded-lg shadow-lg z-20",children:[r.jsx("button",{className:"block w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100",type:"button",onClick:()=>{Se(A.id),J(null)},children:"Duplicate"}),r.jsx("button",{className:"block w-full text-left px-4 py-2 text-sm text-red-600 hover:bg-gray-100",type:"button",onClick:()=>{De(A.id),J(null)},children:"Delete"})]})]})]})]})]},A.id)}),P.length===0&&r.jsx("div",{className:"text-gray-400 text-center py-8 border-2 border-dashed border-gray-300 rounded-lg",onDragOver:$,onDrop:xe,children:"Drag and drop a question type here to add it to your form"})]}):r.jsx(Rg,{initialMode:U==="preview-mobile"?"mobile":"desktop",children:r.jsxs("div",{children:[r.jsx("h2",{className:"text-3xl font-bold mb-2",children:E}),r.jsx("hr",{className:"border-b border-gray-300 mb-6"}),r.jsx("div",{className:"space-y-6",children:P.map((A,le)=>{var Le,Me,At;const we=A.type==="text",ge=A.type==="signature";return r.jsxs("div",{className:we||ge?"mb-6":"bg-white border rounded p-4 shadow-sm mb-6",children:[A.type!=="text"&&A.type!=="signature"&&r.jsx("div",{className:"mb-2",children:r.jsx("span",{className:"text-sm font-medium",children:A.label})}),A.type==="text"&&(A.textStyle==="Heading"?r.jsx("h2",{className:"text-3xl font-bold mb-2",children:A.textContent}):A.textStyle==="Subheading"?r.jsx("h3",{className:"text-xl font-semibold mb-1",children:A.textContent}):r.jsx("p",{className:"text-base leading-relaxed",children:A.textContent})),A.type==="short"&&r.jsx("input",{className:"w-full border rounded px-2 py-2 text-sm",disabled:!0}),A.type==="long"&&r.jsx("textarea",{className:"w-full border rounded px-2 py-2 text-sm",rows:3,disabled:!0}),A.type==="multiple"&&r.jsx("div",{className:"space-y-2",children:(Le=A.options)==null?void 0:Le.map((er,kr)=>r.jsxs("div",{className:"flex items-center",children:[r.jsx("input",{type:"radio",name:`q${A.id}`,id:`q${A.id}_opt${kr}`,disabled:!0}),r.jsx("label",{htmlFor:`q${A.id}_opt${kr}`,className:"ml-2 text-sm",children:er})]},kr))}),A.type==="signature"&&r.jsxs("div",{className:"space-y-4",children:[r.jsx("div",{className:"text-red-600 font-medium",children:"Please sign here *"}),r.jsxs("div",{className:"flex items-center mb-3",children:[r.jsx("input",{type:"checkbox",checked:(Me=A.signatureData)==null?void 0:Me.agreed,disabled:!0,className:"mr-2"}),r.jsx("label",{className:"text-gray-500 text-sm",children:"I agree to use electronic records and signatures."})]}),(At=A.signatureData)!=null&&At.customer?r.jsx("div",{className:"border-b-2 border-gray-600 pb-4",children:r.jsx("img",{src:A.signatureData.customer,alt:"Customer Signature",className:"max-h-32"})}):r.jsx("div",{className:"h-24 border-b-2 border-gray-600 mb-2"})]}),A.required&&!we&&!ge&&r.jsx("div",{className:"mt-2 text-xs text-red-500",children:"* Required"})]},A.id)})})]})},U),r.jsxs("div",{className:"mt-10 border-t pt-6 flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 bg-white rounded shadow-sm p-4",children:[r.jsxs("div",{className:"flex items-center gap-6",children:[r.jsxs("label",{className:"flex items-center text-sm font-medium cursor-pointer",children:[r.jsx("input",{type:"checkbox",checked:_,onChange:A=>B(A.target.checked),className:"mr-2"}),"Mandatory for all clients"]}),r.jsxs("label",{className:"flex items-center text-sm font-medium cursor-pointer",children:[r.jsx("input",{type:"checkbox",checked:H,onChange:A=>F(A.target.checked),className:"mr-2"}),"Notify on submit"]}),r.jsxs("label",{className:"flex items-center text-sm font-medium cursor-pointer",children:["Expiration period (months):",r.jsx("input",{type:"number",min:"0",className:"ml-2 border rounded px-2 py-1 w-20",value:I,onChange:A=>z(A.target.value)})]})]}),r.jsxs("div",{className:"flex gap-2 mt-4 sm:mt-0",children:[r.jsx("button",{className:"px-6 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50",onClick:()=>o("/forms"),type:"button",children:"Cancel"}),r.jsx("button",{className:"px-6 py-2 border border-gray-300 rounded text-gray-700 hover:bg-gray-50",onClick:()=>Te("Draft"),type:"button",disabled:D,children:"Save for Later"}),r.jsx("button",{className:"px-6 py-2 bg-green-600 text-white rounded hover:bg-green-700 font-medium",onClick:()=>Te("Published"),type:"button",disabled:D,children:D?"Saving...":"Publish"})]}),me&&r.jsx("div",{className:"text-red-500 text-sm mt-2 col-span-full",children:me})]})]})]})]})}const Mm=6048e5,Fg=864e5,Ad=Symbol.for("constructDateFrom");function Nr(s,a){return typeof s=="function"?s(a):s&&typeof s=="object"&&Ad in s?s[Ad](a):s instanceof Date?new s.constructor(a):new Date(a)}function Rt(s,a){return Nr(a||s,s)}let zg={};function qa(){return zg}function ps(s,a){var p,y,x,g;const o=qa(),i=(a==null?void 0:a.weekStartsOn)??((y=(p=a==null?void 0:a.locale)==null?void 0:p.options)==null?void 0:y.weekStartsOn)??o.weekStartsOn??((g=(x=o.locale)==null?void 0:x.options)==null?void 0:g.weekStartsOn)??0,u=Rt(s,a==null?void 0:a.in),m=u.getDay(),f=(m<i?7:0)+m-i;return u.setDate(u.getDate()-f),u.setHours(0,0,0,0),u}function _a(s,a){return ps(s,{...a,weekStartsOn:1})}function Rm(s,a){const o=Rt(s,a==null?void 0:a.in),i=o.getFullYear(),u=Nr(o,0);u.setFullYear(i+1,0,4),u.setHours(0,0,0,0);const m=_a(u),f=Nr(o,0);f.setFullYear(i,0,4),f.setHours(0,0,0,0);const p=_a(f);return o.getTime()>=m.getTime()?i+1:o.getTime()>=p.getTime()?i:i-1}function _d(s){const a=Rt(s),o=new Date(Date.UTC(a.getFullYear(),a.getMonth(),a.getDate(),a.getHours(),a.getMinutes(),a.getSeconds(),a.getMilliseconds()));return o.setUTCFullYear(a.getFullYear()),+s-+o}function Ig(s,...a){const o=Nr.bind(null,a.find(i=>typeof i=="object"));return a.map(o)}function Fd(s,a){const o=Rt(s,a==null?void 0:a.in);return o.setHours(0,0,0,0),o}function Bg(s,a,o){const[i,u]=Ig(o==null?void 0:o.in,s,a),m=Fd(i),f=Fd(u),p=+m-_d(m),y=+f-_d(f);return Math.round((p-y)/Fg)}function $g(s,a){const o=Rm(s,a),i=Nr(s,0);return i.setFullYear(o,0,4),i.setHours(0,0,0,0),_a(i)}function Ug(s){return s instanceof Date||typeof s=="object"&&Object.prototype.toString.call(s)==="[object Date]"}function Wg(s){return!(!Ug(s)&&typeof s!="number"||isNaN(+Rt(s)))}function Hg(s,a){const o=Rt(s,a==null?void 0:a.in);return o.setFullYear(o.getFullYear(),0,1),o.setHours(0,0,0,0),o}const Vg={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}},Qg=(s,a,o)=>{let i;const u=Vg[s];return typeof u=="string"?i=u:a===1?i=u.one:i=u.other.replace("{{count}}",a.toString()),o!=null&&o.addSuffix?o.comparison&&o.comparison>0?"in "+i:i+" ago":i};function ql(s){return(a={})=>{const o=a.width?String(a.width):s.defaultWidth;return s.formats[o]||s.formats[s.defaultWidth]}}const Yg={full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},Jg={full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},qg={full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},Kg={date:ql({formats:Yg,defaultWidth:"full"}),time:ql({formats:Jg,defaultWidth:"full"}),dateTime:ql({formats:qg,defaultWidth:"full"})},Xg={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"},Gg=(s,a,o,i)=>Xg[s];function is(s){return(a,o)=>{const i=o!=null&&o.context?String(o.context):"standalone";let u;if(i==="formatting"&&s.formattingValues){const f=s.defaultFormattingWidth||s.defaultWidth,p=o!=null&&o.width?String(o.width):f;u=s.formattingValues[p]||s.formattingValues[f]}else{const f=s.defaultWidth,p=o!=null&&o.width?String(o.width):s.defaultWidth;u=s.values[p]||s.values[f]}const m=s.argumentCallback?s.argumentCallback(a):a;return u[m]}}const Zg={narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},ey={narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},ty={narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},ry={narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},ny={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},sy={narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},ay=(s,a)=>{const o=Number(s),i=o%100;if(i>20||i<10)switch(i%10){case 1:return o+"st";case 2:return o+"nd";case 3:return o+"rd"}return o+"th"},oy={ordinalNumber:ay,era:is({values:Zg,defaultWidth:"wide"}),quarter:is({values:ey,defaultWidth:"wide",argumentCallback:s=>s-1}),month:is({values:ty,defaultWidth:"wide"}),day:is({values:ry,defaultWidth:"wide"}),dayPeriod:is({values:ny,defaultWidth:"wide",formattingValues:sy,defaultFormattingWidth:"wide"})};function cs(s){return(a,o={})=>{const i=o.width,u=i&&s.matchPatterns[i]||s.matchPatterns[s.defaultMatchWidth],m=a.match(u);if(!m)return null;const f=m[0],p=i&&s.parsePatterns[i]||s.parsePatterns[s.defaultParseWidth],y=Array.isArray(p)?iy(p,v=>v.test(f)):ly(p,v=>v.test(f));let x;x=s.valueCallback?s.valueCallback(y):y,x=o.valueCallback?o.valueCallback(x):x;const g=a.slice(f.length);return{value:x,rest:g}}}function ly(s,a){for(const o in s)if(Object.prototype.hasOwnProperty.call(s,o)&&a(s[o]))return o}function iy(s,a){for(let o=0;o<s.length;o++)if(a(s[o]))return o}function cy(s){return(a,o={})=>{const i=a.match(s.matchPattern);if(!i)return null;const u=i[0],m=a.match(s.parsePattern);if(!m)return null;let f=s.valueCallback?s.valueCallback(m[0]):m[0];f=o.valueCallback?o.valueCallback(f):f;const p=a.slice(u.length);return{value:f,rest:p}}}const uy=/^(\d+)(th|st|nd|rd)?/i,dy=/\d+/i,my={narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},fy={any:[/^b/i,/^(a|c)/i]},hy={narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},py={any:[/1/i,/2/i,/3/i,/4/i]},xy={narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},gy={narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},yy={narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},vy={narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},wy={narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},jy={any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},by={ordinalNumber:cy({matchPattern:uy,parsePattern:dy,valueCallback:s=>parseInt(s,10)}),era:cs({matchPatterns:my,defaultMatchWidth:"wide",parsePatterns:fy,defaultParseWidth:"any"}),quarter:cs({matchPatterns:hy,defaultMatchWidth:"wide",parsePatterns:py,defaultParseWidth:"any",valueCallback:s=>s+1}),month:cs({matchPatterns:xy,defaultMatchWidth:"wide",parsePatterns:gy,defaultParseWidth:"any"}),day:cs({matchPatterns:yy,defaultMatchWidth:"wide",parsePatterns:vy,defaultParseWidth:"any"}),dayPeriod:cs({matchPatterns:wy,defaultMatchWidth:"any",parsePatterns:jy,defaultParseWidth:"any"})},Ny={code:"en-US",formatDistance:Qg,formatLong:Kg,formatRelative:Gg,localize:oy,match:by,options:{weekStartsOn:0,firstWeekContainsDate:1}};function ky(s,a){const o=Rt(s,a==null?void 0:a.in);return Bg(o,Hg(o))+1}function Sy(s,a){const o=Rt(s,a==null?void 0:a.in),i=+_a(o)-+$g(o);return Math.round(i/Mm)+1}function Am(s,a){var g,v,j,P;const o=Rt(s,a==null?void 0:a.in),i=o.getFullYear(),u=qa(),m=(a==null?void 0:a.firstWeekContainsDate)??((v=(g=a==null?void 0:a.locale)==null?void 0:g.options)==null?void 0:v.firstWeekContainsDate)??u.firstWeekContainsDate??((P=(j=u.locale)==null?void 0:j.options)==null?void 0:P.firstWeekContainsDate)??1,f=Nr((a==null?void 0:a.in)||s,0);f.setFullYear(i+1,0,m),f.setHours(0,0,0,0);const p=ps(f,a),y=Nr((a==null?void 0:a.in)||s,0);y.setFullYear(i,0,m),y.setHours(0,0,0,0);const x=ps(y,a);return+o>=+p?i+1:+o>=+x?i:i-1}function Cy(s,a){var p,y,x,g;const o=qa(),i=(a==null?void 0:a.firstWeekContainsDate)??((y=(p=a==null?void 0:a.locale)==null?void 0:p.options)==null?void 0:y.firstWeekContainsDate)??o.firstWeekContainsDate??((g=(x=o.locale)==null?void 0:x.options)==null?void 0:g.firstWeekContainsDate)??1,u=Am(s,a),m=Nr((a==null?void 0:a.in)||s,0);return m.setFullYear(u,0,i),m.setHours(0,0,0,0),ps(m,a)}function Ey(s,a){const o=Rt(s,a==null?void 0:a.in),i=+ps(o,a)-+Cy(o,a);return Math.round(i/Mm)+1}function Pe(s,a){const o=s<0?"-":"",i=Math.abs(s).toString().padStart(a,"0");return o+i}const wr={y(s,a){const o=s.getFullYear(),i=o>0?o:1-o;return Pe(a==="yy"?i%100:i,a.length)},M(s,a){const o=s.getMonth();return a==="M"?String(o+1):Pe(o+1,2)},d(s,a){return Pe(s.getDate(),a.length)},a(s,a){const o=s.getHours()/12>=1?"pm":"am";switch(a){case"a":case"aa":return o.toUpperCase();case"aaa":return o;case"aaaaa":return o[0];case"aaaa":default:return o==="am"?"a.m.":"p.m."}},h(s,a){return Pe(s.getHours()%12||12,a.length)},H(s,a){return Pe(s.getHours(),a.length)},m(s,a){return Pe(s.getMinutes(),a.length)},s(s,a){return Pe(s.getSeconds(),a.length)},S(s,a){const o=a.length,i=s.getMilliseconds(),u=Math.trunc(i*Math.pow(10,o-3));return Pe(u,a.length)}},xn={midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},zd={G:function(s,a,o){const i=s.getFullYear()>0?1:0;switch(a){case"G":case"GG":case"GGG":return o.era(i,{width:"abbreviated"});case"GGGGG":return o.era(i,{width:"narrow"});case"GGGG":default:return o.era(i,{width:"wide"})}},y:function(s,a,o){if(a==="yo"){const i=s.getFullYear(),u=i>0?i:1-i;return o.ordinalNumber(u,{unit:"year"})}return wr.y(s,a)},Y:function(s,a,o,i){const u=Am(s,i),m=u>0?u:1-u;if(a==="YY"){const f=m%100;return Pe(f,2)}return a==="Yo"?o.ordinalNumber(m,{unit:"year"}):Pe(m,a.length)},R:function(s,a){const o=Rm(s);return Pe(o,a.length)},u:function(s,a){const o=s.getFullYear();return Pe(o,a.length)},Q:function(s,a,o){const i=Math.ceil((s.getMonth()+1)/3);switch(a){case"Q":return String(i);case"QQ":return Pe(i,2);case"Qo":return o.ordinalNumber(i,{unit:"quarter"});case"QQQ":return o.quarter(i,{width:"abbreviated",context:"formatting"});case"QQQQQ":return o.quarter(i,{width:"narrow",context:"formatting"});case"QQQQ":default:return o.quarter(i,{width:"wide",context:"formatting"})}},q:function(s,a,o){const i=Math.ceil((s.getMonth()+1)/3);switch(a){case"q":return String(i);case"qq":return Pe(i,2);case"qo":return o.ordinalNumber(i,{unit:"quarter"});case"qqq":return o.quarter(i,{width:"abbreviated",context:"standalone"});case"qqqqq":return o.quarter(i,{width:"narrow",context:"standalone"});case"qqqq":default:return o.quarter(i,{width:"wide",context:"standalone"})}},M:function(s,a,o){const i=s.getMonth();switch(a){case"M":case"MM":return wr.M(s,a);case"Mo":return o.ordinalNumber(i+1,{unit:"month"});case"MMM":return o.month(i,{width:"abbreviated",context:"formatting"});case"MMMMM":return o.month(i,{width:"narrow",context:"formatting"});case"MMMM":default:return o.month(i,{width:"wide",context:"formatting"})}},L:function(s,a,o){const i=s.getMonth();switch(a){case"L":return String(i+1);case"LL":return Pe(i+1,2);case"Lo":return o.ordinalNumber(i+1,{unit:"month"});case"LLL":return o.month(i,{width:"abbreviated",context:"standalone"});case"LLLLL":return o.month(i,{width:"narrow",context:"standalone"});case"LLLL":default:return o.month(i,{width:"wide",context:"standalone"})}},w:function(s,a,o,i){const u=Ey(s,i);return a==="wo"?o.ordinalNumber(u,{unit:"week"}):Pe(u,a.length)},I:function(s,a,o){const i=Sy(s);return a==="Io"?o.ordinalNumber(i,{unit:"week"}):Pe(i,a.length)},d:function(s,a,o){return a==="do"?o.ordinalNumber(s.getDate(),{unit:"date"}):wr.d(s,a)},D:function(s,a,o){const i=ky(s);return a==="Do"?o.ordinalNumber(i,{unit:"dayOfYear"}):Pe(i,a.length)},E:function(s,a,o){const i=s.getDay();switch(a){case"E":case"EE":case"EEE":return o.day(i,{width:"abbreviated",context:"formatting"});case"EEEEE":return o.day(i,{width:"narrow",context:"formatting"});case"EEEEEE":return o.day(i,{width:"short",context:"formatting"});case"EEEE":default:return o.day(i,{width:"wide",context:"formatting"})}},e:function(s,a,o,i){const u=s.getDay(),m=(u-i.weekStartsOn+8)%7||7;switch(a){case"e":return String(m);case"ee":return Pe(m,2);case"eo":return o.ordinalNumber(m,{unit:"day"});case"eee":return o.day(u,{width:"abbreviated",context:"formatting"});case"eeeee":return o.day(u,{width:"narrow",context:"formatting"});case"eeeeee":return o.day(u,{width:"short",context:"formatting"});case"eeee":default:return o.day(u,{width:"wide",context:"formatting"})}},c:function(s,a,o,i){const u=s.getDay(),m=(u-i.weekStartsOn+8)%7||7;switch(a){case"c":return String(m);case"cc":return Pe(m,a.length);case"co":return o.ordinalNumber(m,{unit:"day"});case"ccc":return o.day(u,{width:"abbreviated",context:"standalone"});case"ccccc":return o.day(u,{width:"narrow",context:"standalone"});case"cccccc":return o.day(u,{width:"short",context:"standalone"});case"cccc":default:return o.day(u,{width:"wide",context:"standalone"})}},i:function(s,a,o){const i=s.getDay(),u=i===0?7:i;switch(a){case"i":return String(u);case"ii":return Pe(u,a.length);case"io":return o.ordinalNumber(u,{unit:"day"});case"iii":return o.day(i,{width:"abbreviated",context:"formatting"});case"iiiii":return o.day(i,{width:"narrow",context:"formatting"});case"iiiiii":return o.day(i,{width:"short",context:"formatting"});case"iiii":default:return o.day(i,{width:"wide",context:"formatting"})}},a:function(s,a,o){const u=s.getHours()/12>=1?"pm":"am";switch(a){case"a":case"aa":return o.dayPeriod(u,{width:"abbreviated",context:"formatting"});case"aaa":return o.dayPeriod(u,{width:"abbreviated",context:"formatting"}).toLowerCase();case"aaaaa":return o.dayPeriod(u,{width:"narrow",context:"formatting"});case"aaaa":default:return o.dayPeriod(u,{width:"wide",context:"formatting"})}},b:function(s,a,o){const i=s.getHours();let u;switch(i===12?u=xn.noon:i===0?u=xn.midnight:u=i/12>=1?"pm":"am",a){case"b":case"bb":return o.dayPeriod(u,{width:"abbreviated",context:"formatting"});case"bbb":return o.dayPeriod(u,{width:"abbreviated",context:"formatting"}).toLowerCase();case"bbbbb":return o.dayPeriod(u,{width:"narrow",context:"formatting"});case"bbbb":default:return o.dayPeriod(u,{width:"wide",context:"formatting"})}},B:function(s,a,o){const i=s.getHours();let u;switch(i>=17?u=xn.evening:i>=12?u=xn.afternoon:i>=4?u=xn.morning:u=xn.night,a){case"B":case"BB":case"BBB":return o.dayPeriod(u,{width:"abbreviated",context:"formatting"});case"BBBBB":return o.dayPeriod(u,{width:"narrow",context:"formatting"});case"BBBB":default:return o.dayPeriod(u,{width:"wide",context:"formatting"})}},h:function(s,a,o){if(a==="ho"){let i=s.getHours()%12;return i===0&&(i=12),o.ordinalNumber(i,{unit:"hour"})}return wr.h(s,a)},H:function(s,a,o){return a==="Ho"?o.ordinalNumber(s.getHours(),{unit:"hour"}):wr.H(s,a)},K:function(s,a,o){const i=s.getHours()%12;return a==="Ko"?o.ordinalNumber(i,{unit:"hour"}):Pe(i,a.length)},k:function(s,a,o){let i=s.getHours();return i===0&&(i=24),a==="ko"?o.ordinalNumber(i,{unit:"hour"}):Pe(i,a.length)},m:function(s,a,o){return a==="mo"?o.ordinalNumber(s.getMinutes(),{unit:"minute"}):wr.m(s,a)},s:function(s,a,o){return a==="so"?o.ordinalNumber(s.getSeconds(),{unit:"second"}):wr.s(s,a)},S:function(s,a){return wr.S(s,a)},X:function(s,a,o){const i=s.getTimezoneOffset();if(i===0)return"Z";switch(a){case"X":return Bd(i);case"XXXX":case"XX":return zr(i);case"XXXXX":case"XXX":default:return zr(i,":")}},x:function(s,a,o){const i=s.getTimezoneOffset();switch(a){case"x":return Bd(i);case"xxxx":case"xx":return zr(i);case"xxxxx":case"xxx":default:return zr(i,":")}},O:function(s,a,o){const i=s.getTimezoneOffset();switch(a){case"O":case"OO":case"OOO":return"GMT"+Id(i,":");case"OOOO":default:return"GMT"+zr(i,":")}},z:function(s,a,o){const i=s.getTimezoneOffset();switch(a){case"z":case"zz":case"zzz":return"GMT"+Id(i,":");case"zzzz":default:return"GMT"+zr(i,":")}},t:function(s,a,o){const i=Math.trunc(+s/1e3);return Pe(i,a.length)},T:function(s,a,o){return Pe(+s,a.length)}};function Id(s,a=""){const o=s>0?"-":"+",i=Math.abs(s),u=Math.trunc(i/60),m=i%60;return m===0?o+String(u):o+String(u)+a+Pe(m,2)}function Bd(s,a){return s%60===0?(s>0?"-":"+")+Pe(Math.abs(s)/60,2):zr(s,a)}function zr(s,a=""){const o=s>0?"-":"+",i=Math.abs(s),u=Pe(Math.trunc(i/60),2),m=Pe(i%60,2);return o+u+a+m}const $d=(s,a)=>{switch(s){case"P":return a.date({width:"short"});case"PP":return a.date({width:"medium"});case"PPP":return a.date({width:"long"});case"PPPP":default:return a.date({width:"full"})}},_m=(s,a)=>{switch(s){case"p":return a.time({width:"short"});case"pp":return a.time({width:"medium"});case"ppp":return a.time({width:"long"});case"pppp":default:return a.time({width:"full"})}},Py=(s,a)=>{const o=s.match(/(P+)(p+)?/)||[],i=o[1],u=o[2];if(!u)return $d(s,a);let m;switch(i){case"P":m=a.dateTime({width:"short"});break;case"PP":m=a.dateTime({width:"medium"});break;case"PPP":m=a.dateTime({width:"long"});break;case"PPPP":default:m=a.dateTime({width:"full"});break}return m.replace("{{date}}",$d(i,a)).replace("{{time}}",_m(u,a))},Dy={p:_m,P:Py},Ty=/^D+$/,Ly=/^Y+$/,Oy=["D","DD","YY","YYYY"];function My(s){return Ty.test(s)}function Ry(s){return Ly.test(s)}function Ay(s,a,o){const i=_y(s,a,o);if(console.warn(i),Oy.includes(s))throw new RangeError(i)}function _y(s,a,o){const i=s[0]==="Y"?"years":"days of the month";return`Use \`${s.toLowerCase()}\` instead of \`${s}\` (in \`${a}\`) for formatting ${i} to the input \`${o}\`; see: https://github.com/date-fns/date-fns/blob/master/docs/unicodeTokens.md`}const Fy=/[yYQqMLwIdDecihHKkms]o|(\w)\1*|''|'(''|[^'])+('|$)|./g,zy=/P+p+|P+|p+|''|'(''|[^'])+('|$)|./g,Iy=/^'([^]*?)'?$/,By=/''/g,$y=/[a-zA-Z]/;function Uy(s,a,o){var g,v,j,P;const i=qa(),u=i.locale??Ny,m=i.firstWeekContainsDate??((v=(g=i.locale)==null?void 0:g.options)==null?void 0:v.firstWeekContainsDate)??1,f=i.weekStartsOn??((P=(j=i.locale)==null?void 0:j.options)==null?void 0:P.weekStartsOn)??0,p=Rt(s,o==null?void 0:o.in);if(!Wg(p))throw new RangeError("Invalid time value");let y=a.match(zy).map(k=>{const E=k[0];if(E==="p"||E==="P"){const S=Dy[E];return S(k,u.formatLong)}return k}).join("").match(Fy).map(k=>{if(k==="''")return{isToken:!1,value:"'"};const E=k[0];if(E==="'")return{isToken:!1,value:Wy(k)};if(zd[E])return{isToken:!0,value:k};if(E.match($y))throw new RangeError("Format string contains an unescaped latin alphabet character `"+E+"`");return{isToken:!1,value:k}});u.localize.preprocessor&&(y=u.localize.preprocessor(p,y));const x={firstWeekContainsDate:m,weekStartsOn:f,locale:u};return y.map(k=>{if(!k.isToken)return k.value;const E=k.value;(Ry(E)||My(E))&&Ay(E,a,String(s));const S=zd[E[0]];return S(p,E,u.localize,x)}).join("")}function Wy(s){const a=s.match(Iy);return a?a[1].replace(By,"'"):s}const Hy=()=>{var x,g,v,j;const{id:s}=Ia(),a=Zt(),{fetchTemplateDetails:o,templateDetails:i,detailsLoading:u,detailsError:m}=Ja(),[f,p]=b.useState(!1);if(b.useEffect(()=>{s&&(!f||i&&i.id!==parseInt(s,10))&&(async()=>{try{await o(parseInt(s,10)),p(!0)}catch(k){console.error("Error loading template details:",k)}})()},[s,o,f,i]),u&&!f)return r.jsx("div",{className:"flex justify-center items-center h-64",children:r.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"})});if(m)return r.jsxs("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded",children:[r.jsx("p",{className:"font-medium",children:"Error loading template details"}),r.jsx("p",{className:"text-sm",children:m}),r.jsx("button",{onClick:()=>{p(!1)},className:"mt-2 px-4 py-2 bg-red-600 text-white rounded hover:bg-red-700",children:"Try Again"})]});if(!i)return r.jsxs("div",{className:"bg-yellow-50 border border-yellow-200 text-yellow-700 px-4 py-3 rounded",children:[r.jsx("p",{children:"No template details found."}),r.jsx("button",{onClick:()=>a("/forms"),className:"mt-2 px-4 py-2 bg-yellow-600 text-white rounded hover:bg-yellow-700",children:"Back to Forms"})]});const y=P=>{if(!P)return"N/A";try{return Uy(new Date(P),"MMM dd, yyyy HH:mm")}catch{return P}};return r.jsxs("div",{className:"bg-white shadow rounded-lg overflow-hidden",children:[r.jsx("div",{className:"px-6 py-4 border-b",children:r.jsxs("div",{className:"flex justify-between items-center",children:[r.jsx("h2",{className:"text-xl font-semibold text-gray-800",children:i.name}),r.jsxs("div",{className:"flex space-x-2",children:[r.jsx("button",{onClick:()=>a(`/forms/edit/${s}`),className:"px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700",children:"Edit Template"}),r.jsx("button",{onClick:()=>a("/forms"),className:"px-4 py-2 border border-gray-300 text-gray-700 rounded hover:bg-gray-50",children:"Back to List"})]})]})}),r.jsx("div",{className:"p-6",children:r.jsxs("div",{className:"space-y-6",children:[r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[r.jsxs("div",{children:[r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Template Information"}),r.jsx("div",{className:"bg-gray-50 p-4 rounded border",children:r.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[r.jsx("div",{className:"text-sm font-medium text-gray-500",children:"Name"}),r.jsx("div",{className:"text-sm text-gray-900",children:i.name}),r.jsx("div",{className:"text-sm font-medium text-gray-500",children:"Document Type"}),r.jsx("div",{className:"text-sm text-gray-900",children:i.documentType}),r.jsx("div",{className:"text-sm font-medium text-gray-500",children:"Status"}),r.jsx("div",{className:"text-sm",children:r.jsx("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${i.status==="Published"?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:i.status})})]})})]}),r.jsxs("div",{children:[r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Settings"}),r.jsx("div",{className:"bg-gray-50 p-4 rounded border",children:r.jsxs("div",{className:"space-y-2",children:[r.jsxs("div",{className:"flex items-center",children:[r.jsx("div",{className:`w-4 h-4 rounded ${(x=i.settings)!=null&&x.mandatory?"bg-blue-500":"bg-gray-300"}`}),r.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"Mandatory for all clients"})]}),r.jsxs("div",{className:"flex items-center",children:[r.jsx("div",{className:`w-4 h-4 rounded ${(g=i.settings)!=null&&g.notify?"bg-blue-500":"bg-gray-300"}`}),r.jsx("span",{className:"ml-2 text-sm text-gray-700",children:"Notify on submit"})]}),r.jsxs("div",{className:"flex items-center",children:[r.jsx("span",{className:"text-sm font-medium text-gray-500 mr-2",children:"Expiration period:"}),r.jsx("span",{className:"text-sm text-gray-900",children:(v=i.settings)!=null&&v.expiration?`${i.settings.expiration} months`:"No expiration"})]})]})})]})]}),r.jsxs("div",{children:[r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Creation & Modification"}),r.jsx("div",{className:"bg-gray-50 p-4 rounded border",children:r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[r.jsxs("div",{children:[r.jsx("div",{className:"text-sm font-medium text-gray-500",children:"Created By"}),r.jsx("div",{className:"text-sm text-gray-900",children:i.createdBy||"N/A"}),r.jsx("div",{className:"text-sm font-medium text-gray-500 mt-2",children:"Created On"}),r.jsx("div",{className:"text-sm text-gray-900",children:y(i.createdDate)})]}),r.jsxs("div",{children:[r.jsx("div",{className:"text-sm font-medium text-gray-500",children:"Last Updated By"}),r.jsx("div",{className:"text-sm text-gray-900",children:i.updatedBy||"N/A"}),r.jsx("div",{className:"text-sm font-medium text-gray-500 mt-2",children:"Last Updated On"}),r.jsx("div",{className:"text-sm text-gray-900",children:y(i.updatedDate)})]})]})})]}),r.jsxs("div",{children:[r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:"Questions"}),r.jsx("div",{className:"bg-gray-50 p-4 rounded border",children:r.jsxs("div",{className:"text-sm text-gray-700",children:["This template contains ",((j=i.questions)==null?void 0:j.length)||0," questions."]})})]})]})})]})};function Vy(){return r.jsxs("div",{className:"container py-10",children:[r.jsx("h1",{className:"text-3xl mb-6",children:"Reports"}),r.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:r.jsx("p",{className:"text-gray-600",children:"View and analyze business reports here."})})]})}function Qy(){return r.jsxs("div",{className:"container py-10",children:[r.jsx("h1",{className:"text-3xl mb-6",children:"All Reports"}),r.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:r.jsx("p",{className:"text-gray-600",children:"View all available reports here."})})]})}function Yy(){return r.jsxs("div",{className:"container py-10",children:[r.jsx("h1",{className:"text-3xl mb-6",children:"Transaction List"}),r.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:r.jsx("p",{className:"text-gray-600",children:"View and manage your transaction history here."})})]})}function Jy(){return r.jsxs("div",{className:"container py-10",children:[r.jsx("h1",{className:"text-3xl mb-6",children:"Settings"}),r.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:r.jsx("p",{className:"text-gray-600",children:"Configure your application settings here."})})]})}function qy(){return r.jsxs("div",{className:"container py-10",children:[r.jsx("h1",{className:"text-3xl mb-6",children:"All Settings"}),r.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:r.jsx("p",{className:"text-gray-600",children:"Configure all application settings here."})})]})}function Ky(){return r.jsxs("div",{className:"container py-10",children:[r.jsx("h1",{className:"text-3xl mb-6",children:"Employees"}),r.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:r.jsx("p",{className:"text-gray-600",children:"Manage your employees and staff members here."})})]})}function Xy(){return r.jsxs("div",{className:"container py-10",children:[r.jsx("h1",{className:"text-3xl mb-6",children:"Service Menu"}),r.jsx("div",{className:"bg-white p-6 rounded-lg shadow-md",children:r.jsx("p",{className:"text-gray-600",children:"Configure your service offerings and pricing here."})})]})}const Ur=b.forwardRef(({children:s,variant:a="primary",size:o="md",className:i="",type:u="button",disabled:m=!1,...f},p)=>{const y="inline-flex items-center justify-center font-medium rounded-md transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2",x={primary:"bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500",secondary:"bg-gray-200 text-gray-800 hover:bg-gray-300 focus:ring-gray-500",danger:"bg-red-600 text-white hover:bg-red-700 focus:ring-red-500",outline:"bg-transparent text-gray-700 border border-gray-300 hover:bg-gray-50 focus:ring-primary-500"},g={sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"},v=m?"opacity-50 cursor-not-allowed":"cursor-pointer";return r.jsx("button",{ref:p,type:u,className:`${y} ${x[a]} ${g[o]} ${v} ${i}`,disabled:m,...f,children:s})});Ur.displayName="Button";function Gy({isOpen:s,onClose:a}){if(!s)return null;const[o,i]=b.useState("request"),[u,m]=b.useState(""),[f,p]=b.useState(""),[y,x]=b.useState(!1),[g,v]=b.useState(null),[j,P]=b.useState(null),k=/^\+?[1-9]\d{1,14}$/,E=hs("/v1/auth/request-otp/"),S=hs("/v1/auth/verify-otp/"),_=async H=>{if(H.preventDefault(),v(null),!k.test(u)){v("Please enter a valid phone number in E.164 format (e.g. +14255550123)");return}x(!0);const F=await E.createData({phone_number:u});x(!1),F&&F.temp_token?(P(F.temp_token),i("verify")):v((F==null?void 0:F.detail)||"Failed to request OTP")},B=async H=>{if(H.preventDefault(),v(null),f.length!==6){v("OTP must be 6 digits");return}x(!0);const F=await S.createData({phone_number:u,otp:f});x(!1),F&&F.access?(localStorage.setItem("auth_token",F.access),F.refresh&&localStorage.setItem("refresh_token",F.refresh),a(),window.location.href="/"):v((F==null?void 0:F.detail)||"Failed to verify OTP")};return r.jsx("div",{className:"fixed inset-0 bg-black/50 z-50 flex items-center justify-center px-4",children:r.jsxs("div",{className:"bg-white w-full max-w-md rounded-lg shadow-lg p-6 relative",children:[r.jsx("button",{className:"absolute top-3 right-3 text-gray-400 hover:text-gray-600",onClick:a,children:r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"currentColor",className:"w-5 h-5",children:r.jsx("path",{fillRule:"evenodd",d:"M6.225 4.811a.75.75 0 0 1 1.06 0L12 9.525l4.715-4.714a.75.75 0 1 1 1.06 1.06L13.06 10.586l4.714 4.715a.75.75 0 0 1-1.06 1.06L12 11.646l-4.715 4.715a.75.75 0 0 1-1.06-1.06l4.714-4.715-4.714-4.714a.75.75 0 0 1 0-1.06Z",clipRule:"evenodd"})})}),r.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Login with OTP"}),o==="request"&&r.jsxs("form",{onSubmit:_,className:"space-y-4",children:[r.jsxs("div",{children:[r.jsx("label",{htmlFor:"phone",className:"block text-sm font-medium text-gray-700",children:"Phone number"}),r.jsx("input",{id:"phone",type:"tel",className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",placeholder:"+14255550123",value:u,onChange:H=>m(H.target.value.trim()),required:!0})]}),g&&r.jsx("p",{className:"text-sm text-red-600",children:g}),r.jsx(Ur,{type:"submit",className:"w-full",disabled:y,children:y?"Requesting...":"Send OTP"})]}),o==="verify"&&r.jsxs("form",{onSubmit:B,className:"space-y-4",children:[r.jsxs("p",{className:"text-sm text-gray-700",children:["Enter the 6-digit code sent to ",r.jsx("strong",{children:u})]}),r.jsxs("div",{children:[r.jsx("label",{htmlFor:"otp",className:"block text-sm font-medium text-gray-700",children:"OTP Code"}),r.jsx("input",{id:"otp",type:"text",inputMode:"numeric",maxLength:"6",pattern:"[0-9]{6}",className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500 tracking-widest text-center",value:f,onChange:H=>p(H.target.value.trim()),required:!0})]}),g&&r.jsx("p",{className:"text-sm text-red-600",children:g}),r.jsx(Ur,{type:"submit",className:"w-full",disabled:y,children:y?"Verifying...":"Verify & Login"})]})]})})}function Zy(){var S,_;const s=Zt(),o=((_=(S=Gt().state)==null?void 0:S.from)==null?void 0:_.pathname)||"/",[i,u]=b.useState(""),[m,f]=b.useState(""),[p,y]=b.useState(!1),[x,g]=b.useState(null),[v,j]=b.useState(!1),P=hs("/auth/login/"),k=B=>{const H=/^[^\s@]+@[^\s@]+\.[^\s@]+$/,F=/^\+?[0-9]{7,15}$/;return H.test(B)||F.test(B)},E=async B=>{if(B.preventDefault(),g(null),!k(i)){g("Please enter a valid email address or phone number");return}if(!m){g("Password is required");return}const H={email:i,password:m,remember:p},F=await P.createData(H);F&&F.access?(localStorage.setItem("auth_token",F.access),F.refresh&&localStorage.setItem("refresh_token",F.refresh),s(o,{replace:!0})):g((F==null?void 0:F.detail)||"Invalid credentials")};return r.jsxs("div",{className:"container flex justify-center items-center py-16",children:[r.jsxs("div",{className:"w-full max-w-md bg-white rounded-lg shadow-md p-8",children:[r.jsx("h2",{className:"text-2xl font-bold text-center text-gray-900 mb-6",children:"Sign in to your account"}),x&&r.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded mb-4 text-sm",children:x}),r.jsxs("form",{onSubmit:E,className:"space-y-4",children:[r.jsxs("div",{children:[r.jsxs("label",{htmlFor:"username",className:"block text-sm font-medium text-gray-700",children:["Phone number or email ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"username",type:"text",className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",placeholder:"Phone number or email",value:i,onChange:B=>u(B.target.value.trim()),required:!0})]}),r.jsxs("div",{children:[r.jsxs("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700",children:["Password ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"password",type:"password",className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",value:m,onChange:B=>f(B.target.value),required:!0})]}),r.jsxs("div",{className:"flex items-center justify-between",children:[r.jsxs("label",{className:"inline-flex items-center text-sm text-gray-700",children:[r.jsx("input",{type:"checkbox",className:"mr-2 rounded border-gray-300",checked:p,onChange:B=>y(B.target.checked)}),"Remember me"]}),r.jsx("button",{type:"button",className:"text-sm text-primary-600 hover:underline",onClick:()=>j(!0),children:"Login with OTP"})]}),r.jsx(Ur,{type:"submit",className:"w-full",disabled:P.loading,children:P.loading?"Signing in...":"Sign in"})]}),r.jsxs("div",{className:"text-center text-sm text-gray-700 mt-6",children:["Are you a business owner?"," ",r.jsx(ot,{to:"/business/register",className:"text-primary-600 hover:underline",children:"Register your Business"})]}),r.jsxs("div",{className:"flex items-center my-6",children:[r.jsx("hr",{className:"flex-1 border-gray-300"}),r.jsx("span",{className:"px-2 text-sm text-gray-500",children:"Or continue with"}),r.jsx("hr",{className:"flex-1 border-gray-300"})]}),r.jsxs("a",{href:"/accounts/google/login/",className:"flex items-center justify-center gap-2 border border-gray-300 rounded-md px-4 py-2 hover:bg-gray-50 w-full text-sm font-medium text-gray-700",children:[r.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 48 48",className:"w-5 h-5",children:[r.jsx("path",{fill:"#FFC107",d:"M43.611 20.083H42V20H24v8h11.292C33.323 33.73 29.064 37 24 37c-7.18 0-13-5.82-13-13s5.82-13 13-13c3.162 0 6.05 1.123 8.299 2.966l5.657-5.657C34.304 5.092 29.393 3 24 3 12.402 3 3 12.402 3 24s9.402 21 21 21 21-9.402 21-21c0-1.407-.144-2.781-.389-4.117z"}),r.jsx("path",{fill:"#FF3D00",d:"M6.306 14.691l6.571 4.817C14.37 16.597 18.91 13 24 13c3.162 0 6.05 1.123 8.299 2.966l5.657-5.657C34.304 5.092 29.393 3 24 3c-7.18 0-13 5.82-13 13 0 2.445.675 4.733 1.849 6.694z"}),r.jsx("path",{fill:"#4CAF50",d:"M24 45c5.093 0 9.879-1.965 13.463-5.182l-6.207-5.238C29.822 35.43 27.019 37 24 37c-5.04 0-9.284-3.261-10.794-7.787l-6.581 5.08C11.046 40.257 17.074 45 24 45z"}),r.jsx("path",{fill:"#1976D2",d:"M43.611 20.083H42V20H24v8h11.292A11.956 11.956 0 0 1 24 37c-5.04 0-9.284-3.261-10.794-7.787l-6.581 5.08C11.046 40.257 17.074 45 24 45c11.598 0 21-9.402 21-21 0-1.407-.144-2.781-.389-4.117z"})]}),"Sign in with Google"]}),r.jsxs("p",{className:"text-center text-sm text-gray-600 mt-6",children:["Don't have an account?"," ",r.jsx(ot,{to:"/auth/signup",className:"text-primary-600 hover:underline",children:"Sign up"})]})]}),r.jsx(Gy,{isOpen:v,onClose:()=>j(!1)})]})}function ev(){const s=Zt(),[a,o]=b.useState({first_name:"",last_name:"",email:"",phone_number:"",password1:"",password2:""}),[i,u]=b.useState(null),m=hs("/auth/signup/"),f=x=>{const{name:g,value:v}=x.target;o(j=>({...j,[g]:v}))},p=()=>{const x=/^[^\s@]+@[^\s@]+\.[^\s@]+$/;return!a.first_name.trim()||!a.last_name.trim()?"First and last name are required":x.test(a.email)?/^[0-9]{10}$/.test(a.phone_number)?a.password1.length<8?"Password must be at least 8 characters":a.password1!==a.password2?"Passwords do not match":null:"Phone number must be 10 digits":"Invalid email address"},y=async x=>{x.preventDefault();const g=p();if(g){u(g);return}const v=await m.createData(a);!v||v.error?u((v==null?void 0:v.detail)||"Failed to sign up"):s("/auth/login")};return r.jsx("div",{className:"container flex justify-center items-center py-16",children:r.jsxs("div",{className:"w-full max-w-md bg-white rounded-lg shadow-md p-8",children:[r.jsx("h2",{className:"text-2xl font-bold text-center text-gray-900 mb-6",children:"Create your account"}),i&&r.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded mb-4 text-sm",children:i}),r.jsxs("form",{onSubmit:y,className:"space-y-4",children:[r.jsxs("div",{className:"flex gap-4",children:[r.jsxs("div",{className:"flex-1",children:[r.jsxs("label",{htmlFor:"first_name",className:"block text-sm font-medium text-gray-700",children:["First Name ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"first_name",name:"first_name",type:"text",className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",value:a.first_name,onChange:f,required:!0})]}),r.jsxs("div",{className:"flex-1",children:[r.jsxs("label",{htmlFor:"last_name",className:"block text-sm font-medium text-gray-700",children:["Last Name ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"last_name",name:"last_name",type:"text",className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",value:a.last_name,onChange:f,required:!0})]})]}),r.jsxs("div",{children:[r.jsxs("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700",children:["Email ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"email",name:"email",type:"email",className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",value:a.email,onChange:f,required:!0})]}),r.jsxs("div",{children:[r.jsxs("label",{htmlFor:"phone_number",className:"block text-sm font-medium text-gray-700",children:["Phone ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"phone_number",name:"phone_number",type:"tel",className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",placeholder:"5551234567",value:a.phone_number,onChange:f,required:!0})]}),r.jsxs("div",{children:[r.jsxs("label",{htmlFor:"password1",className:"block text-sm font-medium text-gray-700",children:["Password ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"password1",name:"password1",type:"password",className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",value:a.password1,onChange:f,required:!0})]}),r.jsxs("div",{children:[r.jsxs("label",{htmlFor:"password2",className:"block text-sm font-medium text-gray-700",children:["Confirm Password ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{id:"password2",name:"password2",type:"password",className:"mt-1 block w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",value:a.password2,onChange:f,required:!0})]}),r.jsx(Ur,{type:"submit",className:"w-full",disabled:m.loading,children:m.loading?"Creating account...":"Sign up"})]}),r.jsxs("div",{className:"flex items-center my-6",children:[r.jsx("hr",{className:"flex-1 border-gray-300"}),r.jsx("span",{className:"px-2 text-sm text-gray-500",children:"Or continue with"}),r.jsx("hr",{className:"flex-1 border-gray-300"})]}),r.jsxs("a",{href:"/accounts/google/login/",className:"flex items-center justify-center gap-2 border border-gray-300 rounded-md px-4 py-2 hover:bg-gray-50 w-full text-sm font-medium text-gray-700",children:[r.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 48 48",className:"w-5 h-5",children:[r.jsx("path",{fill:"#FFC107",d:"M43.611 20.083H42V20H24v8h11.292C33.323 33.73 29.064 37 24 37c-7.18 0-13-5.82-13-13s5.82-13 13-13c3.162 0 6.05 1.123 8.299 2.966l5.657-5.657C34.304 5.092 29.393 3 24 3 12.402 3 3 12.402 3 24s9.402 21 21 21 21-9.402 21-21c0-1.407-.144-2.781-.389-4.117z"}),r.jsx("path",{fill:"#FF3D00",d:"M6.306 14.691l6.571 4.817C14.37 16.597 18.91 13 24 13c3.162 0 6.05 1.123 8.299 2.966l5.657-5.657C34.304 5.092 29.393 3 24 3c-7.18 0-13 5.82-13 13 0 2.445.675 4.733 1.849 6.694z"}),r.jsx("path",{fill:"#4CAF50",d:"M24 45c5.093 0 9.879-1.965 13.463-5.182l-6.207-5.238C29.822 35.43 27.019 37 24 37c-5.04 0-9.284-3.261-10.794-7.787l-6.581 5.08C11.046 40.257 17.074 45 24 45z"}),r.jsx("path",{fill:"#1976D2",d:"M43.611 20.083H42V20H24v8h11.292A11.956 11.956 0 0 1 24 37c-5.04 0-9.284-3.261-10.794-7.787l-6.581 5.08C11.046 40.257 17.074 45 24 45c11.598 0 21-9.402 21-21 0-1.407-.144-2.781-.389-4.117z"})]}),"Sign up with Google"]}),r.jsxs("p",{className:"text-center text-sm text-gray-600 mt-6",children:["Already have an account?"," ",r.jsx(ot,{to:"/auth/login",className:"text-primary-600 hover:underline",children:"Sign in"})]})]})})}function tv(){const s=Zt(),[a,o]=b.useState({business_name:"",business_description:"",business_phone:"",business_email:"",business_website:"",first_name:"",last_name:"",email:"",phone_number:"",password:"",password_confirm:"",terms_accepted:!1}),[i,u]=b.useState(null),m=hs("/business/register/"),f=x=>{const{name:g,type:v,value:j,checked:P}=x.target;o(k=>({...k,[g]:v==="checkbox"?P:j}))},p=()=>{const x=/^[^\s@]+@[^\s@]+\.[^\s@]+$/,g=/^\+?[0-9]{7,15}$/;return a.business_name.trim()?!a.first_name.trim()||!a.last_name.trim()?"First and last name are required":x.test(a.email)?g.test(a.phone_number)?a.password.length<8?"Password must be at least 8 characters":a.password!==a.password_confirm?"Passwords do not match":a.terms_accepted?null:"You must accept the terms to continue":"Admin phone number is invalid":"Admin email is invalid":"Business name is required"},y=async x=>{x.preventDefault();const g=p();if(g){u(g);return}const v={business:{name:a.business_name,description:a.business_description,phone:a.business_phone||null,email:a.business_email||null,website:a.business_website||null},admin_user:{first_name:a.first_name,last_name:a.last_name,email:a.email,phone_number:a.phone_number,password:a.password}},j=await m.createData(v);!j||j.error?u((j==null?void 0:j.detail)||"Failed to register business"):(j.access&&localStorage.setItem("auth_token",j.access),j.refresh&&localStorage.setItem("refresh_token",j.refresh),s("/business/register/success"))};return r.jsx("div",{className:"container flex justify-center items-center py-10",children:r.jsxs("div",{className:"w-full max-w-3xl bg-white rounded-lg shadow-md p-8 space-y-8",children:[r.jsx("h1",{className:"text-2xl font-bold text-gray-900 text-center",children:"Register Your Business"}),i&&r.jsx("div",{className:"bg-red-50 border border-red-200 text-red-700 px-4 py-2 rounded text-sm",children:i}),r.jsxs("form",{onSubmit:y,className:"space-y-8",children:[r.jsxs("section",{children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Business Information"}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[r.jsxs("div",{className:"col-span-1 md:col-span-2",children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Business Name ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{name:"business_name",type:"text",className:"mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",value:a.business_name,onChange:f,required:!0})]}),r.jsxs("div",{className:"col-span-1 md:col-span-2",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Business Description"}),r.jsx("textarea",{name:"business_description",rows:"3",className:"mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",value:a.business_description,onChange:f})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Business Phone"}),r.jsx("input",{name:"business_phone",type:"tel",className:"mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",value:a.business_phone,onChange:f})]}),r.jsxs("div",{children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Business Email"}),r.jsx("input",{name:"business_email",type:"email",className:"mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",value:a.business_email,onChange:f})]}),r.jsxs("div",{className:"md:col-span-2",children:[r.jsx("label",{className:"block text-sm font-medium text-gray-700",children:"Business Website"}),r.jsx("input",{name:"business_website",type:"url",className:"mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",value:a.business_website,onChange:f})]})]})]}),r.jsxs("section",{children:[r.jsx("h2",{className:"text-lg font-semibold text-gray-800 mb-4",children:"Your Information (Administrator)"}),r.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[r.jsxs("div",{children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["First Name ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{name:"first_name",type:"text",className:"mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",value:a.first_name,onChange:f,required:!0})]}),r.jsxs("div",{children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Last Name ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{name:"last_name",type:"text",className:"mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",value:a.last_name,onChange:f,required:!0})]}),r.jsxs("div",{children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Email ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{name:"email",type:"email",className:"mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",value:a.email,onChange:f,required:!0})]}),r.jsxs("div",{children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Phone Number ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{name:"phone_number",type:"tel",className:"mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",placeholder:"+14255550123",value:a.phone_number,onChange:f,required:!0})]}),r.jsxs("div",{children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Password ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{name:"password",type:"password",className:"mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",value:a.password,onChange:f,required:!0})]}),r.jsxs("div",{children:[r.jsxs("label",{className:"block text-sm font-medium text-gray-700",children:["Confirm Password ",r.jsx("span",{className:"text-red-500",children:"*"})]}),r.jsx("input",{name:"password_confirm",type:"password",className:"mt-1 w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-primary-500 focus:border-primary-500",value:a.password_confirm,onChange:f,required:!0})]})]})]}),r.jsxs("div",{className:"flex items-start",children:[r.jsx("input",{name:"terms_accepted",type:"checkbox",className:"mt-1 mr-2",checked:a.terms_accepted,onChange:f,required:!0}),r.jsxs("label",{className:"text-sm text-gray-700",children:["I agree to the ",r.jsx("a",{href:"#",className:"text-primary-600 underline",children:"Terms and Conditions"})," ",r.jsx("span",{className:"text-red-500",children:"*"})]})]}),r.jsx(Ur,{type:"submit",className:"w-full",disabled:m.loading,children:m.loading?"Registering...":"Register Business"})]})]})})}function rv(){return r.jsx("div",{className:"container flex justify-center items-center py-16",children:r.jsxs("div",{className:"w-full max-w-lg bg-white rounded-lg shadow-md p-8 text-center space-y-6",children:[r.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto",children:r.jsx("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})}),r.jsx("h2",{className:"text-2xl font-bold text-gray-900",children:"Business Registered Successfully!"}),r.jsx("p",{className:"text-gray-700",children:"Your administrator account is ready, and your business profile has been created."}),r.jsxs("div",{className:"space-y-4 text-left",children:[r.jsx("h3",{className:"text-lg font-semibold text-gray-800",children:"Next Steps"}),r.jsxs("ul",{className:"list-disc pl-5 space-y-2 text-sm text-gray-700",children:[r.jsx("li",{children:r.jsx(ot,{to:"/business/settings",className:"text-primary-600 hover:underline",children:"Complete your profile"})}),r.jsx("li",{children:r.jsx(ot,{to:"/services/create",className:"text-primary-600 hover:underline",children:"Add services"})}),r.jsx("li",{children:r.jsx(ot,{to:"/employees/invite",className:"text-primary-600 hover:underline",children:"Invite employees"})})]})]}),r.jsx("div",{className:"flex flex-col sm:flex-row gap-3 justify-center pt-4",children:r.jsx(Ur,{as:ot,to:"/dashboard",className:"px-6 py-2",children:"Go to Dashboard"})})]})})}function nv(){return r.jsxs("div",{className:"container py-20 text-center",children:[r.jsx("h1",{className:"text-6xl font-bold text-primary-600 mb-4",children:"404"}),r.jsx("h2",{className:"text-3xl mb-6",children:"Page Not Found"}),r.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"The page you are looking for doesn't exist or has been moved."}),r.jsx(ot,{to:"/",className:"px-4 py-2 bg-primary-600 text-white rounded-md hover:bg-primary-700",children:"Back to Dashboard"})]})}function sv(){return r.jsx(Tx,{children:r.jsxs(_p,{children:[r.jsxs(Ce,{path:"/",element:r.jsx(Zp,{children:r.jsx(Gp,{})}),children:[r.jsx(Ce,{index:!0,element:r.jsx(Od,{})}),r.jsx(Ce,{path:"dashboard",element:r.jsx(Od,{})}),r.jsx(Ce,{path:"calendar",element:r.jsx(Lx,{})}),r.jsx(Ce,{path:"checkout",element:r.jsx(Ox,{})}),r.jsx(Ce,{path:"checkout/settings",element:r.jsx(Mx,{})}),r.jsx(Ce,{path:"checkout/refunds",element:r.jsx(Rx,{})}),r.jsx(Ce,{path:"checkout/invoices",element:r.jsx(Ax,{})}),r.jsx(Ce,{path:"customers",element:r.jsx(tm,{to:"/customers/management",replace:!0})}),r.jsx(Ce,{path:"customers/management",element:r.jsx(Yx,{})}),r.jsx(Ce,{path:"customers/list",element:r.jsx(Jx,{})}),r.jsx(Ce,{path:"customers/import",element:r.jsx(eg,{})}),r.jsx(Ce,{path:"customers/:id",element:r.jsx(Sg,{})}),r.jsx(Ce,{path:"marketing",element:r.jsx(Cg,{})}),r.jsx(Ce,{path:"forms",element:r.jsx(Mg,{})}),r.jsx(Ce,{path:"forms/edit/:id",element:r.jsx(_g,{})}),r.jsx(Ce,{path:"forms/details/:id",element:r.jsx(Hy,{})}),r.jsx(Ce,{path:"reports",element:r.jsx(Vy,{})}),r.jsx(Ce,{path:"reports/all",element:r.jsx(Qy,{})}),r.jsx(Ce,{path:"reports/transactions",element:r.jsx(Yy,{})}),r.jsx(Ce,{path:"settings",element:r.jsx(Jy,{})}),r.jsx(Ce,{path:"settings/all",element:r.jsx(qy,{})}),r.jsx(Ce,{path:"settings/employees",element:r.jsx(Ky,{})}),r.jsx(Ce,{path:"settings/services",element:r.jsx(Xy,{})}),r.jsx(Ce,{path:"*",element:r.jsx(nv,{})})]}),r.jsx(Ce,{path:"auth/login",element:r.jsx(Zy,{})}),r.jsx(Ce,{path:"auth/signup",element:r.jsx(ev,{})}),r.jsx(Ce,{path:"business/register",element:r.jsx(tv,{})}),r.jsx(Ce,{path:"business/register/success",element:r.jsx(rv,{})})]})})}Hh.createRoot(document.getElementById("root")).render(r.jsx(Wd.StrictMode,{children:r.jsx(Hp,{children:r.jsx(sv,{})})}));export{Aa as _,av as c,Ud as g};
//# sourceMappingURL=index-L2zCFnCv.js.map
