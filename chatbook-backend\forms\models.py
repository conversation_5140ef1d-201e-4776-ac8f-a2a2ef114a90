from django.db import models
from django.conf import settings
from django.utils.translation import gettext_lazy as _
from django.utils import timezone as django_timezone


class FormTemplate(models.Model):
    """
    Model for storing form templates that can be used across businesses.
    """
    name = models.CharField(max_length=255)
    document_type = models.CharField(max_length=100)
    status = models.CharField(
        max_length=50, 
        choices=[('Published', 'Published'), ('Draft', 'Draft')],
        default='Draft'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    business = models.ForeignKey(
        'business.Business', 
        on_delete=models.CASCADE, 
        related_name='form_templates'
    )
    content = models.JSONField(
        default=dict, 
        help_text=_('JSON content of the form template')
    )
    
    class Meta:
        verbose_name = 'Form Template'
        verbose_name_plural = 'Form Templates'
        ordering = ['name']
    
    def __str__(self):
        return f"{self.name} ({self.business.name})"

    def save(self, *args, **kwargs):
        """Override save to automatically sync with BusinessRequiredForm"""
        # Call the parent save method first
        super().save(*args, **kwargs)
        
        # Only sync if the form is published
        if self.status == 'Published':
            self._sync_mandatory_status()
    
    def _sync_mandatory_status(self):
        """Sync the mandatory status with BusinessRequiredForm records"""
        try:
            # Check if the form is marked as mandatory in its content
            content = self.content or {}
            settings = content.get('settings', {})
            is_mandatory = settings.get('mandatory', False)
            
            if is_mandatory:
                # Create or update BusinessRequiredForm record
                business_required_form, created = BusinessRequiredForm.objects.get_or_create(
                    business=self.business,
                    form_template=self,
                    defaults={
                        'is_required': True,
                        'required_for_new_customers': True,
                        'required_for_existing_customers': False,
                        'order': 0
                    }
                )
                
                # Update existing record if needed
                if not created and not business_required_form.is_required:
                    business_required_form.is_required = True
                    business_required_form.save(update_fields=['is_required', 'updated_at'])
            else:
                # Form is not mandatory, remove BusinessRequiredForm record if it exists
                try:
                    business_required_form = BusinessRequiredForm.objects.get(
                        business=self.business,
                        form_template=self
                    )
                    business_required_form.delete()
                except BusinessRequiredForm.DoesNotExist:
                    # No BusinessRequiredForm record exists, which is correct
                    pass
                    
        except Exception as e:
            # Log the error but don't fail the save operation
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error syncing mandatory status for form {self.name}: {str(e)}")

    @property
    def is_mandatory(self):
        """Check if this form is marked as mandatory"""
        content = self.content or {}
        settings = content.get('settings', {})
        return settings.get('mandatory', False)


class BusinessRequiredForm(models.Model):
    """
    Model to track which forms are required for each business.
    This allows businesses to have multiple required forms.
    """
    business = models.ForeignKey(
        'business.Business',
        on_delete=models.CASCADE,
        related_name='required_forms'
    )
    form_template = models.ForeignKey(
        FormTemplate,
        on_delete=models.CASCADE,
        related_name='business_requirements'
    )
    is_required = models.BooleanField(
        default=True,
        help_text=_('Whether this form is required for customers')
    )
    required_for_new_customers = models.BooleanField(
        default=True,
        help_text=_('Whether this form is required for new customers')
    )
    required_for_existing_customers = models.BooleanField(
        default=False,
        help_text=_('Whether this form is required for existing customers')
    )
    order = models.PositiveIntegerField(
        default=0,
        help_text=_('Order in which forms should be presented to customers')
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Business Required Form'
        verbose_name_plural = 'Business Required Forms'
        ordering = ['business', 'order', 'form_template__name']
        unique_together = ('business', 'form_template')

    def __str__(self):
        return f"{self.business.name} - {self.form_template.name} (Required: {self.is_required})"

    def clean(self):
        """Validate that the form template belongs to the same business"""
        from django.core.exceptions import ValidationError
        
        if self.business and self.form_template and self.business != self.form_template.business:
            raise ValidationError({
                'form_template': 'Form template must belong to the same business.'
            })


class FormSubmission(models.Model):
    """
    Model to store form submissions from users
    """
    STATUS_CHOICES = [
        ('Submitted', 'Submitted'), 
        ('Reviewed', 'Reviewed'), 
        ('Approved', 'Approved'),
        ('Rejected', 'Rejected')
    ]
    
    form_template = models.ForeignKey(
        FormTemplate, 
        on_delete=models.CASCADE, 
        related_name='submissions'
    )
    # Link directly to the BusinessCustomer record representing the specific
    # customer–business relationship. This makes it easy to fetch submissions
    # for a customer in the context of a specific business.
    business_customer = models.ForeignKey(
        'business.BusinessCustomer',
        on_delete=models.CASCADE,
        related_name='form_submissions',
        help_text=_('Business–Customer relationship this submission belongs to')
    )
    submitted_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        related_name='form_submissions', 
        null=True, 
        blank=True
    )
    content = models.JSONField(help_text=_('JSON content of the form submission'))
    status = models.CharField(
        max_length=50, 
        default='Submitted', 
        choices=STATUS_CHOICES
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        verbose_name = 'Form Submission'
        verbose_name_plural = 'Form Submissions'
        ordering = ['-created_at']

    def __str__(self):
        template_name = self.form_template.name if self.form_template else 'Unknown'
        return f"Submission for {template_name} ({self.created_at.strftime('%Y-%m-%d')})"

    @property
    def business(self):
        """Get business from business_customer relationship"""
        return self.business_customer.business

    @property
    def customer(self):
        """Get customer from business_customer relationship"""
        return self.business_customer.customer
    
    def save(self, *args, **kwargs):
        """Override save to update BusinessCustomer form tracking"""
        is_new = self.pk is None
        super().save(*args, **kwargs)
        
        # If this is a new submission, update completion status
        if is_new:
            self.business_customer.update_completion_status()


class Signature(models.Model):
    """
    Model to store electronic signatures for both customers and employees
    """
    SIGNER_TYPE_CHOICES = [
        ('customer', 'Customer'),
        ('employee', 'Employee'),
    ]
    
    business = models.ForeignKey(
        'business.Business', 
        on_delete=models.CASCADE, 
        related_name='signatures'
    )
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.CASCADE, 
        related_name='signatures'
    )
    signer_type = models.CharField(max_length=20, choices=SIGNER_TYPE_CHOICES)
    
    # If customer signature
    customer = models.ForeignKey(
        'customers.CustomerProfile', 
        on_delete=models.CASCADE, 
        related_name='signatures', 
        null=True, 
        blank=True
    )
    
    # If employee signature
    employee = models.ForeignKey(
        'employees.Employee', 
        on_delete=models.CASCADE,
        related_name='signatures', 
        null=True, 
        blank=True
    )
    
    # The signature data (typically a base64 encoded image)
    signature_data = models.TextField()
    
    # Form submission this signature is associated with
    # Required for customer signatures, optional for employee signatures
    form_submission = models.ForeignKey(
        FormSubmission, 
        on_delete=models.CASCADE,  # Changed from SET_NULL to CASCADE
        related_name='signatures', 
        null=True, 
        blank=True,
        help_text=_('Form submission this signature is associated with. Required for customer signatures.')
    )
    
    # Metadata
    ip_address = models.GenericIPAddressField(null=True, blank=True)
    user_agent = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    class Meta:
        verbose_name = 'Signature'
        verbose_name_plural = 'Signatures'
        ordering = ['-created_at']
    
    def __str__(self):
        if self.signer_type == 'customer':
            signer = f"Customer: {self.customer}" if self.customer else f"User: {self.user}"
        else:
            signer = f"Employee: {self.employee}" if self.employee else f"User: {self.user}"
        return f"Signature by {signer} ({self.created_at.strftime('%Y-%m-%d')})"

    def clean(self):
        """Validate that form_submission is provided for customer signatures"""
        from django.core.exceptions import ValidationError
        
        if self.signer_type == 'customer' and not self.form_submission:
            raise ValidationError({
                'form_submission': 'Form submission is required for customer signatures.'
            })
        
        if self.signer_type == 'customer' and not self.customer:
            raise ValidationError({
                'customer': 'Customer must be specified for customer signatures.'
            })
        
        if self.signer_type == 'employee' and not self.employee:
            raise ValidationError({
                'employee': 'Employee must be specified for employee signatures.'
            })
    
    def save(self, *args, **kwargs):
        """Override save to update BusinessCustomer form tracking"""
        is_new = self.pk is None
        super().save(*args, **kwargs)
        
        # If this is a new customer signature, update completion status
        if is_new and self.signer_type == 'customer' and self.form_submission:
            self.form_submission.business_customer.update_completion_status()
